-- Y-运营活动-限时抢购.xls
local item_table={
[1]={item_id=26355,num=5,is_bind=1},
[2]={item_id=26356,num=5,is_bind=1},
[3]={item_id=26347,num=5,is_bind=1},
[4]={item_id=26357,num=5,is_bind=1},
[5]={item_id=26348,num=5,is_bind=1},
[6]={item_id=26455,num=1,is_bind=1},
[7]={item_id=26452,num=5,is_bind=1},
[8]={item_id=26449,num=5,is_bind=1},
[9]={item_id=26374,num=5,is_bind=1},
[10]={item_id=26375,num=5,is_bind=1},
[11]={item_id=26371,num=5,is_bind=1},
[12]={item_id=26372,num=5,is_bind=1},
[13]={item_id=26367,num=5,is_bind=1},
[14]={item_id=26368,num=5,is_bind=1},
[15]={item_id=26345,num=5,is_bind=1},
[16]={item_id=26369,num=5,is_bind=1},
[17]={item_id=26380,num=5,is_bind=1},
[18]={item_id=26377,num=5,is_bind=1},
[19]={item_id=27909,num=5,is_bind=1},
[20]={item_id=27908,num=5,is_bind=1},
[21]={item_id=26358,num=5,is_bind=1},
[22]={item_id=27910,num=5,is_bind=1},
[23]={item_id=26360,num=5,is_bind=1},
[24]={item_id=26351,num=5,is_bind=1},
[25]={item_id=26362,num=5,is_bind=1},
[26]={item_id=26361,num=5,is_bind=1},
[27]={item_id=26353,num=5,is_bind=1},
[28]={item_id=26363,num=5,is_bind=1},
[29]={item_id=26354,num=5,is_bind=1},
[30]={item_id=37302,num=1,is_bind=1},
[31]={item_id=37206,num=1,is_bind=1},
[32]={item_id=37002,num=1,is_bind=1},
[33]={item_id=38944,num=1,is_bind=1},
[34]={item_id=27848,num=1,is_bind=1},
[35]={item_id=38946,num=1,is_bind=1},
[36]={item_id=26569,num=1,is_bind=1},
[37]={item_id=38947,num=1,is_bind=1},
[38]={item_id=22633,num=10,is_bind=1},
[39]={item_id=38948,num=1,is_bind=1},
[40]={item_id=22008,num=2,is_bind=1},
[41]={item_id=26450,num=1,is_bind=1},
[42]={item_id=26443,num=5,is_bind=1},
[43]={item_id=37701,num=1,is_bind=1},
[44]={item_id=38945,num=1,is_bind=1},
}

return {
other_cfg={
{}
},

other_cfg_meta_table_map={
},
reward_cfg={
{reward_list={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},},
{rmb_seq=2,rmb_price=198,gift_name="仙途助力礼包",reward_list={[0]=item_table[4],[1]=item_table[5],[2]=item_table[3]},return_gold=1980,price_show=12888,},
{rmb_seq=3,rmb_price=648,gift_name="不败仙王礼包",return_gold=6480,price_show=32800,},
{rmb_seq=4,rmb_price=1288,gift_name="无敌仙帝礼包",reward_list={[0]=item_table[6],[1]=item_table[7],[2]=item_table[8]},return_gold=12880,price_show=64800,},
{grade=2,open_begine_day=4,close_end_day=5,rmb_seq=5,},
{grade=2,open_begine_day=4,close_end_day=5,rmb_seq=6,},
{grade=2,open_begine_day=4,close_end_day=5,rmb_seq=7,},
{grade=2,open_begine_day=4,close_end_day=5,rmb_seq=8,},
{grade=3,open_begine_day=6,close_end_day=7,rmb_seq=9,},
{grade=3,open_begine_day=6,close_end_day=7,rmb_seq=10,},
{grade=3,open_begine_day=6,close_end_day=7,rmb_seq=11,},
{grade=3,open_begine_day=6,close_end_day=7,rmb_seq=12,},
{grade=4,open_begine_day=8,close_end_day=9,rmb_seq=13,},
{grade=4,open_begine_day=8,close_end_day=9,rmb_seq=14,},
{grade=4,open_begine_day=8,close_end_day=9,rmb_seq=15,},
{grade=4,open_begine_day=8,close_end_day=9,rmb_seq=16,},
{grade=5,open_begine_day=10,close_end_day=11,rmb_seq=17,reward_list={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11]},},
{grade=5,open_begine_day=10,close_end_day=11,rmb_seq=18,reward_list={[0]=item_table[10],[1]=item_table[12],[2]=item_table[11]},},
{grade=5,open_begine_day=10,close_end_day=11,rmb_seq=19,},
{grade=5,open_begine_day=10,close_end_day=11,rmb_seq=20,},
{grade=6,open_begine_day=12,close_end_day=13,rmb_seq=21,},
{grade=6,open_begine_day=12,close_end_day=13,rmb_seq=22,},
{grade=6,open_begine_day=12,close_end_day=13,rmb_seq=23,},
{grade=6,open_begine_day=12,close_end_day=13,rmb_seq=24,},
{grade=7,open_begine_day=14,close_end_day=15,rmb_seq=25,reward_list={[0]=item_table[13],[1]=item_table[14],[2]=item_table[15]},},
{grade=7,open_begine_day=14,close_end_day=15,rmb_seq=26,reward_list={[0]=item_table[16],[1]=item_table[17],[2]=item_table[18]},},
{grade=7,open_begine_day=14,close_end_day=15,rmb_seq=27,},
{grade=7,open_begine_day=14,close_end_day=15,rmb_seq=28,},
{grade=8,open_begine_day=16,close_end_day=17,rmb_seq=29,reward_list={[0]=item_table[19],[1]=item_table[20],[2]=item_table[21]},},
{grade=8,open_begine_day=16,close_end_day=17,rmb_seq=30,reward_list={[0]=item_table[22],[1]=item_table[23],[2]=item_table[24]},},
{grade=8,open_begine_day=16,close_end_day=17,rmb_seq=31,},
{grade=8,open_begine_day=16,close_end_day=17,rmb_seq=32,},
{grade=9,open_begine_day=18,close_end_day=999,rmb_seq=33,reward_list={[0]=item_table[25],[1]=item_table[26],[2]=item_table[27]},},
{grade=9,open_begine_day=18,close_end_day=999,rmb_seq=34,reward_list={[0]=item_table[28],[1]=item_table[29],[2]=item_table[27]},},
{grade=9,open_begine_day=18,close_end_day=999,rmb_seq=35,},
{grade=9,open_begine_day=18,close_end_day=999,rmb_seq=36,}
},

reward_cfg_meta_table_map={
[21]=1,	-- depth:1
[9]=29,	-- depth:1
[13]=33,	-- depth:1
[5]=25,	-- depth:1
[15]=3,	-- depth:1
[7]=3,	-- depth:1
[11]=3,	-- depth:1
[35]=3,	-- depth:1
[19]=3,	-- depth:1
[27]=3,	-- depth:1
[23]=3,	-- depth:1
[31]=3,	-- depth:1
[26]=2,	-- depth:1
[30]=2,	-- depth:1
[28]=4,	-- depth:1
[32]=4,	-- depth:1
[34]=2,	-- depth:1
[18]=2,	-- depth:1
[22]=2,	-- depth:1
[20]=4,	-- depth:1
[16]=4,	-- depth:1
[14]=34,	-- depth:2
[12]=4,	-- depth:1
[10]=30,	-- depth:2
[8]=4,	-- depth:1
[6]=26,	-- depth:2
[24]=4,	-- depth:1
[36]=4,	-- depth:1
},
buy_all={
[1]={grade=1,},
[2]={grade=2,rmb_seq=2,},
[3]={grade=3,rmb_seq=3,},
[4]={grade=4,rmb_seq=4,},
[5]={grade=5,rmb_seq=5,},
[6]={grade=6,rmb_seq=6,},
[7]={grade=7,rmb_seq=7,},
[8]={grade=8,rmb_seq=8,},
[9]={grade=9,rmb_seq=9,}
},

buy_all_meta_table_map={
},
drop_reward_cfg={
{},
{rmb_seq=2,},
{rmb_seq=3,},
{rmb_seq=4,},
{rmb_seq=5,},
{rmb_seq=6,},
{rmb_seq=7,},
{rmb_seq=8,},
{rmb_seq=9,},
{rmb_seq=10,},
{rmb_seq=11,},
{rmb_seq=12,},
{rmb_seq=13,},
{rmb_seq=14,},
{rmb_seq=15,},
{rmb_seq=16,},
{rmb_seq=17,},
{rmb_seq=18,},
{rmb_seq=19,},
{rmb_seq=20,},
{rmb_seq=21,},
{rmb_seq=22,reward_item={[0]=item_table[30]},},
{rmb_seq=23,},
{rmb_seq=24,},
{rmb_seq=25,},
{rmb_seq=26,},
{rmb_seq=27,},
{rmb_seq=28,},
{rmb_seq=29,},
{rmb_seq=30,},
{rmb_seq=31,reward_item={[0]=item_table[31]},},
{rmb_seq=32,reward_item={[0]=item_table[32]},},
{rmb_seq=33,},
{rmb_seq=34,},
{rmb_seq=35,},
{rmb_seq=36,}
},

drop_reward_cfg_meta_table_map={
[27]=31,	-- depth:1
[26]=22,	-- depth:1
[24]=32,	-- depth:1
[23]=27,	-- depth:2
[34]=26,	-- depth:2
[30]=34,	-- depth:3
[28]=24,	-- depth:2
[18]=30,	-- depth:4
[19]=23,	-- depth:3
[35]=19,	-- depth:4
[16]=28,	-- depth:3
[15]=35,	-- depth:5
[14]=18,	-- depth:5
[12]=16,	-- depth:4
[11]=15,	-- depth:6
[10]=14,	-- depth:6
[8]=12,	-- depth:5
[7]=11,	-- depth:7
[6]=10,	-- depth:7
[4]=8,	-- depth:6
[3]=7,	-- depth:8
[2]=6,	-- depth:8
[20]=4,	-- depth:7
[36]=20,	-- depth:8
},
extra_reward={
{reward_item={[0]=item_table[33],[1]=item_table[34]},},
{grade=2,},
{grade=3,reward_item={[0]=item_table[35],[1]=item_table[36]},},
{grade=4,reward_item={[0]=item_table[37],[1]=item_table[38]},},
{grade=5,reward_item={[0]=item_table[39],[1]=item_table[36]},},
{grade=6,reward_item={[0]=item_table[33],[1]=item_table[36]},},
{grade=7,},
{grade=8,},
{grade=9,reward_item={[0]=item_table[37],[1]=item_table[36]},}
},

extra_reward_meta_table_map={
[8]=3,	-- depth:1
},
other_cfg_default_table={day_reward_item={[0]=item_table[40]},probability_show="1.活动期间购买礼包必得<color=#99ffbb>绝版外观</color>！\n2.本直购活动消费金额<color=#99ffbb>不计入</color>累充活动。\n3.当任意礼包购买次数为零时<color=#99ffbb>不可以</color>进行一键购买",},

reward_cfg_default_table={grade=1,open_begine_day=1,close_end_day=3,rmb_seq=1,rmb_type=36,rmb_price=98,open_level=100,gift_name="一念仙凡礼包",reward_list={[0]=item_table[41],[1]=item_table[8],[2]=item_table[42]},return_gold=980,buy_limit=1,price_show=6888,gold_show=1,},

buy_all_default_table={grade=1,rmb_type=37,rmb_seq=1,rmb_price=1888,original_price=2232,model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid="37701|37206|37002",special_show_name="无敌",display_pos="33|0",display_scale=0.45,display_rotation="0|140|0",model_pos="0.6|-2.2|0",special_img="",},

drop_reward_cfg_default_table={rmb_seq=1,reward_item={[0]=item_table[43]},drop_per=10000,},

extra_reward_default_table={grade=1,reward_item={[0]=item_table[44],[1]=item_table[36]},}

}

