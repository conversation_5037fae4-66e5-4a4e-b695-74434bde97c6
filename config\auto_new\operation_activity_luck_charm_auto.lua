-- Y-运营活动-幸运锦鲤.xls
local item_table={
[1]={item_id=37043,num=1,is_bind=1},
[2]={item_id=37715,num=1,is_bind=1},
[3]={item_id=26504,num=1,is_bind=1},
[4]={item_id=26519,num=1,is_bind=1},
[5]={item_id=26557,num=1,is_bind=1},
[6]={item_id=37214,num=1,is_bind=1},
[7]={item_id=45017,num=1,is_bind=1},
[8]={item_id=26193,num=1,is_bind=1},
[9]={item_id=26191,num=1,is_bind=1},
[10]={item_id=27908,num=1,is_bind=1},
[11]={item_id=26203,num=1,is_bind=1},
[12]={item_id=26200,num=1,is_bind=1},
[13]={item_id=26415,num=1,is_bind=1},
[14]={item_id=22072,num=1,is_bind=1},
[15]={item_id=26377,num=1,is_bind=1},
[16]={item_id=26345,num=1,is_bind=1},
[17]={item_id=48071,num=1,is_bind=1},
[18]={item_id=29379,num=1,is_bind=1},
[19]={item_id=48106,num=1,is_bind=1},
[20]={item_id=30424,num=1,is_bind=1},
[21]={item_id=26518,num=1,is_bind=1},
[22]={item_id=26502,num=1,is_bind=1},
[23]={item_id=26501,num=1,is_bind=1},
[24]={item_id=37718,num=1,is_bind=1},
[25]={item_id=37616,num=1,is_bind=1},
[26]={item_id=37122,num=1,is_bind=1},
[27]={item_id=37415,num=1,is_bind=1},
[28]={item_id=29380,num=1,is_bind=1},
[29]={item_id=37719,num=1,is_bind=1},
[30]={item_id=37618,num=1,is_bind=1},
[31]={item_id=26669,num=1,is_bind=1},
[32]={item_id=26684,num=1,is_bind=1},
[33]={item_id=37026,num=1,is_bind=1},
[34]={item_id=37215,num=1,is_bind=1},
[35]={item_id=29381,num=1,is_bind=1},
[36]={item_id=37720,num=1,is_bind=1},
[37]={item_id=37124,num=1,is_bind=1},
[38]={item_id=26555,num=1,is_bind=1},
[39]={item_id=37416,num=1,is_bind=1},
[40]={item_id=29382,num=1,is_bind=1},
[41]={item_id=37721,num=1,is_bind=1},
[42]={item_id=37619,num=1,is_bind=1},
[43]={item_id=37029,num=1,is_bind=1},
[44]={item_id=37217,num=1,is_bind=1},
[45]={item_id=29383,num=1,is_bind=1},
[46]={item_id=37723,num=1,is_bind=1},
[47]={item_id=37617,num=1,is_bind=1},
[48]={item_id=37125,num=1,is_bind=1},
[49]={item_id=37417,num=1,is_bind=1},
[50]={item_id=29384,num=1,is_bind=1},
[51]={item_id=26667,num=1,is_bind=1},
[52]={item_id=26666,num=1,is_bind=1},
[53]={item_id=37629,num=1,is_bind=1},
[54]={item_id=37218,num=1,is_bind=1},
[55]={item_id=29394,num=1,is_bind=1},
[56]={item_id=26554,num=1,is_bind=1},
[57]={item_id=37422,num=1,is_bind=1},
[58]={item_id=29396,num=1,is_bind=1},
[59]={item_id=37732,num=1,is_bind=1},
[60]={item_id=37632,num=1,is_bind=1},
[61]={item_id=37221,num=1,is_bind=1},
[62]={item_id=29398,num=1,is_bind=1},
[63]={item_id=26683,num=1,is_bind=1},
[64]={item_id=37734,num=1,is_bind=1},
[65]={item_id=37634,num=1,is_bind=1},
[66]={item_id=37115,num=1,is_bind=1},
[67]={item_id=37423,num=1,is_bind=1},
[68]={item_id=29400,num=1,is_bind=1},
[69]={item_id=48107,num=1,is_bind=1},
[70]={item_id=37909,num=1,is_bind=1},
[71]={item_id=37809,num=1,is_bind=1},
[72]={item_id=29361,num=1,is_bind=1},
[73]={item_id=26194,num=1,is_bind=1},
[74]={item_id=37911,num=1,is_bind=1},
[75]={item_id=37810,num=1,is_bind=1},
[76]={item_id=29362,num=1,is_bind=1},
[77]={item_id=37912,num=1,is_bind=1},
[78]={item_id=37812,num=1,is_bind=1},
[79]={item_id=29363,num=1,is_bind=1},
[80]={item_id=37913,num=1,is_bind=1},
[81]={item_id=37813,num=1,is_bind=1},
[82]={item_id=29364,num=1,is_bind=1},
[83]={item_id=37914,num=1,is_bind=1},
[84]={item_id=37814,num=1,is_bind=1},
[85]={item_id=29365,num=1,is_bind=1},
[86]={item_id=37915,num=1,is_bind=1},
[87]={item_id=37815,num=1,is_bind=1},
[88]={item_id=29366,num=1,is_bind=1},
[89]={item_id=37918,num=1,is_bind=1},
[90]={item_id=37819,num=1,is_bind=1},
[91]={item_id=29385,num=1,is_bind=1},
[92]={item_id=37919,num=1,is_bind=1},
[93]={item_id=37820,num=1,is_bind=1},
[94]={item_id=29387,num=1,is_bind=1},
[95]={item_id=37821,num=1,is_bind=1},
[96]={item_id=37920,num=1,is_bind=1},
[97]={item_id=29389,num=1,is_bind=1},
[98]={item_id=37921,num=1,is_bind=1},
[99]={item_id=37822,num=1,is_bind=1},
[100]={item_id=29391,num=1,is_bind=1},
[101]={item_id=29380,num=1,is_bind=0},
[102]={item_id=26191,num=1,is_bind=0},
[103]={item_id=27860,num=1,is_bind=0},
[104]={item_id=91419,num=1,is_bind=0},
[105]={item_id=48106,num=1,is_bind=0},
[106]={item_id=30424,num=1,is_bind=0},
[107]={item_id=26193,num=1,is_bind=0},
[108]={item_id=46555,num=10,is_bind=1},
[109]={item_id=48071,num=1,is_bind=0},
}

return {
config_param={
{grade=0,},
{start_server_day=17,end_server_day=24,grade=1,interface=1,},
{start_server_day=24,end_server_day=31,grade=2,},
{start_server_day=31,end_server_day=38,grade=3,interface=1,},
{start_server_day=38,end_server_day=45,grade=4,},
{start_server_day=45,end_server_day=52,grade=5,interface=1,},
{start_server_day=52,end_server_day=59,grade=6,},
{start_server_day=59,end_server_day=66,grade=7,interface=1,},
{start_server_day=66,end_server_day=73,grade=8,},
{start_server_day=73,end_server_day=80,interface=1,},
{start_server_day=80,end_server_day=87,},
{start_server_day=87,end_server_day=94,},
{start_server_day=94,end_server_day=101,},
{start_server_day=101,end_server_day=108,},
{start_server_day=108,end_server_day=115,},
{start_server_day=115,end_server_day=122,},
{start_server_day=122,end_server_day=129,},
{start_server_day=129,end_server_day=136,},
{start_server_day=136,end_server_day=143,},
{start_server_day=143,end_server_day=150,},
{start_server_day=150,end_server_day=157,},
{start_server_day=157,end_server_day=164,},
{start_server_day=164,end_server_day=171,},
{start_server_day=171,end_server_day=178,},
{start_server_day=178,end_server_day=185,},
{start_server_day=185,end_server_day=999,}
},

config_param_meta_table_map={
[24]=10,	-- depth:1
[22]=10,	-- depth:1
[20]=10,	-- depth:1
[18]=10,	-- depth:1
[14]=10,	-- depth:1
[12]=10,	-- depth:1
[16]=10,	-- depth:1
[26]=10,	-- depth:1
},
grade={
{},
{grade=1,consume=2,reward=2,rebate=2,},
{grade=2,consume=3,reward=3,rebate=3,},
{grade=3,consume=4,reward=4,rebate=4,},
{grade=4,consume=5,reward=5,rebate=5,},
{grade=5,consume=6,reward=6,rebate=6,},
{grade=6,consume=7,reward=7,rebate=7,},
{grade=7,consume=8,reward=8,rebate=8,},
{grade=8,consume=9,reward=9,rebate=9,},
{grade=9,consume=10,reward=10,rebate=10,}
},

grade_meta_table_map={
},
consume={
{},
{onekey_lotto_num=10,lotto_btn="转10次",consume_count=10,},
{onekey_lotto_num=50,lotto_btn="转50次",consume_count=45,discount_text="9折",},
{consume=2,},
{consume=2,},
{consume=2,},
{consume=3,},
{consume=3,},
{consume=3,},
{consume=4,},
{consume=4,},
{consume=4,},
{consume=5,},
{consume=5,},
{consume=5,},
{consume=6,},
{consume=6,},
{consume=6,},
{consume=7,},
{consume=7,},
{consume=7,},
{consume=8,},
{consume=8,},
{consume=8,},
{consume=9,},
{consume=9,},
{consume=9,},
{consume=10,},
{consume=10,},
{consume=10,},
{consume=11,},
{consume=11,},
{consume=11,},
{consume=12,},
{consume=12,},
{consume=12,}
},

consume_meta_table_map={
[8]=2,	-- depth:1
[29]=8,	-- depth:2
[23]=29,	-- depth:3
[20]=23,	-- depth:4
[35]=20,	-- depth:5
[17]=35,	-- depth:6
[5]=17,	-- depth:7
[26]=5,	-- depth:8
[32]=26,	-- depth:9
[11]=32,	-- depth:10
[14]=11,	-- depth:11
[30]=3,	-- depth:1
[33]=30,	-- depth:2
[18]=33,	-- depth:3
[24]=18,	-- depth:4
[21]=24,	-- depth:5
[15]=21,	-- depth:6
[12]=15,	-- depth:7
[9]=12,	-- depth:8
[6]=9,	-- depth:9
[27]=6,	-- depth:10
[36]=27,	-- depth:11
},
reward={
{reward_item=item_table[1],reward_type=1,guarantee_reward_limit=3,broadcast=1,},
{reward_id=2,reward_item=item_table[2],},
{reward_id=3,reward_item=item_table[3],reward_type=1,guarantee_reward_limit=999,},
{reward_id=4,reward_item=item_table[4],},
{reward_id=5,reward_item=item_table[5],},
{reward_id=6,reward_item=item_table[6],},
{reward_id=7,reward_type=1,guarantee_reward_limit=5,broadcast=1,},
{reward_id=8,reward_item=item_table[7],},
{reward_id=9,reward_item=item_table[8],guarantee_reward_limit=10,rewrad_rare_show=0.005,},
{reward_id=10,reward_item=item_table[9],reward_type=2,},
{reward_id=11,reward_item=item_table[10],},
{reward_id=12,reward_item=item_table[11],},
{reward_id=13,reward_item=item_table[12],rewrad_rare_show=0.15,},
{reward_id=14,reward_item=item_table[13],},
{reward_id=15,reward_item=item_table[14],rewrad_rare_show=0.02,},
{reward_id=16,reward_item=item_table[15],},
{reward_id=17,reward_item=item_table[16],rewrad_rare_show=0.03,},
{reward_id=18,reward_item=item_table[17],rewrad_rare_show=0.05,},
{reward_id=19,reward_item=item_table[18],broadcast=1,},
{reward_id=20,reward_item=item_table[19],reward_type=2,rewrad_rare_show=0.005,},
{reward_id=21,reward_item=item_table[20],rewrad_rare_show=0.135,},
{reward_id=22,reward_item=item_table[21],},
{reward_id=23,reward_item=item_table[22],rewrad_rare_show=0.04,},
{reward_id=24,reward_item=item_table[23],rewrad_rare_show=0.1,},
{reward=2,reward_item=item_table[24],},
{reward_id=2,reward_item=item_table[25],},
{reward=2,},
{reward=2,},
{reward=2,reward_item=item_table[26],},
{reward=2,reward_item=item_table[27],},
{reward=2,},
{reward=2,},
{reward_id=9,reward_item=item_table[8],guarantee_reward_limit=10,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,reward_item=item_table[28],},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=3,reward_item=item_table[29],},
{reward=3,reward_item=item_table[30],},
{reward=3,reward_item=item_table[31],},
{reward_id=4,reward_item=item_table[32],},
{reward=3,reward_item=item_table[33],},
{reward_id=6,reward_item=item_table[34],},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,reward_item=item_table[35],},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=4,reward_item=item_table[36],},
{reward_id=2,reward_item=item_table[37],},
{reward=4,},
{reward=4,},
{reward=4,reward_item=item_table[38],},
{reward=4,reward_item=item_table[39],},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,reward_item=item_table[40],},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=5,reward_item=item_table[41],},
{reward_id=2,reward_item=item_table[42],},
{reward=5,},
{reward=5,},
{reward=5,reward_item=item_table[43],},
{reward=5,reward_item=item_table[44],},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=5,reward_item=item_table[45],},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=6,reward_item=item_table[46],},
{reward_id=2,reward_item=item_table[47],},
{reward=6,},
{reward=6,},
{reward=6,reward_item=item_table[48],},
{reward=6,reward_item=item_table[49],},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=6,reward_item=item_table[50],},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=6,reward_item=item_table[51],},
{reward=6,reward_item=item_table[52],},
{reward=7,reward_item=item_table[5],},
{reward_id=2,reward_item=item_table[53],},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,reward_item=item_table[54],},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,reward_item=item_table[55],},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,reward_item=item_table[56],},
{reward=8,reward_item=item_table[57],},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,reward_item=item_table[58],},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=9,reward_item=item_table[59],},
{reward_id=2,reward_item=item_table[60],},
{reward=9,},
{reward=9,},
{reward=9,},
{reward=9,reward_item=item_table[61],},
{reward=9,},
{reward=9,},
{reward=9,},
{reward=9,},
{reward=9,},
{reward=9,},
{reward=9,},
{reward=9,},
{reward=9,},
{reward=9,},
{reward=9,},
{reward=9,},
{reward=9,reward_item=item_table[62],},
{reward=9,},
{reward=9,},
{reward=9,reward_item=item_table[63],},
{reward=9,},
{reward=9,},
{reward=10,reward_item=item_table[64],},
{reward_id=2,reward_item=item_table[65],},
{reward=10,},
{reward=10,},
{reward=10,reward_item=item_table[66],},
{reward=10,reward_item=item_table[67],},
{reward=10,},
{reward=10,},
{reward=10,},
{reward=10,},
{reward=10,},
{reward=10,},
{reward=10,},
{reward=10,},
{reward=10,},
{reward=10,},
{reward=10,},
{reward=10,},
{reward=10,reward_item=item_table[68],},
{reward=10,reward_item=item_table[69],},
{reward=10,},
{reward=10,},
{reward=10,},
{reward=10,}
},

reward_meta_table_map={
[214]=22,	-- depth:1
[70]=214,	-- depth:2
[46]=22,	-- depth:1
[190]=70,	-- depth:3
[142]=190,	-- depth:4
[118]=142,	-- depth:5
[16]=17,	-- depth:1
[166]=118,	-- depth:6
[12]=18,	-- depth:1
[11]=24,	-- depth:1
[14]=24,	-- depth:1
[8]=10,	-- depth:1
[94]=166,	-- depth:7
[238]=94,	-- depth:8
[143]=23,	-- depth:1
[108]=12,	-- depth:2
[144]=24,	-- depth:1
[107]=11,	-- depth:2
[152]=8,	-- depth:2
[154]=10,	-- depth:1
[157]=13,	-- depth:1
[156]=108,	-- depth:3
[109]=157,	-- depth:2
[158]=14,	-- depth:2
[159]=15,	-- depth:1
[160]=16,	-- depth:2
[161]=17,	-- depth:1
[162]=18,	-- depth:1
[155]=107,	-- depth:3
[141]=21,	-- depth:1
[135]=159,	-- depth:2
[137]=161,	-- depth:2
[112]=160,	-- depth:3
[113]=137,	-- depth:3
[111]=135,	-- depth:3
[114]=162,	-- depth:2
[117]=141,	-- depth:2
[110]=158,	-- depth:3
[119]=143,	-- depth:2
[138]=114,	-- depth:3
[239]=119,	-- depth:3
[128]=152,	-- depth:3
[130]=154,	-- depth:2
[131]=155,	-- depth:4
[132]=156,	-- depth:4
[133]=109,	-- depth:3
[134]=110,	-- depth:4
[136]=112,	-- depth:4
[165]=117,	-- depth:3
[183]=111,	-- depth:4
[168]=144,	-- depth:2
[209]=113,	-- depth:4
[210]=138,	-- depth:4
[213]=165,	-- depth:4
[215]=239,	-- depth:4
[216]=168,	-- depth:3
[224]=128,	-- depth:4
[226]=130,	-- depth:3
[208]=136,	-- depth:5
[227]=131,	-- depth:5
[229]=133,	-- depth:4
[230]=134,	-- depth:5
[231]=183,	-- depth:5
[232]=208,	-- depth:6
[233]=209,	-- depth:5
[234]=210,	-- depth:5
[237]=213,	-- depth:5
[228]=132,	-- depth:5
[207]=231,	-- depth:6
[206]=230,	-- depth:6
[205]=229,	-- depth:5
[176]=224,	-- depth:5
[178]=226,	-- depth:4
[179]=227,	-- depth:6
[180]=228,	-- depth:6
[181]=205,	-- depth:6
[182]=206,	-- depth:7
[106]=178,	-- depth:5
[184]=232,	-- depth:7
[185]=233,	-- depth:6
[186]=234,	-- depth:6
[189]=237,	-- depth:6
[191]=215,	-- depth:5
[192]=216,	-- depth:4
[200]=176,	-- depth:6
[202]=106,	-- depth:6
[203]=179,	-- depth:7
[204]=180,	-- depth:7
[167]=191,	-- depth:6
[104]=200,	-- depth:7
[120]=192,	-- depth:5
[240]=120,	-- depth:6
[32]=104,	-- depth:8
[80]=32,	-- depth:9
[34]=202,	-- depth:7
[36]=204,	-- depth:8
[37]=181,	-- depth:7
[38]=182,	-- depth:8
[39]=207,	-- depth:7
[40]=184,	-- depth:8
[41]=185,	-- depth:7
[72]=240,	-- depth:7
[42]=186,	-- depth:7
[45]=189,	-- depth:7
[82]=34,	-- depth:8
[71]=167,	-- depth:7
[69]=45,	-- depth:8
[48]=24,	-- depth:1
[66]=42,	-- depth:8
[65]=41,	-- depth:8
[64]=40,	-- depth:9
[63]=39,	-- depth:8
[62]=38,	-- depth:9
[61]=37,	-- depth:8
[60]=36,	-- depth:9
[59]=203,	-- depth:8
[58]=82,	-- depth:9
[56]=80,	-- depth:10
[47]=23,	-- depth:1
[83]=59,	-- depth:9
[35]=83,	-- depth:10
[90]=66,	-- depth:9
[85]=61,	-- depth:9
[84]=60,	-- depth:10
[89]=65,	-- depth:9
[4]=3,	-- depth:1
[88]=64,	-- depth:10
[93]=69,	-- depth:9
[86]=62,	-- depth:10
[87]=63,	-- depth:9
[96]=72,	-- depth:8
[95]=71,	-- depth:8
[49]=1,	-- depth:1
[6]=7,	-- depth:1
[51]=3,	-- depth:1
[52]=51,	-- depth:2
[236]=20,	-- depth:1
[55]=7,	-- depth:1
[2]=1,	-- depth:1
[5]=7,	-- depth:1
[212]=236,	-- depth:2
[188]=212,	-- depth:3
[25]=1,	-- depth:1
[44]=20,	-- depth:1
[193]=1,	-- depth:1
[195]=51,	-- depth:2
[196]=52,	-- depth:3
[199]=55,	-- depth:2
[27]=3,	-- depth:1
[223]=199,	-- depth:3
[220]=196,	-- depth:4
[219]=195,	-- depth:3
[217]=1,	-- depth:1
[175]=223,	-- depth:4
[28]=4,	-- depth:2
[31]=175,	-- depth:5
[19]=20,	-- depth:1
[172]=220,	-- depth:5
[171]=219,	-- depth:4
[73]=1,	-- depth:1
[148]=172,	-- depth:6
[127]=31,	-- depth:6
[121]=1,	-- depth:1
[140]=188,	-- depth:4
[151]=127,	-- depth:7
[116]=140,	-- depth:5
[124]=148,	-- depth:7
[68]=116,	-- depth:6
[76]=124,	-- depth:8
[145]=1,	-- depth:1
[92]=68,	-- depth:7
[164]=92,	-- depth:8
[97]=1,	-- depth:1
[123]=171,	-- depth:5
[99]=123,	-- depth:6
[79]=151,	-- depth:8
[169]=1,	-- depth:1
[100]=76,	-- depth:9
[103]=79,	-- depth:9
[147]=99,	-- depth:7
[75]=147,	-- depth:8
[126]=6,	-- depth:2
[29]=5,	-- depth:2
[30]=6,	-- depth:2
[125]=5,	-- depth:2
[173]=5,	-- depth:2
[122]=121,	-- depth:2
[221]=5,	-- depth:2
[222]=6,	-- depth:2
[26]=25,	-- depth:2
[115]=19,	-- depth:2
[91]=19,	-- depth:2
[9]=7,	-- depth:1
[98]=97,	-- depth:2
[235]=19,	-- depth:2
[218]=217,	-- depth:2
[211]=19,	-- depth:2
[77]=5,	-- depth:2
[150]=6,	-- depth:2
[174]=6,	-- depth:2
[170]=2,	-- depth:2
[54]=55,	-- depth:2
[53]=5,	-- depth:2
[163]=19,	-- depth:2
[50]=2,	-- depth:2
[67]=19,	-- depth:2
[187]=19,	-- depth:2
[149]=77,	-- depth:3
[43]=19,	-- depth:2
[102]=6,	-- depth:2
[146]=145,	-- depth:2
[33]=25,	-- depth:2
[78]=6,	-- depth:2
[101]=5,	-- depth:2
[194]=193,	-- depth:2
[74]=73,	-- depth:2
[139]=19,	-- depth:2
[197]=149,	-- depth:4
[198]=6,	-- depth:2
[201]=9,	-- depth:2
[153]=201,	-- depth:3
[57]=153,	-- depth:4
[177]=57,	-- depth:5
[105]=177,	-- depth:6
[81]=105,	-- depth:7
[225]=81,	-- depth:8
[129]=225,	-- depth:9
},
rebate={
{},
{index=2,lotto_num=30,},
{index=3,lotto_num=50,},
{index=4,lotto_num=70,},
{index=5,lotto_num=90,},
{index=6,lotto_num=110,},
{index=7,lotto_num=140,},
{index=8,lotto_num=180,},
{index=9,lotto_num=220,},
{index=10,lotto_num=260,},
{index=11,lotto_num=300,},
{index=12,lotto_num=340,},
{index=13,lotto_num=380,reward_item={[0]=item_table[70]},},
{index=14,lotto_num=420,reward_item={[0]=item_table[71]},},
{index=15,lotto_num=460,reward_item={[0]=item_table[72]},},
{index=16,lotto_num=500,reward_item={[0]=item_table[73]},},
{rebate=2,},
{rebate=2,},
{index=3,lotto_num=50,},
{rebate=2,},
{index=5,lotto_num=90,},
{rebate=2,},
{index=7,lotto_num=140,},
{rebate=2,},
{index=9,lotto_num=220,},
{rebate=2,},
{index=11,lotto_num=300,},
{index=12,lotto_num=340,},
{rebate=2,reward_item={[0]=item_table[74]},},
{rebate=2,reward_item={[0]=item_table[75]},},
{rebate=2,reward_item={[0]=item_table[76]},},
{rebate=2,},
{rebate=3,},
{rebate=3,},
{index=3,lotto_num=50,},
{rebate=3,},
{index=5,lotto_num=90,},
{rebate=3,},
{index=7,lotto_num=140,},
{rebate=3,},
{index=9,lotto_num=220,},
{rebate=3,},
{index=11,lotto_num=300,},
{index=12,lotto_num=340,},
{rebate=3,reward_item={[0]=item_table[77]},},
{rebate=3,reward_item={[0]=item_table[78]},},
{rebate=3,reward_item={[0]=item_table[79]},},
{rebate=3,},
{rebate=4,},
{rebate=4,},
{rebate=4,reward_item={[0]=item_table[80]},},
{rebate=4,},
{rebate=4,reward_item={[0]=item_table[81]},},
{rebate=4,},
{rebate=4,reward_item={[0]=item_table[80]},},
{rebate=4,},
{rebate=4,reward_item={[0]=item_table[81]},},
{rebate=4,},
{rebate=4,reward_item={[0]=item_table[80]},},
{rebate=4,reward_item={[0]=item_table[81]},},
{rebate=4,reward_item={[0]=item_table[80]},},
{rebate=4,reward_item={[0]=item_table[81]},},
{rebate=4,reward_item={[0]=item_table[82]},},
{rebate=4,},
{rebate=5,},
{rebate=5,},
{index=3,lotto_num=50,},
{rebate=5,},
{index=5,lotto_num=90,},
{rebate=5,},
{index=7,lotto_num=140,},
{rebate=5,},
{index=9,lotto_num=220,},
{rebate=5,},
{index=11,lotto_num=300,},
{index=12,lotto_num=340,},
{rebate=5,reward_item={[0]=item_table[83]},},
{rebate=5,reward_item={[0]=item_table[84]},},
{rebate=5,reward_item={[0]=item_table[85]},},
{rebate=5,},
{rebate=6,},
{rebate=6,},
{index=3,lotto_num=50,},
{rebate=6,},
{index=5,lotto_num=90,},
{rebate=6,},
{index=7,lotto_num=140,},
{rebate=6,},
{index=9,lotto_num=220,},
{rebate=6,},
{index=11,lotto_num=300,},
{index=12,lotto_num=340,},
{rebate=6,reward_item={[0]=item_table[86]},},
{rebate=6,reward_item={[0]=item_table[87]},},
{rebate=6,reward_item={[0]=item_table[88]},},
{rebate=6,},
{rebate=7,},
{rebate=7,},
{index=3,lotto_num=50,},
{rebate=7,},
{index=5,lotto_num=90,},
{rebate=7,},
{index=7,lotto_num=140,},
{rebate=7,},
{index=9,lotto_num=220,},
{rebate=7,},
{index=11,lotto_num=300,},
{index=12,lotto_num=340,},
{rebate=7,reward_item={[0]=item_table[89]},},
{rebate=7,reward_item={[0]=item_table[90]},},
{rebate=7,reward_item={[0]=item_table[91]},},
{rebate=7,},
{rebate=8,},
{rebate=8,},
{rebate=8,reward_item={[0]=item_table[92]},},
{rebate=8,},
{rebate=8,reward_item={[0]=item_table[93]},},
{rebate=8,},
{index=7,lotto_num=140,},
{rebate=8,},
{index=9,lotto_num=220,},
{rebate=8,},
{index=11,lotto_num=300,},
{index=12,lotto_num=340,},
{rebate=8,reward_item={[0]=item_table[92]},},
{rebate=8,reward_item={[0]=item_table[93]},},
{rebate=8,reward_item={[0]=item_table[94]},},
{rebate=8,},
{rebate=9,},
{rebate=9,},
{index=3,lotto_num=50,},
{rebate=9,},
{rebate=9,reward_item={[0]=item_table[95]},},
{rebate=9,},
{index=7,lotto_num=140,},
{rebate=9,},
{rebate=9,reward_item={[0]=item_table[95]},},
{rebate=9,},
{rebate=9,reward_item={[0]=item_table[96]},},
{rebate=9,reward_item={[0]=item_table[95]},},
{rebate=9,reward_item={[0]=item_table[96]},},
{rebate=9,reward_item={[0]=item_table[95]},},
{rebate=9,reward_item={[0]=item_table[97]},},
{rebate=9,},
{rebate=10,},
{rebate=10,},
{rebate=10,reward_item={[0]=item_table[98]},},
{rebate=10,},
{index=5,lotto_num=90,},
{rebate=10,},
{rebate=10,reward_item={[0]=item_table[98]},},
{rebate=10,},
{index=9,lotto_num=220,},
{rebate=10,},
{rebate=10,reward_item={[0]=item_table[98]},},
{index=12,lotto_num=340,},
{rebate=10,reward_item={[0]=item_table[98]},},
{rebate=10,reward_item={[0]=item_table[99]},},
{rebate=10,reward_item={[0]=item_table[100]},},
{rebate=10,}
},

rebate_meta_table_map={
[100]=4,	-- depth:1
[130]=2,	-- depth:1
[132]=100,	-- depth:2
[102]=6,	-- depth:1
[58]=10,	-- depth:1
[56]=8,	-- depth:1
[136]=56,	-- depth:2
[54]=102,	-- depth:2
[106]=58,	-- depth:2
[138]=106,	-- depth:3
[134]=54,	-- depth:3
[66]=130,	-- depth:2
[70]=134,	-- depth:4
[72]=136,	-- depth:3
[74]=138,	-- depth:4
[122]=74,	-- depth:5
[50]=66,	-- depth:3
[82]=50,	-- depth:4
[118]=70,	-- depth:5
[84]=132,	-- depth:3
[98]=82,	-- depth:5
[86]=118,	-- depth:6
[116]=84,	-- depth:4
[88]=72,	-- depth:4
[90]=122,	-- depth:6
[114]=98,	-- depth:6
[68]=116,	-- depth:5
[120]=88,	-- depth:5
[52]=68,	-- depth:6
[24]=120,	-- depth:6
[154]=90,	-- depth:7
[26]=154,	-- depth:8
[22]=86,	-- depth:7
[20]=52,	-- depth:7
[18]=114,	-- depth:7
[12]=14,	-- depth:1
[11]=13,	-- depth:1
[9]=14,	-- depth:1
[7]=13,	-- depth:1
[5]=14,	-- depth:1
[3]=13,	-- depth:1
[152]=24,	-- depth:7
[150]=22,	-- depth:8
[104]=152,	-- depth:8
[36]=20,	-- depth:8
[38]=150,	-- depth:9
[148]=36,	-- depth:9
[146]=18,	-- depth:8
[34]=146,	-- depth:9
[40]=104,	-- depth:9
[42]=26,	-- depth:9
[137]=9,	-- depth:2
[115]=3,	-- depth:2
[112]=16,	-- depth:1
[111]=15,	-- depth:1
[110]=14,	-- depth:1
[109]=13,	-- depth:1
[144]=112,	-- depth:2
[139]=11,	-- depth:2
[108]=110,	-- depth:2
[140]=12,	-- depth:2
[107]=109,	-- depth:2
[141]=13,	-- depth:1
[147]=3,	-- depth:2
[117]=5,	-- depth:2
[151]=7,	-- depth:2
[158]=14,	-- depth:1
[149]=158,	-- depth:2
[153]=158,	-- depth:2
[128]=144,	-- depth:3
[133]=5,	-- depth:2
[127]=15,	-- depth:1
[126]=14,	-- depth:1
[155]=11,	-- depth:2
[131]=141,	-- depth:2
[125]=13,	-- depth:1
[124]=126,	-- depth:2
[123]=125,	-- depth:2
[156]=158,	-- depth:2
[121]=126,	-- depth:2
[119]=125,	-- depth:2
[157]=13,	-- depth:1
[135]=141,	-- depth:2
[143]=15,	-- depth:1
[142]=14,	-- depth:1
[80]=128,	-- depth:4
[103]=109,	-- depth:2
[57]=9,	-- depth:2
[55]=7,	-- depth:2
[53]=5,	-- depth:2
[51]=3,	-- depth:2
[48]=80,	-- depth:5
[47]=15,	-- depth:1
[46]=14,	-- depth:1
[45]=13,	-- depth:1
[44]=46,	-- depth:2
[43]=45,	-- depth:2
[41]=46,	-- depth:2
[59]=11,	-- depth:2
[39]=45,	-- depth:2
[35]=45,	-- depth:2
[32]=48,	-- depth:6
[31]=15,	-- depth:1
[30]=14,	-- depth:1
[29]=13,	-- depth:1
[28]=30,	-- depth:2
[27]=29,	-- depth:2
[25]=30,	-- depth:2
[23]=29,	-- depth:2
[21]=30,	-- depth:2
[19]=29,	-- depth:2
[37]=46,	-- depth:2
[105]=110,	-- depth:2
[60]=12,	-- depth:2
[62]=14,	-- depth:1
[101]=110,	-- depth:2
[99]=109,	-- depth:2
[96]=32,	-- depth:7
[95]=15,	-- depth:1
[94]=14,	-- depth:1
[93]=13,	-- depth:1
[92]=94,	-- depth:2
[91]=93,	-- depth:2
[89]=94,	-- depth:2
[87]=93,	-- depth:2
[85]=94,	-- depth:2
[61]=13,	-- depth:1
[83]=93,	-- depth:2
[79]=15,	-- depth:1
[78]=14,	-- depth:1
[77]=13,	-- depth:1
[76]=78,	-- depth:2
[75]=77,	-- depth:2
[73]=78,	-- depth:2
[71]=77,	-- depth:2
[69]=78,	-- depth:2
[67]=77,	-- depth:2
[64]=96,	-- depth:8
[63]=15,	-- depth:1
[159]=15,	-- depth:1
[160]=64,	-- depth:9
},
interface={
{big_reward_record_bg="cslc_dajiangjilu",fish_layout_bide_text_bg="cslc_bide1",},
{interface=1,big_reward_record_bg="cslc_dajiangjilu_1",fish_layout_bide_text_bg="cslc_bide_1",num_front="fish_bide_count_2",zaibu="cslc_zaibu_1",reward_bg="cslc_itouxdi_1",cut_line="cslc_fenge_1",reward_di2="cslc_jiangli_di2_1",fish_pao_img="cslc_pao2",},
{interface=2,},
{interface=3,},
{interface=4,},
{interface=5,},
{interface=6,},
{interface=7,},
{interface=8,},
{interface=9,},
{interface=10,},
{interface=11,}
},

interface_meta_table_map={
},
role_sp_guarantee={
{},
{sp_player_guarantee_id=2,reward_id=2,},
{sp_player_guarantee_id=3,reward_id=3,},
{sp_player_guarantee_id=4,reward_id=4,},
{sp_player_guarantee_id=5,reward_id=5,},
{sp_player_guarantee_id=6,reward_id=6,},
{sp_player_guarantee_id=7,reward_id=7,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=9,},
{reward=9,},
{reward=9,},
{reward=9,},
{reward=9,},
{reward=9,},
{reward=9,},
{reward=10,},
{reward=10,},
{reward=10,},
{reward=10,},
{reward=10,},
{reward=10,},
{reward=10,}
},

role_sp_guarantee_meta_table_map={
[44]=2,	-- depth:1
[45]=3,	-- depth:1
[52]=45,	-- depth:2
[46]=4,	-- depth:1
[47]=5,	-- depth:1
[48]=6,	-- depth:1
[49]=7,	-- depth:1
[51]=44,	-- depth:2
[53]=46,	-- depth:2
[58]=51,	-- depth:3
[55]=48,	-- depth:2
[56]=49,	-- depth:2
[42]=56,	-- depth:3
[59]=52,	-- depth:3
[60]=53,	-- depth:3
[61]=47,	-- depth:2
[62]=55,	-- depth:3
[63]=42,	-- depth:4
[65]=58,	-- depth:4
[66]=59,	-- depth:4
[67]=60,	-- depth:4
[68]=61,	-- depth:3
[54]=68,	-- depth:4
[41]=62,	-- depth:4
[35]=63,	-- depth:5
[39]=67,	-- depth:5
[9]=65,	-- depth:5
[10]=66,	-- depth:5
[11]=39,	-- depth:6
[12]=54,	-- depth:5
[13]=41,	-- depth:5
[14]=35,	-- depth:6
[16]=9,	-- depth:6
[17]=10,	-- depth:6
[18]=11,	-- depth:7
[19]=12,	-- depth:6
[20]=13,	-- depth:6
[21]=14,	-- depth:7
[40]=19,	-- depth:7
[23]=16,	-- depth:7
[25]=18,	-- depth:8
[26]=40,	-- depth:8
[27]=20,	-- depth:7
[28]=21,	-- depth:8
[30]=23,	-- depth:8
[31]=17,	-- depth:7
[32]=25,	-- depth:9
[33]=26,	-- depth:9
[34]=27,	-- depth:8
[69]=34,	-- depth:9
[37]=30,	-- depth:9
[38]=31,	-- depth:8
[24]=38,	-- depth:9
[70]=28,	-- depth:9
},
show_reward={
{},
{id=2,reward_item={[0]=item_table[101]},},
{id=3,reward_item={[0]=item_table[102]},},
{id=4,reward_item={[0]=item_table[103]},},
{id=5,reward_item={[0]=item_table[104]},},
{id=6,reward_item={[0]=item_table[105]},},
{id=7,reward_item={[0]=item_table[106]},},
{id=8,reward_item={[0]=item_table[107]},}
},

show_reward_meta_table_map={
},
config_param_default_table={start_server_day=10,end_server_day=17,week_index=5,grade=9,interface=0,open_level=100,},

grade_default_table={grade=0,cycle=1,cycle_duration=120,consume_item=46555,complement_num=40,consume=1,reward=1,sp_guarantee_x=50,sp_guarantee_n=6,sp_guarantee_finish=1,rebate=1,rule_2="1.单次转50次转盘可享<color=#6fbb6f>9折优惠</color>\n2.转动次数达到一定进度后可领取<color=#6fbb6f>进度奖励</color>\n3.每转动<color=#6fbb6f>50次</color>可得<color=#6fbb6f>珍稀奖池内的大奖之一</color>\n<color=#6fbb6f>活动概率如下：</color>",},

consume_default_table={consume=1,onekey_lotto_num=1,lotto_btn="转1次",consume_count=1,discount_text="",},

reward_default_table={reward=1,reward_id=1,reward_item=item_table[73],reward_type=3,guarantee_reward_limit=0,broadcast=0,reward_pos=0,reward_show=1,rewrad_rare_show=0.01,},

rebate_default_table={rebate=1,index=1,lotto_num=10,reward_item={[0]=item_table[108]},rebate_icon=1,},

interface_default_table={interface=0,fish_notice_bg="fish_notice_bg_1",big_reward_record_bg="bg_record_1",fish_fanli_times_bg="fish_fanli_times_bg_1",cat_say_text="cat_say_text_1",fish_layout_bide_text_bg="bg_lao_text_1",fish_big_bg="cslc_beijing_di",fanli_bg="bg_fanli_1",fanli_highlight="fanli_highlight_1",fanli_mask="fanli_mask_1",fanli_text_bg="fanli_text_bg_1",normal_bubble="normal_bubble_1",better_bubble="better_bubble_1",fish_big_reward_bg="oa_fish_bg_2",cat_sprite_tween="cat",cat_hand_sprite_tween="fish_hand_tween_1",fish_tween_1="fish_item_cell_tween_2",fish_tween_2="珍稀大奖属于你！",fish_tween_3="fish_tween_3",rule_1="珍稀大奖属于你！",num_front="fish_bide_count_1",zaibu="cslc_zaibu",reward_bg="cslc_itouxdi",cut_line="cslc_fenge",reward_di2="cslc_jiangli_di2",fish_pao_img="cslc_pao",},

role_sp_guarantee_default_table={reward=1,sp_player_guarantee_id=1,reward_id=1,show_icon=1,},

show_reward_default_table={id=1,reward_item={[0]=item_table[109]},}

}

