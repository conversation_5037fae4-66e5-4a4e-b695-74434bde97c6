-- Y-运营活动-圣兽召唤.xls
local item_table={
[1]={item_id=23350,num=1,is_bind=1},
[2]={item_id=57837,num=1,is_bind=1},
[3]={item_id=28448,num=1,is_bind=1},
[4]={item_id=28447,num=1,is_bind=1},
[5]={item_id=23351,num=1,is_bind=1},
[6]={item_id=37958,num=1,is_bind=1},
[7]={item_id=37486,num=1,is_bind=1},
[8]={item_id=37631,num=1,is_bind=1},
[9]={item_id=37731,num=1,is_bind=1},
[10]={item_id=57983,num=1,is_bind=1},
[11]={item_id=28451,num=1,is_bind=1},
[12]={item_id=28450,num=1,is_bind=1},
[13]={item_id=28449,num=1,is_bind=1},
[14]={item_id=56316,num=1,is_bind=1},
[15]={item_id=23352,num=1,is_bind=1},
[16]={item_id=57984,num=1,is_bind=1},
[17]={item_id=23353,num=1,is_bind=1},
[18]={item_id=57985,num=1,is_bind=1},
[19]={item_id=28446,num=1,is_bind=1},
[20]={item_id=56317,num=1,is_bind=1},
}

return {
rewards={
{item=item_table[1],pos_id=1,},
{grade=1,item=item_table[2],},
{item=item_table[3],pos_id=3,},
{item=item_table[4],},
{grade=1,},
{grade=1,},
{grade=1,},
{item=item_table[5],pos_id=1,},
{item=item_table[6],},
{item=item_table[7],},
{item=item_table[8],},
{item=item_table[9],},
{grade=2,item=item_table[10],},
{item=item_table[11],is_show=0,pos_id=2,},
{item=item_table[12],pos_id=3,},
{item=item_table[13],},
{grade=2,is_show=0,},
{grade=2,item=item_table[14],},
{item=item_table[15],},
{item=item_table[16],},
{},
{},
{},
{},
{},
{item=item_table[17],pos_id=1,},
{item=item_table[18],is_rare=1,},
{item=item_table[3],pos_id=3,},
{item=item_table[4],},
{item=item_table[19],},
{pos_id=2,},
{item=item_table[14],is_show=0,}
},

rewards_meta_table_map={
[22]=29,	-- depth:1
[23]=30,	-- depth:1
[24]=31,	-- depth:1
[25]=32,	-- depth:1
[9]=18,	-- depth:1
[6]=24,	-- depth:2
[5]=23,	-- depth:2
[4]=5,	-- depth:3
[19]=26,	-- depth:1
[20]=27,	-- depth:1
[28]=27,	-- depth:1
[21]=28,	-- depth:2
[16]=17,	-- depth:1
[13]=27,	-- depth:1
[12]=13,	-- depth:2
[11]=12,	-- depth:3
[10]=11,	-- depth:4
[7]=25,	-- depth:2
[3]=5,	-- depth:3
[2]=27,	-- depth:1
[8]=13,	-- depth:2
[15]=16,	-- depth:2
[1]=2,	-- depth:2
[14]=13,	-- depth:2
},
baodi={
{grade=1,},
{times=25000,item=item_table[5],},
{times=12500,item=item_table[6],},
{times=8000,item=item_table[7],},
{times=4000,item=item_table[8],},
{times=2000,item=item_table[9],},
{grade=3,item=item_table[15],}
},

baodi_meta_table_map={
},
other={
{}
},

other_meta_table_map={
},
item_cost={
{},
{draw_times=50,cost_item_num=50,cost_gold_num=2000,}
},

item_cost_meta_table_map={
},
draw_limit={
{},
{chognzhi_rmb=30,times=1000,},
{chognzhi_rmb=1000,times=999999,}
},

draw_limit_meta_table_map={
},
open_day={
{},
{start_day=8,end_day=15,grade=2,},
{start_day=16,end_day=999,grade=3,}
},

open_day_meta_table_map={
},
item_random_desc={
{grade=1,item_id=23350,},
{grade=1,item_id=57837,},
{grade=1,random_count=0.15,},
{grade=1,random_count=0.2,},
{grade=1,random_count=0.2,},
{grade=1,},
{grade=1,number=7,},
{item_id=23351,random_count=0.0001,},
{number=2,item_id=37958,random_count=0.01,},
{number=3,item_id=37486,random_count=0.02,},
{number=4,item_id=37631,random_count=0.03,},
{number=5,item_id=37731,random_count=0.05,},
{number=6,item_id=57983,},
{number=7,item_id=28451,random_count=0.15,},
{number=8,item_id=28450,},
{number=9,item_id=28449,},
{number=10,random_count=0.08,},
{number=11,item_id=56316,random_count=0.31,},
{grade=3,item_id=23352,},
{grade=3,item_id=57984,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,number=6,random_count=0.04,},
{grade=3,random_count=0.57,},
{grade=4,item_id=23353,},
{grade=4,number=2,item_id=57985,},
{grade=4,number=3,item_id=28448,random_count=0.01,},
{number=4,item_id=28447,},
{grade=4,number=5,item_id=28446,random_count=0.14,},
{grade=4,},
{grade=4,}
},

item_random_desc_meta_table_map={
[26]=8,	-- depth:1
[20]=27,	-- depth:1
[19]=8,	-- depth:1
[31]=24,	-- depth:1
[1]=8,	-- depth:1
[6]=31,	-- depth:2
[2]=27,	-- depth:1
[13]=12,	-- depth:1
[29]=30,	-- depth:1
[3]=28,	-- depth:1
[23]=30,	-- depth:1
[4]=29,	-- depth:2
[22]=29,	-- depth:2
[21]=28,	-- depth:1
[5]=30,	-- depth:1
[7]=18,	-- depth:1
[25]=7,	-- depth:2
[32]=25,	-- depth:3
},
model_show={
{},
{grade=2,model_bundle_name="model/zuoqi/2108_prefab",model_asset_name=2108,model_show_itemid=23351,},
{grade=3,model_bundle_name="model/zuoqi/2109_prefab",model_asset_name=2109,model_show_itemid=23352,},
{grade=4,model_bundle_name="model/zuoqi/2110_prefab",model_asset_name=2110,model_show_itemid=23353,}
},

model_show_meta_table_map={
},
rewards_default_table={grade=3,item=item_table[20],is_rare=0,is_show=1,pos_id=0,},

baodi_default_table={grade=2,times=15000,item=item_table[1],},

other_default_table={cost_item_id=28445,},

item_cost_default_table={cost_item_id=28445,draw_times=10,cost_item_num=10,cost_gold_num=400,},

draw_limit_default_table={chognzhi_rmb=0,times=500,},

open_day_default_table={start_day=1,end_day=7,grade=1,},

item_random_desc_default_table={grade=2,number=1,item_id=56317,random_count=0.1,},

model_show_default_table={grade=1,model_show_type=1,model_bundle_name="model/zuoqi/2016_prefab",model_asset_name=2016,model_show_itemid=23350,special_show_name="",display_pos="-260|-15|0",display_scale=1.7,rotation="0|-10|0",}

}

