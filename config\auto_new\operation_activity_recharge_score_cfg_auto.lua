-- Y-运营活动-再造通天.xls
local item_table={
[1]={item_id=22008,num=1,is_bind=1},
[2]={item_id=22532,num=1,is_bind=1},
[3]={item_id=43086,num=1,is_bind=1},
[4]={item_id=22009,num=1,is_bind=1},
[5]={item_id=30447,num=1,is_bind=1},
[6]={item_id=22532,num=3,is_bind=1},
[7]={item_id=43086,num=3,is_bind=1},
[8]={item_id=44183,num=1,is_bind=1},
[9]={item_id=30447,num=10,is_bind=1},
[10]={item_id=43086,num=5,is_bind=1},
[11]={item_id=29434,num=1,is_bind=1},
[12]={item_id=30805,num=2,is_bind=1},
[13]={item_id=22622,num=1,is_bind=1},
[14]={item_id=29437,num=1,is_bind=1},
[15]={item_id=43086,num=10,is_bind=1},
[16]={item_id=29435,num=1,is_bind=1},
[17]={item_id=30805,num=3,is_bind=1},
[18]={item_id=22623,num=1,is_bind=1},
[19]={item_id=29436,num=1,is_bind=1},
[20]={item_id=43086,num=20,is_bind=1},
[21]={item_id=44495,num=1,is_bind=1},
[22]={item_id=30805,num=5,is_bind=1},
[23]={item_id=29777,num=1,is_bind=1},
[24]={item_id=40217,num=1,is_bind=1},
[25]={item_id=30807,num=2,is_bind=1},
[26]={item_id=30807,num=5,is_bind=1},
[27]={item_id=30807,num=8,is_bind=1},
[28]={item_id=48096,num=1,is_bind=1},
[29]={item_id=26191,num=1,is_bind=1},
[30]={item_id=48099,num=1,is_bind=1},
[31]={item_id=30425,num=3,is_bind=1},
[32]={item_id=48443,num=1,is_bind=1},
[33]={item_id=48584,num=30,is_bind=1},
[34]={item_id=38805,num=1,is_bind=1},
[35]={item_id=30442,num=1,is_bind=1},
[36]={item_id=22587,num=1,is_bind=1},
[37]={item_id=30442,num=3,is_bind=1},
[38]={item_id=22099,num=288,is_bind=1},
[39]={item_id=26415,num=10,is_bind=1},
[40]={item_id=22099,num=588,is_bind=1},
[41]={item_id=29774,num=1,is_bind=1},
[42]={item_id=30806,num=5,is_bind=1},
[43]={item_id=22099,num=688,is_bind=1},
[44]={item_id=29775,num=1,is_bind=1},
[45]={item_id=30806,num=12,is_bind=1},
[46]={item_id=26193,num=1,is_bind=1},
[47]={item_id=22099,num=888,is_bind=1},
[48]={item_id=44182,num=1,is_bind=1},
[49]={item_id=30806,num=3,is_bind=1},
[50]={item_id=26194,num=1,is_bind=1},
[51]={item_id=30806,num=30,is_bind=1},
[52]={item_id=43077,num=1,is_bind=1},
[53]={item_id=29776,num=1,is_bind=1},
[54]={item_id=22624,num=1,is_bind=1},
[55]={item_id=22010,num=1,is_bind=1},
[56]={item_id=26069,num=1,is_bind=1},
[57]={item_id=39197,num=1,is_bind=1},
[58]={item_id=39476,num=3,is_bind=1},
[59]={item_id=26072,num=1,is_bind=1},
[60]={item_id=30442,num=10,is_bind=1},
}

return {
other={
{},
{rate=10,exchange_type=1,}
},

other_meta_table_map={
},
open_day={
{exchange_type=0,open_index="1|2|3",},
{start_day=2,end_day=2,grade=2,open_index="3|4|5",},
{start_day=3,end_day=3,grade=3,},
{start_day=4,end_day=4,grade=4,},
{start_day=5,end_day=5,grade=5,},
{start_day=6,end_day=6,grade=6,},
{start_day=7,end_day=7,grade=7,},
{start_day=8,end_day=999,exchange_type=0,open_index=6,}
},

open_day_meta_table_map={
},
reward={
{item=item_table[1],},
{seq=1,need_score=100,},
{seq=2,need_score=250,item=item_table[2],},
{seq=3,need_score=650,},
{seq=4,need_score=1470,item=item_table[3],grand_prix=1,},
{seq=5,need_score=2250,item=item_table[4],},
{seq=6,need_score=3050,item=item_table[5],},
{seq=7,need_score=4250,item=item_table[6],},
{seq=8,need_score=5600,item=item_table[7],},
{seq=9,need_score=7160,item=item_table[8],grand_prix=1,},
{seq=10,need_score=10240,},
{seq=11,need_score=15120,item=item_table[9],},
{seq=12,need_score=24250,item=item_table[10],},
{seq=13,need_score=37750,item=item_table[11],},
{seq=14,need_score=56080,item=item_table[12],grand_prix=1,},
{seq=15,need_score=76150,item=item_table[13],},
{seq=16,need_score=102150,item=item_table[14],},
{seq=17,need_score=128150,item=item_table[15],},
{seq=18,need_score=154150,item=item_table[16],},
{seq=19,need_score=180150,item=item_table[17],grand_prix=1,},
{seq=20,need_score=206150,item=item_table[18],},
{seq=21,need_score=232150,item=item_table[19],},
{seq=22,need_score=258150,item=item_table[20],},
{seq=23,need_score=284150,item=item_table[21],},
{seq=24,need_score=310150,item=item_table[22],grand_prix=1,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,item=item_table[23],},
{grade=2,seq=10,need_score=1640,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{seq=15,need_score=5370,},
{grade=2,},
{grade=2,},
{grade=2,need_score=7860,},
{grade=2,},
{seq=20,need_score=9520,},
{grade=2,need_score=10350,},
{grade=2,need_score=11180,},
{grade=2,need_score=12010,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,item=item_table[24],},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,need_score=4540,item=item_table[25],},
{grade=3,},
{grade=3,need_score=6200,},
{grade=3,need_score=7030,},
{grade=3,},
{grade=3,need_score=8690,item=item_table[26],},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,need_score=12840,item=item_table[27],},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,item=item_table[28],},
{grade=4,item=item_table[29],},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,item=item_table[30],},
{grade=4,item=item_table[31],},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,item=item_table[32],},
{grade=4,item=item_table[31],},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,item=item_table[33],},
{grade=5,item=item_table[34],},
{grade=5,},
{grade=5,seq=2,need_score=50,item=item_table[35],},
{grade=5,seq=3,need_score=90,item=item_table[36],},
{grade=5,},
{grade=5,seq=5,need_score=240,item=item_table[37],},
{seq=6,need_score=390,},
{grade=5,seq=7,need_score=590,item=item_table[38],},
{grade=5,seq=8,need_score=840,item=item_table[23],},
{grade=5,},
{grade=5,},
{grade=5,seq=11,need_score=2190,item=item_table[39],},
{grade=5,seq=12,need_score=2880,item=item_table[40],},
{grade=5,seq=13,need_score=3710,item=item_table[41],},
{grade=5,item=item_table[42],},
{grade=5,need_score=5790,},
{grade=5,seq=16,need_score=7040,item=item_table[29],},
{grade=5,seq=17,need_score=8430,item=item_table[43],},
{grade=5,seq=18,need_score=9820,item=item_table[44],},
{grade=5,item=item_table[45],},
{grade=5,need_score=12600,},
{grade=5,seq=21,need_score=13990,item=item_table[21],},
{grade=5,seq=22,need_score=15380,item=item_table[46],},
{grade=5,seq=23,need_score=16770,item=item_table[47],},
{grade=5,},
{grade=6,},
{grade=6,seq=1,need_score=20,item=item_table[48],},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{seq=6,need_score=390,},
{grade=6,},
{grade=6,},
{grade=6,item=item_table[49],},
{grade=6,},
{grade=6,item=item_table[29],},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,item=item_table[46],},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,item=item_table[50],},
{grade=6,},
{grade=6,need_score=18160,item=item_table[51],},
{grade=7,},
{grade=7,},
{grade=7,item=item_table[18],},
{grade=7,item=item_table[52],},
{grade=7,need_score=140,item=item_table[53],},
{grade=7,item=item_table[54],},
{grade=7,item=item_table[52],},
{grade=7,},
{grade=7,},
{grade=7,need_score=1190,item=item_table[29],},
{grade=7,item=item_table[55],},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,need_score=4680,item=item_table[56],},
{grade=7,item=item_table[57],},
{grade=7,},
{grade=7,},
{grade=7,},
{seq=19,need_score=11210,},
{grade=7,seq=20,need_score=12650,item=item_table[58],},
{grade=7,need_score=14140,},
{grade=7,need_score=15680,},
{grade=7,need_score=17270,},
{grade=7,need_score=18910,item=item_table[59],}
},

reward_meta_table_map={
[76]=101,	-- depth:1
[26]=76,	-- depth:2
[151]=26,	-- depth:3
[126]=151,	-- depth:4
[51]=126,	-- depth:5
[41]=36,	-- depth:1
[46]=36,	-- depth:1
[136]=36,	-- depth:1
[61]=136,	-- depth:2
[66]=41,	-- depth:2
[121]=46,	-- depth:2
[71]=46,	-- depth:2
[116]=41,	-- depth:2
[111]=61,	-- depth:3
[141]=116,	-- depth:3
[146]=121,	-- depth:3
[4]=1,	-- depth:1
[2]=7,	-- depth:1
[11]=6,	-- depth:1
[169]=119,	-- depth:1
[173]=123,	-- depth:1
[107]=112,	-- depth:1
[102]=127,	-- depth:1
[99]=124,	-- depth:1
[98]=123,	-- depth:1
[97]=122,	-- depth:1
[128]=103,	-- depth:1
[133]=108,	-- depth:1
[172]=122,	-- depth:1
[168]=118,	-- depth:1
[167]=117,	-- depth:1
[166]=116,	-- depth:3
[164]=114,	-- depth:1
[163]=113,	-- depth:1
[162]=112,	-- depth:1
[161]=36,	-- depth:1
[159]=109,	-- depth:1
[158]=133,	-- depth:2
[157]=107,	-- depth:2
[156]=106,	-- depth:1
[154]=104,	-- depth:1
[153]=103,	-- depth:1
[129]=104,	-- depth:1
[152]=102,	-- depth:2
[149]=99,	-- depth:2
[148]=123,	-- depth:1
[147]=97,	-- depth:2
[96]=121,	-- depth:3
[144]=169,	-- depth:2
[143]=168,	-- depth:2
[142]=117,	-- depth:1
[139]=164,	-- depth:2
[138]=163,	-- depth:2
[137]=112,	-- depth:1
[134]=159,	-- depth:2
[132]=137,	-- depth:2
[131]=106,	-- depth:1
[94]=144,	-- depth:3
[88]=138,	-- depth:3
[68]=118,	-- depth:1
[67]=117,	-- depth:1
[31]=131,	-- depth:2
[64]=139,	-- depth:3
[63]=88,	-- depth:4
[62]=162,	-- depth:2
[32]=107,	-- depth:2
[59]=109,	-- depth:1
[58]=158,	-- depth:3
[57]=32,	-- depth:3
[93]=143,	-- depth:3
[56]=31,	-- depth:3
[33]=58,	-- depth:4
[54]=129,	-- depth:2
[53]=128,	-- depth:2
[34]=59,	-- depth:2
[52]=152,	-- depth:3
[49]=124,	-- depth:1
[48]=123,	-- depth:1
[37]=62,	-- depth:3
[47]=122,	-- depth:1
[44]=119,	-- depth:1
[43]=68,	-- depth:2
[38]=63,	-- depth:5
[42]=67,	-- depth:2
[69]=44,	-- depth:2
[39]=64,	-- depth:4
[83]=33,	-- depth:5
[174]=124,	-- depth:1
[87]=37,	-- depth:4
[86]=36,	-- depth:1
[84]=134,	-- depth:3
[29]=54,	-- depth:3
[82]=157,	-- depth:3
[81]=156,	-- depth:2
[79]=154,	-- depth:2
[91]=116,	-- depth:3
[78]=153,	-- depth:2
[77]=52,	-- depth:4
[27]=77,	-- depth:5
[28]=53,	-- depth:3
[74]=49,	-- depth:2
[92]=167,	-- depth:2
[73]=48,	-- depth:2
[72]=47,	-- depth:2
[89]=39,	-- depth:5
[155]=5,	-- depth:1
[165]=15,	-- depth:1
[170]=165,	-- depth:2
[160]=10,	-- depth:1
[30]=155,	-- depth:2
[35]=160,	-- depth:2
[95]=170,	-- depth:3
[135]=160,	-- depth:2
[150]=25,	-- depth:1
[90]=165,	-- depth:2
[100]=150,	-- depth:2
[85]=160,	-- depth:2
[105]=30,	-- depth:3
[110]=135,	-- depth:3
[80]=105,	-- depth:4
[115]=165,	-- depth:2
[75]=25,	-- depth:1
[120]=170,	-- depth:3
[70]=20,	-- depth:1
[125]=150,	-- depth:2
[65]=15,	-- depth:1
[130]=80,	-- depth:5
[60]=35,	-- depth:3
[55]=130,	-- depth:6
[140]=115,	-- depth:3
[50]=75,	-- depth:2
[145]=120,	-- depth:4
[45]=70,	-- depth:2
[40]=65,	-- depth:2
[175]=25,	-- depth:1
},
display_model={
{},
{grade=2,},
{grade=3,},
{grade=4,},
{grade=5,},
{grade=6,},
{grade=7,}
},

display_model_meta_table_map={
},
show_model_map={
{item_id=30643,},
{grade=2,},
{grade=3,},
{grade=4,param1=0,param2=0,item_id=0,model_show_type=2,model_bundle_name="uis/rawimages/a3_wgf_pic_55",model_asset_name="a3_wgf_pic_55.png",},
{grade=5,item_id=30674,},
{grade=6,},
{grade=7,model_show_type=1,model_bundle_name="uis/view/artifact_ui/spineanimations/sx_spine_10006_prefab",model_asset_name="sx_spine_10006",}
},

show_model_map_meta_table_map={
[6]=5,	-- depth:1
[7]=4,	-- depth:1
},
open_panel_map={
[1]={open_index=1,},
[2]={open_index=2,open_panel="XianLingGuZhen",res_name="a3_zjm_icon_zhyf",},
[3]={open_index=3,open_panel="HelpRankView",res_name="a3_zjm_cbzl",},
[4]={open_index=4,open_panel="YanYuGeExchangeShopView",res_name="a3_zjm_icon_tysp",},
[5]={open_index=5,open_panel="ControlBeastsPrizeDrawWGView",res_name="a3_zjm_icon_hsqy",},
[6]={open_index=6,open_panel="TreasureHunt",res_name="a3_zjm_icon_xb",}
},

open_panel_map_meta_table_map={
},
other_default_table={rate=1,exchange_type=0,},

open_day_default_table={start_day=1,end_day=1,grade=1,exchange_type=2,rate_type=1,open_index="3|4",title_img="a3_zztt_bg_txt4",},

reward_default_table={grade=1,round=0,seq=0,need_score=0,item=item_table[60],grand_prix=0,},

display_model_default_table={grade=1,round=0,tzsx_pos="-0.5|-0.4|0",tzsx_rot="-10|0|0",main_scale=1,},

show_model_map_default_table={grade=1,index=0,sys_id=8,param1=1,param2=1,item_id=30700,model_show_type=0,model_bundle_name="",model_asset_name="",},

open_panel_map_default_table={open_index=1,act_type="",open_panel="LayoutZeroBuyView",res_name="a3_zjm_icon_zerobuy",}

}

