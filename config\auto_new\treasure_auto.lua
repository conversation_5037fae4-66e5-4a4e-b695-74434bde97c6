-- H-幻兽特惠.xls
local item_table={
[1]={item_id=59687,num=5,is_bind=1},
[2]={item_id=30447,num=2,is_bind=1},
[3]={item_id=30443,num=2,is_bind=1},
[4]={item_id=22531,num=3,is_bind=1},
[5]={item_id=54800,num=20,is_bind=1},
[6]={item_id=30442,num=3,is_bind=1},
[7]={item_id=59686,num=2,is_bind=1},
[8]={item_id=30447,num=5,is_bind=1},
[9]={item_id=30778,num=1,is_bind=1},
[10]={item_id=30443,num=3,is_bind=1},
[11]={item_id=22531,num=5,is_bind=1},
[12]={item_id=30442,num=5,is_bind=1},
[13]={item_id=59686,num=3,is_bind=1},
[14]={item_id=30447,num=8,is_bind=1},
[15]={item_id=30443,num=5,is_bind=1},
[16]={item_id=22531,num=8,is_bind=1},
[17]={item_id=30442,num=10,is_bind=1},
[18]={item_id=59686,num=5,is_bind=1},
[19]={item_id=30447,num=12,is_bind=1},
[20]={item_id=30779,num=1,is_bind=1},
[21]={item_id=30443,num=8,is_bind=1},
[22]={item_id=22531,num=12,is_bind=1},
[23]={item_id=30442,num=15,is_bind=1},
[24]={item_id=59686,num=10,is_bind=1},
[25]={item_id=30447,num=20,is_bind=1},
[26]={item_id=30443,num=12,is_bind=1},
[27]={item_id=22531,num=30,is_bind=1},
[28]={item_id=30442,num=20,is_bind=1},
[29]={item_id=59686,num=15,is_bind=1},
[30]={item_id=30447,num=40,is_bind=1},
[31]={item_id=30443,num=18,is_bind=1},
[32]={item_id=22531,num=50,is_bind=1},
[33]={item_id=30442,num=40,is_bind=1},
[34]={item_id=59686,num=20,is_bind=1},
[35]={item_id=30780,num=1,is_bind=1},
[36]={item_id=30443,num=24,is_bind=1},
[37]={item_id=22531,num=80,is_bind=1},
[38]={item_id=30442,num=60,is_bind=1},
[39]={item_id=59686,num=30,is_bind=1},
[40]={item_id=30447,num=80,is_bind=1},
[41]={item_id=30443,num=50,is_bind=1},
[42]={item_id=22531,num=120,is_bind=1},
[43]={item_id=30442,num=100,is_bind=1},
[44]={item_id=59686,num=50,is_bind=1},
[45]={item_id=30447,num=100,is_bind=1},
[46]={item_id=30781,num=1,is_bind=1},
[47]={item_id=30443,num=100,is_bind=1},
[48]={item_id=22531,num=150,is_bind=1},
[49]={item_id=30442,num=150,is_bind=1},
[50]={item_id=59686,num=100,is_bind=1},
[51]={item_id=30447,num=150,is_bind=1},
[52]={item_id=30782,num=1,is_bind=1},
[53]={item_id=30443,num=150,is_bind=1},
[54]={item_id=22531,num=180,is_bind=1},
[55]={item_id=59686,num=200,is_bind=1},
[56]={item_id=30783,num=1,is_bind=1},
[57]={item_id=30443,num=200,is_bind=1},
[58]={item_id=22531,num=200,is_bind=1},
[59]={item_id=30442,num=200,is_bind=1},
[60]={item_id=30447,num=200,is_bind=1},
[61]={item_id=30784,num=1,is_bind=1},
[62]={item_id=30443,num=250,is_bind=1},
[63]={item_id=22531,num=300,is_bind=1},
[64]={item_id=30785,num=1,is_bind=1},
[65]={item_id=30443,num=300,is_bind=1},
[66]={item_id=30442,num=300,is_bind=1},
[67]={item_id=59686,num=300,is_bind=1},
[68]={item_id=30447,num=300,is_bind=1},
[69]={item_id=30786,num=1,is_bind=1},
[70]={item_id=22531,num=500,is_bind=1},
[71]={item_id=30442,num=1,is_bind=1},
[72]={item_id=59687,num=1,is_bind=1},
[73]={item_id=59688,num=3,is_bind=1},
[74]={item_id=30443,num=1,is_bind=1},
[75]={item_id=59687,num=2,is_bind=1},
[76]={item_id=59688,num=5,is_bind=1},
[77]={item_id=59687,num=3,is_bind=1},
[78]={item_id=59688,num=8,is_bind=1},
[79]={item_id=59688,num=12,is_bind=1},
[80]={item_id=59687,num=10,is_bind=1},
[81]={item_id=59688,num=20,is_bind=1},
[82]={item_id=59687,num=15,is_bind=1},
[83]={item_id=59688,num=40,is_bind=1},
[84]={item_id=59687,num=20,is_bind=1},
[85]={item_id=59687,num=30,is_bind=1},
[86]={item_id=59688,num=80,is_bind=1},
[87]={item_id=59687,num=50,is_bind=1},
[88]={item_id=59688,num=100,is_bind=1},
[89]={item_id=59687,num=100,is_bind=1},
[90]={item_id=59688,num=150,is_bind=1},
[91]={item_id=59687,num=200,is_bind=1},
[92]={item_id=59688,num=200,is_bind=1},
[93]={item_id=59687,num=300,is_bind=1},
[94]={item_id=59688,num=300,is_bind=1},
[95]={item_id=30807,num=1,is_bind=1},
[96]={item_id=30447,num=1,is_bind=1},
[97]={item_id=22530,num=3,is_bind=1},
[98]={item_id=22530,num=1,is_bind=1},
[99]={item_id=54800,num=5,is_bind=1},
[100]={item_id=22531,num=1,is_bind=1},
[101]={item_id=44182,num=2,is_bind=1},
[102]={item_id=46041,num=1,is_bind=1},
[103]={item_id=46039,num=1,is_bind=1},
[104]={item_id=44051,num=2,is_bind=1},
[105]={item_id=27611,num=30,is_bind=1},
[106]={item_id=44183,num=2,is_bind=1},
[107]={item_id=44049,num=2,is_bind=1},
[108]={item_id=27612,num=10,is_bind=1},
[109]={item_id=44050,num=2,is_bind=1},
[110]={item_id=27613,num=10,is_bind=1},
[111]={item_id=44184,num=2,is_bind=1},
[112]={item_id=30446,num=10,is_bind=1},
[113]={item_id=30443,num=10,is_bind=1},
[114]={item_id=30446,num=25,is_bind=1},
[115]={item_id=30443,num=20,is_bind=1},
[116]={item_id=30446,num=50,is_bind=1},
[117]={item_id=48414,num=1,is_bind=1},
[118]={item_id=39152,num=10,is_bind=1},
[119]={item_id=50011,num=20,is_bind=1},
[120]={item_id=48415,num=1,is_bind=1},
[121]={item_id=48185,num=1,is_bind=1},
[122]={item_id=37043,num=1,is_bind=1},
[123]={item_id=37429,num=1,is_bind=1},
[124]={item_id=38751,num=1,is_bind=1},
[125]={item_id=37261,num=1,is_bind=1},
[126]={item_id=37606,num=1,is_bind=1},
[127]={item_id=37716,num=1,is_bind=1},
[128]={item_id=26191,num=1,is_bind=1},
[129]={item_id=26193,num=1,is_bind=1},
[130]={item_id=48117,num=1,is_bind=1},
[131]={item_id=44330,num=1,is_bind=1},
[132]={item_id=44331,num=1,is_bind=1},
[133]={item_id=44332,num=1,is_bind=1},
[134]={item_id=44333,num=1,is_bind=1},
[135]={item_id=44334,num=1,is_bind=1},
[136]={item_id=27611,num=1,is_bind=1},
[137]={item_id=27612,num=1,is_bind=1},
[138]={item_id=27613,num=1,is_bind=1},
[139]={item_id=38436,num=1,is_bind=1},
[140]={item_id=37878,num=1,is_bind=1},
[141]={item_id=37696,num=1,is_bind=1},
[142]={item_id=37300,num=1,is_bind=1},
[143]={item_id=30441,num=1,is_bind=1},
[144]={item_id=30440,num=1,is_bind=1},
[145]={item_id=48161,num=1,is_bind=1},
[146]={item_id=48160,num=1,is_bind=1},
[147]={item_id=48158,num=1,is_bind=1},
[148]={item_id=48159,num=1,is_bind=1},
[149]={item_id=56321,num=1,is_bind=1},
[150]={item_id=56322,num=1,is_bind=1},
[151]={item_id=56323,num=1,is_bind=1},
[152]={item_id=56324,num=1,is_bind=1},
[153]={item_id=30447,num=4,is_bind=1},
[154]={item_id=22530,num=10,is_bind=1},
[155]={item_id=44182,num=20,is_bind=1},
[156]={item_id=44182,num=30,is_bind=1},
[157]={item_id=30447,num=6,is_bind=1},
[158]={item_id=30443,num=4,is_bind=1},
[159]={item_id=44183,num=10,is_bind=1},
[160]={item_id=22532,num=4,is_bind=1},
[161]={item_id=44183,num=15,is_bind=1},
[162]={item_id=30447,num=10,is_bind=1},
[163]={item_id=30443,num=6,is_bind=1},
[164]={item_id=22532,num=5,is_bind=1},
[165]={item_id=44184,num=5,is_bind=1},
[166]={item_id=30509,num=1,is_bind=1},
[167]={item_id=22532,num=6,is_bind=1},
[168]={item_id=44184,num=8,is_bind=1},
[169]={item_id=22530,num=5,is_bind=1},
[170]={item_id=59687,num=8,is_bind=1},
[171]={item_id=59687,num=12,is_bind=1},
[172]={item_id=22531,num=10,is_bind=1},
[173]={item_id=59687,num=16,is_bind=1},
[174]={item_id=59688,num=16,is_bind=1},
[175]={item_id=22531,num=15,is_bind=1},
[176]={item_id=48567,num=1,is_bind=1},
[177]={item_id=30443,num=15,is_bind=1},
[178]={item_id=22532,num=8,is_bind=1},
[179]={item_id=48573,num=1,is_bind=1},
[180]={item_id=59687,num=25,is_bind=1},
[181]={item_id=59688,num=25,is_bind=1},
[182]={item_id=22532,num=12,is_bind=1},
[183]={item_id=30807,num=1,is_bind=0},
[184]={item_id=22531,num=1,is_bind=0},
[185]={item_id=30443,num=1,is_bind=0},
[186]={item_id=30442,num=1,is_bind=0},
[187]={item_id=59687,num=1,is_bind=0},
[188]={item_id=30447,num=50,is_bind=1},
[189]={item_id=30809,num=10,is_bind=1},
[190]={item_id=48570,num=1,is_bind=1},
[191]={item_id=39197,num=1,is_bind=1},
[192]={item_id=39480,num=1,is_bind=1},
[193]={item_id=30809,num=20,is_bind=1},
[194]={item_id=30809,num=30,is_bind=1},
[195]={item_id=30442,num=30,is_bind=1},
[196]={item_id=39193,num=1,is_bind=1},
[197]={item_id=40038,num=1,is_bind=1},
[198]={item_id=59686,num=80,is_bind=1},
[199]={item_id=30442,num=50,is_bind=1},
[200]={item_id=30809,num=40,is_bind=1},
[201]={item_id=30442,num=80,is_bind=1},
[202]={item_id=30809,num=60,is_bind=1},
[203]={item_id=59686,num=120,is_bind=1},
[204]={item_id=59686,num=150,is_bind=1},
[205]={item_id=30809,num=90,is_bind=1},
[206]={item_id=59686,num=180,is_bind=1},
[207]={item_id=30787,num=1,is_bind=1},
[208]={item_id=30785,num=2,is_bind=1},
[209]={item_id=30442,num=120,is_bind=1},
[210]={item_id=30785,num=3,is_bind=1},
[211]={item_id=30809,num=120,is_bind=1},
[212]={item_id=59686,num=210,is_bind=1},
[213]={item_id=30788,num=1,is_bind=1},
[214]={item_id=30786,num=3,is_bind=1},
[215]={item_id=30789,num=1,is_bind=1},
[216]={item_id=30807,num=10,is_bind=1},
[217]={item_id=30807,num=20,is_bind=1},
[218]={item_id=30807,num=30,is_bind=1},
[219]={item_id=59687,num=40,is_bind=1},
[220]={item_id=59687,num=80,is_bind=1},
[221]={item_id=30807,num=40,is_bind=1},
[222]={item_id=59687,num=60,is_bind=1},
[223]={item_id=59687,num=90,is_bind=1},
[224]={item_id=30807,num=60,is_bind=1},
[225]={item_id=59687,num=120,is_bind=1},
[226]={item_id=59687,num=150,is_bind=1},
[227]={item_id=30807,num=90,is_bind=1},
[228]={item_id=59687,num=180,is_bind=1},
[229]={item_id=30807,num=120,is_bind=1},
[230]={item_id=59687,num=210,is_bind=1},
[231]={item_id=59686,num=1,is_bind=1},
[232]={item_id=30809,num=1,is_bind=1},
[233]={item_id=30447,num=30,is_bind=1},
[234]={item_id=22532,num=10,is_bind=1},
[235]={item_id=30809,num=2,is_bind=1},
[236]={item_id=30809,num=3,is_bind=1},
[237]={item_id=30809,num=5,is_bind=1},
[238]={item_id=22532,num=20,is_bind=1},
[239]={item_id=22532,num=30,is_bind=1},
[240]={item_id=30809,num=15,is_bind=1},
[241]={item_id=30447,num=120,is_bind=1},
[242]={item_id=22532,num=50,is_bind=1},
[243]={item_id=22532,num=100,is_bind=1},
[244]={item_id=59675,num=1,is_bind=1},
[245]={item_id=59680,num=1,is_bind=1},
[246]={item_id=59681,num=1,is_bind=1},
[247]={item_id=27410,num=1,is_bind=1},
[248]={item_id=48588,num=1,is_bind=1},
[249]={item_id=48073,num=1,is_bind=1},
[250]={item_id=48587,num=1,is_bind=1},
[251]={item_id=22533,num=1,is_bind=1},
[252]={item_id=40215,num=1,is_bind=1},
[253]={item_id=59688,num=1,is_bind=1},
[254]={item_id=23600,num=100,is_bind=1},
[255]={item_id=22099,num=60,is_bind=1},
[256]={item_id=22099,num=100,is_bind=1},
[257]={item_id=22099,num=300,is_bind=1},
[258]={item_id=22099,num=680,is_bind=1},
[259]={item_id=23600,num=200,is_bind=1},
[260]={item_id=30809,num=8,is_bind=1},
[261]={item_id=22099,num=980,is_bind=1},
[262]={item_id=30443,num=30,is_bind=1},
[263]={item_id=30809,num=12,is_bind=1},
[264]={item_id=22099,num=1280,is_bind=1},
[265]={item_id=22099,num=1980,is_bind=1},
[266]={item_id=22099,num=3280,is_bind=1},
[267]={item_id=23600,num=500,is_bind=1},
[268]={item_id=23600,num=1000,is_bind=1},
[269]={item_id=30809,num=50,is_bind=1},
[270]={item_id=22099,num=6480,is_bind=1},
[271]={item_id=30807,num=2,is_bind=1},
[272]={item_id=30807,num=3,is_bind=1},
[273]={item_id=30807,num=5,is_bind=1},
[274]={item_id=59688,num=10,is_bind=1},
[275]={item_id=30807,num=8,is_bind=1},
[276]={item_id=30807,num=12,is_bind=1},
[277]={item_id=59688,num=50,is_bind=1},
[278]={item_id=30807,num=50,is_bind=1},
[279]={item_id=30807,num=6,is_bind=1},
[280]={item_id=30807,num=9,is_bind=1},
[281]={item_id=65587,num=1,is_bind=1},
[282]={item_id=65587,num=5,is_bind=1},
[283]={item_id=65587,num=8,is_bind=1},
[284]={item_id=65587,num=10,is_bind=1},
[285]={item_id=65587,num=2,is_bind=1},
[286]={item_id=26191,num=2,is_bind=1},
[287]={item_id=44182,num=5,is_bind=1},
[288]={item_id=54800,num=10,is_bind=1},
[289]={item_id=54800,num=15,is_bind=1},
[290]={item_id=30447,num=3,is_bind=1},
[291]={item_id=31261,num=1,is_bind=1},
[292]={item_id=31260,num=10,is_bind=1},
[293]={item_id=31258,num=20,is_bind=1},
[294]={item_id=48120,num=1,is_bind=1},
[295]={item_id=44182,num=10,is_bind=1},
[296]={item_id=30809,num=1,is_bind=0},
[297]={item_id=59686,num=1,is_bind=0},
[298]={item_id=54800,num=1,is_bind=0},
[299]={item_id=48559,num=1,is_bind=1},
[300]={item_id=65587,num=3,is_bind=1},
}

return {
grade={
{},
{start_day=5,end_day=6,grade=2,entrance_icon="a3_zjm_icon_hsth",before_icon="a3_zjm_icon_hsth",after_icon="a3_zjm_icon_hsth",des_image="a2_tcdb_txt_5",title_image="a2_tcdb_title5",shop_title="a2_tcdb_shop_title5",entrance_title="幻兽特惠",open_index="20|40|70|90|100|140",text_color1="#ffffff",text_color2="#4f327e",open_panel="XuYuanFreshPoolView",}
},

grade_meta_table_map={
},
login_reward={
{},
{day_index=2,reward_name="次日福利礼包",},
{grade=2,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{day_index=2,reward_name="次日福利礼包",}
},

login_reward_meta_table_map={
[4]=3,	-- depth:1
},
shouchong_reward={
{},
{grade=2,}
},

shouchong_reward_meta_table_map={
},
leichong_reward={
{},
{ID=1,stage_value=500,reward_item={[0]=item_table[6],[1]=item_table[7],[2]=item_table[8],[3]=item_table[9],[4]=item_table[10],[5]=item_table[11]},},
{ID=2,stage_value=1000,reward_item={[0]=item_table[12],[1]=item_table[13],[2]=item_table[14],[3]=item_table[9],[4]=item_table[15],[5]=item_table[16]},},
{ID=3,stage_value=5000,reward_item={[0]=item_table[17],[1]=item_table[18],[2]=item_table[19],[3]=item_table[20],[4]=item_table[21],[5]=item_table[22]},},
{ID=4,stage_value=10000,reward_item={[0]=item_table[23],[1]=item_table[24],[2]=item_table[25],[3]=item_table[20],[4]=item_table[26],[5]=item_table[27]},},
{ID=5,stage_value=30000,reward_item={[0]=item_table[28],[1]=item_table[29],[2]=item_table[30],[3]=item_table[20],[4]=item_table[31],[5]=item_table[32]},},
{ID=6,stage_value=50000,reward_item={[0]=item_table[33],[1]=item_table[34],[2]=item_table[30],[3]=item_table[35],[4]=item_table[36],[5]=item_table[37]},},
{ID=7,stage_value=100000,reward_item={[0]=item_table[38],[1]=item_table[39],[2]=item_table[40],[3]=item_table[35],[4]=item_table[41],[5]=item_table[42]},},
{ID=8,stage_value=200000,reward_item={[0]=item_table[43],[1]=item_table[44],[2]=item_table[45],[3]=item_table[46],[4]=item_table[47],[5]=item_table[48]},},
{ID=9,stage_value=400000,reward_item={[0]=item_table[49],[1]=item_table[50],[2]=item_table[51],[3]=item_table[52],[4]=item_table[53],[5]=item_table[54]},},
{ID=10,stage_value=800000,reward_item={[0]=item_table[49],[1]=item_table[55],[2]=item_table[51],[3]=item_table[56],[4]=item_table[57],[5]=item_table[58]},},
{ID=11,stage_value=1200000,reward_item={[0]=item_table[59],[1]=item_table[55],[2]=item_table[60],[3]=item_table[61],[4]=item_table[62],[5]=item_table[63]},},
{ID=12,stage_value=1600000,reward_item={[0]=item_table[59],[1]=item_table[55],[2]=item_table[60],[3]=item_table[64],[4]=item_table[65],[5]=item_table[63]},},
{ID=13,stage_value=2000000,reward_item={[0]=item_table[66],[1]=item_table[67],[2]=item_table[68],[3]=item_table[69],[4]=item_table[65],[5]=item_table[70]},},
{grade=2,reward_item={[0]=item_table[71],[1]=item_table[72],[2]=item_table[73],[3]=item_table[9],[4]=item_table[74],[5]=item_table[4]},},
{grade=2,reward_item={[0]=item_table[6],[1]=item_table[75],[2]=item_table[76],[3]=item_table[9],[4]=item_table[10],[5]=item_table[11]},},
{grade=2,reward_item={[0]=item_table[12],[1]=item_table[77],[2]=item_table[78],[3]=item_table[9],[4]=item_table[15],[5]=item_table[16]},},
{grade=2,reward_item={[0]=item_table[17],[1]=item_table[1],[2]=item_table[79],[3]=item_table[20],[4]=item_table[21],[5]=item_table[22]},},
{grade=2,reward_item={[0]=item_table[23],[1]=item_table[80],[2]=item_table[81],[3]=item_table[20],[4]=item_table[26],[5]=item_table[27]},},
{grade=2,reward_item={[0]=item_table[28],[1]=item_table[82],[2]=item_table[83],[3]=item_table[20],[4]=item_table[31],[5]=item_table[32]},},
{grade=2,reward_item={[0]=item_table[33],[1]=item_table[84],[2]=item_table[83],[3]=item_table[35],[4]=item_table[36],[5]=item_table[37]},},
{grade=2,reward_item={[0]=item_table[38],[1]=item_table[85],[2]=item_table[86],[3]=item_table[35],[4]=item_table[41],[5]=item_table[42]},},
{grade=2,reward_item={[0]=item_table[43],[1]=item_table[87],[2]=item_table[88],[3]=item_table[46],[4]=item_table[47],[5]=item_table[48]},},
{grade=2,reward_item={[0]=item_table[49],[1]=item_table[89],[2]=item_table[90],[3]=item_table[52],[4]=item_table[53],[5]=item_table[54]},},
{grade=2,reward_item={[0]=item_table[49],[1]=item_table[91],[2]=item_table[90],[3]=item_table[56],[4]=item_table[57],[5]=item_table[58]},},
{grade=2,reward_item={[0]=item_table[59],[1]=item_table[91],[2]=item_table[92],[3]=item_table[61],[4]=item_table[62],[5]=item_table[63]},},
{grade=2,reward_item={[0]=item_table[59],[1]=item_table[91],[2]=item_table[92],[3]=item_table[64],[4]=item_table[65],[5]=item_table[63]},},
{grade=2,reward_item={[0]=item_table[66],[1]=item_table[93],[2]=item_table[94],[3]=item_table[69],[4]=item_table[65],[5]=item_table[70]},}
},

leichong_reward_meta_table_map={
[26]=12,	-- depth:1
[25]=11,	-- depth:1
[24]=10,	-- depth:1
[23]=9,	-- depth:1
[22]=8,	-- depth:1
[18]=4,	-- depth:1
[20]=6,	-- depth:1
[19]=5,	-- depth:1
[17]=3,	-- depth:1
[16]=2,	-- depth:1
[27]=13,	-- depth:1
[21]=7,	-- depth:1
[28]=14,	-- depth:1
},
leichong_model={
{},
{grade=2,model_show_itemid=30700,display_pos="-158|-40|0",display_scale=1,bg_res_name="a3_lchl_nw_db",}
},

leichong_model_meta_table_map={
},
puzzle_task={
{task_name="每日首充",task_condition_id=0,reward_item={[0]=item_table[95],[1]=item_table[71],[2]=item_table[96],[3]=item_table[97]},panel="recharge#recharge_cz",},
{ID=1,task_name="仙遗洞天",task_condition_id=13,task_param1=5,task_desc="击杀%s/5只仙遗洞天BOSS",panel="boss#boss_vip",},
{ID=2,task_param1=10,task_desc="击杀%s/10只仙遗洞天BOSS",},
{ID=3,task_name="猎魔深渊",task_condition_id=13,task_param1=15,task_desc="击杀%s/15只仙遗洞天BOSS",panel="boss#boss_world",},
{ID=4,task_param1=160,task_desc="达到%s/160级",},
{ID=5,task_name="达到等级",task_condition_id=17,task_param1=200,task_desc="达到%s/200级",panel="boss#boss_vip",},
{ID=6,task_param1=230,task_desc="达到%s/230级",reward_item={[0]=item_table[95],[1]=item_table[71],[2]=item_table[96],[3]=item_table[97]},},
{ID=7,task_param1=10,task_desc="幻兽抽奖%s/10次",},
{ID=8,task_param1=20,task_desc="幻兽抽奖%s/20次",},
{ID=9,task_name="幻兽抽奖",task_condition_id=22,task_param1=30,task_param2=2,task_desc="幻兽抽奖%s/30次",},
{ID=10,task_name="极品幻兽",task_condition_id=23,task_param2=3,task_desc="获得%s/1只sr幻兽",},
{ID=11,task_param1=2,task_desc="获得%s/2只sr幻兽",},
{ID=12,task_param2=4,task_desc="获得%s/1只ssr幻兽",},
{ID=13,task_name="暗翼之巢",task_condition_id=27,task_desc="完成%s/1次暗翼之巢",panel="fubenpanel#fubenpanel_pet",},
{ID=14,task_param1=2,task_desc="完成%s/2次暗翼之巢",},
{ID=15,task_param1=4,task_desc="完成%s/4次暗翼之巢",},
{ID=16,task_desc="前往查看百亿补贴",reward_item={[0]=item_table[95],[1]=item_table[71],[2]=item_table[96],[3]=item_table[97]},panel="BillionSubsidy",},
{ID=17,task_param1=2,task_desc="前往查看烟雨商店",panel="YanYuGeExchangeShopView#yanyuge_shop_nwsd",},
{ID=18,task_param1=3,task_desc="前往查看财神爷",panel="recharge_volume",},
{ID=19,task_param1=4,task_desc="前往查看招财进宝活动",reward_item={[0]=item_table[95],[1]=item_table[71],[2]=item_table[96],[3]=item_table[97]},panel="GodOfWealthView#god_of_wealth",activity_id=20052,},
{ID=20,task_param1=5,task_desc="前往查看至尊特权",panel="PrivilegeCollectionView",},
{grade=2,reward_item={[0]=item_table[95],[1]=item_table[71],[2]=item_table[98],[3]=item_table[99]},},
{grade=2,reward_item={[0]=item_table[73],[1]=item_table[71],[2]=item_table[72],[3]=item_table[99]},},
{grade=2,reward_item={[0]=item_table[73],[1]=item_table[71],[2]=item_table[100],[3]=item_table[99]},},
{grade=2,reward_item={[0]=item_table[95],[1]=item_table[71],[2]=item_table[72],[3]=item_table[99]},},
{grade=2,ID=4,reward_item={[0]=item_table[73],[1]=item_table[71],[2]=item_table[100],[3]=item_table[99]},},
{grade=2,ID=5,reward_item={[0]=item_table[73],[1]=item_table[71],[2]=item_table[72],[3]=item_table[99]},},
{grade=2,ID=6,reward_item={[0]=item_table[95],[1]=item_table[71],[2]=item_table[100],[3]=item_table[99]},},
{grade=2,ID=7,task_desc="完成%s/1次守护副本",reward_item={[0]=item_table[73],[1]=item_table[71],[2]=item_table[72],[3]=item_table[99]},},
{ID=8,task_param1=2,task_desc="完成%s/2次守护副本",reward_item={[0]=item_table[73],[1]=item_table[71],[2]=item_table[100],[3]=item_table[99]},},
{ID=9,task_param1=4,task_desc="完成%s/4次守护副本",reward_item={[0]=item_table[95],[1]=item_table[71],[2]=item_table[72],[3]=item_table[99]},},
{grade=2,ID=10,task_param1=6,task_desc="前往查看万象玄生",reward_item={[0]=item_table[73],[1]=item_table[71],[2]=item_table[100],[3]=item_table[99]},panel="SunRainbowView#sun_rainbow_lottery",activity_id=2330,},
{grade=2,ID=11,reward_item={[0]=item_table[73],[1]=item_table[71],[2]=item_table[72],[3]=item_table[99]},},
{grade=2,ID=12,reward_item={[0]=item_table[95],[1]=item_table[71],[2]=item_table[100],[3]=item_table[99]},}
},

puzzle_task_meta_table_map={
[18]=17,	-- depth:1
[22]=1,	-- depth:1
[21]=17,	-- depth:1
[19]=17,	-- depth:1
[13]=11,	-- depth:1
[33]=19,	-- depth:2
[16]=14,	-- depth:1
[15]=14,	-- depth:1
[3]=2,	-- depth:1
[9]=10,	-- depth:1
[8]=10,	-- depth:1
[5]=6,	-- depth:1
[12]=11,	-- depth:1
[29]=14,	-- depth:1
[34]=20,	-- depth:1
[7]=6,	-- depth:1
[26]=8,	-- depth:2
[27]=9,	-- depth:2
[28]=10,	-- depth:1
[23]=2,	-- depth:1
[30]=29,	-- depth:2
[31]=29,	-- depth:2
[24]=3,	-- depth:2
[25]=4,	-- depth:1
},
puzzle_gift={
{reward_item={[0]=item_table[101],[1]=item_table[102],[2]=item_table[103],[3]=item_table[104],[4]=item_table[105]},price=648,old_price=6480,},
{ID=1,reward_item={[0]=item_table[106],[1]=item_table[107],[2]=item_table[108],[3]=item_table[102],[4]=item_table[103]},price=1288,old_price=12880,gift_name="<color=#fff691>超值礼包</color>",},
{ID=2,reward_item={[0]=item_table[109],[1]=item_table[110],[2]=item_table[111],[3]=item_table[102],[4]=item_table[103]},price=3288,old_price=32880,gift_name="<color=#fff691>终极礼包</color>",},
{grade=2,reward_item={[0]=item_table[112],[1]=item_table[113],[2]=item_table[101],[3]=item_table[102],[4]=item_table[103]},},
{grade=2,reward_item={[0]=item_table[114],[1]=item_table[115],[2]=item_table[106],[3]=item_table[102],[4]=item_table[103]},},
{grade=2,reward_item={[0]=item_table[116],[1]=item_table[41],[2]=item_table[111],[3]=item_table[102],[4]=item_table[103]},},
{grade=3,reward_item={[0]=item_table[117],[1]=item_table[118],[2]=item_table[119],[3]=item_table[102],[4]=item_table[103]},},
{grade=3,reward_item={[0]=item_table[120],[1]=item_table[118],[2]=item_table[119],[3]=item_table[102],[4]=item_table[103]},},
{grade=3,reward_item={[0]=item_table[121],[1]=item_table[118],[2]=item_table[119],[3]=item_table[102],[4]=item_table[103]},},
{grade=4,},
{ID=1,price=3240,old_price=32400,gift_name="<color=#fff691>超值礼包</color>",},
{ID=2,price=6480,old_price=64800,gift_name="<color=#fff691>终极礼包</color>",}
},

puzzle_gift_meta_table_map={
[11]=10,	-- depth:1
[12]=11,	-- depth:2
[5]=11,	-- depth:2
[6]=12,	-- depth:3
[8]=11,	-- depth:2
[9]=12,	-- depth:3
},
puzzle_model={
{},
{grade=2,model_show_itemid=30700,display_pos="0|-30|0",display_scale=0.9,}
},

puzzle_model_meta_table_map={
},
puzzle_reward={
{},
{grade=2,}
},

puzzle_reward_meta_table_map={
},
limit_buy={
{shop=1,type=2,rmb_type=209,discount_rmb_type=210,rmb_seq=100,reward_item={[0]=item_table[122]},goods_txt="火系幻兽",},
{seq=1,rmb_seq=101,reward_item={[0]=item_table[123]},goods_txt="水系幻兽",},
{seq=2,rmb_seq=102,reward_item={[0]=item_table[124]},goods_txt="雷系幻兽",},
{seq=3,price=10000,discount_price=10000,reward_item={[0]=item_table[125]},},
{seq=4,reward_item={[0]=item_table[126]},},
{seq=5,price=5000,discount_price=5000,reward_item={[0]=item_table[127]},buy_count_limit=5,},
{seq=6,price=3000,discount_price=3000,reward_item={[0]=item_table[128]},},
{seq=7,price=8000,discount_price=8000,reward_item={[0]=item_table[129]},},
{seq=8,price=22500,discount_price=22500,},
{seq=9,price=37500,discount_price=37500,buy_count_limit=2,},
{seq=10,price=7500,discount_price=7500,reward_item={[0]=item_table[130]},},
{seq=11,price=12500,discount_price=12500,buy_count_limit=2,},
{seq=12,price=100,discount_price=100,reward_item={[0]=item_table[131]},},
{seq=13,reward_item={[0]=item_table[132]},},
{seq=14,price=120,discount_price=120,reward_item={[0]=item_table[133]},buy_count_limit=10,},
{seq=15,reward_item={[0]=item_table[134]},buy_count_limit=10,},
{seq=16,reward_item={[0]=item_table[135]},buy_count_limit=10,},
{seq=17,reward_item={[0]=item_table[136]},buy_count_limit=100,},
{seq=18,price=180,discount_price=180,reward_item={[0]=item_table[137]},buy_count_limit=60,},
{seq=19,price=200,discount_price=200,reward_item={[0]=item_table[138]},buy_count_limit=30,},
{grade=2,rmb_seq=200,reward_item={[0]=item_table[139]},goods_txt="天上武魂",},
{seq=1,rmb_seq=201,reward_item={[0]=item_table[140]},goods_txt="地狱武魂",},
{grade=2,rmb_seq=202,reward_item={[0]=item_table[141]},goods_txt="灵界武魂",},
{grade=2,reward_item={[0]=item_table[142]},},
{grade=2,price=500,discount_price=500,reward_item={[0]=item_table[71]},},
{seq=5,price=300,discount_price=300,reward_item={[0]=item_table[143]},},
{seq=6,reward_item={[0]=item_table[144]},},
{grade=2,seq=7,reward_item={[0]=item_table[74]},buy_count_limit=100,},
{grade=3,rmb_seq=300,reward_item={[0]=item_table[145]},goods_txt="嗜血幽鬼",},
{grade=3,rmb_seq=301,reward_item={[0]=item_table[146]},goods_txt="破盾幽鬼",},
{grade=3,rmb_seq=302,reward_item={[0]=item_table[147]},goods_txt="连杀幽鬼",},
{grade=3,seq=3,price=12000,discount_price=12000,reward_item={[0]=item_table[148]},},
{grade=3,seq=4,reward_item={[0]=item_table[149]},buy_count_limit=100,},
{grade=3,seq=5,price=1000,discount_price=1000,reward_item={[0]=item_table[150]},buy_count_limit=10,},
{seq=6,reward_item={[0]=item_table[151]},},
{grade=3,seq=7,price=2000,discount_price=2000,reward_item={[0]=item_table[152]},buy_count_limit=5,},
{grade=4,shop=1,type=2,rmb_type=209,discount_rmb_type=210,rmb_seq=400,reward_item={[0]=item_table[128]},},
{seq=1,rmb_seq=401,reward_item={[0]=item_table[129]},},
{grade=4,shop=1,seq=2,type=2,rmb_type=209,discount_rmb_type=210,rmb_seq=402,},
{grade=4,seq=3,},
{grade=4,seq=4,},
{grade=4,seq=5,},
{grade=4,seq=6,},
{seq=7,price=500,discount_price=500,reward_item={[0]=item_table[132]},},
{grade=4,seq=8,buy_count_limit=5,},
{grade=4,seq=9,buy_count_limit=5,},
{grade=4,seq=10,buy_count_limit=3,}
},

limit_buy_meta_table_map={
[40]=10,	-- depth:1
[41]=11,	-- depth:1
[18]=15,	-- depth:1
[17]=20,	-- depth:1
[16]=19,	-- depth:1
[13]=15,	-- depth:1
[12]=11,	-- depth:1
[5]=6,	-- depth:1
[4]=6,	-- depth:1
[14]=6,	-- depth:1
[33]=20,	-- depth:1
[43]=13,	-- depth:2
[42]=12,	-- depth:2
[44]=40,	-- depth:2
[45]=15,	-- depth:1
[35]=36,	-- depth:1
[24]=4,	-- depth:2
[47]=17,	-- depth:2
[46]=16,	-- depth:2
[28]=13,	-- depth:2
[27]=28,	-- depth:3
[26]=28,	-- depth:3
[25]=33,	-- depth:2
[38]=37,	-- depth:1
[29]=37,	-- depth:1
[21]=37,	-- depth:1
[3]=1,	-- depth:1
[2]=1,	-- depth:1
[30]=2,	-- depth:2
[23]=39,	-- depth:1
[22]=23,	-- depth:2
[31]=39,	-- depth:1
},
shop_model={
{},
{model_show_itemid=30534,},
{model_show_itemid=30545,},
{grade=2,},
{model_show_itemid=30534,},
{model_show_itemid=30545,}
},

shop_model_meta_table_map={
[5]=4,	-- depth:1
[6]=5,	-- depth:2
},
client_desc={
{txt="高品质幻兽不仅仅拥有更强属性，还有更高资质和技能",},
{tab=30,txt="在战斗的适当时机使用幻兽技能，来扭转乾坤哦",rule_desc="首充送大礼",},
{tab=50,txt="幻兽升星可大幅度提升自身属性，并提升技能效果上限",rule_desc="升星礼赠",},
{tab=70,txt="幻兽的资质会影响升级属性，想要精益求精，就得提升资质哦",rule_desc="完成任务获取奖励",},
{tab=80,txt="幻兽拥有不同属性，属性还有克制关系哦",rule_desc="售卖商品",},
{tab=90,txt="只有出战幻兽可以提供属性哦",rule_desc="累充送大礼",},
{tab=100,txt="只有出战幻兽可以提供属性，努力开启更多出战位吧",rule_desc="<color=#FFF8BB>魔界异动，携带了大量幻兽材料的魔罗入侵了人界和魔界的通道。请仙家们感觉前往异地，阻止魔罗入侵人界。\n活动期间内的11:00-11:15和19:00-19:15，都会开启活动入口，请各位仙家留意活动开启。</color>",},
{tab=110,rule_desc="<color=#FFF8BB>天灵灵地灵灵，魔罗尽退散！为了让幻兽预热活动正常进行，景天堂发布了大量悬赏令。\n活动期间内，击杀任意魔罗，都可以获得额外奖赏，可用于幻兽培养和幻兽抽奖！</color>",},
{grade=2,},
{tab=100,rule_desc="<color=#FFF8BB>魔界异动，携带了大量幻兽材料的魔罗入侵了人界和魔界的通道。请仙家们感觉前往异地，阻止魔罗入侵人界。\n活动期间内的11:00-11:15和19:00-19:15，都会开启活动入口，请各位仙家留意活动开启。</color>",},
{tab=110,rule_desc="<color=#FFF8BB>天灵灵地灵灵，魔罗尽退散！为了让幻兽预热活动正常进行，景天堂发布了大量悬赏令。\n活动期间内，击杀任意魔罗，都可以获得额外奖赏，可用于幻兽培养和幻兽抽奖！</color>",}
},

client_desc_meta_table_map={
[8]=7,	-- depth:1
[10]=9,	-- depth:1
[11]=10,	-- depth:2
},
dragon_egg={
{},
{level=2,exp=120,reward_item={[0]=item_table[153],[1]=item_table[3],[2]=item_table[154],[3]=item_table[155]},},
{level=3,exp=180,reward_item={[0]=item_table[8],[1]=item_table[10],[2]=item_table[11],[3]=item_table[156]},},
{level=4,exp=200,reward_item={[0]=item_table[157],[1]=item_table[158],[2]=item_table[16],[3]=item_table[159]},},
{level=5,exp=300,reward_item={[0]=item_table[14],[1]=item_table[15],[2]=item_table[160],[3]=item_table[161]},},
{level=6,exp=400,reward_item={[0]=item_table[162],[1]=item_table[163],[2]=item_table[164],[3]=item_table[165]},},
{level=7,exp=500,reward_item={[0]=item_table[166],[1]=item_table[12],[2]=item_table[163],[3]=item_table[167],[4]=item_table[168]},},
{grade=2,reward_item={[0]=item_table[77],[1]=item_table[73],[2]=item_table[74],[3]=item_table[169]},},
{grade=2,reward_item={[0]=item_table[1],[1]=item_table[76],[2]=item_table[3],[3]=item_table[154]},},
{grade=2,reward_item={[0]=item_table[170],[1]=item_table[78],[2]=item_table[8],[3]=item_table[10],[4]=item_table[11]},},
{grade=2,reward_item={[0]=item_table[171],[1]=item_table[79],[2]=item_table[14],[3]=item_table[15],[4]=item_table[172]},},
{grade=2,reward_item={[0]=item_table[173],[1]=item_table[174],[2]=item_table[19],[3]=item_table[26],[4]=item_table[175]},},
{grade=2,reward_item={[0]=item_table[176],[1]=item_table[84],[2]=item_table[81],[3]=item_table[177],[4]=item_table[178]},},
{grade=2,reward_item={[0]=item_table[179],[1]=item_table[180],[2]=item_table[181],[3]=item_table[177],[4]=item_table[182]},}
},

dragon_egg_meta_table_map={
[9]=2,	-- depth:1
[10]=3,	-- depth:1
[11]=4,	-- depth:1
[12]=5,	-- depth:1
[13]=6,	-- depth:1
[14]=7,	-- depth:1
},
other={
{}
},

other_meta_table_map={
},
boss_cfg={
{},
{limit_lv=181,boss_id=53022,},
{limit_lv=201,boss_id=53023,},
{limit_lv=251,boss_id=53024,},
{limit_lv=266,boss_id=53025,},
{limit_lv=281,boss_id=53026,},
{limit_lv=301,boss_id=53027,},
{limit_lv=316,boss_id=53028,},
{limit_lv=331,boss_id=53029,},
{limit_lv=341,boss_id=53030,},
{limit_lv=351,boss_id=54020,},
{limit_lv=361,boss_id=54021,},
{limit_lv=371,boss_id=54022,},
{limit_lv=381,boss_id=54023,},
{limit_lv=386,boss_id=54024,},
{limit_lv=391,boss_id=54025,},
{limit_lv=396,boss_id=54026,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

boss_cfg_meta_table_map={
[32]=15,	-- depth:1
[31]=14,	-- depth:1
[30]=13,	-- depth:1
[29]=12,	-- depth:1
[28]=11,	-- depth:1
[27]=10,	-- depth:1
[26]=9,	-- depth:1
[22]=5,	-- depth:1
[24]=7,	-- depth:1
[23]=6,	-- depth:1
[21]=4,	-- depth:1
[20]=3,	-- depth:1
[19]=2,	-- depth:1
[33]=16,	-- depth:1
[25]=8,	-- depth:1
[34]=17,	-- depth:1
},
refresh_time={
{},
{refresh_time=1930,},
{grade=2,},
{refresh_time=1930,}
},

refresh_time_meta_table_map={
[4]=3,	-- depth:1
},
reward_config={
{},
{min_lv=341,max_lv=350,},
{min_lv=351,max_lv=360,},
{min_lv=361,max_lv=9999,},
{grade=2,reward_show={[0]=item_table[183],[1]=item_table[184],[2]=item_table[185],[3]=item_table[186],[4]=item_table[187]},},
{min_lv=341,max_lv=350,},
{min_lv=351,max_lv=360,},
{min_lv=361,max_lv=9999,}
},

reward_config_meta_table_map={
[6]=5,	-- depth:1
[7]=6,	-- depth:2
[8]=6,	-- depth:2
},
monster_drop_cfg={
{},
{grade=2,}
},

monster_drop_cfg_meta_table_map={
},
boss_drop_cfg={
{},
{grade=2,}
},

boss_drop_cfg_meta_table_map={
},
extra_drop={
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{grade=2,},
{},
{}
},

extra_drop_meta_table_map={
[21]=22,	-- depth:1
[20]=21,	-- depth:2
[19]=20,	-- depth:3
[18]=19,	-- depth:4
[16]=18,	-- depth:5
[15]=16,	-- depth:6
[14]=15,	-- depth:7
[13]=14,	-- depth:8
[23]=13,	-- depth:9
[17]=23,	-- depth:10
[24]=17,	-- depth:11
},
extra_drop_model={
{},
{grade=2,}
},

extra_drop_model_meta_table_map={
},
open_task={
{},
{ID=2,panel="YanYuGeExchangeShopView",},
{ID=3,panel="recharge_volume",},
{ID=4,panel="GodOfWealthView",},
{ID=5,panel="PrivilegeCollectionView",},
{ID=6,panel="SunRainbowView",}
},

open_task_meta_table_map={
},
upstar_gift_grade={
{},
{grade=2,start_day=5,end_day=6,beast_type=35,img_id=2,spine_id=1004,scale=0.4,model_show_itemid=30700,bg_res_name="a3_yushou_jsrh_bg_1",rmb_seq=2,}
},

upstar_gift_grade_meta_table_map={
},
upstar_gift={
{rmb_seq=100,price=98,free_reward={[0]=item_table[17],[1]=item_table[13],[2]=item_table[188]},pay_reward={[0]=item_table[189],[1]=item_table[17],[2]=item_table[24],[3]=item_table[190],[4]=item_table[191]},},
{seq=1,target=6,rmb_seq=101,price=128,free_reward={[0]=item_table[17],[1]=item_table[13],[2]=item_table[45]},pay_reward={[0]=item_table[189],[1]=item_table[17],[2]=item_table[24],[3]=item_table[190],[4]=item_table[192]},},
{seq=2,target=7,rmb_seq=102,price=168,free_reward={[0]=item_table[17],[1]=item_table[18],[2]=item_table[45]},pay_reward={[0]=item_table[189],[1]=item_table[17],[2]=item_table[34],[3]=item_table[35],[4]=item_table[192]},},
{seq=3,target=8,rmb_seq=103,price=198,free_reward={[0]=item_table[28],[1]=item_table[18],[2]=item_table[20]},pay_reward={[0]=item_table[193],[1]=item_table[28],[2]=item_table[34],[3]=item_table[35],[4]=item_table[192]},},
{seq=4,target=9,rmb_seq=104,price=328,free_reward={[0]=item_table[28],[1]=item_table[24],[2]=item_table[35]},},
{seq=5,target=10,rmb_seq=105,price=488,free_reward={[0]=item_table[28],[1]=item_table[24],[2]=item_table[46]},},
{seq=6,target=11,rmb_seq=106,price=688,pay_reward={[0]=item_table[194],[1]=item_table[195],[2]=item_table[44],[3]=item_table[52],[4]=item_table[46],[5]=item_table[196]},},
{seq=7,target=12,rmb_seq=107,price=888,pay_reward={[0]=item_table[194],[1]=item_table[195],[2]=item_table[44],[3]=item_table[52],[4]=item_table[46],[5]=item_table[197]},},
{seq=8,target=13,rmb_seq=108,price=1000,free_reward={[0]=item_table[195],[1]=item_table[34],[2]=item_table[52]},pay_reward={[0]=item_table[194],[1]=item_table[195],[2]=item_table[198],[3]=item_table[56],[4]=item_table[46],[5]=item_table[197]},},
{seq=9,target=14,rmb_seq=109,price=1288,free_reward={[0]=item_table[199],[1]=item_table[34],[2]=item_table[52]},pay_reward={[0]=item_table[200],[1]=item_table[199],[2]=item_table[198],[3]=item_table[56],[4]=item_table[52],[5]=item_table[197]},},
{seq=10,target=15,rmb_seq=110,price=1588,free_reward={[0]=item_table[199],[1]=item_table[39],[2]=item_table[52]},pay_reward={[0]=item_table[200],[1]=item_table[199],[2]=item_table[50],[3]=item_table[61],[4]=item_table[52],[5]=item_table[191]},},
{seq=11,target=16,rmb_seq=111,price=1888,free_reward={[0]=item_table[199],[1]=item_table[39],[2]=item_table[56]},pay_reward={[0]=item_table[200],[1]=item_table[199],[2]=item_table[50],[3]=item_table[61],[4]=item_table[56],[5]=item_table[191]},},
{seq=12,target=17,rmb_seq=112,price=2000,free_reward={[0]=item_table[201],[1]=item_table[44],[2]=item_table[56]},pay_reward={[0]=item_table[202],[1]=item_table[201],[2]=item_table[203],[3]=item_table[64],[4]=item_table[56],[5]=item_table[191]},},
{seq=13,target=18,rmb_seq=113,price=2100,free_reward={[0]=item_table[201],[1]=item_table[44],[2]=item_table[56]},pay_reward={[0]=item_table[202],[1]=item_table[201],[2]=item_table[203],[3]=item_table[64],[4]=item_table[61],[5]=item_table[191]},},
{seq=14,target=19,rmb_seq=114,price=2500,free_reward={[0]=item_table[201],[1]=item_table[198],[2]=item_table[61]},pay_reward={[0]=item_table[202],[1]=item_table[201],[2]=item_table[204],[3]=item_table[69],[4]=item_table[61],[5]=item_table[191]},},
{seq=15,target=20,free_reward={[0]=item_table[43],[1]=item_table[198],[2]=item_table[61]},pay_reward={[0]=item_table[205],[1]=item_table[43],[2]=item_table[204],[3]=item_table[69],[4]=item_table[64],[5]=item_table[191]},},
{seq=16,target=21,free_reward={[0]=item_table[43],[1]=item_table[203],[2]=item_table[64]},pay_reward={[0]=item_table[205],[1]=item_table[43],[2]=item_table[206],[3]=item_table[207],[4]=item_table[64],[5]=item_table[191]},},
{seq=17,target=22,free_reward={[0]=item_table[43],[1]=item_table[203],[2]=item_table[208]},pay_reward={[0]=item_table[205],[1]=item_table[43],[2]=item_table[206],[3]=item_table[207],[4]=item_table[69],[5]=item_table[191]},},
{seq=18,target=23,free_reward={[0]=item_table[209],[1]=item_table[206],[2]=item_table[210]},pay_reward={[0]=item_table[211],[1]=item_table[209],[2]=item_table[212],[3]=item_table[213],[4]=item_table[69],[5]=item_table[191]},},
{seq=19,target=24,free_reward={[0]=item_table[209],[1]=item_table[206],[2]=item_table[214]},pay_reward={[0]=item_table[211],[1]=item_table[209],[2]=item_table[212],[3]=item_table[215],[4]=item_table[207],[5]=item_table[191]},},
{grade=2,rmb_seq=200,free_reward={[0]=item_table[17],[1]=item_table[1],[2]=item_table[188]},pay_reward={[0]=item_table[216],[1]=item_table[17],[2]=item_table[80],[3]=item_table[190],[4]=item_table[191]},},
{grade=2,rmb_seq=201,free_reward={[0]=item_table[17],[1]=item_table[1],[2]=item_table[45]},pay_reward={[0]=item_table[216],[1]=item_table[17],[2]=item_table[80],[3]=item_table[190],[4]=item_table[192]},},
{grade=2,rmb_seq=202,free_reward={[0]=item_table[17],[1]=item_table[80],[2]=item_table[45]},pay_reward={[0]=item_table[216],[1]=item_table[17],[2]=item_table[84],[3]=item_table[35],[4]=item_table[192]},},
{grade=2,rmb_seq=203,free_reward={[0]=item_table[28],[1]=item_table[80],[2]=item_table[20]},pay_reward={[0]=item_table[217],[1]=item_table[28],[2]=item_table[84],[3]=item_table[35],[4]=item_table[192]},},
{grade=2,rmb_seq=204,free_reward={[0]=item_table[28],[1]=item_table[82],[2]=item_table[35]},pay_reward={[0]=item_table[217],[1]=item_table[28],[2]=item_table[85],[3]=item_table[46],[4]=item_table[35],[5]=item_table[196]},},
{grade=2,rmb_seq=205,free_reward={[0]=item_table[28],[1]=item_table[82],[2]=item_table[46]},pay_reward={[0]=item_table[217],[1]=item_table[28],[2]=item_table[85],[3]=item_table[46],[4]=item_table[35],[5]=item_table[196]},},
{grade=2,rmb_seq=206,free_reward={[0]=item_table[195],[1]=item_table[84],[2]=item_table[46]},pay_reward={[0]=item_table[218],[1]=item_table[195],[2]=item_table[87],[3]=item_table[52],[4]=item_table[46],[5]=item_table[196]},},
{grade=2,rmb_seq=207,free_reward={[0]=item_table[195],[1]=item_table[84],[2]=item_table[46]},pay_reward={[0]=item_table[218],[1]=item_table[195],[2]=item_table[87],[3]=item_table[52],[4]=item_table[46],[5]=item_table[197]},},
{grade=2,rmb_seq=208,free_reward={[0]=item_table[195],[1]=item_table[219],[2]=item_table[52]},pay_reward={[0]=item_table[218],[1]=item_table[195],[2]=item_table[220],[3]=item_table[56],[4]=item_table[46],[5]=item_table[197]},},
{grade=2,rmb_seq=209,free_reward={[0]=item_table[199],[1]=item_table[219],[2]=item_table[52]},pay_reward={[0]=item_table[221],[1]=item_table[199],[2]=item_table[220],[3]=item_table[56],[4]=item_table[52],[5]=item_table[197]},},
{grade=2,rmb_seq=210,free_reward={[0]=item_table[199],[1]=item_table[222],[2]=item_table[52]},pay_reward={[0]=item_table[221],[1]=item_table[199],[2]=item_table[89],[3]=item_table[61],[4]=item_table[52],[5]=item_table[191]},},
{grade=2,rmb_seq=211,free_reward={[0]=item_table[199],[1]=item_table[222],[2]=item_table[56]},pay_reward={[0]=item_table[221],[1]=item_table[199],[2]=item_table[89],[3]=item_table[61],[4]=item_table[56],[5]=item_table[191]},},
{grade=2,rmb_seq=212,free_reward={[0]=item_table[201],[1]=item_table[223],[2]=item_table[56]},pay_reward={[0]=item_table[224],[1]=item_table[201],[2]=item_table[225],[3]=item_table[64],[4]=item_table[56],[5]=item_table[191]},},
{grade=2,rmb_seq=213,free_reward={[0]=item_table[201],[1]=item_table[223],[2]=item_table[56]},pay_reward={[0]=item_table[224],[1]=item_table[201],[2]=item_table[225],[3]=item_table[64],[4]=item_table[61],[5]=item_table[191]},},
{grade=2,rmb_seq=214,free_reward={[0]=item_table[201],[1]=item_table[225],[2]=item_table[61]},pay_reward={[0]=item_table[224],[1]=item_table[201],[2]=item_table[226],[3]=item_table[69],[4]=item_table[61],[5]=item_table[191]},},
{grade=2,rmb_seq=215,free_reward={[0]=item_table[43],[1]=item_table[225],[2]=item_table[61]},pay_reward={[0]=item_table[227],[1]=item_table[43],[2]=item_table[226],[3]=item_table[69],[4]=item_table[64],[5]=item_table[191]},},
{grade=2,rmb_seq=216,free_reward={[0]=item_table[43],[1]=item_table[226],[2]=item_table[64]},pay_reward={[0]=item_table[227],[1]=item_table[43],[2]=item_table[228],[3]=item_table[207],[4]=item_table[64],[5]=item_table[191]},},
{grade=2,rmb_seq=217,free_reward={[0]=item_table[43],[1]=item_table[226],[2]=item_table[208]},pay_reward={[0]=item_table[227],[1]=item_table[43],[2]=item_table[228],[3]=item_table[207],[4]=item_table[69],[5]=item_table[191]},},
{grade=2,rmb_seq=218,free_reward={[0]=item_table[209],[1]=item_table[228],[2]=item_table[210]},pay_reward={[0]=item_table[229],[1]=item_table[209],[2]=item_table[230],[3]=item_table[213],[4]=item_table[69],[5]=item_table[191]},},
{grade=2,rmb_seq=219,free_reward={[0]=item_table[209],[1]=item_table[228],[2]=item_table[214]},pay_reward={[0]=item_table[229],[1]=item_table[209],[2]=item_table[230],[3]=item_table[215],[4]=item_table[207],[5]=item_table[191]},}
},

upstar_gift_meta_table_map={
[21]=1,	-- depth:1
[38]=18,	-- depth:1
[37]=17,	-- depth:1
[36]=16,	-- depth:1
[39]=19,	-- depth:1
[40]=20,	-- depth:1
[35]=15,	-- depth:1
[34]=14,	-- depth:1
[33]=13,	-- depth:1
[32]=12,	-- depth:1
[29]=9,	-- depth:1
[30]=10,	-- depth:1
[28]=8,	-- depth:1
[27]=7,	-- depth:1
[26]=6,	-- depth:1
[25]=5,	-- depth:1
[24]=4,	-- depth:1
[22]=2,	-- depth:1
[31]=11,	-- depth:1
[23]=3,	-- depth:1
},
walk_together={
{},
{grade=2,}
},

walk_together_meta_table_map={
},
walk_together_reward={
{target=10,reward_item={[0]=item_table[231],[1]=item_table[6],[2]=item_table[162],[3]=item_table[9],[4]=item_table[164]},},
{seq=1,reward_item={[0]=item_table[7],[1]=item_table[12],[2]=item_table[162],[3]=item_table[9],[4]=item_table[164]},},
{seq=2,target=100,reward_item={[0]=item_table[232],[1]=item_table[13],[2]=item_table[17],[3]=item_table[233],[4]=item_table[20],[5]=item_table[234]},},
{seq=3,target=200,reward_item={[0]=item_table[235],[1]=item_table[13],[2]=item_table[28],[3]=item_table[188],[4]=item_table[20],[5]=item_table[234]},},
{seq=4,target=300,reward_item={[0]=item_table[236],[1]=item_table[18],[2]=item_table[28],[3]=item_table[188],[4]=item_table[35],[5]=item_table[234]},},
{seq=5,target=500,reward_item={[0]=item_table[237],[1]=item_table[24],[2]=item_table[28],[3]=item_table[40],[4]=item_table[35],[5]=item_table[238]},},
{seq=6,target=800,reward_item={[0]=item_table[237],[1]=item_table[24],[2]=item_table[195],[3]=item_table[40],[4]=item_table[46],[5]=item_table[238]},},
{seq=7,target=1200,reward_item={[0]=item_table[189],[1]=item_table[34],[2]=item_table[195],[3]=item_table[45],[4]=item_table[46],[5]=item_table[238]},},
{seq=8,target=1800,reward_item={[0]=item_table[189],[1]=item_table[34],[2]=item_table[195],[3]=item_table[45],[4]=item_table[52],[5]=item_table[239]},},
{seq=9,target=2400,reward_item={[0]=item_table[240],[1]=item_table[39],[2]=item_table[199],[3]=item_table[241],[4]=item_table[52],[5]=item_table[239]},},
{seq=10,target=3000,reward_item={[0]=item_table[240],[1]=item_table[44],[2]=item_table[199],[3]=item_table[51],[4]=item_table[56],[5]=item_table[242]},},
{seq=11,target=4000,reward_item={[0]=item_table[193],[1]=item_table[44],[2]=item_table[43],[3]=item_table[60],[4]=item_table[56],[5]=item_table[243]},},
{grade=2,},
{seq=1,target=40,}
},

walk_together_reward_meta_table_map={
[14]=13,	-- depth:1
},
convert_shop={
{limit_num=1,item=item_table[244],stuff_count=1200,},
{seq=1,limit_num=100,item=item_table[232],stuff_count=5,},
{seq=2,limit_num=10,item=item_table[128],},
{seq=3,item=item_table[129],stuff_count=30,},
{seq=4,limit_num=20,item=item_table[71],stuff_count=3,},
{seq=5,limit_num=3,stuff_count=30,},
{seq=6,limit_num=3,item=item_table[215],stuff_count=1000,},
{seq=7,limit_num=3,item=item_table[213],stuff_count=800,},
{seq=8,limit_num=3,item=item_table[207],stuff_count=650,},
{seq=9,limit_num=3,item=item_table[69],stuff_count=500,},
{seq=10,item=item_table[64],stuff_count=380,},
{seq=11,item=item_table[61],stuff_count=280,},
{seq=12,item=item_table[56],stuff_count=200,},
{seq=13,limit_num=10,item=item_table[52],stuff_count=120,},
{seq=14,limit_num=10,item=item_table[46],stuff_count=70,},
{seq=15,limit_num=10,item=item_table[35],stuff_count=40,},
{seq=16,limit_num=20,item=item_table[20],stuff_count=20,},
{seq=17,limit_num=50,item=item_table[9],},
{grade=2,limit_num=10,item=item_table[245],stuff_id=59687,stuff_count=500,},
{seq=1,limit_num=100,item=item_table[95],stuff_count=15,},
{seq=2,limit_num=50,item=item_table[246],},
{seq=3,limit_num=1,item=item_table[69],},
{seq=4,item=item_table[64],stuff_count=400,},
{seq=5,item=item_table[247],stuff_count=300,},
{seq=6,item=item_table[130],},
{seq=7,limit_num=4,item=item_table[248],},
{seq=8,item=item_table[61],stuff_count=280,},
{seq=9,item=item_table[56],stuff_count=200,},
{seq=10,limit_num=15,item=item_table[52],stuff_count=130,},
{grade=2,seq=11,stuff_id=59687,stuff_count=100,},
{seq=12,item=item_table[249],stuff_count=50,},
{seq=13,limit_num=3,item=item_table[250],},
{seq=14,limit_num=20,item=item_table[251],stuff_count=20,},
{grade=2,seq=15,limit_num=25,item=item_table[252],stuff_id=59687,},
{seq=16,limit_num=500,item=item_table[253],stuff_count=5,}
},

convert_shop_meta_table_map={
[27]=30,	-- depth:1
[31]=30,	-- depth:1
[24]=30,	-- depth:1
[25]=30,	-- depth:1
[33]=19,	-- depth:1
[32]=31,	-- depth:2
[29]=19,	-- depth:1
[21]=33,	-- depth:2
[26]=30,	-- depth:1
[23]=32,	-- depth:3
[22]=19,	-- depth:1
[20]=19,	-- depth:1
[28]=19,	-- depth:1
[35]=19,	-- depth:1
},
convert_shop_show={
{},
{index=2,model_show_itemid=59675,show_time=6,},
{grade=2,model_show_itemid=30873,}
},

convert_shop_show_meta_table_map={
},
flowing={
{},
{seq=1,rmb_seq=101,reward_item={[0]=item_table[162],[1]=item_table[254]},},
{seq=2,rmb_seq=102,price=6,reward_item={[0]=item_table[232],[1]=item_table[255]},},
{seq=3,rmb_seq=103,reward_item={[0]=item_table[162],[1]=item_table[113]},},
{seq=4,rmb_seq=104,reward_item={[0]=item_table[17],[1]=item_table[234]},},
{seq=5,rmb_seq=105,price=10,reward_item={[0]=item_table[235],[1]=item_table[256]},},
{seq=6,rmb_seq=106,reward_item={[0]=item_table[25],[1]=item_table[234]},},
{seq=7,rmb_seq=107,reward_item={[0]=item_table[28],[1]=item_table[231]},},
{seq=8,rmb_seq=108,price=30,reward_item={[0]=item_table[236],[1]=item_table[257]},},
{seq=9,rmb_seq=109,reward_item={[0]=item_table[25],[1]=item_table[13]},},
{seq=10,rmb_seq=110,},
{seq=11,rmb_seq=111,price=68,reward_item={[0]=item_table[237],[1]=item_table[258]},},
{seq=12,rmb_seq=112,reward_item={[0]=item_table[233],[1]=item_table[13]},},
{seq=13,rmb_seq=113,reward_item={[0]=item_table[28],[1]=item_table[259]},},
{seq=14,rmb_seq=114,price=98,reward_item={[0]=item_table[260],[1]=item_table[261]},},
{seq=15,rmb_seq=115,reward_item={[0]=item_table[233],[1]=item_table[262]},},
{seq=16,rmb_seq=116,reward_item={[0]=item_table[195],[1]=item_table[18]},},
{seq=17,rmb_seq=117,price=128,reward_item={[0]=item_table[263],[1]=item_table[264]},},
{seq=18,rmb_seq=118,reward_item={[0]=item_table[30],[1]=item_table[239]},},
{seq=19,rmb_seq=119,},
{seq=20,rmb_seq=120,price=198,reward_item={[0]=item_table[193],[1]=item_table[265]},},
{seq=21,rmb_seq=121,reward_item={[0]=item_table[188],[1]=item_table[24]},},
{seq=22,rmb_seq=122,},
{seq=23,rmb_seq=123,price=328,reward_item={[0]=item_table[194],[1]=item_table[266]},},
{seq=24,rmb_seq=124,reward_item={[0]=item_table[40],[1]=item_table[24]},},
{seq=25,rmb_seq=125,reward_item={[0]=item_table[199],[1]=item_table[267]},},
{seq=26,rmb_seq=126,},
{seq=27,rmb_seq=127,reward_item={[0]=item_table[45],[1]=item_table[34]},},
{seq=28,rmb_seq=128,reward_item={[0]=item_table[199],[1]=item_table[268]},},
{seq=29,type=1,rmb_seq=129,price=648,reward_item={[0]=item_table[269],[1]=item_table[270]},},
{grade=2,rmb_seq=200,},
{grade=2,seq=1,rmb_seq=201,reward_item={[0]=item_table[162],[1]=item_table[73]},},
{grade=2,rmb_seq=202,reward_item={[0]=item_table[95],[1]=item_table[255]},},
{grade=2,seq=3,rmb_seq=203,reward_item={[0]=item_table[162],[1]=item_table[234]},},
{grade=2,seq=4,rmb_seq=204,reward_item={[0]=item_table[17],[1]=item_table[76]},},
{grade=2,rmb_seq=205,reward_item={[0]=item_table[271],[1]=item_table[256]},},
{grade=2,rmb_seq=206,},
{grade=2,seq=7,rmb_seq=207,reward_item={[0]=item_table[28],[1]=item_table[75]},},
{grade=2,rmb_seq=208,reward_item={[0]=item_table[272],[1]=item_table[257]},},
{grade=2,seq=9,rmb_seq=209,reward_item={[0]=item_table[25],[1]=item_table[1]},},
{seq=10,rmb_seq=210,},
{grade=2,rmb_seq=211,reward_item={[0]=item_table[273],[1]=item_table[258]},},
{grade=2,seq=12,rmb_seq=212,reward_item={[0]=item_table[233],[1]=item_table[1]},},
{grade=2,seq=13,rmb_seq=213,reward_item={[0]=item_table[28],[1]=item_table[274]},},
{grade=2,rmb_seq=214,reward_item={[0]=item_table[275],[1]=item_table[261]},},
{grade=2,rmb_seq=215,},
{grade=2,seq=16,rmb_seq=216,reward_item={[0]=item_table[195],[1]=item_table[80]},},
{grade=2,rmb_seq=217,reward_item={[0]=item_table[276],[1]=item_table[264]},},
{grade=2,rmb_seq=218,},
{seq=19,rmb_seq=219,},
{grade=2,rmb_seq=220,reward_item={[0]=item_table[217],[1]=item_table[265]},},
{grade=2,seq=21,rmb_seq=221,reward_item={[0]=item_table[188],[1]=item_table[84]},},
{grade=2,seq=22,rmb_seq=222,reward_item={[0]=item_table[199],[1]=item_table[81]},},
{grade=2,rmb_seq=223,reward_item={[0]=item_table[218],[1]=item_table[266]},},
{grade=2,seq=24,rmb_seq=224,reward_item={[0]=item_table[40],[1]=item_table[84]},},
{grade=2,seq=25,rmb_seq=225,reward_item={[0]=item_table[199],[1]=item_table[277]},},
{grade=2,rmb_seq=226,reward_item={[0]=item_table[278],[1]=item_table[270]},},
{grade=2,seq=27,rmb_seq=227,reward_item={[0]=item_table[45],[1]=item_table[219]},},
{grade=2,seq=28,rmb_seq=228,reward_item={[0]=item_table[199],[1]=item_table[88]},},
{seq=29,rmb_seq=229,}
},

flowing_meta_table_map={
[23]=26,	-- depth:1
[20]=17,	-- depth:1
[11]=14,	-- depth:1
[37]=7,	-- depth:1
[46]=16,	-- depth:1
[49]=19,	-- depth:1
[41]=44,	-- depth:1
[50]=47,	-- depth:1
[3]=30,	-- depth:1
[6]=30,	-- depth:1
[27]=30,	-- depth:1
[24]=30,	-- depth:1
[9]=30,	-- depth:1
[21]=30,	-- depth:1
[18]=30,	-- depth:1
[15]=30,	-- depth:1
[12]=30,	-- depth:1
[42]=12,	-- depth:2
[57]=27,	-- depth:2
[54]=24,	-- depth:2
[51]=21,	-- depth:2
[48]=18,	-- depth:2
[45]=15,	-- depth:2
[33]=3,	-- depth:2
[36]=6,	-- depth:2
[39]=9,	-- depth:2
[60]=57,	-- depth:3
},
scheme={
{},
{grade=2,reward_item={[0]=item_table[28]},}
},

scheme_meta_table_map={
},
pursuit={
{},
{grade=2,reward_preview_item={[0]=item_table[279]},}
},

pursuit_meta_table_map={
},
pursuit_turn={
{},
{turn=2,},
{turn=3,},
{grade=2,reward_item={[0]=item_table[279]},},
{turn=2,reward_item={[0]=item_table[280]},},
{turn=3,reward_item={[0]=item_table[276]},}
},

pursuit_turn_meta_table_map={
[5]=4,	-- depth:1
[6]=4,	-- depth:1
},
pursuit_task={
{task_type=3,task_param2=36,task_desc="上阵幻兽·通天教主",panel="ControlBeastsView#beasts_battle",},
{seq=1,task_param2=33,task_desc="上阵幻兽·火神三太子",},
{seq=2,task_param1=100,task_desc="活跃值达到%s/100",reward_item={[0]=item_table[281]},dart_num=1,},
{seq=3,task_param1=150,task_desc="活跃值达到%s/150",},
{seq=4,task_param1=250,task_desc="活跃值达到%s/250",reward_item={[0]=item_table[282]},dart_num=5,},
{seq=5,task_param1=300,task_desc="活跃值达到%s/300",reward_item={[0]=item_table[283]},dart_num=8,},
{seq=6,task_param1=400,task_desc="活跃值达到%s/400",reward_item={[0]=item_table[284]},dart_num=10,},
{seq=7,task_type=2,task_desc="累充达到达到%s/1",panel="recharge#recharge_cz",},
{seq=8,task_type=2,task_param1=5,task_desc="累充达到达到%s/5",panel="recharge#recharge_cz",reward_item={[0]=item_table[282]},dart_num=5,},
{seq=9,task_param1=10,task_desc="累充达到达到%s/10",},
{seq=10,task_type=2,task_param1=30,task_desc="累充达到达到%s/30",panel="recharge#recharge_cz",reward_item={[0]=item_table[284]},dart_num=10,},
{seq=11,task_param1=50,task_desc="累充达到达到%s/50",},
{seq=12,task_type=4,task_param1=1000,panel="market",reward_item={[0]=item_table[281]},dart_num=1,},
{seq=13,task_param1=10000,reward_item={[0]=item_table[285]},dart_num=2,},
{seq=14,task_param1=30000,task_desc="消耗灵玉%s/30000",},
{seq=15,task_type=4,task_param1=50000,task_desc="消耗灵玉%s/50000",panel="market",},
{seq=16,task_type=4,task_param1=100000,task_desc="消耗灵玉%s/100000",panel="market",reward_item={[0]=item_table[282]},dart_num=5,},
{grade=2,task_param1=10,task_desc="活跃值达到%s/10",reward_item={[0]=item_table[281]},dart_num=1,},
{grade=2,seq=1,task_type=2,task_param1=100,task_desc="累充达到达到%s/100",panel="recharge#recharge_cz",reward_item={[0]=item_table[281]},dart_num=1,},
{grade=2,seq=2,task_type=3,task_param2=36,task_desc="上阵幻兽通天教主",panel="ControlBeastsView#beasts_battle",reward_item={[0]=item_table[281]},dart_num=1,},
{grade=2,seq=3,}
},

pursuit_task_meta_table_map={
[2]=1,	-- depth:1
[15]=16,	-- depth:1
[12]=11,	-- depth:1
[10]=11,	-- depth:1
[14]=17,	-- depth:1
[21]=13,	-- depth:1
},
first_page={
{},
{seq=2,image_icon="a3_zjm_icon_zlcb",jump_path="HelpRankView",},
{seq=3,image_icon="a3_zjm_icon_clbx",jump_path="WorldTreasureView#tcdb_flowing",},
{seq=4,image_icon="a3_zjm_icon_dhsd",jump_path="WorldTreasureView#tcdb_premium_shop",},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

first_page_meta_table_map={
[6]=2,	-- depth:1
[7]=3,	-- depth:1
[8]=4,	-- depth:1
},
grade_default_table={start_day=2,end_day=3,grade=1,entrance_icon="a3_zjm_icon_ttjl",before_icon="a3_zjm_icon_ttjl",after_icon="a3_zjm_icon_ttjl",des_image="a2_tcdb_txt_1",title_image="a2_tcdb_title2",shop_title="a2_tcdb_shop_title2",entrance_title="通天降临",rmb_shop_discount_time=86400,is_can_buy_one=1,consume_item_id=54800,add_exp=10,reward_item={[0]=item_table[286]},open_panel_name="ControlBeastsView",res_path="yushou",drop_item="46378|46380",drop_item_limilt="40|4",grade_name="龙蛋",drop_open_panel_name="ControlBeastsPrizeDrawWGView",open_type=1,open_index="10|40|60|90|120|130|140",text_color1="#fff8bb",text_color2="#a84b05",open_panel="",},

login_reward_default_table={grade=1,day_index=1,reward_item={[0]=item_table[2],[1]=item_table[3],[2]=item_table[4],[3]=item_table[287],[4]=item_table[288]},reward_name="首日登录礼包",},

shouchong_reward_default_table={grade=1,reward_item={[0]=item_table[95],[1]=item_table[6],[2]=item_table[9],[3]=item_table[162],[4]=item_table[289]},},

leichong_reward_default_table={grade=1,ID=0,stage_value=100,reward_item={[0]=item_table[71],[1]=item_table[231],[2]=item_table[290],[3]=item_table[9],[4]=item_table[74],[5]=item_table[4]},},

leichong_model_default_table={grade=1,model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid=30742,effect_bundle_name="",effect_asset_name="",special_show_name="",display_pos="-158|20|0",rotation="0|0|0",display_scale=1.1,effect_pos="",effect_scale="",show_special_effect="",special_img="",if_showHalo=0,type=0,is_move="",is_show_character="",bg_res_name="a3_ttjl_bg1",},

puzzle_task_default_table={grade=1,ID=0,task_name="观看活动",task_condition_id=26,task_param1=1,task_param2=0,task_param3=0,task_param4=0,task_desc="完成活动首充%s/1次",reward_item={[0]=item_table[71],[1]=item_table[71],[2]=item_table[96],[3]=item_table[97]},panel="ControlBeastsPrizeDrawWGView",activity_id="",},

puzzle_gift_default_table={grade=1,ID=0,reward_item={[0]=item_table[291],[1]=item_table[292],[2]=item_table[293],[3]=item_table[102],[4]=item_table[103]},price=1080,old_price=10800,discount=1,gift_name="<color=#fff691>特惠礼包</color>",},

puzzle_model_default_table={grade=1,model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid=30701,effect_bundle_name="",effect_asset_name="",special_show_name="",display_pos="0|-20|0",rotation="0|0|0",display_scale=1,effect_pos="",effect_scale="",show_special_effect="",special_img="",if_showHalo=0,type=0,is_move="",is_show_character="",},

puzzle_reward_default_table={grade=1,reward_item={[0]=item_table[272]},delay_get_time=86400,},

limit_buy_default_table={grade=1,shop=2,seq=0,type=0,rmb_type=0,discount_rmb_type=0,rmb_seq=0,price=198,discount_price=98,reward_item={[0]=item_table[294]},buy_count_limit=1,goods_txt="",},

shop_model_default_table={grade=1,model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid=30523,effect_bundle_name="",effect_asset_name="",special_show_name="",display_pos="0|-50|0",rotation="0|0|0",display_scale=1.1,effect_pos="",effect_scale="",show_special_effect="",special_img="",if_showHalo=0,type=0,is_move="",is_show_character="",},

client_desc_default_table={grade=1,tab=20,txt="",rule_desc="<color=#FFF8BB>幻兽预热活动全民开启！连续两天登录都可以获得丰厚奖励。\n前往完成各个预热活动后，可以获得龙蛋精华。<color=#99FFBB>给予龙蛋进行升级后可以提升龙蛋品质，努力提升龙蛋品质，领取的奖励将更加丰厚。</color>\n龙蛋奖励仅可领取一次，请尽量提升龙蛋至满级再领取。</color>",},

dragon_egg_default_table={grade=1,level=1,exp=50,reward_item={[0]=item_table[290],[1]=item_table[74],[2]=item_table[169],[3]=item_table[295]},},

other_default_table={refresh_times=2,lang_id=1,model_id=1,boss_time=900,first_ready_times=180,second_ready_times=60,npc_scene=1003,npc_local_x=380,npc_local_y=270,npcid=10340,player_local_x=52,player_local_y=66,},

boss_cfg_default_table={grade=1,scene_id=9620,boss_time=900,limit_lv=1,boss_id=53021,boss_posx=60,boss_posy=66,scale=110,position="0|-1.12|0",},

refresh_time_default_table={grade=1,refresh_time=1100,},

reward_config_default_table={grade=1,min_lv=0,max_lv=340,do_times_limit=1,reward_show={[0]=item_table[296],[1]=item_table[184],[2]=item_table[185],[3]=item_table[186],[4]=item_table[297]},kill_drop_device_id=0,last_kill_reward_show={},},

monster_drop_cfg_default_table={grade=1,random_item={},},

boss_drop_cfg_default_table={grade=1,random_item={[0]=item_table[186],[1]=item_table[185],[2]=item_table[298]},},

extra_drop_default_table={grade=1,},

extra_drop_model_default_table={grade=1,model_bundle_name="model/yushou/4087_prefab",model_asset_name=4087,whole_display_pos="-81|20",model_rot="0|-20|0",model_scale=0.9,},

open_task_default_table={ID=1,panel="BillionSubsidy",},

upstar_gift_grade_default_table={grade=1,start_day=2,end_day=3,beast_type=36,img_id=1,spine_id=1006,scale=0.5,model_show_itemid=30742,display_pos="0|0",display_scale=1,display_rotation="0|0|0",bg_res_name="a3_ttjl_bg1",rmb_type=226,rmb_seq=1,price=648,},

upstar_gift_default_table={grade=1,seq=0,target=5,money_type=1,rmb_type=226,rmb_seq=115,price=3000,free_reward={[0]=item_table[195],[1]=item_table[29],[2]=item_table[46]},pay_reward={[0]=item_table[193],[1]=item_table[28],[2]=item_table[39],[3]=item_table[46],[4]=item_table[35],[5]=item_table[196]},},

walk_together_default_table={grade=1,team_num=3,},

walk_together_reward_default_table={grade=1,seq=0,target=30,reward_item={[0]=item_table[95],[1]=item_table[6],[2]=item_table[9],[3]=item_table[162],[4]=item_table[289]},},

convert_shop_default_table={grade=1,seq=0,limit_num=5,item=item_table[299],stuff_id=59686,stuff_count=10,},

convert_shop_show_default_table={grade=1,index=1,scale=1,model_show_itemid=30742,display_pos="0.3|-0.3",display_scale=1.1,display_rotation="0|0|0",show_time=10,},

flowing_default_table={grade=1,seq=0,type=0,rmb_type=233,rmb_seq=100,price=0,reward_item={[0]=item_table[162],[1]=item_table[164]},},

scheme_default_table={grade=1,start_time=1200,end_time=2000,open_level=100,reward_item={[0]=item_table[232],[1]=item_table[13],[2]=item_table[17],[3]=item_table[25]},},

pursuit_default_table={grade=1,reward_preview_item={[0]=item_table[6],[1]=item_table[162],[2]=item_table[113],[3]=item_table[164]},rule_desc="通过完成任务获取<color=#99ffbb>凤尾羽箭</color>，一根可以扎破一个锦囊，锦囊下方概率藏有教主徽章，集齐3个徽章即可领取一轮奖励~",},

pursuit_turn_default_table={grade=1,turn=1,reward_item={[0]=item_table[6],[1]=item_table[162],[2]=item_table[113],[3]=item_table[164]},pass_badge_num=3,},

pursuit_task_default_table={grade=1,seq=0,task_type=1,task_param1=1,task_param2=0,task_desc="消耗灵玉%s/1000",panel="bizuo",reward_item={[0]=item_table[300]},dart_num=3,},

first_page_default_table={grade=1,seq=1,image_icon="a3_zjm_icon_hsqy",jump_path="ControlBeastsPrizeDrawWGView##op=oa_act_draw_type_item",}

}

