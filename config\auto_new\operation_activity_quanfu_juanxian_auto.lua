-- Y-运营活动-全服捐献.xls
local item_table={
[1]={item_id=26000,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
config_param={
{},
{grade=1,},
{start_server_day=22,end_server_day=29,grade=2,},
{grade=3,},
{start_server_day=29,end_server_day=36,grade=4,},
{grade=5,},
{start_server_day=36,end_server_day=43,grade=6,},
{grade=7,},
{start_server_day=43,end_server_day=50,grade=8,},
{grade=9,},
{start_server_day=50,end_server_day=57,grade=10,},
{grade=11,}
},

config_param_meta_table_map={
[4]=3,	-- depth:1
[6]=5,	-- depth:1
[8]=7,	-- depth:1
[10]=9,	-- depth:1
[12]=11,	-- depth:1
},
oa_qfjx_role_jx_reward={
{},
{reward_id=2,},
{reward_id=3,},
{reward_id=4,},
{reward_id=5,},
{reward_id=6,},
{reward_id=7,},
{reward_id=8,},
{reward_id=9,},
{reward_id=10,},
{grade=1,},
{reward_id=2,},
{reward_id=3,},
{reward_id=4,},
{reward_id=5,},
{reward_id=6,},
{reward_id=7,},
{reward_id=8,},
{reward_id=9,},
{grade=1,},
{grade=2,},
{reward_id=2,},
{grade=2,},
{reward_id=4,},
{reward_id=5,},
{reward_id=6,},
{reward_id=7,},
{reward_id=8,},
{reward_id=9,},
{grade=2,},
{grade=3,},
{reward_id=2,},
{reward_id=3,},
{reward_id=4,},
{grade=3,},
{grade=3,},
{grade=3,},
{reward_id=8,},
{reward_id=9,},
{grade=3,},
{grade=4,},
{reward_id=2,},
{reward_id=3,},
{reward_id=4,},
{reward_id=5,},
{reward_id=6,},
{reward_id=7,},
{reward_id=8,},
{reward_id=9,},
{grade=4,},
{grade=5,},
{reward_id=2,},
{reward_id=3,},
{reward_id=4,},
{reward_id=5,},
{reward_id=6,},
{reward_id=7,},
{reward_id=8,},
{grade=5,},
{grade=5,},
{grade=6,},
{reward_id=2,},
{reward_id=3,},
{reward_id=4,},
{grade=6,},
{reward_id=6,},
{grade=6,},
{reward_id=8,},
{grade=6,},
{grade=6,},
{grade=7,},
{reward_id=2,},
{reward_id=3,},
{reward_id=4,},
{reward_id=5,},
{reward_id=6,},
{reward_id=7,},
{reward_id=8,},
{reward_id=9,},
{grade=7,},
{grade=8,},
{reward_id=2,},
{grade=8,},
{reward_id=4,},
{reward_id=5,},
{reward_id=6,},
{reward_id=7,},
{grade=8,},
{reward_id=9,},
{reward_id=10,},
{grade=9,},
{reward_id=2,},
{reward_id=3,},
{reward_id=4,},
{reward_id=5,},
{reward_id=6,},
{reward_id=7,},
{grade=9,},
{reward_id=9,},
{grade=9,},
{grade=10,},
{reward_id=2,},
{reward_id=3,},
{reward_id=4,},
{reward_id=5,},
{reward_id=6,},
{reward_id=7,},
{reward_id=8,},
{reward_id=9,},
{grade=10,},
{grade=11,},
{reward_id=2,},
{reward_id=3,},
{reward_id=4,},
{reward_id=5,},
{reward_id=6,},
{reward_id=7,},
{grade=11,},
{grade=11,},
{grade=11,}
},

oa_qfjx_role_jx_reward_meta_table_map={
[88]=8,	-- depth:1
[89]=88,	-- depth:2
[86]=89,	-- depth:3
[85]=86,	-- depth:4
[84]=85,	-- depth:5
[90]=84,	-- depth:6
[87]=90,	-- depth:7
[70]=90,	-- depth:7
[82]=87,	-- depth:8
[80]=70,	-- depth:8
[79]=80,	-- depth:9
[78]=79,	-- depth:10
[77]=78,	-- depth:11
[75]=77,	-- depth:12
[74]=75,	-- depth:13
[73]=74,	-- depth:14
[72]=73,	-- depth:15
[83]=73,	-- depth:15
[76]=72,	-- depth:16
[98]=78,	-- depth:11
[93]=98,	-- depth:12
[118]=98,	-- depth:12
[117]=118,	-- depth:13
[116]=117,	-- depth:14
[115]=116,	-- depth:15
[114]=115,	-- depth:16
[113]=114,	-- depth:17
[112]=113,	-- depth:18
[110]=80,	-- depth:9
[109]=110,	-- depth:10
[108]=109,	-- depth:11
[107]=108,	-- depth:12
[106]=107,	-- depth:13
[105]=106,	-- depth:14
[104]=105,	-- depth:15
[103]=104,	-- depth:16
[102]=103,	-- depth:17
[100]=110,	-- depth:10
[99]=100,	-- depth:11
[69]=99,	-- depth:12
[97]=99,	-- depth:12
[96]=97,	-- depth:13
[95]=96,	-- depth:14
[94]=95,	-- depth:15
[92]=94,	-- depth:16
[68]=69,	-- depth:13
[60]=100,	-- depth:11
[66]=68,	-- depth:14
[35]=95,	-- depth:15
[34]=35,	-- depth:16
[33]=34,	-- depth:17
[32]=33,	-- depth:18
[30]=60,	-- depth:12
[29]=30,	-- depth:13
[28]=29,	-- depth:14
[27]=28,	-- depth:15
[26]=27,	-- depth:16
[25]=26,	-- depth:17
[36]=26,	-- depth:17
[24]=25,	-- depth:18
[22]=24,	-- depth:19
[20]=30,	-- depth:13
[19]=20,	-- depth:14
[18]=19,	-- depth:15
[17]=18,	-- depth:16
[16]=17,	-- depth:17
[15]=16,	-- depth:18
[14]=15,	-- depth:19
[13]=14,	-- depth:20
[12]=13,	-- depth:21
[23]=13,	-- depth:21
[37]=17,	-- depth:17
[38]=37,	-- depth:18
[39]=38,	-- depth:19
[65]=15,	-- depth:19
[64]=65,	-- depth:20
[63]=64,	-- depth:21
[62]=63,	-- depth:22
[119]=39,	-- depth:20
[59]=119,	-- depth:21
[58]=59,	-- depth:22
[57]=58,	-- depth:23
[56]=57,	-- depth:24
[55]=56,	-- depth:25
[54]=55,	-- depth:26
[53]=54,	-- depth:27
[52]=53,	-- depth:28
[50]=20,	-- depth:14
[49]=50,	-- depth:15
[48]=49,	-- depth:16
[47]=48,	-- depth:17
[46]=47,	-- depth:18
[45]=46,	-- depth:19
[44]=45,	-- depth:20
[43]=44,	-- depth:21
[42]=43,	-- depth:22
[40]=50,	-- depth:15
[67]=47,	-- depth:18
[120]=40,	-- depth:16
},
oa_qfjx_role_lj_reward={
{},
{index=2,juanxian_num=20,},
{index=3,juanxian_num=40,},
{index=4,juanxian_num=60,},
{index=5,juanxian_num=80,},
{index=6,juanxian_num=100,chuanwen=948,},
{index=7,juanxian_num=125,},
{index=8,juanxian_num=150,},
{index=9,juanxian_num=200,},
{index=10,juanxian_num=250,},
{index=11,juanxian_num=300,},
{index=12,juanxian_num=350,},
{index=13,juanxian_num=400,},
{index=14,juanxian_num=450,},
{index=15,juanxian_num=500,},
{index=16,juanxian_num=550,},
{index=17,juanxian_num=600,},
{index=18,juanxian_num=650,},
{index=19,juanxian_num=700,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,}
},

oa_qfjx_role_lj_reward_meta_table_map={
[135]=2,	-- depth:1
[136]=3,	-- depth:1
[137]=4,	-- depth:1
[138]=5,	-- depth:1
[149]=16,	-- depth:1
[144]=11,	-- depth:1
[141]=8,	-- depth:1
[142]=9,	-- depth:1
[150]=17,	-- depth:1
[143]=10,	-- depth:1
[145]=12,	-- depth:1
[208]=18,	-- depth:1
[148]=15,	-- depth:1
[140]=7,	-- depth:1
[147]=14,	-- depth:1
[123]=142,	-- depth:2
[131]=150,	-- depth:2
[111]=149,	-- depth:2
[112]=131,	-- depth:3
[113]=208,	-- depth:2
[227]=113,	-- depth:3
[116]=135,	-- depth:2
[117]=136,	-- depth:2
[118]=137,	-- depth:2
[119]=138,	-- depth:2
[132]=227,	-- depth:4
[212]=117,	-- depth:3
[122]=141,	-- depth:2
[124]=143,	-- depth:2
[125]=144,	-- depth:2
[126]=145,	-- depth:2
[211]=116,	-- depth:3
[128]=147,	-- depth:2
[129]=148,	-- depth:2
[130]=111,	-- depth:3
[121]=140,	-- depth:2
[151]=132,	-- depth:5
[161]=123,	-- depth:3
[154]=211,	-- depth:4
[179]=122,	-- depth:3
[180]=161,	-- depth:4
[181]=124,	-- depth:3
[182]=125,	-- depth:3
[183]=126,	-- depth:3
[202]=183,	-- depth:4
[185]=128,	-- depth:3
[186]=129,	-- depth:3
[178]=121,	-- depth:3
[187]=130,	-- depth:4
[189]=151,	-- depth:6
[201]=182,	-- depth:4
[192]=154,	-- depth:5
[193]=212,	-- depth:4
[194]=118,	-- depth:3
[195]=119,	-- depth:3
[200]=181,	-- depth:4
[197]=178,	-- depth:4
[188]=112,	-- depth:4
[207]=188,	-- depth:5
[176]=195,	-- depth:4
[174]=193,	-- depth:5
[155]=174,	-- depth:6
[156]=194,	-- depth:4
[157]=176,	-- depth:5
[206]=187,	-- depth:5
[159]=197,	-- depth:5
[160]=179,	-- depth:4
[110]=186,	-- depth:4
[162]=200,	-- depth:5
[175]=156,	-- depth:5
[163]=201,	-- depth:5
[205]=110,	-- depth:5
[166]=185,	-- depth:4
[167]=205,	-- depth:6
[168]=206,	-- depth:6
[169]=207,	-- depth:6
[170]=189,	-- depth:7
[204]=166,	-- depth:5
[173]=192,	-- depth:6
[164]=202,	-- depth:5
[109]=204,	-- depth:6
[100]=157,	-- depth:6
[107]=164,	-- depth:6
[41]=155,	-- depth:7
[42]=175,	-- depth:6
[43]=100,	-- depth:7
[223]=109,	-- depth:7
[45]=159,	-- depth:6
[46]=160,	-- depth:5
[47]=180,	-- depth:5
[48]=162,	-- depth:6
[40]=173,	-- depth:7
[49]=163,	-- depth:6
[52]=223,	-- depth:8
[53]=167,	-- depth:7
[54]=168,	-- depth:7
[55]=169,	-- depth:7
[56]=170,	-- depth:8
[221]=107,	-- depth:7
[59]=40,	-- depth:8
[60]=41,	-- depth:8
[50]=221,	-- depth:8
[61]=42,	-- depth:7
[224]=53,	-- depth:8
[36]=55,	-- depth:8
[13]=6,	-- depth:1
[19]=6,	-- depth:1
[21]=59,	-- depth:9
[22]=60,	-- depth:9
[23]=61,	-- depth:8
[24]=43,	-- depth:8
[226]=36,	-- depth:9
[37]=56,	-- depth:9
[26]=45,	-- depth:7
[28]=47,	-- depth:6
[29]=48,	-- depth:7
[30]=49,	-- depth:7
[31]=50,	-- depth:9
[225]=54,	-- depth:8
[33]=52,	-- depth:9
[34]=224,	-- depth:9
[35]=225,	-- depth:9
[27]=46,	-- depth:6
[213]=23,	-- depth:9
[62]=24,	-- depth:9
[64]=26,	-- depth:8
[87]=30,	-- depth:8
[88]=31,	-- depth:10
[216]=64,	-- depth:9
[90]=33,	-- depth:10
[91]=34,	-- depth:10
[92]=35,	-- depth:10
[93]=226,	-- depth:10
[94]=37,	-- depth:10
[86]=29,	-- depth:8
[97]=21,	-- depth:10
[99]=213,	-- depth:10
[198]=27,	-- depth:7
[214]=62,	-- depth:10
[102]=216,	-- depth:10
[103]=198,	-- depth:8
[104]=28,	-- depth:7
[105]=86,	-- depth:9
[106]=87,	-- depth:9
[98]=22,	-- depth:10
[220]=106,	-- depth:10
[85]=104,	-- depth:8
[83]=102,	-- depth:11
[65]=103,	-- depth:9
[66]=85,	-- depth:9
[67]=105,	-- depth:10
[68]=220,	-- depth:11
[69]=88,	-- depth:11
[219]=67,	-- depth:11
[71]=90,	-- depth:11
[72]=91,	-- depth:11
[73]=92,	-- depth:11
[84]=65,	-- depth:10
[199]=66,	-- depth:10
[75]=94,	-- depth:11
[218]=199,	-- depth:11
[78]=97,	-- depth:11
[79]=98,	-- depth:11
[80]=99,	-- depth:11
[81]=214,	-- depth:11
[217]=84,	-- depth:11
[74]=93,	-- depth:11
[222]=13,	-- depth:2
[209]=19,	-- depth:2
[203]=222,	-- depth:3
[215]=6,	-- depth:1
[114]=209,	-- depth:3
[190]=114,	-- depth:4
[25]=215,	-- depth:2
[32]=203,	-- depth:4
[38]=190,	-- depth:5
[44]=25,	-- depth:3
[51]=32,	-- depth:5
[57]=38,	-- depth:6
[63]=44,	-- depth:4
[70]=51,	-- depth:6
[76]=57,	-- depth:7
[82]=63,	-- depth:5
[89]=70,	-- depth:7
[95]=76,	-- depth:8
[101]=82,	-- depth:6
[108]=89,	-- depth:8
[120]=101,	-- depth:7
[127]=108,	-- depth:9
[133]=95,	-- depth:9
[139]=120,	-- depth:8
[146]=127,	-- depth:10
[152]=133,	-- depth:10
[158]=139,	-- depth:9
[165]=146,	-- depth:11
[171]=152,	-- depth:11
[177]=158,	-- depth:10
[184]=165,	-- depth:12
[196]=177,	-- depth:11
[228]=171,	-- depth:12
},
oa_qfjx_server_day_reward={
{param2=3,desc="幸运猫爪*3",},
{index=2,server_day_juanxian_num=200,param1=1,param2=0,desc="伏魔战场重生",},
{index=3,server_day_juanxian_num=400,desc="2级蓝晶石*1",},
{index=4,server_day_juanxian_num=600,},
{index=5,server_day_juanxian_num=800,desc="仙遗洞天次数药水*1",},
{index=6,server_day_juanxian_num=1000,},
{index=7,server_day_juanxian_num=1500,desc="冥虎精魄*1",},
{index=8,server_day_juanxian_num=2000,},
{index=9,server_day_juanxian_num=2500,desc="2级红晶石*1",},
{index=10,server_day_juanxian_num=3000,},
{index=11,server_day_juanxian_num=3500,desc="蜀山弟子*1",},
{index=12,server_day_juanxian_num=4000,},
{index=13,server_day_juanxian_num=4500,desc="九幽魔境次数药水*1",},
{index=14,server_day_juanxian_num=5000,param2=0,param4=1,desc="九幽魔境重生",},
{index=15,server_day_juanxian_num=6000,},
{index=16,server_day_juanxian_num=7000,reward_type=4,icon_type=2,param1=0,name="魔王重生",icon="jx_boss",},
{index=17,server_day_juanxian_num=8000,desc="紫檀暮雨·衣*1",},
{index=18,server_day_juanxian_num=9000,},
{index=19,server_day_juanxian_num=10000,desc="须弥神域次数药水*1",},
{index=20,server_day_juanxian_num=11000,param2=0,param5=1,desc="须弥神域重生",},
{index=21,server_day_juanxian_num=12000,desc="紫檀暮雨·武*1",},
{index=22,server_day_juanxian_num=13000,},
{index=23,server_day_juanxian_num=14000,param2=5,desc="幸运猫爪*5",},
{index=24,server_day_juanxian_num=15000,reward_type=1,param1=5000,param2=1800,name="经验BUFF奖励",icon="jx_exp",desc="全服经验BUFF+50%",},
{index=25,server_day_juanxian_num=16000,desc="珍稀藏宝图*1",},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,desc="海上靓影·衣*1",},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,desc="海上靓影·武*1",},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,desc="清夏灵韵·衣*1",},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,desc="清夏灵韵·武*1",},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,desc="墨落成白·衣*1",},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,desc="墨落成白·武*1",},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,desc="琼壶歌月·衣*1",},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,desc="琼壶歌月·武*1",},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,desc="噬血法珠*1",},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,desc="莫风韵*1",},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,desc="醉酒当歌·衣*1",},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,desc="醉酒当歌·武*1",},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,desc="温暖如夏·衣*1",},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,desc="温暖如夏·武*1",},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,desc="碧鸾翎羽*1",},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,desc="齐天清*1",},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,desc="潇湘云雨·衣*1",},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,desc="潇湘云雨·武*1",},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,desc="蜀山二长老*1",},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,desc="时空战士·衣*1",},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,desc="时空战士·武*1",},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,desc="冯依菡*1",},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,desc="豆蔻年华·衣*1",},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,desc="豆蔻年华·武*1",},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,desc="柳心兰*1",},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,}
},

oa_qfjx_server_day_reward_meta_table_map={
[51]=1,	-- depth:1
[226]=51,	-- depth:2
[176]=226,	-- depth:3
[76]=176,	-- depth:4
[251]=76,	-- depth:5
[151]=251,	-- depth:6
[26]=151,	-- depth:7
[276]=26,	-- depth:8
[126]=276,	-- depth:9
[201]=126,	-- depth:10
[15]=25,	-- depth:1
[101]=201,	-- depth:11
[125]=25,	-- depth:1
[171]=21,	-- depth:1
[175]=125,	-- depth:2
[115]=15,	-- depth:2
[178]=3,	-- depth:1
[180]=5,	-- depth:1
[182]=7,	-- depth:1
[169]=19,	-- depth:1
[184]=9,	-- depth:1
[186]=11,	-- depth:1
[188]=13,	-- depth:1
[190]=115,	-- depth:3
[192]=17,	-- depth:1
[194]=169,	-- depth:2
[196]=21,	-- depth:1
[113]=188,	-- depth:2
[167]=17,	-- depth:1
[159]=184,	-- depth:2
[163]=113,	-- depth:3
[128]=178,	-- depth:2
[121]=21,	-- depth:1
[130]=180,	-- depth:2
[119]=194,	-- depth:3
[132]=7,	-- depth:1
[134]=159,	-- depth:3
[136]=11,	-- depth:1
[138]=163,	-- depth:4
[140]=190,	-- depth:4
[142]=17,	-- depth:1
[200]=175,	-- depth:3
[146]=21,	-- depth:1
[153]=128,	-- depth:3
[155]=130,	-- depth:3
[117]=17,	-- depth:1
[157]=7,	-- depth:1
[161]=11,	-- depth:1
[165]=140,	-- depth:5
[144]=119,	-- depth:4
[217]=17,	-- depth:1
[205]=155,	-- depth:4
[259]=134,	-- depth:4
[261]=11,	-- depth:1
[263]=138,	-- depth:5
[265]=165,	-- depth:6
[267]=117,	-- depth:2
[269]=144,	-- depth:5
[271]=121,	-- depth:2
[275]=200,	-- depth:4
[257]=182,	-- depth:2
[278]=153,	-- depth:4
[282]=132,	-- depth:2
[284]=259,	-- depth:5
[286]=136,	-- depth:2
[288]=263,	-- depth:6
[290]=265,	-- depth:7
[292]=142,	-- depth:2
[294]=269,	-- depth:6
[296]=146,	-- depth:2
[280]=205,	-- depth:5
[255]=280,	-- depth:6
[253]=278,	-- depth:5
[250]=275,	-- depth:5
[207]=282,	-- depth:3
[209]=284,	-- depth:6
[211]=11,	-- depth:1
[213]=288,	-- depth:7
[215]=290,	-- depth:8
[111]=261,	-- depth:2
[219]=294,	-- depth:7
[221]=21,	-- depth:1
[225]=250,	-- depth:6
[228]=253,	-- depth:6
[230]=255,	-- depth:7
[232]=157,	-- depth:2
[234]=209,	-- depth:7
[236]=11,	-- depth:1
[238]=213,	-- depth:8
[240]=215,	-- depth:9
[242]=17,	-- depth:1
[244]=219,	-- depth:8
[246]=21,	-- depth:1
[203]=228,	-- depth:7
[109]=234,	-- depth:8
[150]=225,	-- depth:7
[300]=150,	-- depth:8
[90]=240,	-- depth:10
[38]=238,	-- depth:9
[40]=90,	-- depth:11
[88]=38,	-- depth:10
[67]=17,	-- depth:1
[42]=17,	-- depth:1
[44]=244,	-- depth:9
[86]=236,	-- depth:2
[46]=21,	-- depth:1
[84]=109,	-- depth:9
[82]=232,	-- depth:3
[36]=186,	-- depth:2
[50]=300,	-- depth:9
[80]=230,	-- depth:8
[55]=80,	-- depth:9
[78]=203,	-- depth:8
[57]=207,	-- depth:4
[59]=84,	-- depth:10
[107]=257,	-- depth:3
[75]=50,	-- depth:10
[61]=211,	-- depth:2
[63]=88,	-- depth:11
[65]=40,	-- depth:12
[71]=21,	-- depth:1
[53]=78,	-- depth:9
[92]=17,	-- depth:1
[69]=44,	-- depth:10
[34]=59,	-- depth:11
[105]=55,	-- depth:10
[103]=53,	-- depth:10
[100]=75,	-- depth:11
[28]=103,	-- depth:11
[96]=21,	-- depth:1
[30]=105,	-- depth:11
[32]=107,	-- depth:4
[94]=69,	-- depth:11
[248]=23,	-- depth:1
[173]=248,	-- depth:2
[273]=173,	-- depth:3
[198]=273,	-- depth:4
[223]=198,	-- depth:5
[123]=223,	-- depth:6
[73]=123,	-- depth:7
[48]=73,	-- depth:8
[98]=48,	-- depth:9
[148]=98,	-- depth:10
[298]=148,	-- depth:11
[4]=16,	-- depth:1
[22]=16,	-- depth:1
[10]=16,	-- depth:1
[35]=10,	-- depth:2
[297]=22,	-- depth:2
[191]=16,	-- depth:1
[66]=191,	-- depth:2
[60]=35,	-- depth:3
[291]=66,	-- depth:3
[197]=297,	-- depth:3
[29]=4,	-- depth:2
[54]=29,	-- depth:3
[210]=60,	-- depth:4
[279]=54,	-- depth:4
[260]=210,	-- depth:5
[216]=291,	-- depth:4
[254]=279,	-- depth:5
[222]=197,	-- depth:4
[47]=222,	-- depth:5
[229]=254,	-- depth:6
[272]=47,	-- depth:6
[235]=260,	-- depth:6
[266]=216,	-- depth:5
[247]=272,	-- depth:7
[241]=266,	-- depth:6
[285]=235,	-- depth:7
[41]=241,	-- depth:7
[204]=229,	-- depth:7
[79]=204,	-- depth:8
[97]=247,	-- depth:8
[185]=285,	-- depth:8
[122]=97,	-- depth:9
[166]=41,	-- depth:8
[154]=79,	-- depth:9
[85]=185,	-- depth:9
[135]=85,	-- depth:10
[129]=154,	-- depth:10
[91]=166,	-- depth:9
[116]=91,	-- depth:10
[172]=122,	-- depth:10
[160]=135,	-- depth:11
[72]=172,	-- depth:11
[141]=116,	-- depth:11
[110]=160,	-- depth:12
[104]=129,	-- depth:11
[147]=72,	-- depth:12
[179]=104,	-- depth:12
[24]=16,	-- depth:1
[2]=16,	-- depth:1
[12]=2,	-- depth:2
[18]=24,	-- depth:2
[6]=24,	-- depth:2
[8]=2,	-- depth:2
[93]=18,	-- depth:3
[37]=12,	-- depth:3
[33]=8,	-- depth:3
[137]=37,	-- depth:4
[258]=33,	-- depth:4
[252]=2,	-- depth:2
[133]=258,	-- depth:5
[131]=6,	-- depth:3
[31]=131,	-- depth:4
[256]=31,	-- depth:5
[249]=24,	-- depth:2
[68]=93,	-- depth:4
[127]=252,	-- depth:3
[106]=256,	-- depth:6
[112]=137,	-- depth:5
[293]=68,	-- depth:5
[287]=112,	-- depth:6
[14]=16,	-- depth:1
[283]=133,	-- depth:6
[102]=127,	-- depth:4
[262]=287,	-- depth:7
[281]=106,	-- depth:7
[20]=16,	-- depth:1
[277]=102,	-- depth:5
[99]=249,	-- depth:3
[274]=99,	-- depth:4
[124]=274,	-- depth:5
[27]=277,	-- depth:6
[268]=293,	-- depth:6
[118]=268,	-- depth:7
[143]=118,	-- depth:8
[243]=143,	-- depth:9
[87]=262,	-- depth:8
[81]=281,	-- depth:8
[162]=87,	-- depth:9
[212]=162,	-- depth:10
[56]=81,	-- depth:9
[168]=243,	-- depth:10
[208]=283,	-- depth:7
[58]=208,	-- depth:8
[206]=56,	-- depth:10
[77]=27,	-- depth:7
[202]=77,	-- depth:8
[174]=124,	-- depth:6
[62]=212,	-- depth:11
[199]=174,	-- depth:7
[74]=199,	-- depth:8
[177]=202,	-- depth:9
[193]=168,	-- depth:11
[181]=206,	-- depth:11
[183]=58,	-- depth:9
[187]=62,	-- depth:12
[218]=193,	-- depth:12
[52]=177,	-- depth:10
[108]=183,	-- depth:10
[299]=74,	-- depth:9
[237]=187,	-- depth:13
[43]=218,	-- depth:13
[149]=299,	-- depth:10
[233]=108,	-- depth:11
[231]=181,	-- depth:12
[152]=52,	-- depth:11
[156]=231,	-- depth:13
[227]=152,	-- depth:12
[224]=149,	-- depth:11
[83]=233,	-- depth:12
[158]=83,	-- depth:13
[49]=224,	-- depth:12
[70]=20,	-- depth:2
[195]=70,	-- depth:3
[295]=195,	-- depth:4
[189]=14,	-- depth:2
[89]=189,	-- depth:3
[239]=89,	-- depth:4
[245]=295,	-- depth:5
[64]=239,	-- depth:5
[39]=64,	-- depth:6
[289]=39,	-- depth:7
[264]=289,	-- depth:8
[139]=264,	-- depth:9
[220]=245,	-- depth:6
[170]=220,	-- depth:7
[45]=170,	-- depth:8
[120]=45,	-- depth:9
[214]=139,	-- depth:10
[145]=120,	-- depth:10
[270]=145,	-- depth:11
[95]=270,	-- depth:12
[114]=214,	-- depth:11
[164]=114,	-- depth:12
},
interface={
{},
{interface=1,},
{interface=2,},
{interface=3,},
{interface=4,},
{interface=5,},
{interface=6,},
{interface=7,},
{interface=8,},
{interface=9,},
{interface=10,},
{interface=11,}
},

interface_meta_table_map={
},
other_default_table={juanxian_stuff_item_id=29974,juanxian_stuff_item_num=1,zhenlong_rate1=1,zhenlong_rate2=2,},

config_param_default_table={start_server_day=15,end_server_day=22,grade=0,open_level=100,if_open=0,},

oa_qfjx_role_jx_reward_default_table={grade=0,reward_id=1,reward_item={[0]=item_table[1]},add_exp=0,},

oa_qfjx_role_lj_reward_default_table={grade=0,index=1,juanxian_num=10,reward_item={[0]=item_table[1]},chuanwen=0,},

oa_qfjx_server_day_reward_default_table={grade=0,index=1,server_day_juanxian_num=100,reward_type=2,icon_type=1,param1=26000,param2=1,param3=0,param4=0,param5=0,param6=0,name="道具奖励",icon="jx_bx",desc="仙遗洞天重生",},

interface_default_table={interface=0,jx_xuanchuantu="operatipn_juanxian_xuanchuan_2",jx_xuanchuantu_line="operatipn_juanxian_line_0",biaoyu_text="全服捐献次数：",text_bg="operatipn_juanxian_text_bg_0",bg_left_person="operatipn_juanxian_person_0",reward_bg="day_first_kuang",slider_bg="operatipn_juanxian_pro_bg_0",slider_value_bg="operatipn_juanxian_pro_0",one_slider_bg="operatipn_juanxian_pro_shusong_bg_0",one_slider_fill="operatipn_juanxian_pro_shusong_0",cell_bg="operatipn_juanxian_pro_tubiao_bg_0",jx_xuanchuantu_bg="operation_juanxian_bg_0",tip_label="每捐献一次，可获得随机大礼！",rule_1="1.捐献活动材料增加全服的捐献进度，进度达到一定数量时获得奖励\n2.材料通过日常副本、活动等获得",jx_my_own="我的捐献次数：<color=#009621>%s</color>（活动期间不重置）",}

}

