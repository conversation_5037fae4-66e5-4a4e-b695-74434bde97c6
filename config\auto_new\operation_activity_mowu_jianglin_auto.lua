-- Y-运营活动-魔物降临.xls
local item_table={
[1]={item_id=46555,num=1,is_bind=1},
[2]={item_id=46560,num=1,is_bind=1},
[3]={item_id=46559,num=1,is_bind=1},
[4]={item_id=26191,num=1,is_bind=1},
[5]={item_id=27613,num=1,is_bind=1},
[6]={item_id=27814,num=1,is_bind=1},
[7]={item_id=46513,num=1,is_bind=1},
[8]={item_id=27612,num=1,is_bind=1},
[9]={item_id=44070,num=1,is_bind=1},
[10]={item_id=44071,num=2,is_bind=1},
[11]={item_id=26502,num=1,is_bind=0},
}

return {
config_param={
{grade=0,},
{start_server_day=10,end_server_day=20,},
{start_server_day=20,end_server_day=27,grade=1,interface=1,},
{start_server_day=27,end_server_day=34,grade=2,interface=2,},
{start_server_day=34,end_server_day=41,grade=3,interface=3,},
{start_server_day=41,end_server_day=48,grade=4,interface=4,},
{start_server_day=48,end_server_day=55,grade=5,interface=5,},
{start_server_day=55,end_server_day=62,grade=6,interface=6,},
{start_server_day=62,end_server_day=69,grade=7,interface=7,},
{start_server_day=69,end_server_day=76,grade=8,interface=8,},
{start_server_day=76,end_server_day=83,grade=9,interface=9,},
{start_server_day=83,end_server_day=90,grade=10,interface=10,},
{start_server_day=90,end_server_day=97,interface=11,},
{start_server_day=97,end_server_day=104,open_act_day="1|2|3|4|5|6|7|8|9|11",},
{start_server_day=104,end_server_day=111,interface=1,open_act_day="1|2|3|4|5|6|7|8|9|12",},
{start_server_day=111,end_server_day=118,interface=2,open_act_day="1|2|3|4|5|6|7|8|9|13",},
{start_server_day=118,end_server_day=125,interface=3,open_act_day="1|2|3|4|5|6|7|8|9|14",},
{start_server_day=125,end_server_day=132,interface=4,open_act_day="1|2|3|4|5|6|7|8|9|15",},
{start_server_day=132,end_server_day=139,interface=5,open_act_day="1|2|3|4|5|6|7|8|9|16",},
{start_server_day=139,end_server_day=146,interface=6,open_act_day="1|2|3|4|5|6|7|8|9|17",},
{start_server_day=146,end_server_day=153,interface=7,open_act_day="1|2|3|4|5|6|7|8|9|18",},
{start_server_day=153,end_server_day=160,interface=8,open_act_day="1|2|3|4|5|6|7|8|9|19",},
{start_server_day=160,end_server_day=167,interface=9,open_act_day="1|2|3|4|5|6|7|8|9|20",},
{start_server_day=167,end_server_day=174,interface=10,open_act_day="1|2|3|4|5|6|7|8|9|21",},
{start_server_day=174,end_server_day=181,interface=11,open_act_day="1|2|3|4|5|6|7|8|9|22",},
{start_server_day=181,end_server_day=188,open_act_day="1|2|3|4|5|6|7|8|9|22",},
{start_server_day=188,end_server_day=999,interface=1,open_act_day="1|2|3|4|5|6|7|8|9|22",}
},

config_param_meta_table_map={
[2]=1,	-- depth:1
},
boss_cfg={
{boss_id=54030,},
{limit_lv=420,boss_id=54031,},
{grade=1,boss_id=54032,},
{limit_lv=450,boss_id=54033,},
{grade=2,boss_id=54034,},
{limit_lv=480,boss_id=54035,},
{grade=3,boss_id=54036,},
{limit_lv=510,boss_id=54037,},
{grade=4,boss_id=54038,},
{limit_lv=540,boss_id=54039,},
{grade=5,boss_id=54040,},
{limit_lv=560,boss_id=54041,},
{grade=6,boss_id=54042,},
{limit_lv=580,boss_id=54043,},
{grade=7,boss_id=54044,},
{limit_lv=600,boss_id=54045,},
{grade=8,boss_id=54046,},
{limit_lv=620,boss_id=54047,},
{grade=9,boss_id=54048,},
{grade=9,limit_lv=640,},
{grade=10,},
{limit_lv=660,},
{grade=11,},
{limit_lv=680,}
},

boss_cfg_meta_table_map={
[22]=21,	-- depth:1
[24]=23,	-- depth:1
[8]=7,	-- depth:1
[14]=13,	-- depth:1
[16]=15,	-- depth:1
[6]=5,	-- depth:1
[18]=17,	-- depth:1
[4]=3,	-- depth:1
[10]=9,	-- depth:1
[12]=11,	-- depth:1
},
refresh_time={
{},
{grade=1,},
{grade=2,},
{grade=3,},
{grade=4,},
{grade=5,},
{grade=6,},
{grade=7,},
{grade=8,},
{grade=9,},
{grade=10,},
{grade=11,}
},

refresh_time_meta_table_map={
},
reward_config={
{},
{min_lv=341,max_lv=350,},
{min_lv=351,max_lv=360,},
{min_lv=361,max_lv=9999,},
{do_times_limit=1,},
{do_times_limit=1,},
{do_times_limit=1,},
{do_times_limit=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{do_times_limit=1,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,}
},

reward_config_meta_table_map={
[29]=5,	-- depth:1
[53]=29,	-- depth:2
[77]=53,	-- depth:3
[37]=77,	-- depth:4
[85]=37,	-- depth:5
[45]=85,	-- depth:6
[21]=45,	-- depth:7
[61]=21,	-- depth:8
[93]=61,	-- depth:9
[13]=93,	-- depth:10
[69]=13,	-- depth:11
[60]=4,	-- depth:1
[44]=60,	-- depth:2
[82]=2,	-- depth:1
[68]=44,	-- depth:3
[67]=3,	-- depth:1
[50]=82,	-- depth:2
[59]=67,	-- depth:2
[51]=59,	-- depth:3
[74]=50,	-- depth:3
[52]=68,	-- depth:4
[75]=51,	-- depth:4
[76]=52,	-- depth:5
[58]=74,	-- depth:4
[66]=58,	-- depth:5
[43]=75,	-- depth:5
[36]=76,	-- depth:6
[83]=43,	-- depth:6
[6]=2,	-- depth:1
[7]=3,	-- depth:1
[8]=4,	-- depth:1
[10]=66,	-- depth:6
[11]=83,	-- depth:7
[12]=36,	-- depth:7
[92]=12,	-- depth:8
[18]=10,	-- depth:7
[19]=11,	-- depth:8
[20]=92,	-- depth:9
[91]=19,	-- depth:9
[42]=18,	-- depth:8
[90]=42,	-- depth:9
[27]=91,	-- depth:10
[28]=20,	-- depth:10
[34]=90,	-- depth:10
[35]=27,	-- depth:11
[84]=28,	-- depth:11
[26]=34,	-- depth:11
[78]=6,	-- depth:2
[79]=7,	-- depth:2
[88]=84,	-- depth:12
[94]=78,	-- depth:3
[87]=79,	-- depth:3
[86]=94,	-- depth:4
[80]=88,	-- depth:13
[72]=80,	-- depth:14
[48]=72,	-- depth:15
[70]=86,	-- depth:5
[14]=70,	-- depth:6
[15]=87,	-- depth:4
[16]=48,	-- depth:16
[22]=14,	-- depth:7
[23]=15,	-- depth:5
[24]=16,	-- depth:17
[30]=22,	-- depth:8
[31]=23,	-- depth:6
[32]=24,	-- depth:18
[38]=30,	-- depth:9
[39]=31,	-- depth:7
[40]=32,	-- depth:19
[46]=38,	-- depth:10
[47]=39,	-- depth:8
[95]=47,	-- depth:9
[54]=46,	-- depth:11
[55]=95,	-- depth:10
[56]=40,	-- depth:20
[62]=54,	-- depth:12
[63]=55,	-- depth:11
[64]=56,	-- depth:21
[71]=63,	-- depth:12
[96]=64,	-- depth:22
},
monster_drop_cfg={
{},
{grade=1,},
{grade=2,},
{grade=3,},
{grade=4,},
{grade=5,},
{grade=6,},
{grade=7,},
{grade=8,},
{grade=9,},
{grade=10,},
{grade=11,},
{grade=12,},
{grade=13,},
{grade=14,}
},

monster_drop_cfg_meta_table_map={
},
boss_drop_cfg={
{},
{grade=1,},
{grade=2,},
{grade=3,},
{grade=4,},
{grade=5,},
{grade=6,},
{grade=7,},
{grade=8,},
{grade=9,},
{grade=10,},
{grade=11,}
},

boss_drop_cfg_meta_table_map={
},
other={
{},
{grade=1,},
{grade=2,},
{grade=3,},
{grade=4,},
{grade=5,},
{grade=6,},
{grade=7,},
{grade=8,},
{grade=9,},
{grade=10,},
{grade=11,}
},

other_meta_table_map={
},
interface={
{},
{interface=1,bg_xuanchuan="mowu_jianglin_xuanchun_1",left_bg="cslc_04_1",biaoti="mowu_jianglin_text_1",right_bg="mowang_left_2",mowu_name_bg="total_left_wenzidi_1",jianglin_title="mowu_jianglin_title_1",list_bg="ctn_item_bg1",},
{interface=2,},
{interface=3,},
{interface=4,},
{interface=5,},
{interface=6,},
{interface=7,},
{interface=8,},
{interface=9,},
{interface=10,},
{interface=11,mowu_name="",}
},

interface_meta_table_map={
[4]=2,	-- depth:1
[6]=4,	-- depth:2
[8]=6,	-- depth:3
[10]=8,	-- depth:4
[12]=2,	-- depth:1
},
monster_drop_dynamic_cfg={
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{}
},

monster_drop_dynamic_cfg_meta_table_map={
},
safe_area={
{}
},

safe_area_meta_table_map={
},
area_effect={
{},
{},
{},
{}
},

area_effect_meta_table_map={
},
config_param_default_table={start_server_day=3,end_server_day=10,week_index=5,grade=11,interface=0,open_level=100,open_act_day="1|2|3|4|5|6|7|8|9|10",},

boss_cfg_default_table={grade=0,scene_id=9622,boss_time=1800,limit_lv=0,boss_id=54049,boss_posx=434,boss_posy=236,render_type=0,render_int_param1=0,model_bundle_name="model/boss/8042_prefab",model_asset_name=8042,whole_display_pos="-91|65",model_rot="0|20|0",model_scale=1.4,},

refresh_time_default_table={grade=0,refresh_time=1800,},

reward_config_default_table={grade=0,min_lv=0,max_lv=340,do_times_limit=0,reward_show={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},last_kill_reward_show={[0]=item_table[4],[1]=item_table[1],[2]=item_table[5],[3]=item_table[6],[4]=item_table[2],[5]=item_table[7],[6]=item_table[8],[7]=item_table[9],[8]=item_table[3],[9]=item_table[10]},},

monster_drop_cfg_default_table={grade=0,random_item={[0]=item_table[11]},},

boss_drop_cfg_default_table={grade=0,random_item={[0]=item_table[4],[1]=item_table[1],[2]=item_table[5],[3]=item_table[6],[4]=item_table[2],[5]=item_table[7],[6]=item_table[8],[7]=item_table[9],[8]=item_table[3],[9]=item_table[10]},},

other_default_table={grade=0,refresh_times=1,boss_time=1800,first_ready_times=180,second_ready_times=60,npc_scene=1003,npc_local_x=380,npc_local_y=270,npcid=10340,player_local_x=412,player_local_y=236,},

interface_default_table={interface=0,bg_xuanchuan="mowu_jianglin_xuanchun_0",model_id=1,left_bg="cslc_04",reward_text_1="参与试炼可获得：",reward_text_2="掉落奖励展示：",biaoti="mowu_jianglin_text_0",jl_title_text="魔王降临时间：今日18：00",right_bg="mowang_left_1",mowu_name="异魔领主",mowu_name_bg="total_left_wenzidi",rule_1="机不可失，一定要准时参加哦！",rule_2="1.活动期间，每日<color=#99ffbb>18:00</color>开启试炼副本，全体玩家可前往参与\n2.活动中对BOSS造成伤害，即可获得<color=#99ffbb>参与奖励</color>，奖励通过<color=#99ffbb>邮件</color>发放\n3.成功击杀BOSS，可获得掉落奖励！",btn_name_1="前往挑战",jianglin_title="mowu_jianglin_title",list_bg="ctn_item_bg",},

monster_drop_dynamic_cfg_default_table={},

safe_area_default_table={posi1="305,240",posi2="305,320",posi3="390,240",posi4="390,320",},

area_effect_default_table={point_x=320,point_y=240,effect_name="anquanshibiequ",}

}

