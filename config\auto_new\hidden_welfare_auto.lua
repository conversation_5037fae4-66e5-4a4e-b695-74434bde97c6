return {
	["daily_default_table"]={type=2,skill_describe="变身状态暴击几率<color=#7cffb7>+14%</color>，暴击增伤<color=#7cffb7>+14%</color>；攻击时有<color=#7cffb7>10%</color>几率晕眩目标<color=#7cffb7>1秒</color>（CD<color=#7cffb7>12秒</color>）",skill="2阶变身：光明剑圣，万剑归一",image_id=1,skill_icon=44,position="-1.04|2.01|6.69",sub_type=1,capability_show=50000,stage=1,dur_times=86400,},
	["conceal_task_default_table"]={param_c=0,condition_type=6,close_panel=0,desc="转职完成2转",open_panel="daily#daily_xsboss",open_level=0,param_a=10,is_transfer=0,
		reward_item={[0]={item_id=32413,num=1,is_bind=1,},},param_b=0,stage=1,task_index=1,},
	["conceal_task"]={
		{condition_type=1,desc="角色等级达到110级",close_panel=1,task_index=0,reward_item={[0]={item_id=32401,num=1,is_bind=1,},},param_a=110,},
		{condition_type=14,open_panel="mount_lingchong#mount_lingchong_lingchong",desc="灵宠进阶至3阶",reward_item={[0]={item_id=32402,num=1,is_bind=1,},},param_a=3,},
		{condition_type=12,open_panel="role_view#role_wing",desc="进阶羽翼至20级",task_index=2,reward_item={[0]={item_id=32403,num=1,is_bind=1,},},param_a=20,},
		{condition_type=9,open_panel="fubenpanel#fubenpanel_material",desc="铭纹阁通关5层",task_index=3,reward_item={[0]={item_id=32404,num=1,is_bind=1,},},param_a=5,},
		{condition_type=2,desc="日常任务完成3次",close_panel=1,task_index=4,reward_item={[0]={item_id=32405,num=1,is_bind=1,},},param_a=3,},
		{open_panel="transfer",desc="转职完成1转",close_panel=1,task_index=5,reward_item={[0]={item_id=32406,num=1,is_bind=1,},},param_a=1,},
		{condition_type=3,open_panel="act_jjc#arena_field1v1",desc="竞技场胜利3次",task_index=0,reward_item={[0]={item_id=32408,num=1,is_bind=1,},},stage=2,param_a=3,},
		{condition_type=8,open_panel="fubenpanel#fubenpanel_exp",desc="参与1次渡劫仙舟",reward_item={[0]={item_id=32409,num=1,is_bind=1,},},stage=2,param_a=1,},
		{condition_type=10,open_panel="role_view#role_fabao",desc="进阶法宝至10级",task_index=2,reward_item={[0]={item_id=32410,num=1,is_bind=1,},},stage=2,},
		{condition_type=11,open_panel="role_view#role_wuqi",desc="进阶神兵至10级",task_index=3,reward_item={[0]={item_id=32411,num=1,is_bind=1,},},stage=2,},
		{open_panel="transfer#transfer_2",open_level=220,close_panel=1,task_index=4,is_transfer=1,reward_item={[0]={item_id=32412,num=1,is_bind=1,},},stage=2,param_a=2,},
		{condition_type=7,open_panel="jingjie",desc="境界提升至渡劫",task_index=5,stage=2,param_a=5,},
		{condition_type=4,open_panel="act_jjc#arena_field1v1",desc="达成竞技场500名以上",task_index=0,reward_item={[0]={item_id=32415,num=1,is_bind=1,},},stage=3,param_a=500,},
		{condition_type=9,open_panel="fubenpanel#fubenpanel_material",desc="铭纹阁通关10层",reward_item={[0]={item_id=32416,num=1,is_bind=1,},},stage=3,},
		{condition_type=13,open_panel="mount_lingchong#mount_lingchong_mount",desc="坐骑进阶至2阶",task_index=2,reward_item={[0]={item_id=32417,num=1,is_bind=1,},},stage=3,param_a=2,},
		{condition_type=15,open_panel="mount_lingchong#mount_lingchong_pkmount",desc="灵骑进阶至20级",task_index=3,reward_item={[0]={item_id=32418,num=1,is_bind=1,},},stage=3,},
		{open_panel="transfer#transfer_3",desc="转职完成3转",open_level=280,close_panel=1,task_index=4,is_transfer=1,reward_item={[0]={item_id=32419,num=1,is_bind=1,},},stage=3,param_a=3,},
		{condition_type=5,open_panel="equipment#equipment_baoshi",desc="全身宝石共达到10级",task_index=5,reward_item={[0]={item_id=32420,num=1,is_bind=1,},},stage=3,},},
	["daily"]={
		{skill_describe="变身状态暴击几率<color=#7cffb7>+12%</color>，暴击增伤<color=#7cffb7>+12%</color>；攻击时有<color=#7cffb7>9%</color>几率晕眩目标<color=#7cffb7>1秒</color>（CD<color=#7cffb7>12秒</color>）",image_id=0,sub_type=2,capability_show=30000,},
		{skill="3阶变身：九天玄女，庇护万物",position="-1|2.3|7",sub_type=3,capability_show=40000,stage=2,},
		{type=3,skill_describe="变身状态暴击几率<color=#7cffb7>+16%</color>，暴击增伤<color=#7cffb7>+16%</color>；攻击时有<color=#7cffb7>13%</color>几率晕眩目标<color=#7cffb7>1秒</color>（CD<color=#7cffb7>12秒</color>）",skill="4阶变身：六壬法扇，万魔皆服",image_id=2,stage=3,},},
}
