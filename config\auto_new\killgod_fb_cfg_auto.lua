-- F-副本-诛神塔.xls
local item_table={
[1]={item_id=90595,num=22000,is_bind=1},
[2]={item_id=90595,num=24000,is_bind=1},
[3]={item_id=90595,num=26000,is_bind=1},
[4]={item_id=90595,num=28100,is_bind=1},
[5]={item_id=90595,num=30100,is_bind=1},
[6]={item_id=90595,num=32200,is_bind=1},
[7]={item_id=90595,num=34200,is_bind=1},
[8]={item_id=90595,num=36300,is_bind=1},
[9]={item_id=90595,num=38400,is_bind=1},
[10]={item_id=90595,num=40500,is_bind=1},
[11]={item_id=90595,num=42600,is_bind=1},
[12]={item_id=90595,num=44700,is_bind=1},
[13]={item_id=90595,num=46800,is_bind=1},
[14]={item_id=90595,num=48900,is_bind=1},
[15]={item_id=90595,num=51100,is_bind=1},
[16]={item_id=90595,num=53200,is_bind=1},
[17]={item_id=90595,num=55400,is_bind=1},
[18]={item_id=90595,num=57600,is_bind=1},
[19]={item_id=90595,num=59800,is_bind=1},
[20]={item_id=90595,num=62000,is_bind=1},
[21]={item_id=90595,num=64200,is_bind=1},
[22]={item_id=90595,num=66400,is_bind=1},
[23]={item_id=90595,num=68600,is_bind=1},
[24]={item_id=90595,num=70900,is_bind=1},
[25]={item_id=90595,num=73100,is_bind=1},
[26]={item_id=90595,num=75400,is_bind=1},
[27]={item_id=90595,num=77700,is_bind=1},
[28]={item_id=90595,num=79900,is_bind=1},
[29]={item_id=90595,num=82200,is_bind=1},
[30]={item_id=90595,num=84600,is_bind=1},
[31]={item_id=90595,num=86900,is_bind=1},
[32]={item_id=90595,num=89200,is_bind=1},
[33]={item_id=90595,num=91600,is_bind=1},
[34]={item_id=90595,num=93900,is_bind=1},
[35]={item_id=90595,num=96300,is_bind=1},
[36]={item_id=90595,num=98700,is_bind=1},
[37]={item_id=90595,num=101100,is_bind=1},
[38]={item_id=90595,num=103500,is_bind=1},
[39]={item_id=90595,num=105900,is_bind=1},
[40]={item_id=90595,num=108300,is_bind=1},
[41]={item_id=90595,num=110800,is_bind=1},
[42]={item_id=90595,num=113200,is_bind=1},
[43]={item_id=90595,num=115700,is_bind=1},
[44]={item_id=90595,num=118200,is_bind=1},
[45]={item_id=90595,num=120600,is_bind=1},
[46]={item_id=90595,num=123200,is_bind=1},
[47]={item_id=90595,num=125700,is_bind=1},
[48]={item_id=90595,num=128200,is_bind=1},
[49]={item_id=90595,num=130700,is_bind=1},
[50]={item_id=90595,num=20000,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
level={
{monster_count=6,monster_id=11717,},
{level=1,name="第2层",boss_count=2,boss_id_1=11645,boss_id_2=11824,com_monster_id=11595,skill_type=2,lingli=22000,lingli_2=15700,lingli_1=11000,fb_des="当击杀其中一只BOSS时，另一只BOSS的防御大幅度提升。",res_id=11645,show_list={[0]=item_table[1]},equip_part=90604,},
{level=2,name="第3层",boss_id_1=11646,com_monster_id=11596,skill_type=3,monster_count=6,monster_id=11718,lingli=24000,lingli_2=17200,lingli_1=12000,fb_des="击杀完BOSS召唤出的小怪,可对BOSS造成大量伤害",res_id=11646,show_list={[0]=item_table[2]},equip_part=90598,},
{level=3,name="第4层",boss_count=2,boss_id_1=11647,boss_id_2=11825,com_monster_id=11597,skill_type=4,lingli=26000,lingli_2=18600,lingli_1=13000,fb_des="30秒内无法击杀剩余BOSS，则死亡的BOSS复活",res_id=11647,monster_scale=1.1,show_list={[0]=item_table[3]},equip_part=90605,},
{level=4,name="第5层",boss_id_1=11648,com_monster_id=11598,skill_type=5,lingli=28100,lingli_2=20000,lingli_1=14000,fb_des="每次BOSS释放技能，BOSS吸收全部伤害并回炉为自身回血",res_id=11648,show_list={[0]=item_table[4]},equip_part=90599,},
{level=5,name="第6层",boss_id_1=11649,com_monster_id=11599,monster_count=6,monster_id=11717,lingli=30100,lingli_2=21500,lingli_1=15100,res_id=11649,show_list={[0]=item_table[5]},equip_part=90603,},
{level=6,name="第7层",boss_count=2,boss_id_1=11650,boss_id_2=11826,com_monster_id=11600,skill_type=2,lingli=32200,lingli_2=23000,lingli_1=16100,fb_des="当击杀其中一只BOSS时，另一只BOSS的防御大幅度提升。",res_id=11650,show_list={[0]=item_table[6]},equip_part=90596,},
{level=7,name="第8层",boss_id_1=11651,com_monster_id=11601,skill_type=3,monster_count=6,monster_id=11718,lingli=34200,lingli_2=24400,lingli_1=17100,fb_des="击杀完BOSS召唤出的小怪,可对BOSS造成大量伤害",res_id=11651,monster_scale=0.5,show_list={[0]=item_table[7]},equip_part=90602,},
{level=8,name="第9层",boss_count=2,boss_id_1=11652,boss_id_2=11827,com_monster_id=11602,skill_type=4,lingli=36300,lingli_2=25900,lingli_1=18100,fb_des="30秒内无法击杀剩余BOSS，则死亡的BOSS复活",res_id=11652,monster_scale=0.8,show_list={[0]=item_table[8]},equip_part=90597,},
{level=9,name="第10层",boss_id_1=11653,com_monster_id=11603,skill_type=5,lingli=38400,lingli_2=27400,lingli_1=19200,fb_des="每次BOSS释放技能，BOSS吸收全部伤害并回炉为自身回血",res_id=11653,show_list={[0]=item_table[9]},equip_part=90601,},
{level=10,name="第11层",boss_id_1=11654,com_monster_id=11604,monster_count=6,monster_id=11717,lingli=40500,lingli_2=28900,lingli_1=20200,res_id=11654,monster_scale=0.6,show_list={[0]=item_table[10]},},
{level=11,name="第12层",boss_count=2,boss_id_1=11655,boss_id_2=11828,com_monster_id=11605,skill_type=2,lingli=42600,lingli_2=30400,lingli_1=21300,fb_des="当击杀其中一只BOSS时，另一只BOSS的防御大幅度提升。",res_id=11655,show_list={[0]=item_table[11]},equip_part=90604,},
{level=12,name="第13层",boss_id_1=11656,com_monster_id=11606,skill_type=3,monster_count=6,monster_id=11718,lingli=44700,lingli_2=31900,lingli_1=22300,fb_des="击杀完BOSS召唤出的小怪,可对BOSS造成大量伤害",res_id=11656,monster_scale=0.6,show_list={[0]=item_table[12]},equip_part=90598,},
{level=13,name="第14层",boss_count=2,boss_id_1=11657,boss_id_2=11829,com_monster_id=11607,skill_type=4,lingli=46800,lingli_2=33400,lingli_1=23400,fb_des="30秒内无法击杀剩余BOSS，则死亡的BOSS复活",res_id=11657,show_list={[0]=item_table[13]},equip_part=90605,},
{level=14,name="第15层",boss_id_1=11658,com_monster_id=11608,skill_type=5,lingli=48900,lingli_2=34900,lingli_1=24500,fb_des="每次BOSS释放技能，BOSS吸收全部伤害并回炉为自身回血",res_id=11658,show_list={[0]=item_table[14]},equip_part=90599,},
{level=15,name="第16层",boss_id_1=11659,com_monster_id=11609,monster_count=6,monster_id=11717,lingli=51100,lingli_2=36500,lingli_1=25500,res_id=11659,show_list={[0]=item_table[15]},equip_part=90603,},
{level=16,name="第17层",boss_count=2,boss_id_1=11660,boss_id_2=11830,com_monster_id=11610,skill_type=2,lingli=53200,lingli_2=38000,lingli_1=26600,fb_des="当击杀其中一只BOSS时，另一只BOSS的防御大幅度提升。",res_id=11660,show_list={[0]=item_table[16]},equip_part=90596,},
{level=17,name="第18层",boss_id_1=11661,com_monster_id=11611,skill_type=3,monster_count=6,monster_id=11718,lingli=55400,lingli_2=39600,lingli_1=27700,fb_des="击杀完BOSS召唤出的小怪,可对BOSS造成大量伤害",res_id=11661,show_list={[0]=item_table[17]},equip_part=90602,},
{level=18,name="第19层",boss_count=2,boss_id_1=11662,boss_id_2=11831,com_monster_id=11612,skill_type=4,lingli=57600,lingli_2=41100,lingli_1=28800,fb_des="30秒内无法击杀剩余BOSS，则死亡的BOSS复活",res_id=11662,show_list={[0]=item_table[18]},equip_part=90597,},
{level=19,name="第20层",boss_id_1=11663,com_monster_id=11613,skill_type=5,lingli=59800,lingli_2=42700,lingli_1=29900,fb_des="每次BOSS释放技能，BOSS吸收全部伤害并回炉为自身回血",res_id=11663,show_list={[0]=item_table[19]},equip_part=90601,},
{level=20,name="第21层",boss_id_1=11664,com_monster_id=11614,monster_count=6,monster_id=11717,lingli=62000,lingli_2=44300,lingli_1=31000,res_id=11664,show_list={[0]=item_table[20]},},
{level=21,name="第22层",boss_count=2,boss_id_1=11665,boss_id_2=11832,com_monster_id=11615,skill_type=2,lingli=64200,lingli_2=45800,lingli_1=32100,fb_des="当击杀其中一只BOSS时，另一只BOSS的防御大幅度提升。",res_id=11665,monster_scale=0.6,show_list={[0]=item_table[21]},equip_part=90604,},
{level=22,name="第23层",boss_id_1=11666,com_monster_id=11616,skill_type=3,monster_count=6,monster_id=11718,lingli=66400,lingli_2=47400,lingli_1=33200,fb_des="击杀完BOSS召唤出的小怪,可对BOSS造成大量伤害",res_id=11666,show_list={[0]=item_table[22]},equip_part=90598,},
{level=23,name="第24层",boss_count=2,boss_id_1=11667,boss_id_2=11833,com_monster_id=11617,skill_type=4,lingli=68600,lingli_2=49000,lingli_1=34300,fb_des="30秒内无法击杀剩余BOSS，则死亡的BOSS复活",res_id=11667,monster_scale=1.1,show_list={[0]=item_table[23]},equip_part=90605,},
{level=24,name="第25层",boss_id_1=11668,com_monster_id=11618,skill_type=5,lingli=70900,lingli_2=50600,lingli_1=35400,fb_des="每次BOSS释放技能，BOSS吸收全部伤害并回炉为自身回血",res_id=11668,show_list={[0]=item_table[24]},equip_part=90599,},
{level=25,name="第26层",boss_id_1=11669,com_monster_id=11619,monster_count=6,monster_id=11717,lingli=73100,lingli_2=52200,lingli_1=36600,res_id=11669,show_list={[0]=item_table[25]},equip_part=90603,},
{level=26,name="第27层",boss_count=2,boss_id_1=11670,boss_id_2=11834,com_monster_id=11620,skill_type=2,lingli=75400,lingli_2=53800,lingli_1=37700,fb_des="当击杀其中一只BOSS时，另一只BOSS的防御大幅度提升。",res_id=11670,monster_scale=0.5,show_list={[0]=item_table[26]},equip_part=90596,},
{level=27,name="第28层",boss_id_1=11671,com_monster_id=11621,skill_type=3,monster_count=6,monster_id=11718,lingli=77700,lingli_2=55500,lingli_1=38800,fb_des="击杀完BOSS召唤出的小怪,可对BOSS造成大量伤害",res_id=11671,show_list={[0]=item_table[27]},equip_part=90602,},
{level=28,name="第29层",boss_count=2,boss_id_1=11672,boss_id_2=11835,com_monster_id=11622,skill_type=4,lingli=79900,lingli_2=57100,lingli_1=40000,fb_des="30秒内无法击杀剩余BOSS，则死亡的BOSS复活",res_id=11672,show_list={[0]=item_table[28]},equip_part=90597,},
{level=29,name="第30层",boss_id_1=11673,com_monster_id=11623,skill_type=5,lingli=82200,lingli_2=58700,lingli_1=41100,fb_des="每次BOSS释放技能，BOSS吸收全部伤害并回炉为自身回血",res_id=11673,show_list={[0]=item_table[29]},equip_part=90601,},
{level=30,name="第31层",boss_id_1=11674,com_monster_id=11624,monster_count=6,monster_id=11717,lingli=84600,lingli_2=60400,lingli_1=42300,res_id=11674,show_list={[0]=item_table[30]},},
{level=31,name="第32层",boss_count=2,boss_id_1=11675,boss_id_2=11836,com_monster_id=11625,skill_type=2,lingli=86900,lingli_2=62100,lingli_1=43400,fb_des="当击杀其中一只BOSS时，另一只BOSS的防御大幅度提升。",res_id=11675,show_list={[0]=item_table[31]},equip_part=90604,},
{level=32,name="第33层",boss_id_1=11676,com_monster_id=11626,skill_type=3,monster_count=6,monster_id=11718,lingli=89200,lingli_2=63700,lingli_1=44600,fb_des="击杀完BOSS召唤出的小怪,可对BOSS造成大量伤害",res_id=11676,show_list={[0]=item_table[32]},equip_part=90598,},
{level=33,name="第34层",boss_count=2,boss_id_1=11677,boss_id_2=11837,com_monster_id=11627,skill_type=4,lingli=91600,lingli_2=65400,lingli_1=45800,fb_des="30秒内无法击杀剩余BOSS，则死亡的BOSS复活",res_id=11677,show_list={[0]=item_table[33]},equip_part=90605,},
{level=34,name="第35层",boss_id_1=11678,com_monster_id=11628,skill_type=5,lingli=93900,lingli_2=67100,lingli_1=47000,fb_des="每次BOSS释放技能，BOSS吸收全部伤害并回炉为自身回血",res_id=11678,show_list={[0]=item_table[34]},equip_part=90599,},
{level=35,name="第36层",boss_id_1=11679,com_monster_id=11629,monster_count=6,monster_id=11717,lingli=96300,lingli_2=68800,lingli_1=48100,res_id=11679,show_list={[0]=item_table[35]},equip_part=90603,},
{level=36,name="第37层",boss_count=2,boss_id_1=11680,boss_id_2=11838,com_monster_id=11630,skill_type=2,lingli=98700,lingli_2=70500,lingli_1=49300,fb_des="当击杀其中一只BOSS时，另一只BOSS的防御大幅度提升。",res_id=11680,show_list={[0]=item_table[36]},equip_part=90596,},
{level=37,name="第38层",boss_id_1=11681,com_monster_id=11631,skill_type=3,monster_count=6,monster_id=11718,lingli=101100,lingli_2=72200,lingli_1=50500,fb_des="击杀完BOSS召唤出的小怪,可对BOSS造成大量伤害",res_id=11681,show_list={[0]=item_table[37]},equip_part=90602,},
{level=38,name="第39层",boss_count=2,boss_id_1=11682,boss_id_2=11839,com_monster_id=11632,skill_type=4,lingli=103500,lingli_2=73900,lingli_1=51700,fb_des="30秒内无法击杀剩余BOSS，则死亡的BOSS复活",res_id=11682,show_list={[0]=item_table[38]},equip_part=90597,},
{level=39,name="第40层",boss_id_1=11683,com_monster_id=11633,skill_type=5,lingli=105900,lingli_2=75600,lingli_1=52900,fb_des="每次BOSS释放技能，BOSS吸收全部伤害并回炉为自身回血",res_id=11683,show_list={[0]=item_table[39]},equip_part=90601,},
{level=40,name="第41层",boss_id_1=11684,com_monster_id=11634,monster_count=6,monster_id=11717,lingli=108300,lingli_2=77400,lingli_1=54200,res_id=11684,show_list={[0]=item_table[40]},},
{level=41,name="第42层",boss_count=2,boss_id_1=11685,boss_id_2=11840,com_monster_id=11635,skill_type=2,lingli=110800,lingli_2=79100,lingli_1=55400,fb_des="当击杀其中一只BOSS时，另一只BOSS的防御大幅度提升。",res_id=11685,monster_scale=0.6,show_list={[0]=item_table[41]},equip_part=90604,},
{level=42,name="第43层",boss_id_1=11686,com_monster_id=11636,skill_type=3,monster_count=6,monster_id=11718,lingli=113200,lingli_2=80900,lingli_1=56600,fb_des="击杀完BOSS召唤出的小怪,可对BOSS造成大量伤害",res_id=11686,show_list={[0]=item_table[42]},equip_part=90598,},
{level=43,name="第44层",boss_count=2,boss_id_1=11687,boss_id_2=11841,com_monster_id=11637,skill_type=4,lingli=115700,lingli_2=82600,lingli_1=57800,fb_des="30秒内无法击杀剩余BOSS，则死亡的BOSS复活",res_id=11687,monster_scale=1.1,show_list={[0]=item_table[43]},equip_part=90605,},
{level=44,name="第45层",boss_id_1=11688,com_monster_id=11638,skill_type=5,lingli=118200,lingli_2=84400,lingli_1=59100,fb_des="每次BOSS释放技能，BOSS吸收全部伤害并回炉为自身回血",res_id=11688,show_list={[0]=item_table[44]},equip_part=90599,},
{level=45,name="第46层",boss_id_1=11689,com_monster_id=11639,monster_count=6,monster_id=11717,lingli=120600,lingli_2=86200,lingli_1=60300,res_id=11689,show_list={[0]=item_table[45]},equip_part=90603,},
{level=46,name="第47层",boss_count=2,boss_id_1=11690,boss_id_2=11842,com_monster_id=11640,skill_type=2,lingli=123200,lingli_2=88000,lingli_1=61600,fb_des="当击杀其中一只BOSS时，另一只BOSS的防御大幅度提升。",res_id=11690,monster_scale=0.5,show_list={[0]=item_table[46]},equip_part=90596,},
{level=47,name="第48层",boss_id_1=11691,com_monster_id=11641,skill_type=3,monster_count=6,monster_id=11718,lingli=125700,lingli_2=89800,lingli_1=62800,fb_des="击杀完BOSS召唤出的小怪,可对BOSS造成大量伤害",res_id=11691,show_list={[0]=item_table[47]},equip_part=90602,},
{level=48,name="第49层",boss_count=2,boss_id_1=11692,boss_id_2=11843,com_monster_id=11642,skill_type=4,lingli=128200,lingli_2=91600,lingli_1=64100,fb_des="30秒内无法击杀剩余BOSS，则死亡的BOSS复活",res_id=11692,show_list={[0]=item_table[48]},equip_part=90597,},
{level=49,name="第50层",boss_id_1=11693,com_monster_id=11643,skill_type=5,lingli=130700,lingli_2=93400,lingli_1=65400,fb_des="每次BOSS释放技能，BOSS吸收全部伤害并回炉为自身回血",res_id=11693,show_list={[0]=item_table[49]},equip_part=90601,}
},

level_meta_table_map={
},
boss_add_hurt={
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{}
},

boss_add_hurt_meta_table_map={
},
other_default_table={open_level=420,everyday_times=2,prepare_time_s=7,fb_time_s=1200,three_star_time_s=180,three_star_Prob=140,two_star_time_s=300,two_star_Prob=100,one_star_time_s=300,one_star_Prob=80,zero_star_Prob=50,kick_time_s=10,enter_next_layer_times=5,honor_value=800,help_times=20,cengshu_max=50,},

level_default_table={level=0,name="第1层",scene_id=6142,boss_count=1,boss_id_1=11644,boss_id_2=0,com_monster_id=11594,com_monster_num=12,skill_type=1,monster_count=0,monster_id=0,lingli=20000,lingli_2=14300,lingli_1=10000,lingli_0=0,reward_item={},fb_des="BOSS血量低于30%时会在周围出现6个回血小怪",res_id=11644,monster_scale=0.7,show_list={[0]=item_table[50]},equip_part=90600,},

boss_add_hurt_default_table={}

}

