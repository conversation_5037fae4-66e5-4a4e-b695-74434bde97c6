-- Y-永夜幻都.xls
local item_table={
[1]={item_id=57991,num=13,is_bind=1},
[2]={item_id=27741,num=30,is_bind=1},
[3]={item_id=26346,num=50,is_bind=1},
[4]={item_id=57991,num=10,is_bind=1},
[5]={item_id=27741,num=20,is_bind=1},
[6]={item_id=57991,num=8,is_bind=1},
[7]={item_id=27741,num=15,is_bind=1},
[8]={item_id=26346,num=45,is_bind=1},
[9]={item_id=57991,num=5,is_bind=1},
[10]={item_id=27741,num=10,is_bind=1},
[11]={item_id=26346,num=40,is_bind=1},
[12]={item_id=39986,num=1,is_bind=1},
[13]={item_id=26129,num=4,is_bind=1},
[14]={item_id=26200,num=2,is_bind=1},
[15]={item_id=26346,num=8,is_bind=1},
[16]={item_id=26200,num=3,is_bind=1},
[17]={item_id=26346,num=12,is_bind=1},
[18]={item_id=26356,num=1,is_bind=1},
[19]={item_id=26346,num=16,is_bind=1},
[20]={item_id=26355,num=1,is_bind=1},
[21]={item_id=26129,num=5,is_bind=1},
[22]={item_id=26346,num=20,is_bind=1},
[23]={item_id=26347,num=10,is_bind=1},
[24]={item_id=26356,num=2,is_bind=1},
[25]={item_id=26355,num=2,is_bind=1},
[26]={item_id=50425,num=2,is_bind=1},
[27]={item_id=26347,num=15,is_bind=1},
[28]={item_id=50425,num=3,is_bind=1},
[29]={item_id=26347,num=20,is_bind=1},
[30]={item_id=26356,num=3,is_bind=1},
[31]={item_id=26355,num=3,is_bind=1},
[32]={item_id=50425,num=5,is_bind=1},
[33]={item_id=26129,num=9,is_bind=1},
[34]={item_id=26348,num=5,is_bind=1},
[35]={item_id=26357,num=2,is_bind=1},
[36]={item_id=26356,num=5,is_bind=1},
[37]={item_id=26355,num=5,is_bind=1},
[38]={item_id=26214,num=1,is_bind=1},
[39]={item_id=26129,num=6,is_bind=1},
[40]={item_id=57991,num=15,is_bind=1},
[41]={item_id=27741,num=40,is_bind=1},
[42]={item_id=26200,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
rank_reward_list={
[1]={show_id=1,},
[2]={show_id=2,min_pos=2,max_pos=3,item_id={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},},
["57991:15:1,"]={show_id="57991:15:1,",min_pos=4,max_pos=5,item_id={[0]=item_table[4],[1]=item_table[5],[2]=item_table[3]},},
[4]={show_id=4,min_pos=6,max_pos=10,item_id={[0]=item_table[6],[1]=item_table[7],[2]=item_table[8]},},
[5]={show_id=5,min_pos=11,max_pos=9999,item_id={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11]},}
},

rank_reward_list_meta_table_map={
},
activity_time_cfg={
{}
},

activity_time_cfg_meta_table_map={
},
join_limit={
{}
},

join_limit_meta_table_map={
},
layer_list={
{},
{layer=1,scene_id=7011,up_layer_need_kills=2,reward={[0]=item_table[12],[1]=item_table[13],[2]=item_table[14],[3]=item_table[15]},},
{layer=2,scene_id=7012,up_layer_need_kills=3,reward={[0]=item_table[12],[1]=item_table[13],[2]=item_table[16],[3]=item_table[17]},},
{layer=3,scene_id=7013,up_layer_need_kills=4,reward={[0]=item_table[12],[1]=item_table[13],[2]=item_table[18],[3]=item_table[19],[4]=item_table[20]},},
{layer=4,scene_id=7014,up_layer_need_kills=5,reward={[0]=item_table[12],[1]=item_table[21],[2]=item_table[18],[3]=item_table[22],[4]=item_table[20]},},
{layer=5,scene_id=7015,up_layer_need_kills=6,reward={[0]=item_table[12],[1]=item_table[21],[2]=item_table[23],[3]=item_table[24],[4]=item_table[25]},},
{layer=6,scene_id=7016,up_layer_need_kills=7,reward={[0]=item_table[26],[1]=item_table[21],[2]=item_table[27],[3]=item_table[24],[4]=item_table[25]},},
{layer=7,scene_id=7017,up_layer_need_kills=8,reward={[0]=item_table[28],[1]=item_table[21],[2]=item_table[29],[3]=item_table[30],[4]=item_table[31]},},
{layer=8,scene_id=7018,up_layer_need_kills=9,reward={[0]=item_table[32],[1]=item_table[33],[2]=item_table[34],[3]=item_table[35],[4]=item_table[36],[5]=item_table[37]},},
{layer=9,scene_id=7019,up_layer_need_kills=10,reward={},}
},

layer_list_meta_table_map={
},
layer_reward_list={
{},
{},
{},
{},
{},
{},
{},
{},
{}
},

layer_reward_list_meta_table_map={
},
dur_dead_buff_list={
{},
{},
{},
{},
{}
},

dur_dead_buff_list_meta_table_map={
},
dur_kill_buff_list={
{},
{},
{},
{}
},

dur_kill_buff_list_meta_table_map={
},
first_reward={
{},
{},
{},
{},
{},
{},
{},
{},
{}
},

first_reward_meta_table_map={
},
boss_cfg={
{}
},

boss_cfg_meta_table_map={
},
title_reward={
{},
{rank=2,title_id=4006,item_id=32495,},
{rank=3,title_id=4007,item_id=32496,}
},

title_reward_meta_table_map={
},
robot_cfg={
{}
},

robot_cfg_meta_table_map={
},
other_default_table={fuhuo_count=2,yb_guwu_cost=20,buy_fuhuo_cost=30,add_guwu=50,delay_kick_out_time=5,delay_next_layer=4,show_reward={[0]=item_table[38],[1]=item_table[39],[2]=item_table[34],[3]=item_table[35],[4]=item_table[36],[5]=item_table[37]},shop_desc="折扣多多，任君选购",},

rank_reward_list_default_table={show_id=1,min_pos=1,max_pos=1,item_id={[0]=item_table[40],[1]=item_table[41],[2]=item_table[3]},},

activity_time_cfg_default_table={},

join_limit_default_table={openday_limit=10,},

layer_list_default_table={layer=0,scene_id=7010,up_layer_need_kills=1,reward={[0]=item_table[12],[1]=item_table[13],[2]=item_table[42]},},

layer_reward_list_default_table={},

dur_dead_buff_list_default_table={},

dur_kill_buff_list_default_table={},

first_reward_default_table={},

boss_cfg_default_table={},

title_reward_default_table={rank=1,title_id=4005,item_id=32494,},

robot_cfg_default_table={}

}

