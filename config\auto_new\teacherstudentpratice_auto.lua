-- S-师徒修炼.xls
local item_table={
[1]={item_id=39194,num=1,is_bind=0},
[2]={item_id=28812,num=10,is_bind=1},
[3]={item_id=22010,num=1,is_bind=1},
[4]={item_id=26415,num=10,is_bind=1},
[5]={item_id=48071,num=6,is_bind=1},
[6]={item_id=27860,num=1,is_bind=1},
[7]={item_id=22590,num=1,is_bind=1},
[8]={item_id=26410,num=5,is_bind=1},
[9]={item_id=39195,num=1,is_bind=0},
[10]={item_id=37421,num=1,is_bind=1},
[11]={item_id=22576,num=1,is_bind=1},
[12]={item_id=22012,num=1,is_bind=1},
[13]={item_id=26369,num=1,is_bind=1},
[14]={item_id=26351,num=1,is_bind=1},
[15]={item_id=30425,num=5,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
teacher_loginreward={
[0]={index=0,},
[1]={index=1,login_day=2,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},},
[2]={index=2,login_day=3,reward_item={[0]=item_table[1],[1]=item_table[4],[2]=item_table[3]},},
[3]={index=3,login_day=4,reward_item={[0]=item_table[1],[1]=item_table[5],[2]=item_table[3]},},
[4]={index=4,login_day=5,reward_item={[0]=item_table[1],[1]=item_table[6],[2]=item_table[7]},},
[5]={index=5,login_day=6,reward_item={[0]=item_table[1],[1]=item_table[8],[2]=item_table[7]},},
[6]={index=6,login_day=7,reward_item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[7]},}
},

teacher_loginreward_meta_table_map={
},
student_task={
{},
{taskid=2,task_type=1,reward_item={[0]=item_table[11],[1]=item_table[12]},progress=5,text="完成%s次日月修行",turn_panel_name="fubenpanel#fubenpanel_exp",},
{taskid=3,task_type=8,reward_item={[0]=item_table[11],[1]=item_table[13]},progress=21,text="坐骑达到3阶1星",turn_panel_name="NewAppearanceWGView#new_appearance_upgrade_mount",}
},

student_task_meta_table_map={
},
other_default_table={open_game_day=9999,role_level=2000,dur_day=7,teacher_vip_limit=6,student_vip_min=4,student_vip_max=5,teaching_cost=711,student_task_hour=120,vip_task_login_day=3,vip_exp_reward=150,vip_day_reward=180,title_card_id=38911,title_show_id=5021,title_card_vip_limit=6,tipdes="<color=#fffc00>师傅</color>\n达到<color=#95d12b>vip6</color>即可收徒\n当有玩家发布了<color=#95d12b>拜师请求</color>，即可在<color=#95d12b>右边的栏目</color>中收取徒弟\n玩家也可以主动发起<color=#95d12b>收徒申请</color>，成为师傅后，无论有没有徒弟都可领取对应天数的<color=#95d12b>登录奖励</color>哦\n响应了收徒申请的玩家将显示在<color=#95d12b>师傅页签的拜师申请人列表</color>中，师傅们请注意挑选自己中意的徒弟哦\n收徒后师傅<color=#95d12b>每日</color>可领取灵玉奖励\n<color=#95d12b>协助徒弟</color>完成任务可获得奖励\n<color=#95d12b>徒弟成为V6</color>后可领取专属师徒称号\n<color=#fffc00>徒弟</color>\n<color=#95d12b>vip4-5</color>可申请拜师\n<color=#95d12b>累计登录3天</color>，即可直升V6\n成为<color=#95d12b>V6</color>后即可领取专属师徒称号",notify_acp_student_cd=300,teacher_reward_show_itemid=37421,},

teacher_loginreward_default_table={index=0,login_day=1,reward_item={[0]=item_table[1],[1]=item_table[14],[2]=item_table[3]},},

student_task_default_table={taskid=1,task_type=2,reward_item={[0]=item_table[11],[1]=item_table[15]},progress=30,param_0=0,param_1=0,text="通关九重劫塔%s层",turn_panel_name="fubenpanel#fubenpanel_welkin",}

}

