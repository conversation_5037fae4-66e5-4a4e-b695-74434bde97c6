-- Y-运营活动-限时直购3.xls
local item_table={
[1]={item_id=22753,num=1,is_bind=1},
[2]={item_id=26444,num=1,is_bind=1},
[3]={item_id=26505,num=1,is_bind=1},
[4]={item_id=26520,num=1,is_bind=1},
[5]={item_id=48071,num=1,is_bind=1},
[6]={item_id=48118,num=1,is_bind=1},
[7]={item_id=44180,num=5,is_bind=1},
[8]={item_id=26463,num=1,is_bind=1},
[9]={item_id=26464,num=1,is_bind=1},
[10]={item_id=26530,num=1,is_bind=1},
[11]={item_id=26540,num=1,is_bind=1},
[12]={item_id=22753,num=6,is_bind=1},
[13]={item_id=48441,num=5,is_bind=1},
[14]={item_id=44180,num=100,is_bind=1},
[15]={item_id=26509,num=1,is_bind=1},
[16]={item_id=26524,num=1,is_bind=1},
[17]={item_id=22753,num=3,is_bind=1},
[18]={item_id=48118,num=5,is_bind=1},
[19]={item_id=44180,num=50,is_bind=1},
[20]={item_id=38789,num=1,is_bind=1},
[21]={item_id=26450,num=1,is_bind=1},
[22]={item_id=57803,num=2,is_bind=1},
[23]={item_id=57802,num=8,is_bind=1},
[24]={item_id=57801,num=5,is_bind=1},
[25]={item_id=57805,num=2,is_bind=1},
[26]={item_id=57803,num=5,is_bind=1},
[27]={item_id=57802,num=15,is_bind=1},
[28]={item_id=26462,num=1,is_bind=1},
[29]={item_id=57805,num=5,is_bind=1},
[30]={item_id=57804,num=3,is_bind=1},
[31]={item_id=57809,num=2,is_bind=1},
[32]={item_id=57808,num=8,is_bind=1},
[33]={item_id=57807,num=5,is_bind=1},
[34]={item_id=57811,num=2,is_bind=1},
[35]={item_id=57809,num=5,is_bind=1},
[36]={item_id=57808,num=15,is_bind=1},
[37]={item_id=57811,num=5,is_bind=1},
[38]={item_id=57810,num=3,is_bind=1},
[39]={item_id=57821,num=2,is_bind=1},
[40]={item_id=57820,num=8,is_bind=1},
[41]={item_id=57819,num=5,is_bind=1},
[42]={item_id=57823,num=2,is_bind=1},
[43]={item_id=57821,num=5,is_bind=1},
[44]={item_id=57820,num=15,is_bind=1},
[45]={item_id=57823,num=5,is_bind=1},
[46]={item_id=57822,num=3,is_bind=1},
[47]={item_id=57827,num=2,is_bind=1},
[48]={item_id=57826,num=8,is_bind=1},
[49]={item_id=57825,num=5,is_bind=1},
[50]={item_id=57829,num=2,is_bind=1},
[51]={item_id=57827,num=5,is_bind=1},
[52]={item_id=57826,num=15,is_bind=1},
[53]={item_id=57829,num=5,is_bind=1},
[54]={item_id=57828,num=3,is_bind=1},
}

return {
open_day={
{},
{start_day=13,end_day=9999,grade=2,}
},

open_day_meta_table_map={
},
rmb_buy={
{reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7]},name="试炼令牌",show_icon_ground=1,label_name="等级",},
{seq=1,rmb_seq=1101,rmb_price=688,show_item=item_table[8],show_icon_ground=2,},
{seq=2,rmb_seq=1102,rmb_price=1288,reward_item={[0]=item_table[9],[1]=item_table[9],[2]=item_table[10],[3]=item_table[11],[4]=item_table[12],[5]=item_table[13],[6]=item_table[14]},show_item=item_table[9],name="紫云升仙丹",},
{seq=3,rmb_seq=1103,rmb_price=980,discount=5,is_all_buy=1,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7],[7]=item_table[8],[8]=item_table[8],[9]=item_table[15],[10]=item_table[16],[11]=item_table[17],[12]=item_table[18],[13]=item_table[19],[14]=item_table[9],[15]=item_table[9],[16]=item_table[10],[17]=item_table[11],[18]=item_table[12],[19]=item_table[13],[20]=item_table[14]},name=0,show_icon_ground="",label_name="",},
{activity_day=2,rmb_seq=1200,reward_item={[0]=item_table[20],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7]},show_item=item_table[20],name="可爱喵娘",},
{activity_day=2,rmb_seq=1201,show_icon_ground=4,},
{activity_day=2,rmb_seq=1202,show_icon_ground=1,},
{activity_day=2,rmb_seq=1203,},
{activity_day=3,rmb_seq=1300,show_icon_ground=2,},
{activity_day=3,seq=1,rmb_seq=1301,rmb_price=688,show_item=item_table[8],},
{activity_day=3,rmb_seq=1302,},
{activity_day=3,rmb_seq=1303,},
{activity_day=4,rmb_seq=1400,show_icon_ground=4,},
{activity_day=4,rmb_seq=1401,},
{activity_day=4,rmb_seq=1402,show_icon_ground=4,},
{activity_day=4,rmb_seq=1403,reward_item={[0]=item_table[20],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7],[7]=item_table[8],[8]=item_table[8],[9]=item_table[15],[10]=item_table[16],[11]=item_table[17],[12]=item_table[18],[13]=item_table[19],[14]=item_table[9],[15]=item_table[9],[16]=item_table[10],[17]=item_table[11],[18]=item_table[12],[19]=item_table[13],[20]=item_table[14]},show_item=item_table[20],},
{grade=2,rmb_seq=2100,reward_item={[0]=item_table[1],[1]=item_table[21],[2]=item_table[3],[3]=item_table[4],[4]=item_table[22],[5]=item_table[23],[6]=item_table[24]},show_icon_ground=4,},
{grade=2,seq=1,rmb_seq=2101,rmb_price=688,reward_item={[0]=item_table[8],[1]=item_table[8],[2]=item_table[15],[3]=item_table[16],[4]=item_table[25],[5]=item_table[26],[6]=item_table[27]},show_item=item_table[8],show_icon_ground=5,label_name="冲榜",},
{grade=2,rmb_seq=2102,reward_item={[0]=item_table[9],[1]=item_table[28],[2]=item_table[10],[3]=item_table[11],[4]=item_table[29],[5]=item_table[30],[6]=item_table[26]},show_icon_ground=5,label_name="冲榜",},
{grade=2,rmb_seq=2103,reward_item={[0]=item_table[1],[1]=item_table[21],[2]=item_table[3],[3]=item_table[4],[4]=item_table[22],[5]=item_table[23],[6]=item_table[24],[7]=item_table[8],[8]=item_table[8],[9]=item_table[15],[10]=item_table[16],[11]=item_table[25],[12]=item_table[26],[13]=item_table[27],[14]=item_table[9],[15]=item_table[28],[16]=item_table[10],[17]=item_table[11],[18]=item_table[29],[19]=item_table[30],[20]=item_table[26]},},
{grade=2,activity_day=2,rmb_seq=2200,reward_item={[0]=item_table[1],[1]=item_table[21],[2]=item_table[3],[3]=item_table[4],[4]=item_table[31],[5]=item_table[32],[6]=item_table[33]},name="试炼令牌",label_name="等级",},
{activity_day=2,rmb_seq=2201,reward_item={[0]=item_table[8],[1]=item_table[8],[2]=item_table[15],[3]=item_table[16],[4]=item_table[34],[5]=item_table[35],[6]=item_table[36]},show_icon_ground=4,},
{activity_day=2,rmb_seq=2202,reward_item={[0]=item_table[9],[1]=item_table[28],[2]=item_table[10],[3]=item_table[11],[4]=item_table[37],[5]=item_table[38],[6]=item_table[35]},show_icon_ground=4,},
{activity_day=2,rmb_seq=2203,reward_item={[0]=item_table[1],[1]=item_table[21],[2]=item_table[3],[3]=item_table[4],[4]=item_table[31],[5]=item_table[32],[6]=item_table[33],[7]=item_table[8],[8]=item_table[8],[9]=item_table[15],[10]=item_table[16],[11]=item_table[34],[12]=item_table[35],[13]=item_table[36],[14]=item_table[9],[15]=item_table[28],[16]=item_table[10],[17]=item_table[11],[18]=item_table[37],[19]=item_table[38],[20]=item_table[35]},},
{activity_day=3,rmb_seq=2300,reward_item={[0]=item_table[1],[1]=item_table[21],[2]=item_table[3],[3]=item_table[4],[4]=item_table[39],[5]=item_table[40],[6]=item_table[41]},show_icon_ground=5,},
{activity_day=3,rmb_seq=2301,reward_item={[0]=item_table[8],[1]=item_table[8],[2]=item_table[15],[3]=item_table[16],[4]=item_table[42],[5]=item_table[43],[6]=item_table[44]},},
{activity_day=3,rmb_seq=2302,reward_item={[0]=item_table[9],[1]=item_table[28],[2]=item_table[10],[3]=item_table[11],[4]=item_table[45],[5]=item_table[46],[6]=item_table[43]},show_icon_ground=1,},
{activity_day=3,rmb_seq=2303,reward_item={[0]=item_table[1],[1]=item_table[21],[2]=item_table[3],[3]=item_table[4],[4]=item_table[39],[5]=item_table[40],[6]=item_table[41],[7]=item_table[8],[8]=item_table[8],[9]=item_table[15],[10]=item_table[16],[11]=item_table[42],[12]=item_table[43],[13]=item_table[44],[14]=item_table[9],[15]=item_table[28],[16]=item_table[10],[17]=item_table[11],[18]=item_table[45],[19]=item_table[46],[20]=item_table[43]},},
{activity_day=4,rmb_seq=2400,reward_item={[0]=item_table[1],[1]=item_table[21],[2]=item_table[3],[3]=item_table[4],[4]=item_table[47],[5]=item_table[48],[6]=item_table[49]},show_icon_ground=2,},
{grade=2,rmb_seq=2401,reward_item={[0]=item_table[8],[1]=item_table[8],[2]=item_table[15],[3]=item_table[16],[4]=item_table[50],[5]=item_table[51],[6]=item_table[52]},label_name="冲榜",},
{grade=2,activity_day=4,rmb_seq=2402,reward_item={[0]=item_table[9],[1]=item_table[28],[2]=item_table[10],[3]=item_table[11],[4]=item_table[53],[5]=item_table[54],[6]=item_table[51]},label_name="冲榜",},
{activity_day=4,rmb_seq=2403,reward_item={[0]=item_table[1],[1]=item_table[21],[2]=item_table[3],[3]=item_table[4],[4]=item_table[47],[5]=item_table[48],[6]=item_table[49],[7]=item_table[8],[8]=item_table[8],[9]=item_table[15],[10]=item_table[16],[11]=item_table[50],[12]=item_table[51],[13]=item_table[52],[14]=item_table[9],[15]=item_table[28],[16]=item_table[10],[17]=item_table[11],[18]=item_table[53],[19]=item_table[54],[20]=item_table[51]},}
},

rmb_buy_meta_table_map={
[14]=10,	-- depth:1
[6]=2,	-- depth:1
[9]=1,	-- depth:1
[13]=5,	-- depth:1
[17]=1,	-- depth:1
[11]=3,	-- depth:1
[25]=21,	-- depth:1
[29]=21,	-- depth:1
[7]=3,	-- depth:1
[30]=14,	-- depth:2
[15]=3,	-- depth:1
[22]=18,	-- depth:1
[31]=3,	-- depth:1
[19]=3,	-- depth:1
[26]=18,	-- depth:1
[23]=19,	-- depth:2
[12]=4,	-- depth:1
[20]=4,	-- depth:1
[27]=19,	-- depth:2
[16]=4,	-- depth:1
[24]=20,	-- depth:2
[8]=16,	-- depth:2
[28]=20,	-- depth:2
[32]=20,	-- depth:2
},
open_day_default_table={start_day=1,end_day=12,grade=1,},

rmb_buy_default_table={grade=1,activity_day=1,seq=0,rmb_type=139,rmb_seq=1100,rmb_price=6,discount="",buy_times=1,is_all_buy=0,reward_item={[0]=item_table[8],[1]=item_table[8],[2]=item_table[15],[3]=item_table[16],[4]=item_table[17],[5]=item_table[18],[6]=item_table[19]},show_item=item_table[1],name="火龙帝灵丹",show_icon_ground=3,label_name="星辰",}

}

