-- Y-运营活动-系统冲榜.xls
local item_table={
[1]={item_id=38654,num=1,is_bind=1},
[2]={item_id=26464,num=1,is_bind=1},
[3]={item_id=26463,num=1,is_bind=1},
[4]={item_id=26509,num=1,is_bind=1},
[5]={item_id=31259,num=3,is_bind=1},
[6]={item_id=31256,num=30,is_bind=1},
[7]={item_id=31258,num=30,is_bind=1},
[8]={item_id=26461,num=1,is_bind=1},
[9]={item_id=26508,num=1,is_bind=1},
[10]={item_id=31259,num=2,is_bind=1},
[11]={item_id=31256,num=25,is_bind=1},
[12]={item_id=31258,num=25,is_bind=1},
[13]={item_id=26459,num=1,is_bind=1},
[14]={item_id=26507,num=1,is_bind=1},
[15]={item_id=31259,num=1,is_bind=1},
[16]={item_id=31256,num=20,is_bind=1},
[17]={item_id=31258,num=20,is_bind=1},
[18]={item_id=26455,num=1,is_bind=1},
[19]={item_id=26506,num=1,is_bind=1},
[20]={item_id=31256,num=15,is_bind=1},
[21]={item_id=31258,num=15,is_bind=1},
[22]={item_id=37030,num=1,is_bind=1},
[23]={item_id=38748,num=1,is_bind=1},
[24]={item_id=29854,num=2,is_bind=1},
[25]={item_id=29852,num=30,is_bind=1},
[26]={item_id=36980,num=50,is_bind=1},
[27]={item_id=29854,num=1,is_bind=1},
[28]={item_id=29852,num=25,is_bind=1},
[29]={item_id=36980,num=25,is_bind=1},
[30]={item_id=29855,num=1,is_bind=1},
[31]={item_id=29853,num=20,is_bind=1},
[32]={item_id=36981,num=20,is_bind=1},
[33]={item_id=29853,num=15,is_bind=1},
[34]={item_id=36981,num=15,is_bind=1},
[35]={item_id=38749,num=1,is_bind=1},
[36]={item_id=31047,num=2,is_bind=1},
[37]={item_id=31067,num=30,is_bind=1},
[38]={item_id=31060,num=30,is_bind=1},
[39]={item_id=31047,num=1,is_bind=1},
[40]={item_id=31067,num=25,is_bind=1},
[41]={item_id=31060,num=25,is_bind=1},
[42]={item_id=31054,num=1,is_bind=1},
[43]={item_id=31067,num=20,is_bind=1},
[44]={item_id=31060,num=20,is_bind=1},
[45]={item_id=31067,num=15,is_bind=1},
[46]={item_id=31060,num=15,is_bind=1},
[47]={item_id=48456,num=1,is_bind=1},
[48]={item_id=48441,num=5,is_bind=1},
[49]={item_id=50311,num=30,is_bind=1},
[50]={item_id=50395,num=5,is_bind=1},
[51]={item_id=48441,num=3,is_bind=1},
[52]={item_id=50311,num=25,is_bind=1},
[53]={item_id=50395,num=3,is_bind=1},
[54]={item_id=48441,num=2,is_bind=1},
[55]={item_id=50311,num=20,is_bind=1},
[56]={item_id=50395,num=2,is_bind=1},
[57]={item_id=48441,num=1,is_bind=1},
[58]={item_id=50311,num=15,is_bind=1},
[59]={item_id=50395,num=1,is_bind=1},
[60]={item_id=26100,num=1,is_bind=1},
[61]={item_id=48120,num=1,is_bind=1},
[62]={item_id=39991,num=1,is_bind=1},
[63]={item_id=28448,num=20,is_bind=1},
[64]={item_id=28447,num=50,is_bind=1},
[65]={item_id=28446,num=50,is_bind=1},
[66]={item_id=57837,num=30,is_bind=1},
[67]={item_id=48117,num=1,is_bind=1},
[68]={item_id=39990,num=1,is_bind=1},
[69]={item_id=28448,num=15,is_bind=1},
[70]={item_id=28447,num=30,is_bind=1},
[71]={item_id=28446,num=30,is_bind=1},
[72]={item_id=57837,num=20,is_bind=1},
[73]={item_id=48073,num=1,is_bind=1},
[74]={item_id=39989,num=1,is_bind=1},
[75]={item_id=28448,num=10,is_bind=1},
[76]={item_id=28447,num=20,is_bind=1},
[77]={item_id=28446,num=20,is_bind=1},
[78]={item_id=57837,num=10,is_bind=1},
[79]={item_id=39988,num=1,is_bind=1},
[80]={item_id=28448,num=5,is_bind=1},
[81]={item_id=28447,num=10,is_bind=1},
[82]={item_id=28446,num=10,is_bind=1},
[83]={item_id=50312,num=25,is_bind=1},
[84]={item_id=50403,num=3,is_bind=1},
[85]={item_id=50312,num=20,is_bind=1},
[86]={item_id=50403,num=2,is_bind=1},
[87]={item_id=50312,num=15,is_bind=1},
[88]={item_id=50403,num=1,is_bind=1},
[89]={item_id=23300,num=1,is_bind=1},
[90]={item_id=26456,num=20,is_bind=1},
[91]={item_id=26451,num=30,is_bind=1},
[92]={item_id=26447,num=50,is_bind=1},
[93]={item_id=26449,num=50,is_bind=1},
[94]={item_id=26456,num=15,is_bind=1},
[95]={item_id=26451,num=20,is_bind=1},
[96]={item_id=26447,num=30,is_bind=1},
[97]={item_id=26449,num=30,is_bind=1},
[98]={item_id=26456,num=10,is_bind=1},
[99]={item_id=26451,num=15,is_bind=1},
[100]={item_id=26447,num=20,is_bind=1},
[101]={item_id=26449,num=20,is_bind=1},
[102]={item_id=26456,num=5,is_bind=1},
[103]={item_id=26451,num=10,is_bind=1},
[104]={item_id=26447,num=10,is_bind=1},
[105]={item_id=26449,num=10,is_bind=1},
[106]={item_id=30453,num=1,is_bind=1},
[107]={item_id=26457,num=20,is_bind=1},
[108]={item_id=26452,num=30,is_bind=1},
[109]={item_id=26446,num=50,is_bind=1},
[110]={item_id=26448,num=50,is_bind=1},
[111]={item_id=26457,num=15,is_bind=1},
[112]={item_id=26452,num=20,is_bind=1},
[113]={item_id=26446,num=30,is_bind=1},
[114]={item_id=26448,num=30,is_bind=1},
[115]={item_id=26457,num=10,is_bind=1},
[116]={item_id=26452,num=15,is_bind=1},
[117]={item_id=26446,num=20,is_bind=1},
[118]={item_id=26448,num=20,is_bind=1},
[119]={item_id=26457,num=5,is_bind=1},
[120]={item_id=26452,num=10,is_bind=1},
[121]={item_id=26446,num=10,is_bind=1},
[122]={item_id=26448,num=10,is_bind=1},
[123]={item_id=50312,num=30,is_bind=1},
[124]={item_id=50403,num=5,is_bind=1},
}

return {
other={
{},
{type=8,bottom_name1="武魂直购",open_panel1="TianShenPurchaseView",act_type1=2283,bottom_name2="贯日长虹",open_panel2="SunRainbowView",act_type2=2330,bottom_name3="荒谷神冢",open_panel3="fubenpanel#fubenpanel_copper",open_panel4="WuHunView",},
{type=9,bottom_name1="幻兽结缘",open_panel1="ControlBeastsContractWGView",act_type1="",bottom_name3="",open_panel3="",open_panel4="ControlBeastsView#beasts_culture",}
},

other_meta_table_map={
},
type={
{},
{type=1,activity_type=2287,rank_type=73,model_show=37030,},
{type=2,activity_type=2288,rank_type=74,model_show=38749,},
{type=3,activity_type=2289,rank_type=75,open_panel="HolyWeapon",},
{type=4,activity_type=2290,rank_type=76,open_panel="DarkWeapon",},
{type=5,activity_type=2295,rank_type=77,},
{type=6,activity_type=2296,rank_type=78,model_show=23350,},
{type=7,activity_type=2297,rank_type=79,},
{type=8,activity_type=2332,rank_type=82,model_show=23300,},
{type=9,activity_type=2333,rank_type=83,model_show=30453,}
},

type_meta_table_map={
},
reward={
{reach_value=1000000,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7]},},
{min_rank=2,max_rank=3,reach_value=500000,reward_item={[0]=item_table[2],[1]=item_table[8],[2]=item_table[9],[3]=item_table[10],[4]=item_table[11],[5]=item_table[12]},},
{min_rank=4,max_rank=10,reach_value=250000,reward_item={[0]=item_table[3],[1]=item_table[13],[2]=item_table[14],[3]=item_table[15],[4]=item_table[16],[5]=item_table[17]},},
{min_rank=11,max_rank=20,reach_value=100000,reward_item={[0]=item_table[8],[1]=item_table[18],[2]=item_table[19],[3]=item_table[15],[4]=item_table[20],[5]=item_table[21]},},
{type=1,reach_value=3000000,reward_item={[0]=item_table[22],[1]=item_table[23],[2]=item_table[2],[3]=item_table[3],[4]=item_table[4],[5]=item_table[24],[6]=item_table[25],[7]=item_table[26]},},
{type=1,reach_value=1500000,reward_item={[0]=item_table[23],[1]=item_table[2],[2]=item_table[8],[3]=item_table[9],[4]=item_table[27],[5]=item_table[28],[6]=item_table[29]},},
{type=1,reach_value=700000,reward_item={[0]=item_table[3],[1]=item_table[13],[2]=item_table[14],[3]=item_table[30],[4]=item_table[31],[5]=item_table[32]},},
{type=1,reach_value=300000,reward_item={[0]=item_table[8],[1]=item_table[18],[2]=item_table[19],[3]=item_table[30],[4]=item_table[33],[5]=item_table[34]},},
{type=2,reach_value=643000,reward_item={[0]=item_table[35],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[36],[5]=item_table[37],[6]=item_table[38]},},
{type=2,reach_value=282000,reward_item={[0]=item_table[2],[1]=item_table[8],[2]=item_table[9],[3]=item_table[39],[4]=item_table[40],[5]=item_table[41]},},
{type=2,reach_value=143000,reward_item={[0]=item_table[3],[1]=item_table[13],[2]=item_table[14],[3]=item_table[42],[4]=item_table[43],[5]=item_table[44]},},
{type=2,reach_value=50000,reward_item={[0]=item_table[8],[1]=item_table[18],[2]=item_table[19],[3]=item_table[42],[4]=item_table[45],[5]=item_table[46]},},
{type=3,reward_item={[0]=item_table[47],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[48],[5]=item_table[49],[6]=item_table[50]},},
{type=3,reward_item={[0]=item_table[2],[1]=item_table[8],[2]=item_table[9],[3]=item_table[51],[4]=item_table[52],[5]=item_table[53]},},
{type=3,reward_item={[0]=item_table[3],[1]=item_table[13],[2]=item_table[14],[3]=item_table[54],[4]=item_table[55],[5]=item_table[56]},},
{type=3,reward_item={[0]=item_table[8],[1]=item_table[18],[2]=item_table[19],[3]=item_table[57],[4]=item_table[58],[5]=item_table[59]},},
{type=4,},
{type=4,},
{type=4,},
{type=4,},
{type=5,},
{type=5,},
{type=5,},
{type=5,},
{type=6,reach_value=9200000,reward_item={[0]=item_table[60],[1]=item_table[61],[2]=item_table[62],[3]=item_table[63],[4]=item_table[64],[5]=item_table[65]},},
{type=6,reach_value=5200000,reward_item={[0]=item_table[66],[1]=item_table[67],[2]=item_table[68],[3]=item_table[69],[4]=item_table[70],[5]=item_table[71]},},
{type=6,reach_value=2264000,reward_item={[0]=item_table[72],[1]=item_table[73],[2]=item_table[74],[3]=item_table[75],[4]=item_table[76],[5]=item_table[77]},},
{type=6,reach_value=700000,reward_item={[0]=item_table[78],[1]=item_table[73],[2]=item_table[79],[3]=item_table[80],[4]=item_table[81],[5]=item_table[82]},},
{type=7,},
{type=7,reach_value=136000,reward_item={[0]=item_table[2],[1]=item_table[8],[2]=item_table[9],[3]=item_table[51],[4]=item_table[83],[5]=item_table[84]},},
{type=7,reach_value=101000,reward_item={[0]=item_table[3],[1]=item_table[13],[2]=item_table[14],[3]=item_table[54],[4]=item_table[85],[5]=item_table[86]},},
{type=7,reach_value=63000,reward_item={[0]=item_table[8],[1]=item_table[18],[2]=item_table[19],[3]=item_table[57],[4]=item_table[87],[5]=item_table[88]},},
{type=8,reach_value=8000000,reward_item={[0]=item_table[89],[1]=item_table[2],[2]=item_table[3],[3]=item_table[90],[4]=item_table[91],[5]=item_table[92],[6]=item_table[93]},},
{type=8,reward_item={[0]=item_table[2],[1]=item_table[8],[2]=item_table[94],[3]=item_table[95],[4]=item_table[96],[5]=item_table[97]},},
{type=8,reach_value=1800000,reward_item={[0]=item_table[3],[1]=item_table[13],[2]=item_table[98],[3]=item_table[99],[4]=item_table[100],[5]=item_table[101]},},
{type=8,reach_value=600000,reward_item={[0]=item_table[8],[1]=item_table[18],[2]=item_table[102],[3]=item_table[103],[4]=item_table[104],[5]=item_table[105]},},
{type=9,reward_item={[0]=item_table[106],[1]=item_table[2],[2]=item_table[3],[3]=item_table[107],[4]=item_table[108],[5]=item_table[109],[6]=item_table[110]},},
{type=9,reach_value=4000000,reward_item={[0]=item_table[2],[1]=item_table[8],[2]=item_table[111],[3]=item_table[112],[4]=item_table[113],[5]=item_table[114]},},
{type=9,reward_item={[0]=item_table[3],[1]=item_table[13],[2]=item_table[115],[3]=item_table[116],[4]=item_table[117],[5]=item_table[118]},},
{type=9,reward_item={[0]=item_table[8],[1]=item_table[18],[2]=item_table[119],[3]=item_table[120],[4]=item_table[121],[5]=item_table[122]},}
},

reward_meta_table_map={
[37]=33,	-- depth:1
[36]=4,	-- depth:1
[38]=2,	-- depth:1
[35]=3,	-- depth:1
[34]=38,	-- depth:2
[26]=2,	-- depth:1
[32]=4,	-- depth:1
[31]=3,	-- depth:1
[30]=2,	-- depth:1
[28]=4,	-- depth:1
[27]=3,	-- depth:1
[20]=32,	-- depth:2
[23]=31,	-- depth:2
[22]=30,	-- depth:2
[39]=35,	-- depth:2
[19]=23,	-- depth:3
[18]=22,	-- depth:3
[16]=32,	-- depth:2
[15]=31,	-- depth:2
[14]=30,	-- depth:2
[12]=4,	-- depth:1
[11]=3,	-- depth:1
[10]=2,	-- depth:1
[8]=4,	-- depth:1
[7]=3,	-- depth:1
[6]=2,	-- depth:1
[24]=20,	-- depth:3
[40]=36,	-- depth:2
},
other_default_table={type=6,bottom_name1="圣兽召唤",open_panel1="HolyBeastCallView",act_type1=2316,bottom_name2="冲榜助力",open_panel2="HelpRankView",act_type2=1029,bottom_name3="升级龙神",open_panel3="DragonTempleLevelBuyView",act_type3="",bottom_name4="前往提战",open_panel4="DragonTempleView",act_type4="",},

type_default_table={type=0,activity_type=2286,rank_type=72,model_show=38654,open_panel="",},

reward_default_table={type=0,min_rank=1,max_rank=1,reach_value=371000,reward_item={[0]=item_table[47],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[48],[5]=item_table[123],[6]=item_table[124]},}

}

