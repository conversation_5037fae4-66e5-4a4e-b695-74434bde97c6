-- Y-运营活动-天裳仙衣.xls
local item_table={
[1]={item_id=37235,num=1,is_bind=1},
[2]={item_id=37620,num=1,is_bind=1},
[3]={item_id=37929,num=1,is_bind=1},
[4]={item_id=38610,num=1,is_bind=1},
[5]={item_id=37416,num=1,is_bind=1},
[6]={item_id=38306,num=1,is_bind=1},
[7]={item_id=38203,num=1,is_bind=1},
[8]={item_id=38011,num=1,is_bind=1},
[9]={item_id=59670,num=1,is_bind=1},
[10]={item_id=26556,num=1,is_bind=1},
[11]={item_id=26555,num=1,is_bind=1},
[12]={item_id=26554,num=1,is_bind=1},
[13]={item_id=26553,num=1,is_bind=1},
[14]={item_id=57832,num=1,is_bind=1},
[15]={item_id=57833,num=1,is_bind=1},
[16]={item_id=57834,num=1,is_bind=1},
[17]={item_id=57835,num=1,is_bind=1},
[18]={item_id=57802,num=1,is_bind=1},
[19]={item_id=57803,num=1,is_bind=1},
[20]={item_id=57804,num=1,is_bind=1},
[21]={item_id=57805,num=1,is_bind=1},
[22]={item_id=57826,num=1,is_bind=1},
[23]={item_id=57827,num=1,is_bind=1},
[24]={item_id=57828,num=1,is_bind=1},
[25]={item_id=57829,num=1,is_bind=1},
[26]={item_id=26369,num=1,is_bind=1},
[27]={item_id=26380,num=1,is_bind=1},
[28]={item_id=26357,num=1,is_bind=1},
[29]={item_id=26367,num=1,is_bind=1},
[30]={item_id=26368,num=1,is_bind=1},
[31]={item_id=26378,num=1,is_bind=1},
[32]={item_id=26379,num=1,is_bind=1},
[33]={item_id=26355,num=1,is_bind=1},
[34]={item_id=26356,num=1,is_bind=1},
[35]={item_id=37276,num=1,is_bind=1},
[36]={item_id=37630,num=1,is_bind=1},
[37]={item_id=37922,num=1,is_bind=1},
[38]={item_id=38612,num=1,is_bind=1},
[39]={item_id=37427,num=1,is_bind=1},
[40]={item_id=38304,num=1,is_bind=1},
[41]={item_id=38208,num=1,is_bind=1},
[42]={item_id=38010,num=1,is_bind=1},
[43]={item_id=37706,num=1,is_bind=1},
[44]={item_id=37732,num=1,is_bind=1},
[45]={item_id=37702,num=1,is_bind=1},
[46]={item_id=37738,num=1,is_bind=1},
[47]={item_id=32292,num=1,is_bind=1},
[48]={item_id=22012,num=1,is_bind=1},
[49]={item_id=26125,num=1,is_bind=1},
[50]={item_id=26126,num=1,is_bind=1},
[51]={item_id=26127,num=1,is_bind=1},
[52]={item_id=39198,num=1,is_bind=1},
[53]={item_id=22728,num=1,is_bind=1},
[54]={item_id=22533,num=1,is_bind=1},
[55]={item_id=26568,num=1,is_bind=1},
[56]={item_id=26569,num=1,is_bind=1},
[57]={item_id=26570,num=1,is_bind=1},
[58]={item_id=26186,num=1,is_bind=1},
[59]={item_id=48576,num=1,is_bind=1},
[60]={item_id=48559,num=1,is_bind=1},
[61]={item_id=48570,num=1,is_bind=1},
[62]={item_id=26562,num=1,is_bind=1},
[63]={item_id=26563,num=1,is_bind=1},
[64]={item_id=26564,num=1,is_bind=1},
[65]={item_id=26557,num=1,is_bind=1},
[66]={item_id=26558,num=1,is_bind=1},
[67]={item_id=59669,num=2,is_bind=1},
[68]={item_id=48504,num=2,is_bind=1},
[69]={item_id=48504,num=10,is_bind=1},
[70]={item_id=48504,num=20,is_bind=1},
[71]={item_id=48504,num=30,is_bind=1},
[72]={item_id=37044,num=1,is_bind=1},
[73]={item_id=59670,num=5,is_bind=1},
[74]={item_id=59669,num=20,is_bind=1},
[75]={item_id=37048,num=1,is_bind=1},
[76]={item_id=48504,num=1,is_bind=1},
[77]={item_id=26367,num=5,is_bind=1},
[78]={item_id=26368,num=5,is_bind=1},
[79]={item_id=26345,num=5,is_bind=1},
[80]={item_id=26344,num=10,is_bind=1},
[81]={item_id=48504,num=6,is_bind=1},
[82]={item_id=22099,num=300,is_bind=1},
[83]={item_id=26369,num=5,is_bind=1},
[84]={item_id=26345,num=20,is_bind=1},
[85]={item_id=22099,num=980,is_bind=1},
[86]={item_id=26369,num=10,is_bind=1},
[87]={item_id=26345,num=50,is_bind=1},
[88]={item_id=48504,num=4,is_bind=1},
[89]={item_id=26369,num=2,is_bind=1},
[90]={item_id=26368,num=6,is_bind=1},
[91]={item_id=26367,num=6,is_bind=1},
[92]={item_id=26369,num=3,is_bind=1},
[93]={item_id=26368,num=9,is_bind=1},
[94]={item_id=26367,num=9,is_bind=1},
[95]={item_id=48504,num=8,is_bind=1},
[96]={item_id=26369,num=4,is_bind=1},
[97]={item_id=26368,num=12,is_bind=1},
[98]={item_id=26367,num=12,is_bind=1},
[99]={item_id=57834,num=2,is_bind=1},
[100]={item_id=26368,num=15,is_bind=1},
[101]={item_id=26367,num=15,is_bind=1},
[102]={item_id=48504,num=15,is_bind=1},
[103]={item_id=26369,num=7,is_bind=1},
[104]={item_id=26368,num=21,is_bind=1},
[105]={item_id=26367,num=21,is_bind=1},
[106]={item_id=57835,num=2,is_bind=1},
[107]={item_id=26368,num=30,is_bind=1},
[108]={item_id=26367,num=30,is_bind=1},
[109]={item_id=38948,num=1,is_bind=1},
[110]={item_id=57835,num=4,is_bind=1},
[111]={item_id=26369,num=20,is_bind=1},
[112]={item_id=26368,num=60,is_bind=1},
[113]={item_id=38950,num=1,is_bind=1},
[114]={item_id=59669,num=1,is_bind=1},
[115]={item_id=48504,num=5,is_bind=1},
[116]={item_id=59670,num=10,is_bind=1},
[117]={item_id=40213,num=1,is_bind=1},
[118]={item_id=40214,num=1,is_bind=1},
[119]={item_id=26345,num=1,is_bind=1},
[120]={item_id=26344,num=2,is_bind=1},
[121]={item_id=26368,num=3,is_bind=1},
[122]={item_id=26367,num=3,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_day={
{},
{start_day=6,end_day=10,grade=2,sp_model_id=37048,},
{start_day=11,end_day=15,},
{start_day=16,end_day=20,},
{start_day=21,end_day=25,},
{start_day=26,end_day=30,},
{start_day=31,end_day=35,},
{start_day=36,end_day=40,},
{start_day=41,end_day=45,},
{start_day=46,end_day=50,},
{start_day=51,end_day=55,},
{start_day=56,end_day=60,},
{start_day=61,end_day=65,},
{start_day=66,end_day=70,},
{start_day=71,end_day=75,},
{start_day=76,end_day=80,},
{start_day=81,end_day=85,},
{start_day=86,end_day=90,},
{start_day=91,end_day=95,},
{start_day=96,end_day=100,},
{start_day=101,end_day=105,},
{start_day=106,end_day=110,},
{start_day=111,end_day=115,},
{start_day=116,end_day=120,},
{start_day=121,end_day=125,},
{start_day=126,end_day=130,},
{start_day=131,end_day=135,},
{start_day=136,end_day=140,},
{start_day=141,end_day=145,},
{start_day=146,end_day=150,},
{start_day=151,end_day=155,},
{start_day=156,end_day=160,},
{start_day=161,end_day=165,}
},

open_day_meta_table_map={
[30]=2,	-- depth:1
[28]=2,	-- depth:1
[4]=2,	-- depth:1
[26]=2,	-- depth:1
[24]=2,	-- depth:1
[22]=2,	-- depth:1
[20]=2,	-- depth:1
[8]=2,	-- depth:1
[18]=2,	-- depth:1
[32]=2,	-- depth:1
[16]=2,	-- depth:1
[14]=2,	-- depth:1
[10]=2,	-- depth:1
[6]=2,	-- depth:1
[12]=2,	-- depth:1
},
mode={
{},
{mode=2,times=10,cost_item_num=10,add_lucky=10,},
{mode=3,times=50,cost_item_num=50,add_lucky=50,}
},

mode_meta_table_map={
},
reward_pool={
{item=item_table[1],},
{seq=1,item=item_table[2],},
{seq=2,item=item_table[3],},
{seq=3,item=item_table[4],},
{seq=4,item=item_table[5],},
{seq=5,item=item_table[6],},
{seq=6,item=item_table[7],},
{seq=7,item=item_table[8],},
{seq=8,is_rare=0,},
{seq=9,item=item_table[9],},
{seq=10,item=item_table[10],},
{seq=11,item=item_table[11],},
{seq=12,item=item_table[12],},
{seq=13,item=item_table[13],},
{seq=14,item=item_table[14],},
{seq=15,item=item_table[15],},
{seq=16,item=item_table[16],},
{seq=17,item=item_table[17],},
{seq=18,item=item_table[18],},
{seq=19,item=item_table[19],},
{seq=20,item=item_table[20],},
{seq=21,item=item_table[21],},
{seq=22,item=item_table[22],},
{seq=23,item=item_table[23],},
{seq=24,item=item_table[24],},
{seq=25,item=item_table[25],},
{seq=26,item=item_table[26],},
{seq=27,item=item_table[27],},
{seq=28,item=item_table[28],},
{seq=29,item=item_table[29],},
{seq=30,item=item_table[30],},
{seq=31,item=item_table[31],},
{seq=32,item=item_table[32],},
{seq=33,item=item_table[33],},
{seq=34,item=item_table[34],},
{grade=2,item=item_table[35],},
{grade=2,item=item_table[36],},
{grade=2,item=item_table[37],},
{grade=2,item=item_table[38],},
{grade=2,item=item_table[39],},
{grade=2,item=item_table[40],},
{grade=2,item=item_table[41],},
{seq=7,item=item_table[42],},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

reward_pool_meta_table_map={
[47]=12,	-- depth:1
[41]=6,	-- depth:1
[43]=36,	-- depth:1
[44]=9,	-- depth:1
[45]=10,	-- depth:1
[46]=11,	-- depth:1
[59]=24,	-- depth:1
[49]=14,	-- depth:1
[50]=15,	-- depth:1
[53]=18,	-- depth:1
[56]=21,	-- depth:1
[40]=5,	-- depth:1
[62]=27,	-- depth:1
[65]=30,	-- depth:1
[68]=33,	-- depth:1
[48]=13,	-- depth:1
[39]=4,	-- depth:1
[35]=9,	-- depth:1
[37]=2,	-- depth:1
[17]=9,	-- depth:1
[20]=9,	-- depth:1
[38]=3,	-- depth:1
[22]=9,	-- depth:1
[16]=9,	-- depth:1
[19]=9,	-- depth:1
[25]=9,	-- depth:1
[23]=9,	-- depth:1
[28]=9,	-- depth:1
[29]=9,	-- depth:1
[31]=9,	-- depth:1
[32]=9,	-- depth:1
[34]=9,	-- depth:1
[7]=9,	-- depth:1
[26]=9,	-- depth:1
[66]=31,	-- depth:2
[64]=29,	-- depth:2
[67]=32,	-- depth:2
[63]=28,	-- depth:2
[61]=26,	-- depth:2
[55]=20,	-- depth:2
[58]=23,	-- depth:2
[57]=22,	-- depth:2
[54]=19,	-- depth:2
[52]=17,	-- depth:2
[51]=16,	-- depth:2
[42]=7,	-- depth:2
[69]=34,	-- depth:2
[60]=25,	-- depth:2
[70]=35,	-- depth:2
},
baodi={
{guarantee_limit=200,},
{seq=1,guarantee_count=50,guarantee_limit=200,item=item_table[7],},
{seq=2,guarantee_count=100,guarantee_limit=200,item=item_table[6],},
{seq=3,guarantee_count=201,guarantee_limit=200,item=item_table[43],},
{seq=4,guarantee_count=300,item=item_table[5],},
{seq=5,guarantee_count=400,guarantee_limit=2,item=item_table[4],},
{seq=6,guarantee_count=501,item=item_table[3],},
{seq=7,guarantee_count=801,item=item_table[2],},
{seq=8,guarantee_count=1601,item=item_table[1],},
{seq=9,guarantee_count=2001,item=item_table[44],},
{grade=2,item=item_table[42],},
{grade=2,guarantee_count=20,item=item_table[41],},
{grade=2,seq=2,item=item_table[40],},
{grade=2,seq=3,item=item_table[39],},
{grade=2,seq=4,guarantee_count=150,item=item_table[38],},
{grade=2,guarantee_count=201,item=item_table[45],},
{grade=2,seq=6,guarantee_count=401,item=item_table[37],},
{grade=2,item=item_table[36],},
{grade=2,item=item_table[35],},
{grade=2,item=item_table[46],}
},

baodi_meta_table_map={
[11]=1,	-- depth:1
[19]=9,	-- depth:1
[18]=8,	-- depth:1
[20]=10,	-- depth:1
[12]=2,	-- depth:1
[13]=2,	-- depth:1
[14]=3,	-- depth:1
[16]=6,	-- depth:1
},
convert={
{stuff_id_1=59669,buy_limit="10|0|0",buy_type=10,item_flag=0,},
{seq=1,item=item_table[47],buy_limit="10|0|0",},
{seq=2,item=item_table[48],buy_limit="1|0|0",},
{seq=3,item=item_table[49],stuff_id_1=59669,buy_type=10,item_flag=0,},
{seq=4,item=item_table[50],},
{seq=5,item=item_table[51],stuff_num_1=20,},
{seq=6,item=item_table[52],},
{seq=7,item=item_table[53],},
{seq=8,item=item_table[54],stuff_num_1=10,},
{seq=9,item=item_table[55],},
{seq=10,item=item_table[56],},
{seq=11,item=item_table[57],stuff_num_1=10,buy_limit="0|0|1",},
{seq=12,item=item_table[58],stuff_num_1=1,buy_limit="0|1|0",},
{seq=13,item=item_table[59],buy_limit="10|0|0",},
{seq=14,item=item_table[60],buy_limit="50|0|0",},
{seq=15,item=item_table[61],stuff_num_1=3,buy_limit="50|0|0",},
{seq=16,item=item_table[62],},
{seq=17,item=item_table[63],},
{seq=18,item=item_table[64],buy_limit="5|0|0",},
{seq=19,item=item_table[65],stuff_num_1=2,buy_limit="5|0|0",},
{seq=20,item=item_table[66],},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

convert_meta_table_map={
[18]=19,	-- depth:1
[17]=19,	-- depth:1
[40]=19,	-- depth:1
[39]=18,	-- depth:2
[38]=17,	-- depth:2
[36]=15,	-- depth:1
[35]=14,	-- depth:1
[21]=20,	-- depth:1
[11]=12,	-- depth:1
[10]=12,	-- depth:1
[37]=16,	-- depth:1
[34]=13,	-- depth:1
[33]=12,	-- depth:1
[32]=11,	-- depth:2
[31]=10,	-- depth:2
[42]=21,	-- depth:2
[7]=4,	-- depth:1
[8]=4,	-- depth:1
[22]=1,	-- depth:1
[41]=20,	-- depth:1
[9]=4,	-- depth:1
[5]=9,	-- depth:2
[6]=4,	-- depth:1
[25]=4,	-- depth:1
[28]=7,	-- depth:2
[29]=8,	-- depth:2
[30]=9,	-- depth:2
[27]=6,	-- depth:2
[3]=9,	-- depth:2
[2]=6,	-- depth:2
[26]=5,	-- depth:3
[24]=3,	-- depth:3
[23]=2,	-- depth:3
},
times_reward={
{item=item_table[67],},
{seq=1,need_draw_times=20,item=item_table[68],},
{seq=2,need_draw_times=40,},
{seq=3,need_draw_times=100,},
{seq=4,need_draw_times=200,item=item_table[69],},
{seq=5,need_draw_times=300,item=item_table[70],},
{seq=6,need_draw_times=400,item=item_table[71],},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

times_reward_meta_table_map={
[8]=1,	-- depth:1
[10]=3,	-- depth:1
[11]=4,	-- depth:1
[9]=2,	-- depth:1
[12]=5,	-- depth:1
[13]=6,	-- depth:1
[14]=7,	-- depth:1
},
item_random_desc={
{item_id=37732,random_count=0.001,grid=1,},
{seq=1,item_id=37235,random_count=0.01,grid=2,},
{seq=2,item_id=37620,random_count=0.1,grid=2,},
{seq=3,item_id=37706,random_count=0.5,grid=2,},
{seq=4,item_id=37929,},
{seq=5,item_id=38610,random_count=1.5,},
{seq=6,item_id=37416,},
{seq=7,item_id=38306,random_count=2.5,},
{seq=8,item_id=38203,random_count=3,},
{seq=9,item_id=38011,random_count=3.5,},
{seq=10,random_count=6,},
{seq=11,item_id=59670,random_count=4,},
{seq=12,item_id=26556,},
{seq=13,item_id=26555,},
{seq=14,item_id=26554,},
{seq=15,item_id=26553,},
{seq=16,item_id=57832,},
{seq=17,item_id=57833,},
{seq=18,item_id=57834,},
{seq=19,item_id=57835,},
{seq=20,item_id=57802,},
{seq=21,item_id=57803,},
{seq=22,item_id=57804,},
{seq=23,item_id=57805,},
{seq=24,item_id=57826,random_count=12,},
{seq=25,item_id=57827,random_count=8,},
{seq=26,item_id=57828,random_count=2,},
{seq=27,item_id=57829,},
{grade=2,item_id=37738,},
{grade=2,item_id=37276,},
{grade=2,item_id=37630,},
{grade=2,item_id=37702,},
{grade=2,item_id=37922,},
{grade=2,item_id=38612,},
{grade=2,item_id=37427,},
{grade=2,item_id=38304,},
{grade=2,item_id=38208,},
{grade=2,item_id=38010,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

item_random_desc_meta_table_map={
[39]=11,	-- depth:1
[33]=5,	-- depth:1
[42]=14,	-- depth:1
[43]=15,	-- depth:1
[44]=16,	-- depth:1
[48]=20,	-- depth:1
[52]=24,	-- depth:1
[41]=13,	-- depth:1
[23]=27,	-- depth:1
[21]=25,	-- depth:1
[7]=27,	-- depth:1
[22]=26,	-- depth:1
[19]=27,	-- depth:1
[18]=26,	-- depth:1
[17]=25,	-- depth:1
[56]=28,	-- depth:1
[35]=7,	-- depth:2
[54]=26,	-- depth:1
[53]=25,	-- depth:1
[51]=23,	-- depth:2
[50]=22,	-- depth:2
[49]=21,	-- depth:2
[47]=19,	-- depth:2
[46]=18,	-- depth:2
[45]=17,	-- depth:2
[29]=1,	-- depth:1
[40]=12,	-- depth:1
[55]=27,	-- depth:1
[38]=10,	-- depth:1
[37]=9,	-- depth:1
[36]=8,	-- depth:1
[34]=6,	-- depth:1
[31]=3,	-- depth:1
[32]=4,	-- depth:1
[30]=2,	-- depth:1
},
collect={
{param1=6,param2=28,},
{seq=1,param1=4,param2=35,},
{seq=2,param2=32,},
{task_seq=1,param2=6,},
{task_seq=2,param1=5,param2=16,},
{task_seq=3,param1=2,param2=11,},
{grade=2,param1=6,},
{grade=2,param2=76,},
{grade=2,seq=2,},
{grade=2,param2=2,},
{grade=2,param2=27,},
{grade=2,param2=10,}
},

collect_meta_table_map={
[10]=4,	-- depth:1
[8]=2,	-- depth:1
[11]=5,	-- depth:1
[12]=6,	-- depth:1
},
collect_reward={
{item_reward={[0]=item_table[72]},open_panel="WardrobeView#wardrobe_suit#op=0",},
{task_seq=1,task_des="激活八荒嘉言衣",},
{task_seq=2,item_reward={[0]=item_table[73]},task_des="激活业火战铃",},
{task_seq=3,item_reward={[0]=item_table[74]},task_des="激活青葱律动",},
{grade=2,item_reward={[0]=item_table[75]},task_des="激活敦煌飞天3件套",open_panel="WardrobeView#wardrobe_suit#op=9",},
{grade=2,task_des="激活尤学柯克衣",},
{grade=2,task_des="激活天罗环夜",},
{grade=2,task_des="激活龙神之息",}
},

collect_reward_meta_table_map={
[6]=2,	-- depth:1
[7]=3,	-- depth:1
[8]=4,	-- depth:1
},
task_grade={
{},
{start_day=11,end_day=99999,grade=2,}
},

task_grade_meta_table_map={
},
task={
{},
{task_id=1,task_type=15,param1=2,param2=69,},
{task_id=2,task_type=21,param1=20000,},
{task_id=3,task_type=17,param1=1,param2=8,},
{task_grade=2,},
{task_grade=2,},
{task_grade=2,},
{task_grade=2,}
},

task_meta_table_map={
[7]=3,	-- depth:1
[6]=2,	-- depth:1
[8]=4,	-- depth:1
},
display_model={
{index=32,},
{show_item_type=1,color_pro="",show_item_id=37706,},
{grade=2,index=38,show_item_id=37738,},
{grade=2,show_item_id=37702,}
},

display_model_meta_table_map={
[4]=2,	-- depth:1
},
daily_task={
{param1=50,open_panel="ControlBeastsPrizeDrawWGView",},
{task_seq=1,task_type=2,param1=1680,task_desc="消费1680灵玉",open_panel="market#Tab_market20",},
{task_seq=2,task_type=8,task_desc="通关1次副本暗翼之巢",open_panel="fubenpanel#fubenpanel_pet",},
{task_seq=3,task_type=6,param1=3,task_desc="击杀3只灵妖奇脉BOSS",open_panel="boss#boss_personal",},
{task_seq=4,task_type=7,param1=100,task_desc="日常活跃度达到100",open_panel="bizuo#bizuo_bizuo",},
{task_seq=5,task_type=9,param1=2,task_desc="挑战2次副本日月修行",open_panel="fubenpanel#fubenpanel_exp",},
{task_seq=6,task_type=26,task_desc="经验祈福1次",},
{task_seq=7,task_type=24,task_desc="参加5次天梯争霸",open_panel="act_jjc#arena_tianti",},
{task_seq=8,task_type=33,param1=5,task_desc="铜钱祈福5次",},
{task_seq=9,task_type=14,task_desc="参与击杀1次谪仙之境BOSS",open_panel="WorldServer#world_new_shenyuan_boss",},
{start_day=6,end_day=10,},
{start_day=6,end_day=10,},
{start_day=6,end_day=10,},
{start_day=6,end_day=10,},
{start_day=6,end_day=10,},
{start_day=6,end_day=10,},
{start_day=6,end_day=10,},
{start_day=6,end_day=10,},
{start_day=6,end_day=10,},
{start_day=6,end_day=10,},
{start_day=11,end_day=15,},
{start_day=11,end_day=15,},
{start_day=11,end_day=15,},
{start_day=11,end_day=15,},
{start_day=11,end_day=15,},
{start_day=11,end_day=15,},
{start_day=11,end_day=15,},
{start_day=11,end_day=15,},
{start_day=11,end_day=15,},
{start_day=11,end_day=15,},
{start_day=16,end_day=20,},
{start_day=16,end_day=20,},
{start_day=16,end_day=20,},
{start_day=16,end_day=20,},
{start_day=16,end_day=20,},
{start_day=16,end_day=20,},
{start_day=16,end_day=20,},
{start_day=16,end_day=20,},
{start_day=16,end_day=20,},
{start_day=16,end_day=20,},
{start_day=21,end_day=25,},
{start_day=21,end_day=25,},
{start_day=21,end_day=25,},
{start_day=21,end_day=25,},
{start_day=21,end_day=25,},
{start_day=21,end_day=25,},
{start_day=21,end_day=25,},
{start_day=21,end_day=25,},
{start_day=21,end_day=25,},
{start_day=21,end_day=25,},
{start_day=26,end_day=30,},
{start_day=26,end_day=30,},
{start_day=26,end_day=30,},
{start_day=26,end_day=30,},
{start_day=26,end_day=30,},
{start_day=26,end_day=30,},
{start_day=26,end_day=30,},
{start_day=26,end_day=30,},
{start_day=26,end_day=30,},
{start_day=26,end_day=30,},
{start_day=31,end_day=35,},
{start_day=31,end_day=35,},
{start_day=31,end_day=35,},
{start_day=31,end_day=35,},
{start_day=31,end_day=35,},
{start_day=31,end_day=35,},
{start_day=31,end_day=35,},
{start_day=31,end_day=35,},
{start_day=31,end_day=35,},
{start_day=31,end_day=35,},
{start_day=36,end_day=40,},
{start_day=36,end_day=40,},
{start_day=36,end_day=40,},
{start_day=36,end_day=40,},
{start_day=36,end_day=40,},
{start_day=36,end_day=40,},
{start_day=36,end_day=40,},
{start_day=36,end_day=40,},
{start_day=36,end_day=40,},
{start_day=36,end_day=40,},
{start_day=41,end_day=45,},
{start_day=41,end_day=45,},
{start_day=41,end_day=45,},
{start_day=41,end_day=45,},
{start_day=41,end_day=45,},
{start_day=41,end_day=45,},
{start_day=41,end_day=45,},
{start_day=41,end_day=45,},
{start_day=41,end_day=45,},
{start_day=41,end_day=45,},
{start_day=46,end_day=50,},
{start_day=46,end_day=50,},
{start_day=46,end_day=50,},
{start_day=46,end_day=50,},
{start_day=46,end_day=50,},
{start_day=46,end_day=50,},
{start_day=46,end_day=50,},
{start_day=46,end_day=50,},
{start_day=46,end_day=50,},
{start_day=46,end_day=50,},
{start_day=51,end_day=55,},
{start_day=51,end_day=55,},
{start_day=51,end_day=55,},
{start_day=51,end_day=55,},
{start_day=51,end_day=55,},
{start_day=51,end_day=55,},
{start_day=51,end_day=55,},
{start_day=51,end_day=55,},
{start_day=51,end_day=55,},
{start_day=51,end_day=55,},
{start_day=56,end_day=999,},
{start_day=56,end_day=999,},
{start_day=56,end_day=999,},
{start_day=56,end_day=999,},
{start_day=56,end_day=999,},
{start_day=56,end_day=999,},
{start_day=56,end_day=999,},
{start_day=56,end_day=999,},
{start_day=56,end_day=999,},
{start_day=56,end_day=999,}
},

daily_task_meta_table_map={
[61]=1,	-- depth:1
[41]=61,	-- depth:2
[71]=61,	-- depth:2
[81]=61,	-- depth:2
[31]=61,	-- depth:2
[21]=61,	-- depth:2
[101]=61,	-- depth:2
[111]=61,	-- depth:2
[91]=61,	-- depth:2
[51]=61,	-- depth:2
[11]=61,	-- depth:2
[57]=7,	-- depth:1
[47]=57,	-- depth:2
[67]=57,	-- depth:2
[37]=57,	-- depth:2
[77]=57,	-- depth:2
[87]=57,	-- depth:2
[27]=57,	-- depth:2
[117]=57,	-- depth:2
[97]=57,	-- depth:2
[17]=57,	-- depth:2
[8]=9,	-- depth:1
[107]=57,	-- depth:2
[113]=3,	-- depth:1
[59]=9,	-- depth:1
[110]=10,	-- depth:1
[119]=59,	-- depth:2
[109]=59,	-- depth:2
[63]=113,	-- depth:2
[69]=59,	-- depth:2
[103]=113,	-- depth:2
[53]=113,	-- depth:2
[73]=113,	-- depth:2
[100]=110,	-- depth:2
[79]=59,	-- depth:2
[80]=110,	-- depth:2
[99]=59,	-- depth:2
[83]=113,	-- depth:2
[93]=113,	-- depth:2
[89]=59,	-- depth:2
[70]=110,	-- depth:2
[90]=110,	-- depth:2
[60]=110,	-- depth:2
[50]=110,	-- depth:2
[13]=113,	-- depth:2
[19]=59,	-- depth:2
[20]=110,	-- depth:2
[23]=113,	-- depth:2
[29]=59,	-- depth:2
[30]=110,	-- depth:2
[33]=113,	-- depth:2
[39]=59,	-- depth:2
[40]=110,	-- depth:2
[43]=113,	-- depth:2
[120]=110,	-- depth:2
[49]=59,	-- depth:2
[65]=5,	-- depth:1
[95]=65,	-- depth:2
[96]=6,	-- depth:1
[24]=4,	-- depth:1
[98]=8,	-- depth:2
[22]=2,	-- depth:1
[56]=96,	-- depth:2
[102]=22,	-- depth:2
[55]=65,	-- depth:2
[104]=24,	-- depth:2
[105]=65,	-- depth:2
[94]=24,	-- depth:2
[106]=96,	-- depth:2
[108]=98,	-- depth:3
[16]=96,	-- depth:2
[15]=65,	-- depth:2
[14]=24,	-- depth:2
[112]=22,	-- depth:2
[54]=24,	-- depth:2
[114]=24,	-- depth:2
[115]=65,	-- depth:2
[116]=96,	-- depth:2
[12]=22,	-- depth:2
[118]=98,	-- depth:3
[18]=98,	-- depth:3
[25]=65,	-- depth:2
[92]=22,	-- depth:2
[26]=96,	-- depth:2
[66]=96,	-- depth:2
[44]=24,	-- depth:2
[68]=98,	-- depth:3
[42]=22,	-- depth:2
[62]=22,	-- depth:2
[45]=65,	-- depth:2
[72]=22,	-- depth:2
[38]=98,	-- depth:3
[74]=24,	-- depth:2
[75]=65,	-- depth:2
[76]=96,	-- depth:2
[36]=96,	-- depth:2
[78]=98,	-- depth:3
[35]=65,	-- depth:2
[34]=24,	-- depth:2
[46]=96,	-- depth:2
[82]=22,	-- depth:2
[32]=22,	-- depth:2
[84]=24,	-- depth:2
[85]=65,	-- depth:2
[52]=22,	-- depth:2
[48]=98,	-- depth:3
[88]=98,	-- depth:3
[58]=98,	-- depth:3
[28]=98,	-- depth:3
[64]=24,	-- depth:2
[86]=96,	-- depth:2
},
rmb_gift={
{rmb_seq=100,},
{seq=1,price_type=2,rmb_type=0,price=1688,reward_item={[0]=item_table[76],[1]=item_table[77],[2]=item_table[78],[3]=item_table[79],[4]=item_table[80]},goods_bg=2,gift_name="灵玉豪礼",},
{seq=2,rmb_seq=102,price=30,reward_item={[0]=item_table[81],[1]=item_table[82],[2]=item_table[14],[3]=item_table[83],[4]=item_table[84]},limit_num=5,goods_bg=3,gift_name="臻选礼包",},
{seq=3,rmb_seq=103,price=98,reward_item={[0]=item_table[70],[1]=item_table[85],[2]=item_table[15],[3]=item_table[86],[4]=item_table[87]},limit_num=2,goods_bg=4,gift_name="甄选豪华礼包",},
{grade=2,rmb_seq=200,},
{grade=2,},
{grade=2,rmb_seq=202,},
{grade=2,rmb_seq=203,}
},

rmb_gift_meta_table_map={
[6]=2,	-- depth:1
[7]=3,	-- depth:1
[8]=4,	-- depth:1
},
rmb_one_key={
{},
{grade=2,rmb_seq=2,}
},

rmb_one_key_meta_table_map={
},
total_recharge={
{},
{seq=1,real_recharge_num=30,reward_item={[0]=item_table[15],[1]=item_table[88],[2]=item_table[89],[3]=item_table[90],[4]=item_table[91]},},
{seq=2,real_recharge_num=98,reward_item={[0]=item_table[16],[1]=item_table[81],[2]=item_table[92],[3]=item_table[93],[4]=item_table[94]},},
{seq=3,real_recharge_num=168,reward_item={[0]=item_table[16],[1]=item_table[95],[2]=item_table[96],[3]=item_table[97],[4]=item_table[98]},},
{seq=4,real_recharge_num=328,reward_item={[0]=item_table[99],[1]=item_table[69],[2]=item_table[83],[3]=item_table[100],[4]=item_table[101]},},
{seq=5,real_recharge_num=648,reward_item={[0]=item_table[17],[1]=item_table[102],[2]=item_table[103],[3]=item_table[104],[4]=item_table[105]},},
{seq=6,real_recharge_num=1000,reward_item={[0]=item_table[106],[1]=item_table[70],[2]=item_table[86],[3]=item_table[107],[4]=item_table[108]},},
{seq=7,real_recharge_num=2000,reward_item={[0]=item_table[109],[1]=item_table[71],[2]=item_table[110],[3]=item_table[111],[4]=item_table[112]},},
{grade=2,},
{grade=2,model_show_itemid=37738,},
{grade=2,model_show_itemid=37738,},
{grade=2,reward_item={[0]=item_table[99],[1]=item_table[95],[2]=item_table[96],[3]=item_table[97],[4]=item_table[98]},model_show_itemid=37738,},
{grade=2,model_show_itemid=37738,},
{grade=2,reward_item={[0]=item_table[106],[1]=item_table[102],[2]=item_table[103],[3]=item_table[104],[4]=item_table[105]},model_show_itemid=37738,},
{grade=2,reward_item={[0]=item_table[17],[1]=item_table[70],[2]=item_table[86],[3]=item_table[107],[4]=item_table[108]},model_show_itemid=37738,},
{grade=2,reward_item={[0]=item_table[113],[1]=item_table[71],[2]=item_table[110],[3]=item_table[111],[4]=item_table[112]},model_show_itemid=37738,}
},

total_recharge_meta_table_map={
[10]=2,	-- depth:1
[11]=3,	-- depth:1
[12]=4,	-- depth:1
[13]=5,	-- depth:1
[14]=6,	-- depth:1
[15]=7,	-- depth:1
[16]=8,	-- depth:1
},
view_type={
{},
{grade=2,}
},

view_type_meta_table_map={
},
other_default_table={cost_item_id=48504,cost_gold=40,free_draw_times=1,buy_item_id="59669|59670",sp_model_id=37048,cost_score=100,},

open_day_default_table={start_day=1,end_day=5,grade=1,cost_item_id=48504,sp_model_id=37044,},

mode_default_table={mode=1,times=1,cost_item_num=1,add_lucky=1,},

reward_pool_default_table={grade=1,seq=0,item=item_table[114],is_rare=1,show_item=2,},

baodi_default_table={grade=1,need_lucky=600,seq=0,guarantee_count=10,guarantee_limit=5,item=item_table[8],},

convert_default_table={grade=1,seq=0,time_limit=1,item=item_table[76],stuff_id_1=59670,stuff_num_1=5,stuff_id_2=0,stuff_num_2=0,stuff_id_3=0,stuff_num_3=0,buy_limit="0|0|0",buy_type=20,item_flag=1,model_show_type=4,image_name="",model_pos="",model_scale="",model_rot="",},

times_reward_default_table={grade=1,seq=0,need_draw_times=10,item=item_table[115],},

item_random_desc_default_table={grade=1,seq=0,item_id=59669,random_count=1,grid=3,},

collect_default_table={grade=1,task_seq=0,seq=0,type=1,param1=1,param2=38,},

collect_reward_default_table={grade=1,task_seq=0,item_reward={[0]=item_table[116]},task_des="激活万界龙渊3件套",open_panel="GloryCrystalView",},

task_grade_default_table={start_day=1,end_day=10,grade=1,},

task_default_table={task_grade=1,task_id=0,task_type=14,param1=280,param2=0,param3=0,param4=0,reward_item={[0]=item_table[76]},},

display_model_default_table={grade=1,show_item_type=0,part_type=1,index=6,model_pos="0|-0.46|0",model_rot="0|6|0",model_scale=1.18,color_pro="2|5|1",show_item_id=37732,},

daily_task_default_table={start_day=1,end_day=5,task_seq=0,task_type=32,param1=1,param2=0,param3=0,reward_item={[0]=item_table[88],[1]=item_table[29],[2]=item_table[30],[3]=item_table[117],[4]=item_table[118]},task_desc="幻兽召唤50次",open_panel="qifu#qifu_qf",},

rmb_gift_default_table={grade=1,seq=0,price_type=1,rmb_type=234,rmb_seq=0,price=0,reward_item={[0]=item_table[76],[1]=item_table[29],[2]=item_table[30],[3]=item_table[119],[4]=item_table[120]},limit_num=1,goods_bg=1,gift_name="每日好礼",},

rmb_one_key_default_table={grade=1,rmb_type=235,rmb_seq=1,price=328,reward_item={[0]=item_table[69],[1]=item_table[65],[2]=item_table[66]},discount="立享8折",},

total_recharge_default_table={grade=1,seq=0,real_recharge_num=6,reward_item={[0]=item_table[14],[1]=item_table[68],[2]=item_table[26],[3]=item_table[121],[4]=item_table[122]},open_panel="vip",model_show_type=1,model_bundle_name="",model_asset_name="",model_name="极品幻兽",model_show_itemid=37732,display_pos="-280|-0.5|0",display_scale=1.3,display_rotation="0|0|0",},

view_type_default_table={grade=1,ui_scene_config_index=32,color_index=1,}

}

