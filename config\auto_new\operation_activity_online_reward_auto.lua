-- Y-运营活动-在线奖励.xls
local item_table={
[1]={item_id=39933,num=1,is_bind=1},
[2]={item_id=39933,num=2,is_bind=1},
[3]={item_id=39933,num=3,is_bind=1},
}

return {
open_day={
{},
{start_day=4,end_day=9999,grade=2,}
},

open_day_meta_table_map={
},
reward={
{online_time=600,item=item_table[1],},
{seq=1,item=item_table[2],},
{seq=2,},
{seq=3,},
{seq=4,online_time=3600,},
{seq=5,online_time=5400,},
{seq=6,online_time=7200,},
{seq=7,online_time=9000,},
{seq=8,online_time=10800,},
{seq=9,online_time=14400,},
{seq=10,online_time=18000,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{seq=4,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

reward_meta_table_map={
[14]=3,	-- depth:1
[4]=5,	-- depth:1
[21]=10,	-- depth:1
[12]=1,	-- depth:1
[13]=2,	-- depth:1
[15]=4,	-- depth:2
[16]=15,	-- depth:3
[17]=6,	-- depth:1
[18]=7,	-- depth:1
[19]=8,	-- depth:1
[20]=9,	-- depth:1
[22]=11,	-- depth:1
},
open_day_default_table={start_day=1,end_day=3,grade=1,},

reward_default_table={grade=1,seq=0,online_time=1800,item=item_table[3],}

}

