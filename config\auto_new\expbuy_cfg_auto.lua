-- J-经验购买.xls

return {
open_cfg={
{},
{og_open_day=7,},
{og_open_day=11,},
{og_open_day=15,}
},

open_cfg_meta_table_map={
},
price_cfg={
{},
{buy_time=1,price=15,},
{buy_time=2,price=20,},
{buy_time=3,price=25,},
{buy_time=4,price=50,}
},

price_cfg_meta_table_map={
},
exp_cfg={
{},
{buy_time=2,exp=138992963,},
{buy_time=3,exp=145611675,},
{buy_time=4,exp=152230388,},
{buy_time=5,exp=158849100,},
{og_open_day=7,exp=881725500,},
{buy_time=2,exp=925811775,},
{buy_time=3,exp=969898050,},
{buy_time=4,exp=1013984325,},
{buy_time=5,exp=1058070600,},
{og_open_day=11,exp=2057558512,},
{buy_time=2,exp=2160436438,},
{buy_time=3,exp=2263314363,},
{buy_time=4,exp=2366192289,},
{buy_time=5,exp=2469070214,},
{og_open_day=15,exp=5121055797,},
{buy_time=2,exp=5377108587,},
{buy_time=3,exp=5633161377,},
{buy_time=4,exp=5889214167,},
{buy_time=5,exp=6145266956,}
},

exp_cfg_meta_table_map={
[18]=16,	-- depth:1
[17]=16,	-- depth:1
[15]=11,	-- depth:1
[14]=11,	-- depth:1
[10]=6,	-- depth:1
[12]=11,	-- depth:1
[19]=16,	-- depth:1
[9]=6,	-- depth:1
[8]=6,	-- depth:1
[7]=6,	-- depth:1
[13]=11,	-- depth:1
[20]=16,	-- depth:1
},
open_cfg_default_table={og_open_day=4,dur_day=1,buy_limit_count=5,rank_region_min=1,rank_region_max=1,correction_value=20,},

price_cfg_default_table={buy_time=0,price=10,},

exp_cfg_default_table={og_open_day=4,buy_time=1,exp=132374250,}

}

