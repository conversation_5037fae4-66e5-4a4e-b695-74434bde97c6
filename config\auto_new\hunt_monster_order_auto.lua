-- L-猎魔战令.xls
local item_table={
[1]={item_id=22099,num=800,is_bind=1},
[2]={item_id=39153,num=1,is_bind=1},
[3]={item_id=26369,num=1,is_bind=1},
[4]={item_id=26129,num=10,is_bind=1},
[5]={item_id=22014,num=500,is_bind=1},
[6]={item_id=26357,num=1,is_bind=1},
[7]={item_id=22014,num=200,is_bind=1},
[8]={item_id=22014,num=1000,is_bind=1},
[9]={item_id=30777,num=1,is_bind=1},
[10]={item_id=26129,num=15,is_bind=1},
[11]={item_id=22014,num=750,is_bind=1},
[12]={item_id=22014,num=400,is_bind=1},
[13]={item_id=22014,num=1250,is_bind=1},
[14]={item_id=30778,num=1,is_bind=1},
[15]={item_id=26195,num=1,is_bind=1},
[16]={item_id=22014,num=600,is_bind=1},
[17]={item_id=22014,num=1500,is_bind=1},
[18]={item_id=26129,num=20,is_bind=1},
[19]={item_id=22014,num=800,is_bind=1},
[20]={item_id=22014,num=1750,is_bind=1},
[21]={item_id=22576,num=800,is_bind=1},
[22]={item_id=26191,num=1,is_bind=1},
[23]={item_id=22014,num=2000,is_bind=1},
[24]={item_id=22576,num=1,is_bind=1},
[25]={item_id=22576,num=10407,is_bind=1},
[26]={item_id=26369,num=6,is_bind=1},
[27]={item_id=26129,num=250,is_bind=1},
[28]={item_id=39153,num=13,is_bind=1},
[29]={item_id=22014,num=16750,is_bind=1},
[30]={item_id=26195,num=11,is_bind=1},
[31]={item_id=30778,num=6,is_bind=1},
[32]={item_id=26191,num=2,is_bind=1},
[33]={item_id=26357,num=7,is_bind=1},
[34]={item_id=30777,num=2,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_day={
{},
{min_open_day=6,max_open_day=10,grade=2,}
},

open_day_meta_table_map={
},
order_reward={
{added_reward={[0]=item_table[1],[1]=item_table[2]},},
{seq=1,need_score=300,free_reward={[0]=item_table[3]},added_reward={[0]=item_table[4],[1]=item_table[5]},},
{seq=2,need_score=450,free_reward={[0]=item_table[4]},},
{seq=3,need_score=600,free_reward={[0]=item_table[2]},added_reward={[0]=item_table[6],[1]=item_table[2]},},
{seq=4,need_score=750,free_reward={[0]=item_table[7]},added_reward={[0]=item_table[8],[1]=item_table[9]},},
{seq=5,need_score=900,},
{seq=6,need_score=1050,free_reward={[0]=item_table[3]},added_reward={[0]=item_table[10],[1]=item_table[11]},},
{seq=7,need_score=1200,free_reward={[0]=item_table[10]},},
{seq=8,need_score=1350,},
{seq=9,need_score=1500,free_reward={[0]=item_table[12]},added_reward={[0]=item_table[13],[1]=item_table[14]},},
{seq=10,need_score=1650,},
{seq=11,need_score=1800,},
{seq=12,need_score=1950,},
{seq=13,need_score=2100,free_reward={[0]=item_table[15]},added_reward={[0]=item_table[6],[1]=item_table[2]},},
{seq=14,need_score=2250,free_reward={[0]=item_table[16]},added_reward={[0]=item_table[17],[1]=item_table[9]},},
{seq=15,need_score=2400,},
{seq=16,need_score=2550,added_reward={[0]=item_table[18],[1]=item_table[8]},},
{seq=17,need_score=2700,},
{seq=18,need_score=2850,},
{seq=19,need_score=3000,free_reward={[0]=item_table[19]},added_reward={[0]=item_table[20],[1]=item_table[14]},},
{seq=20,need_score=3150,added_reward={[0]=item_table[21],[1]=item_table[2]},},
{seq=21,need_score=3300,},
{seq=22,need_score=3450,},
{seq=23,need_score=3600,free_reward={[0]=item_table[22]},added_reward={[0]=item_table[6],[1]=item_table[22]},},
{seq=24,need_score=3750,free_reward={[0]=item_table[8]},added_reward={[0]=item_table[23],[1]=item_table[14]},},
{seq=25,need_score=3900,},
{seq=26,need_score=4050,},
{seq=27,need_score=4200,},
{seq=28,need_score=4350,},
{seq=29,need_score=4500,},
{seq=30,need_score=4650,free_reward={[0]=item_table[3]},added_reward={[0]=item_table[18],[1]=item_table[8]},},
{seq=31,need_score=4800,free_reward={[0]=item_table[18]},},
{seq=32,need_score=5000,free_reward={[0]=item_table[14]},fixed_show=1,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

order_reward_meta_table_map={
[34]=1,	-- depth:1
[59]=26,	-- depth:1
[28]=32,	-- depth:1
[23]=32,	-- depth:1
[18]=32,	-- depth:1
[63]=30,	-- depth:1
[49]=16,	-- depth:1
[11]=21,	-- depth:1
[6]=21,	-- depth:1
[13]=32,	-- depth:1
[46]=13,	-- depth:2
[44]=11,	-- depth:2
[54]=21,	-- depth:1
[41]=8,	-- depth:1
[39]=6,	-- depth:2
[56]=23,	-- depth:2
[36]=3,	-- depth:1
[65]=32,	-- depth:1
[12]=31,	-- depth:1
[19]=4,	-- depth:1
[9]=4,	-- depth:1
[61]=28,	-- depth:2
[22]=31,	-- depth:1
[51]=18,	-- depth:2
[27]=31,	-- depth:1
[55]=22,	-- depth:2
[64]=31,	-- depth:1
[53]=20,	-- depth:1
[52]=19,	-- depth:2
[58]=25,	-- depth:1
[60]=27,	-- depth:2
[33]=4,	-- depth:1
[37]=4,	-- depth:1
[47]=14,	-- depth:1
[45]=12,	-- depth:2
[10]=33,	-- depth:2
[42]=9,	-- depth:2
[40]=7,	-- depth:1
[38]=5,	-- depth:1
[17]=33,	-- depth:2
[48]=15,	-- depth:1
[35]=2,	-- depth:1
[29]=33,	-- depth:2
[24]=33,	-- depth:2
[50]=17,	-- depth:3
[57]=24,	-- depth:3
[43]=10,	-- depth:3
[62]=29,	-- depth:3
[66]=33,	-- depth:2
},
senior_order={
{},
{grade=2,seq=1,rmb_seq=1,},
{grade=3,seq=2,rmb_seq=2,}
},

senior_order_meta_table_map={
},
order_score={
{price=10,score=200,return_lingyu=2000,},
{seq=1,rmb_seq=101,},
{seq=2,rmb_seq=102,price=50,score=1000,return_lingyu=10000,},
{seq=3,rmb_seq=103,price=100,score=2000,return_lingyu=20000,},
{grade=2,rmb_seq=200,},
{grade=2,seq=1,rmb_seq=201,price=40,score=800,return_lingyu=8000,},
{grade=2,seq=2,rmb_seq=202,price=60,score=1200,return_lingyu=12000,},
{grade=2,rmb_seq=203,}
},

order_score_meta_table_map={
[8]=4,	-- depth:1
},
monster_quality={
{quality=2,},
{monster_id=43015,},
{monster_id=43016,},
{monster_id=43017,},
{monster_id=43018,},
{monster_id=43019,},
{monster_id=43020,},
{monster_id=43021,},
{monster_id=43022,},
{monster_id=43023,},
{monster_id=43024,},
{monster_id=43025,},
{monster_id=43026,},
{monster_id=43027,},
{monster_id=43028,},
{monster_id=43029,},
{monster_id=43030,},
{monster_id=43031,},
{monster_id=43032,},
{monster_id=43033,},
{monster_id=43034,},
{monster_id=43035,},
{monster_id=43036,},
{monster_id=43037,},
{monster_id=43038,},
{monster_id=43039,},
{monster_id=43040,},
{monster_id=43041,},
{monster_id=43042,},
{monster_id=43043,},
{monster_id=43044,},
{monster_id=43045,},
{monster_id=43046,},
{monster_id=43047,},
{monster_id=43048,},
{monster_id=43049,},
{monster_id=43350,},
{monster_id=43351,},
{monster_id=43352,},
{monster_id=43353,},
{monster_id=43354,},
{monster_id=43355,},
{monster_id=43356,},
{monster_id=43357,},
{monster_id=43358,},
{monster_id=43359,},
{monster_id=43360,},
{monster_id=43361,},
{monster_id=43362,},
{monster_id=43363,},
{monster_id=43364,},
{monster_id=43365,},
{monster_id=43366,},
{monster_id=43367,},
{monster_id=43368,},
{monster_id=43369,},
{monster_id=1546,},
{monster_id=1547,},
{monster_id=1548,},
{monster_id=1549,},
{monster_id=1550,},
{monster_id=1551,},
{monster_id=1552,},
{monster_id=1553,},
{monster_id=1554,},
{monster_id=1555,},
{monster_id=1556,},
{monster_id=1557,},
{monster_id=1558,},
{monster_id=1559,},
{monster_id=1560,},
{monster_id=1561,},
{monster_id=1562,},
{monster_id=1563,},
{monster_id=1564,},
{monster_id=1565,},
{monster_id=1566,},
{monster_id=1567,},
{monster_id=1568,},
{monster_id=1569,},
{monster_id=1570,},
{monster_id=1571,},
{monster_id=1572,},
{monster_id=1573,},
{monster_id=1574,},
{monster_id=1575,},
{monster_id=1576,},
{monster_id=1577,},
{monster_id=1578,},
{monster_id=1579,},
{monster_id=1580,},
{monster_id=1581,},
{monster_id=1582,},
{monster_id=1583,},
{monster_id=1584,},
{monster_id=1585,},
{monster_id=1586,},
{monster_id=1587,},
{monster_id=1588,},
{monster_id=1589,},
{monster_id=1590,},
{monster_id=1591,},
{monster_id=1592,},
{monster_id=1593,},
{monster_id=45201,},
{monster_id=45202,},
{monster_id=45203,},
{monster_id=45204,},
{monster_id=45205,},
{monster_id=45206,},
{monster_id=45207,},
{monster_id=45208,},
{monster_id=45209,},
{monster_id=45210,},
{monster_id=45211,},
{monster_id=45212,},
{monster_id=45213,},
{monster_id=45214,},
{monster_id=45215,},
{monster_id=45216,},
{monster_id=45217,},
{monster_id=45218,},
{monster_id=45219,},
{monster_id=45220,},
{monster_id=45221,},
{monster_id=45222,},
{monster_id=45223,},
{monster_id=45224,},
{monster_id=45225,},
{monster_id=45226,},
{monster_id=45227,},
{monster_id=45228,quality=0,},
{monster_id=45229,},
{monster_id=45230,},
{monster_id=45231,},
{monster_id=45232,},
{monster_id=45233,},
{monster_id=45234,},
{monster_id=45235,},
{monster_id=45236,},
{monster_id=45237,},
{monster_id=45238,},
{monster_id=45239,},
{monster_id=45240,},
{monster_id=45241,},
{monster_id=45242,},
{monster_id=45243,},
{monster_id=45244,},
{monster_id=45245,},
{monster_id=45246,},
{monster_id=45247,},
{monster_id=45248,},
{monster_id=45249,},
{monster_id=45250,},
{monster_id=45251,},
{monster_id=45252,},
{monster_id=45253,},
{monster_id=45254,},
{monster_id=1800,},
{monster_id=1801,},
{monster_id=1802,},
{monster_id=1803,},
{monster_id=1804,},
{monster_id=1805,},
{monster_id=1806,},
{monster_id=1807,},
{monster_id=1808,},
{monster_id=1809,},
{monster_id=1810,},
{monster_id=1811,},
{monster_id=1812,},
{monster_id=1813,},
{monster_id=1814,},
{monster_id=1815,},
{monster_id=1816,},
{monster_id=1817,},
{monster_id=1818,},
{monster_id=1819,},
{monster_id=1820,},
{monster_id=1821,},
{monster_id=1822,},
{monster_id=1823,},
{monster_id=1824,},
{monster_id=1825,},
{monster_id=1826,},
{monster_id=1827,},
{monster_id=1828,},
{monster_id=1829,},
{monster_id=1830,},
{monster_id=1831,},
{monster_id=1832,},
{monster_id=1833,},
{monster_id=1834,},
{monster_id=1835,},
{monster_id=1836,},
{monster_id=1837,},
{monster_id=1838,},
{monster_id=1839,},
{monster_id=1840,},
{monster_id=1841,},
{monster_id=1842,},
{monster_id=1843,},
{monster_id=1844,},
{monster_id=1845,},
{monster_id=1846,},
{monster_id=1847,},
{monster_id=1848,},
{monster_id=1849,},
{monster_id=1850,},
{monster_id=1851,},
{monster_id=1852,},
{monster_id=1853,},
{monster_id=1854,},
{monster_id=1855,},
{monster_id=1856,},
{monster_id=1857,},
{monster_id=1858,},
{monster_id=1859,},
{monster_id=1860,},
{monster_id=1861,},
{monster_id=1862,},
{monster_id=1863,},
{monster_id=1864,},
{monster_id=1865,},
{monster_id=1866,},
{monster_id=1867,},
{monster_id=1868,},
{monster_id=1869,},
{monster_id=1870,},
{monster_id=1871,},
{monster_id=1872,},
{monster_id=1873,},
{monster_id=1874,},
{monster_id=1875,},
{monster_id=1876,},
{monster_id=1877,},
{monster_id=1878,},
{monster_id=1879,},
{monster_id=1880,},
{monster_id=1881,},
{monster_id=1882,},
{monster_id=1883,},
{monster_id=1884,},
{monster_id=1885,},
{monster_id=1886,},
{monster_id=1887,},
{monster_id=1888,},
{monster_id=1889,},
{monster_id=1890,},
{monster_id=1891,},
{monster_id=1892,},
{monster_id=1893,},
{monster_id=1894,},
{monster_id=1895,},
{monster_id=1896,},
{monster_id=1897,},
{monster_id=1898,},
{monster_id=1899,},
{monster_id=1900,},
{monster_id=1901,},
{monster_id=1902,},
{monster_id=1903,},
{monster_id=1904,},
{monster_id=1905,},
{monster_id=1906,},
{monster_id=1907,},
{monster_id=1908,},
{monster_id=1909,},
{monster_id=1910,},
{monster_id=1911,},
{monster_id=1912,},
{monster_id=1913,},
{monster_id=1914,},
{monster_id=1915,},
{monster_id=1916,},
{monster_id=1917,},
{monster_id=1918,},
{monster_id=1919,},
{monster_id=1920,},
{monster_id=1921,},
{monster_id=1922,},
{monster_id=1923,},
{monster_id=1924,},
{monster_id=1925,},
{monster_id=1926,},
{monster_id=1927,},
{monster_id=1928,},
{monster_id=1929,},
{monster_id=1930,},
{monster_id=1931,},
{monster_id=1932,},
{monster_id=1933,},
{monster_id=1934,},
{monster_id=1935,},
{monster_id=1936,},
{monster_id=1937,},
{monster_id=1938,},
{monster_id=1939,},
{monster_id=1940,},
{monster_id=1941,},
{monster_id=1942,},
{monster_id=1943,},
{monster_id=1944,},
{monster_id=1945,},
{monster_id=1946,},
{monster_id=1947,},
{monster_id=1948,},
{monster_id=1949,},
{monster_id=1950,},
{monster_id=1951,},
{monster_id=1952,},
{monster_id=1953,},
{monster_id=1954,},
{monster_id=1955,},
{monster_id=1956,},
{monster_id=1957,},
{monster_id=1958,},
{monster_id=1959,},
{monster_id=61,},
{monster_id=62,},
{monster_id=63,},
{monster_id=64,},
{monster_id=65,},
{monster_id=66,},
{monster_id=67,},
{monster_id=68,},
{monster_id=69,},
{monster_id=70,},
{monster_id=71,},
{monster_id=72,},
{monster_id=73,},
{monster_id=74,},
{monster_id=75,},
{monster_id=76,},
{monster_id=44000,quality=4,},
{monster_id=44001,},
{monster_id=44002,},
{monster_id=44007,},
{monster_id=44003,},
{monster_id=44004,},
{monster_id=44005,},
{monster_id=44006,},
{monster_id=44008,},
{monster_id=44009,},
{monster_id=1700,},
{monster_id=1701,},
{monster_id=1702,},
{monster_id=1703,},
{monster_id=1704,},
{monster_id=1705,},
{monster_id=1706,},
{monster_id=1707,},
{monster_id=1708,},
{monster_id=1709,},
{monster_id=1711,},
{monster_id=1713,},
{monster_id=1714,},
{monster_id=1715,},
{monster_id=1716,},
{monster_id=1717,},
{monster_id=1718,},
{monster_id=1719,},
{monster_id=5300,},
{monster_id=5301,},
{monster_id=5302,},
{monster_id=5304,},
{monster_id=5305,},
{monster_id=5306,},
{monster_id=5307,},
{monster_id=5308,},
{monster_id=5309,},
{monster_id=5310,},
{monster_id=5311,},
{monster_id=5312,},
{monster_id=45301,},
{monster_id=45302,},
{monster_id=45303,},
{monster_id=45304,},
{monster_id=45305,},
{monster_id=45306,},
{monster_id=45307,},
{monster_id=45308,},
{monster_id=45309,},
{monster_id=45310,},
{monster_id=45311,},
{monster_id=45312,},
{monster_id=45313,},
{monster_id=45314,},
{monster_id=45315,},
{monster_id=45316,},
{monster_id=45317,},
{monster_id=45318,}
},

monster_quality_meta_table_map={
[22]=1,	-- depth:1
[13]=22,	-- depth:2
[21]=13,	-- depth:3
[20]=21,	-- depth:4
[19]=20,	-- depth:5
[18]=19,	-- depth:6
[17]=18,	-- depth:7
[16]=17,	-- depth:8
[15]=16,	-- depth:9
[14]=15,	-- depth:10
[12]=14,	-- depth:11
[8]=12,	-- depth:12
[10]=8,	-- depth:13
[9]=10,	-- depth:14
[23]=9,	-- depth:15
[7]=23,	-- depth:16
[6]=7,	-- depth:17
[5]=6,	-- depth:18
[4]=5,	-- depth:19
[3]=4,	-- depth:20
[2]=3,	-- depth:21
[11]=2,	-- depth:22
[24]=11,	-- depth:23
[29]=24,	-- depth:24
[26]=29,	-- depth:25
[49]=26,	-- depth:26
[48]=49,	-- depth:27
[47]=48,	-- depth:28
[46]=47,	-- depth:29
[45]=46,	-- depth:30
[44]=45,	-- depth:31
[43]=44,	-- depth:32
[42]=43,	-- depth:33
[41]=42,	-- depth:34
[40]=41,	-- depth:35
[39]=40,	-- depth:36
[38]=39,	-- depth:37
[37]=38,	-- depth:38
[36]=37,	-- depth:39
[35]=36,	-- depth:40
[34]=35,	-- depth:41
[33]=34,	-- depth:42
[32]=33,	-- depth:43
[31]=32,	-- depth:44
[30]=31,	-- depth:45
[336]=335,	-- depth:1
[28]=30,	-- depth:46
[27]=28,	-- depth:47
[25]=27,	-- depth:48
[337]=336,	-- depth:2
[341]=337,	-- depth:3
[339]=341,	-- depth:4
[368]=25,	-- depth:49
[369]=368,	-- depth:50
[370]=369,	-- depth:51
[371]=370,	-- depth:52
[372]=371,	-- depth:53
[373]=372,	-- depth:54
[374]=373,	-- depth:55
[375]=374,	-- depth:56
[376]=375,	-- depth:57
[377]=376,	-- depth:58
[378]=377,	-- depth:59
[379]=378,	-- depth:60
[380]=379,	-- depth:61
[381]=380,	-- depth:62
[382]=381,	-- depth:63
[383]=382,	-- depth:64
[384]=383,	-- depth:65
[385]=384,	-- depth:66
[386]=385,	-- depth:67
[387]=386,	-- depth:68
[388]=387,	-- depth:69
[389]=388,	-- depth:70
[390]=389,	-- depth:71
[367]=390,	-- depth:72
[366]=367,	-- depth:73
[365]=366,	-- depth:74
[364]=365,	-- depth:75
[340]=339,	-- depth:5
[50]=364,	-- depth:76
[342]=340,	-- depth:6
[343]=342,	-- depth:7
[344]=343,	-- depth:8
[345]=50,	-- depth:77
[346]=345,	-- depth:78
[347]=346,	-- depth:79
[348]=347,	-- depth:80
[349]=348,	-- depth:81
[350]=349,	-- depth:82
[338]=344,	-- depth:9
[351]=350,	-- depth:83
[353]=351,	-- depth:84
[354]=353,	-- depth:85
[355]=354,	-- depth:86
[356]=355,	-- depth:87
[357]=356,	-- depth:88
[358]=357,	-- depth:89
[359]=358,	-- depth:90
[360]=359,	-- depth:91
[361]=360,	-- depth:92
[362]=361,	-- depth:93
[363]=362,	-- depth:94
[352]=363,	-- depth:95
[51]=352,	-- depth:96
[56]=51,	-- depth:97
[53]=56,	-- depth:98
[131]=132,	-- depth:1
[130]=131,	-- depth:2
[129]=130,	-- depth:3
[128]=129,	-- depth:4
[127]=128,	-- depth:5
[126]=127,	-- depth:6
[125]=126,	-- depth:7
[124]=125,	-- depth:8
[123]=124,	-- depth:9
[122]=123,	-- depth:10
[121]=122,	-- depth:11
[120]=121,	-- depth:12
[119]=120,	-- depth:13
[118]=119,	-- depth:14
[117]=118,	-- depth:15
[116]=117,	-- depth:16
[115]=116,	-- depth:17
[114]=115,	-- depth:18
[113]=114,	-- depth:19
[112]=113,	-- depth:20
[111]=112,	-- depth:21
[110]=111,	-- depth:22
[133]=110,	-- depth:23
[109]=133,	-- depth:24
[134]=109,	-- depth:25
[136]=134,	-- depth:26
[158]=136,	-- depth:27
[157]=158,	-- depth:28
[156]=157,	-- depth:29
[155]=156,	-- depth:30
[154]=155,	-- depth:31
[153]=154,	-- depth:32
[152]=153,	-- depth:33
[151]=152,	-- depth:34
[150]=151,	-- depth:35
[149]=150,	-- depth:36
[148]=149,	-- depth:37
[147]=148,	-- depth:38
[146]=147,	-- depth:39
[145]=146,	-- depth:40
[144]=145,	-- depth:41
[143]=144,	-- depth:42
[142]=143,	-- depth:43
[141]=142,	-- depth:44
[140]=141,	-- depth:45
[391]=53,	-- depth:99
[139]=140,	-- depth:46
[138]=139,	-- depth:47
[137]=138,	-- depth:48
[135]=137,	-- depth:49
[108]=135,	-- depth:50
[107]=108,	-- depth:51
[106]=107,	-- depth:52
[77]=106,	-- depth:53
[76]=77,	-- depth:54
[75]=76,	-- depth:55
[74]=75,	-- depth:56
[73]=74,	-- depth:57
[72]=73,	-- depth:58
[71]=72,	-- depth:59
[70]=71,	-- depth:60
[69]=70,	-- depth:61
[68]=69,	-- depth:62
[67]=68,	-- depth:63
[66]=67,	-- depth:64
[65]=66,	-- depth:65
[64]=65,	-- depth:66
[63]=64,	-- depth:67
[62]=63,	-- depth:68
[61]=62,	-- depth:69
[60]=61,	-- depth:70
[59]=60,	-- depth:71
[58]=59,	-- depth:72
[57]=58,	-- depth:73
[55]=391,	-- depth:100
[54]=55,	-- depth:101
[78]=57,	-- depth:74
[79]=78,	-- depth:75
[80]=79,	-- depth:76
[81]=80,	-- depth:77
[105]=81,	-- depth:78
[104]=105,	-- depth:79
[103]=104,	-- depth:80
[102]=103,	-- depth:81
[101]=102,	-- depth:82
[100]=101,	-- depth:83
[99]=100,	-- depth:84
[98]=99,	-- depth:85
[97]=98,	-- depth:86
[96]=97,	-- depth:87
[95]=96,	-- depth:88
[52]=54,	-- depth:102
[94]=95,	-- depth:89
[92]=94,	-- depth:90
[91]=92,	-- depth:91
[90]=91,	-- depth:92
[89]=90,	-- depth:93
[88]=89,	-- depth:94
[87]=88,	-- depth:95
[86]=87,	-- depth:96
[85]=86,	-- depth:97
[84]=85,	-- depth:98
[83]=84,	-- depth:99
[82]=83,	-- depth:100
[93]=82,	-- depth:101
[392]=52,	-- depth:103
},
quality_score={
{},
{quality=1,score=20,},
{quality=2,score=30,},
{quality=3,score=40,},
{quality=4,score=50,},
{quality=5,score=60,}
},

quality_score_meta_table_map={
},
other_default_table={daily_score_limit=1000,},

open_day_default_table={min_open_day=1,max_open_day=5,grade=1,},

order_reward_default_table={grade=1,seq=0,need_score=150,free_reward={[0]=item_table[24]},added_reward={[0]=item_table[21],[1]=item_table[15]},fixed_show=0,},

senior_order_default_table={grade=1,seq=0,rmb_type=189,rmb_seq=0,price=80,return_score=800,return_lingyu=8000,order_preview={[0]=item_table[1],[1]=item_table[25],[2]=item_table[26],[3]=item_table[27],[4]=item_table[28],[5]=item_table[29],[6]=item_table[30],[7]=item_table[31],[8]=item_table[32],[9]=item_table[33],[10]=item_table[34]},},

order_score_default_table={grade=1,seq=0,rmb_type=190,rmb_seq=100,price=20,score=400,return_lingyu=4000,},

monster_quality_default_table={monster_id=43014,quality=1,},

quality_score_default_table={quality=0,score=10,}

}

