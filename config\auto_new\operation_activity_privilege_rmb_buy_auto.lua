-- Y-运营活动-七曜战备.xls
local item_table={
[1]={item_id=22753,num=2,is_bind=1},
[2]={item_id=22013,num=2,is_bind=1},
[3]={item_id=29058,num=3,is_bind=1},
[4]={item_id=29061,num=3,is_bind=1},
[5]={item_id=26410,num=50,is_bind=1},
[6]={item_id=30446,num=50,is_bind=1},
[7]={item_id=56317,num=10,is_bind=1},
[8]={item_id=29064,num=3,is_bind=1},
[9]={item_id=29067,num=3,is_bind=1},
[10]={item_id=29070,num=3,is_bind=1},
[11]={item_id=29073,num=3,is_bind=1},
[12]={item_id=47520,num=1,is_bind=1},
[13]={item_id=47523,num=1,is_bind=1},
[14]={item_id=47526,num=1,is_bind=1},
[15]={item_id=26670,num=1,is_bind=1},
[16]={item_id=26411,num=100,is_bind=1},
[17]={item_id=30446,num=100,is_bind=1},
[18]={item_id=56317,num=30,is_bind=1},
[19]={item_id=47521,num=1,is_bind=1},
[20]={item_id=47524,num=1,is_bind=1},
[21]={item_id=47527,num=1,is_bind=1},
[22]={item_id=26685,num=1,is_bind=1},
[23]={item_id=47522,num=1,is_bind=1},
[24]={item_id=47525,num=1,is_bind=1},
[25]={item_id=38448,num=1,is_bind=1},
[26]={item_id=37668,num=1,is_bind=1},
[27]={item_id=57316,num=1,is_bind=1},
[28]={item_id=57315,num=1,is_bind=1},
[29]={item_id=26464,num=5,is_bind=1},
[30]={item_id=26462,num=10,is_bind=1},
[31]={item_id=26463,num=10,is_bind=1},
[32]={item_id=38162,num=1,is_bind=1},
[33]={item_id=37956,num=1,is_bind=1},
[34]={item_id=57317,num=1,is_bind=1},
[35]={item_id=57318,num=1,is_bind=1},
[36]={item_id=37484,num=1,is_bind=1},
[37]={item_id=37852,num=1,is_bind=1},
[38]={item_id=57319,num=1,is_bind=1},
[39]={item_id=26548,num=5,is_bind=1},
[40]={item_id=47530,num=1,is_bind=1},
[41]={item_id=47533,num=1,is_bind=1},
[42]={item_id=29004,num=3,is_bind=1},
[43]={item_id=29007,num=3,is_bind=1},
[44]={item_id=38449,num=1,is_bind=1},
[45]={item_id=37669,num=1,is_bind=1},
[46]={item_id=57310,num=1,is_bind=1},
[47]={item_id=57311,num=1,is_bind=1},
[48]={item_id=38165,num=1,is_bind=1},
[49]={item_id=37854,num=1,is_bind=1},
[50]={item_id=57314,num=1,is_bind=1},
[51]={item_id=29022,num=3,is_bind=1},
[52]={item_id=29025,num=3,is_bind=1},
[53]={item_id=29028,num=3,is_bind=1},
[54]={item_id=29031,num=3,is_bind=1},
[55]={item_id=29034,num=3,is_bind=1},
[56]={item_id=29037,num=3,is_bind=1},
[57]={item_id=47528,num=1,is_bind=1},
[58]={item_id=47531,num=1,is_bind=1},
[59]={item_id=47534,num=1,is_bind=1},
[60]={item_id=47529,num=1,is_bind=1},
[61]={item_id=47532,num=1,is_bind=1},
[62]={item_id=47535,num=1,is_bind=1},
[63]={item_id=38454,num=1,is_bind=1},
[64]={item_id=37673,num=1,is_bind=1},
[65]={item_id=57305,num=1,is_bind=1},
[66]={item_id=57306,num=1,is_bind=1},
[67]={item_id=38166,num=1,is_bind=1},
[68]={item_id=37959,num=1,is_bind=1},
[69]={item_id=57307,num=1,is_bind=1},
[70]={item_id=57308,num=1,is_bind=1},
[71]={item_id=37550,num=1,is_bind=1},
[72]={item_id=37857,num=1,is_bind=1},
[73]={item_id=57309,num=1,is_bind=1},
[74]={item_id=29010,num=3,is_bind=1},
[75]={item_id=29013,num=3,is_bind=1},
[76]={item_id=29016,num=3,is_bind=1},
[77]={item_id=29019,num=3,is_bind=1},
[78]={item_id=47536,num=1,is_bind=1},
[79]={item_id=47539,num=1,is_bind=1},
[80]={item_id=47542,num=1,is_bind=1},
[81]={item_id=47537,num=1,is_bind=1},
[82]={item_id=47540,num=1,is_bind=1},
[83]={item_id=47543,num=1,is_bind=1},
[84]={item_id=47538,num=1,is_bind=1},
[85]={item_id=47541,num=1,is_bind=1},
[86]={item_id=37487,num=1,is_bind=1},
[87]={item_id=37958,num=1,is_bind=1},
[88]={item_id=57312,num=1,is_bind=1},
[89]={item_id=57313,num=1,is_bind=1},
[90]={item_id=26549,num=1,is_bind=1},
[91]={item_id=26550,num=1,is_bind=1},
[92]={item_id=26551,num=1,is_bind=1},
[93]={item_id=26552,num=1,is_bind=1},
[94]={item_id=26545,num=1,is_bind=1},
[95]={item_id=26546,num=1,is_bind=1},
[96]={item_id=26547,num=1,is_bind=1},
}

return {
open_day={
{grade=1,},
{start_day=18,end_day=24,grade=2,},
{start_day=25,end_day=31,grade=3,},
{start_day=32,end_day=38,},
{start_day=39,end_day=45,},
{start_day=46,end_day=52,},
{start_day=53,end_day=59,},
{start_day=60,end_day=66,},
{start_day=67,end_day=73,},
{start_day=74,end_day=80,},
{start_day=81,end_day=87,},
{start_day=88,end_day=94,},
{start_day=95,end_day=101,},
{start_day=102,end_day=108,},
{start_day=109,end_day=115,},
{start_day=116,end_day=122,},
{start_day=123,end_day=129,},
{start_day=130,end_day=136,},
{start_day=137,end_day=143,},
{start_day=144,end_day=150,},
{start_day=151,end_day=157,},
{start_day=158,end_day=164,},
{start_day=165,end_day=171,},
{start_day=172,end_day=178,},
{start_day=179,end_day=185,},
{start_day=186,end_day=192,},
{start_day=193,end_day=199,},
{start_day=200,end_day=206,},
{start_day=207,end_day=213,},
{start_day=214,end_day=220,},
{start_day=221,end_day=227,},
{start_day=228,end_day=234,},
{start_day=235,end_day=241,},
{start_day=242,end_day=248,},
{start_day=249,end_day=255,},
{start_day=256,end_day=262,},
{start_day=263,end_day=269,},
{start_day=270,end_day=276,grade=5,},
{start_day=277,end_day=283,grade=6,},
{start_day=284,end_day=290,},
{start_day=291,end_day=9999,}
},

open_day_meta_table_map={
[36]=39,	-- depth:1
[35]=38,	-- depth:1
[33]=39,	-- depth:1
[32]=38,	-- depth:1
[5]=38,	-- depth:1
[29]=39,	-- depth:1
[6]=39,	-- depth:1
[12]=38,	-- depth:1
[26]=39,	-- depth:1
[25]=38,	-- depth:1
[23]=39,	-- depth:1
[22]=38,	-- depth:1
[8]=38,	-- depth:1
[9]=39,	-- depth:1
[19]=39,	-- depth:1
[18]=38,	-- depth:1
[16]=39,	-- depth:1
[15]=38,	-- depth:1
[28]=38,	-- depth:1
[13]=39,	-- depth:1
},
rmb_buy={
{},
{seq=1,rmb_seq=101,rmb_price=128,name="千秋绝艳",},
{seq=2,rmb_seq=102,rmb_price=2000,name="战力暴涨",},
{seq=3,rmb_seq=103,rmb_price=5000,name="绝版返场",},
{grade=2,rmb_seq=200,},
{grade=2,rmb_seq=201,},
{grade=2,rmb_seq=202,},
{grade=2,rmb_seq=203,},
{grade=3,rmb_seq=300,},
{grade=3,rmb_seq=301,},
{grade=3,rmb_seq=302,},
{grade=3,rmb_seq=303,},
{grade=4,rmb_seq=400,},
{grade=4,rmb_seq=401,},
{grade=4,rmb_seq=402,},
{grade=4,rmb_seq=403,},
{grade=5,rmb_seq=500,},
{grade=5,rmb_seq=501,},
{grade=5,rmb_seq=502,},
{grade=5,rmb_seq=503,},
{grade=6,rmb_seq=600,},
{grade=6,rmb_seq=601,},
{grade=6,rmb_seq=602,},
{grade=6,rmb_seq=603,}
},

rmb_buy_meta_table_map={
[22]=2,	-- depth:1
[20]=4,	-- depth:1
[19]=3,	-- depth:1
[18]=2,	-- depth:1
[16]=4,	-- depth:1
[12]=4,	-- depth:1
[14]=2,	-- depth:1
[23]=3,	-- depth:1
[11]=3,	-- depth:1
[10]=2,	-- depth:1
[8]=4,	-- depth:1
[7]=3,	-- depth:1
[6]=2,	-- depth:1
[15]=3,	-- depth:1
[24]=4,	-- depth:1
},
reward={
{reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7]},},
{activity_day=2,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[8],[3]=item_table[9],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7]},},
{activity_day=3,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[10],[3]=item_table[11],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7]},},
{seq=1,reward_item={[0]=item_table[12],[1]=item_table[13],[2]=item_table[14],[3]=item_table[15],[4]=item_table[16],[5]=item_table[17],[6]=item_table[18]},},
{seq=1,reward_item={[0]=item_table[19],[1]=item_table[20],[2]=item_table[21],[3]=item_table[22],[4]=item_table[16],[5]=item_table[17],[6]=item_table[18]},},
{seq=1,reward_item={[0]=item_table[23],[1]=item_table[24],[2]=item_table[15],[3]=item_table[22],[4]=item_table[16],[5]=item_table[17],[6]=item_table[18]},},
{seq=2,},
{activity_day=2,},
{activity_day=3,},
{seq=3,reward_item={[0]=item_table[25],[1]=item_table[26],[2]=item_table[27],[3]=item_table[28],[4]=item_table[29],[5]=item_table[30],[6]=item_table[31]},},
{activity_day=2,reward_item={[0]=item_table[32],[1]=item_table[33],[2]=item_table[34],[3]=item_table[35],[4]=item_table[29],[5]=item_table[30],[6]=item_table[31]},},
{activity_day=3,reward_item={[0]=item_table[36],[1]=item_table[37],[2]=item_table[38],[3]=item_table[29],[4]=item_table[39],[5]=item_table[30],[6]=item_table[31]},},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,reward_item={[0]=item_table[40],[1]=item_table[41],[2]=item_table[15],[3]=item_table[22],[4]=item_table[16],[5]=item_table[17],[6]=item_table[18]},},
{grade=2,},
{grade=2,},
{activity_day=3,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[42],[3]=item_table[43],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7]},},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{activity_day=3,},
{grade=3,reward_item={[0]=item_table[44],[1]=item_table[45],[2]=item_table[46],[3]=item_table[47],[4]=item_table[29],[5]=item_table[30],[6]=item_table[31]},},
{grade=3,},
{grade=3,reward_item={[0]=item_table[48],[1]=item_table[49],[2]=item_table[50],[3]=item_table[29],[4]=item_table[39],[5]=item_table[30],[6]=item_table[31]},},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{activity_day=2,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=5,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[51],[3]=item_table[52],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7]},},
{grade=5,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[53],[3]=item_table[54],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7]},},
{grade=5,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[55],[3]=item_table[56],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7]},},
{grade=5,reward_item={[0]=item_table[57],[1]=item_table[58],[2]=item_table[59],[3]=item_table[15],[4]=item_table[16],[5]=item_table[17],[6]=item_table[18]},},
{grade=5,reward_item={[0]=item_table[60],[1]=item_table[61],[2]=item_table[62],[3]=item_table[22],[4]=item_table[16],[5]=item_table[17],[6]=item_table[18]},},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,reward_item={[0]=item_table[63],[1]=item_table[64],[2]=item_table[65],[3]=item_table[66],[4]=item_table[29],[5]=item_table[30],[6]=item_table[31]},},
{grade=5,reward_item={[0]=item_table[67],[1]=item_table[68],[2]=item_table[69],[3]=item_table[70],[4]=item_table[29],[5]=item_table[30],[6]=item_table[31]},},
{grade=5,reward_item={[0]=item_table[71],[1]=item_table[72],[2]=item_table[73],[3]=item_table[29],[4]=item_table[39],[5]=item_table[30],[6]=item_table[31]},},
{grade=6,},
{grade=6,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[74],[3]=item_table[75],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7]},},
{grade=6,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[76],[3]=item_table[77],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7]},},
{grade=6,reward_item={[0]=item_table[78],[1]=item_table[79],[2]=item_table[80],[3]=item_table[15],[4]=item_table[16],[5]=item_table[17],[6]=item_table[18]},},
{grade=6,reward_item={[0]=item_table[81],[1]=item_table[82],[2]=item_table[83],[3]=item_table[22],[4]=item_table[16],[5]=item_table[17],[6]=item_table[18]},},
{grade=6,reward_item={[0]=item_table[84],[1]=item_table[85],[2]=item_table[15],[3]=item_table[22],[4]=item_table[16],[5]=item_table[17],[6]=item_table[18]},},
{grade=6,},
{grade=6,},
{activity_day=3,},
{grade=6,},
{grade=6,reward_item={[0]=item_table[86],[1]=item_table[87],[2]=item_table[88],[3]=item_table[89],[4]=item_table[29],[5]=item_table[30],[6]=item_table[31]},},
{grade=6,}
},

reward_meta_table_map={
[43]=7,	-- depth:1
[19]=43,	-- depth:2
[67]=19,	-- depth:3
[13]=49,	-- depth:1
[55]=67,	-- depth:4
[61]=25,	-- depth:1
[37]=1,	-- depth:1
[9]=7,	-- depth:1
[8]=9,	-- depth:2
[31]=55,	-- depth:5
[38]=2,	-- depth:1
[63]=3,	-- depth:1
[39]=3,	-- depth:1
[40]=4,	-- depth:1
[45]=9,	-- depth:2
[44]=45,	-- depth:3
[64]=4,	-- depth:1
[46]=10,	-- depth:1
[50]=2,	-- depth:1
[51]=3,	-- depth:1
[52]=4,	-- depth:1
[58]=10,	-- depth:1
[57]=45,	-- depth:3
[62]=2,	-- depth:1
[34]=10,	-- depth:1
[28]=64,	-- depth:2
[32]=44,	-- depth:4
[5]=2,	-- depth:1
[6]=3,	-- depth:1
[11]=10,	-- depth:1
[12]=10,	-- depth:1
[14]=50,	-- depth:2
[15]=51,	-- depth:2
[16]=52,	-- depth:2
[33]=32,	-- depth:5
[70]=34,	-- depth:2
[20]=32,	-- depth:5
[21]=20,	-- depth:6
[22]=58,	-- depth:2
[68]=20,	-- depth:6
[26]=62,	-- depth:2
[27]=63,	-- depth:2
[69]=68,	-- depth:7
[56]=68,	-- depth:7
[60]=12,	-- depth:2
[59]=11,	-- depth:2
[66]=6,	-- depth:2
[65]=5,	-- depth:2
[36]=12,	-- depth:2
[53]=5,	-- depth:2
[48]=12,	-- depth:2
[47]=11,	-- depth:2
[42]=6,	-- depth:2
[41]=5,	-- depth:2
[71]=11,	-- depth:2
[35]=71,	-- depth:3
[30]=66,	-- depth:3
[29]=65,	-- depth:3
[24]=60,	-- depth:3
[23]=59,	-- depth:3
[18]=6,	-- depth:2
[17]=53,	-- depth:3
[54]=18,	-- depth:3
[72]=36,	-- depth:3
},
open_day_default_table={start_day=1,end_day=17,grade=4,},

rmb_buy_default_table={grade=1,seq=0,rmb_seq=100,rmb_type=143,rmb_price=30,name="等级战备",open_day=0,close_day=3,},

reward_default_table={grade=1,seq=0,activity_day=1,reward_item={[0]=item_table[90],[1]=item_table[91],[2]=item_table[92],[3]=item_table[93],[4]=item_table[94],[5]=item_table[95],[6]=item_table[96]},}

}

