-- Y-运营活动-兔女郎宝藏.xls
local item_table={
[1]={item_id=43805,num=10,is_bind=1},
[2]={item_id=39142,num=2000,is_bind=1},
[3]={item_id=26667,num=2,is_bind=1},
[4]={item_id=26682,num=2,is_bind=1},
[5]={item_id=48071,num=5,is_bind=1},
[6]={item_id=44050,num=1,is_bind=1},
[7]={item_id=27838,num=1,is_bind=1},
[8]={item_id=27050,num=1,is_bind=1},
[9]={item_id=47097,num=1,is_bind=1},
[10]={item_id=29621,num=400,is_bind=1},
[11]={item_id=47052,num=1,is_bind=1},
[12]={item_id=27051,num=1,is_bind=1},
[13]={item_id=27052,num=1,is_bind=1},
[14]={item_id=27053,num=1,is_bind=1},
[15]={item_id=38727,num=1,is_bind=1},
[16]={item_id=27054,num=1,is_bind=1},
[17]={item_id=47060,num=1,is_bind=1},
[18]={item_id=27055,num=1,is_bind=1},
[19]={item_id=27056,num=1,is_bind=1},
[20]={item_id=27057,num=1,is_bind=1},
[21]={item_id=27058,num=1,is_bind=1},
[22]={item_id=47068,num=1,is_bind=1},
[23]={item_id=27061,num=1,is_bind=1},
[24]={item_id=47076,num=1,is_bind=1},
[25]={item_id=47084,num=1,is_bind=1},
[26]={item_id=47092,num=1,is_bind=1},
[27]={item_id=38666,num=1,is_bind=1},
[28]={item_id=27073,num=1,is_bind=1},
[29]={item_id=27074,num=1,is_bind=1},
[30]={item_id=47053,num=1,is_bind=1},
[31]={item_id=27075,num=1,is_bind=1},
[32]={item_id=27076,num=1,is_bind=1},
[33]={item_id=27077,num=1,is_bind=1},
[34]={item_id=27078,num=1,is_bind=1},
[35]={item_id=47061,num=1,is_bind=1},
[36]={item_id=27079,num=1,is_bind=1},
[37]={item_id=27080,num=1,is_bind=1},
[38]={item_id=38667,num=1,is_bind=1},
[39]={item_id=27081,num=1,is_bind=1},
[40]={item_id=27082,num=1,is_bind=1},
[41]={item_id=47069,num=1,is_bind=1},
[42]={item_id=27083,num=1,is_bind=1},
[43]={item_id=27084,num=1,is_bind=1},
[44]={item_id=27085,num=1,is_bind=1},
[45]={item_id=27086,num=1,is_bind=1},
[46]={item_id=47077,num=1,is_bind=1},
[47]={item_id=27087,num=1,is_bind=1},
[48]={item_id=27088,num=1,is_bind=1},
[49]={item_id=27089,num=1,is_bind=1},
[50]={item_id=27090,num=1,is_bind=1},
[51]={item_id=47085,num=1,is_bind=1},
[52]={item_id=27091,num=1,is_bind=1},
[53]={item_id=27092,num=1,is_bind=1},
[54]={item_id=27093,num=1,is_bind=1},
[55]={item_id=27094,num=1,is_bind=1},
[56]={item_id=47093,num=1,is_bind=1},
[57]={item_id=27095,num=1,is_bind=1},
[58]={item_id=27096,num=1,is_bind=1},
[59]={item_id=27097,num=1,is_bind=1},
[60]={item_id=27098,num=1,is_bind=1},
[61]={item_id=47005,num=1,is_bind=1},
[62]={item_id=27099,num=1,is_bind=1},
[63]={item_id=27100,num=1,is_bind=1},
[64]={item_id=27101,num=1,is_bind=1},
[65]={item_id=27102,num=1,is_bind=1},
[66]={item_id=47013,num=1,is_bind=1},
[67]={item_id=27103,num=1,is_bind=1},
[68]={item_id=27104,num=1,is_bind=1},
[69]={item_id=38665,num=1,is_bind=1},
[70]={item_id=27105,num=1,is_bind=1},
[71]={item_id=27106,num=1,is_bind=1},
[72]={item_id=47023,num=1,is_bind=1},
[73]={item_id=27107,num=1,is_bind=1},
[74]={item_id=27108,num=1,is_bind=1},
[75]={item_id=27109,num=1,is_bind=1},
[76]={item_id=27110,num=1,is_bind=1},
[77]={item_id=47029,num=1,is_bind=1},
[78]={item_id=27111,num=1,is_bind=1},
[79]={item_id=27112,num=1,is_bind=1},
[80]={item_id=27113,num=1,is_bind=1},
[81]={item_id=27114,num=1,is_bind=1},
[82]={item_id=47036,num=1,is_bind=1},
[83]={item_id=27115,num=1,is_bind=1},
[84]={item_id=27116,num=1,is_bind=1},
[85]={item_id=27117,num=1,is_bind=1},
[86]={item_id=38739,num=1,is_bind=1},
[87]={item_id=27118,num=1,is_bind=1},
[88]={item_id=47044,num=1,is_bind=1},
[89]={item_id=27119,num=1,is_bind=1},
[90]={item_id=27120,num=1,is_bind=1},
[91]={item_id=27121,num=1,is_bind=1},
[92]={item_id=27122,num=1,is_bind=1},
[93]={item_id=47045,num=1,is_bind=1},
[94]={item_id=27123,num=1,is_bind=1},
[95]={item_id=27124,num=1,is_bind=1},
[96]={item_id=27125,num=1,is_bind=1},
[97]={item_id=38734,num=1,is_bind=1},
[98]={item_id=27126,num=1,is_bind=1},
[99]={item_id=47037,num=1,is_bind=1},
[100]={item_id=27127,num=1,is_bind=1},
[101]={item_id=27128,num=1,is_bind=1},
[102]={item_id=38668,num=1,is_bind=1},
[103]={item_id=27129,num=1,is_bind=1},
[104]={item_id=27130,num=1,is_bind=1},
[105]={item_id=27131,num=1,is_bind=1},
[106]={item_id=27132,num=1,is_bind=1},
[107]={item_id=27133,num=1,is_bind=1},
[108]={item_id=27134,num=1,is_bind=1},
[109]={item_id=48117,num=1,is_bind=1},
[110]={item_id=27135,num=1,is_bind=1},
[111]={item_id=27136,num=1,is_bind=1},
[112]={item_id=38656,num=1,is_bind=1},
[113]={item_id=27137,num=1,is_bind=1},
[114]={item_id=27059,num=1,is_bind=1},
[115]={item_id=27060,num=1,is_bind=1},
[116]={item_id=38728,num=1,is_bind=1},
[117]={item_id=27062,num=1,is_bind=1},
[118]={item_id=27063,num=1,is_bind=1},
[119]={item_id=27064,num=1,is_bind=1},
[120]={item_id=27065,num=1,is_bind=1},
[121]={item_id=27066,num=1,is_bind=1},
[122]={item_id=27067,num=1,is_bind=1},
[123]={item_id=27068,num=1,is_bind=1},
[124]={item_id=27069,num=1,is_bind=1},
[125]={item_id=38729,num=1,is_bind=1},
[126]={item_id=26345,num=2,is_bind=1},
[127]={item_id=27070,num=1,is_bind=1},
[128]={item_id=27071,num=1,is_bind=1},
[129]={item_id=27072,num=1,is_bind=1},
[130]={item_id=28718,num=10,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
config_param={
{layer=1,},
{start_server_day=17,end_server_day=24,layer=2,},
{start_server_day=24,end_server_day=31,layer=3,},
{start_server_day=31,end_server_day=38,layer=4,},
{start_server_day=38,end_server_day=45,layer=5,},
{start_server_day=45,end_server_day=52,layer=6,},
{start_server_day=52,end_server_day=59,layer=7,},
{start_server_day=59,end_server_day=66,layer=8,},
{start_server_day=66,end_server_day=73,layer=9,},
{start_server_day=73,end_server_day=80,layer=10,},
{start_server_day=80,end_server_day=87,layer=11,},
{start_server_day=87,end_server_day=94,layer=12,},
{start_server_day=94,end_server_day=101,layer=13,},
{start_server_day=101,end_server_day=108,},
{start_server_day=108,end_server_day=115,},
{start_server_day=115,end_server_day=122,},
{start_server_day=122,end_server_day=129,},
{start_server_day=129,end_server_day=136,},
{start_server_day=136,end_server_day=143,},
{start_server_day=143,end_server_day=150,},
{start_server_day=150,end_server_day=157,},
{start_server_day=157,end_server_day=164,},
{start_server_day=164,end_server_day=171,},
{start_server_day=171,end_server_day=178,},
{start_server_day=178,end_server_day=185,},
{start_server_day=185,end_server_day=999,}
},

config_param_meta_table_map={
},
layer={
{},
{layer=2,},
{layer=3,},
{layer=4,},
{layer=5,},
{layer=6,},
{layer=7,},
{layer=8,turntable_circle_1="turntable_1_2",turntable_circle_2="turntable_1_1",pole="turntable_sp_gan_1",main_ca="fx_07_1",sp_title="turntable_sp_title_1",tips_di="bg_tip_di_1",},
{layer=9,},
{layer=10,},
{layer=11,},
{layer=12,},
{layer=13,},
{layer=14,},
{layer=15,},
{layer=16,},
{layer=17,},
{layer=18,},
{layer=19,}
},

layer_meta_table_map={
[6]=8,	-- depth:1
[18]=6,	-- depth:2
[12]=18,	-- depth:3
[4]=12,	-- depth:4
[14]=4,	-- depth:5
[16]=14,	-- depth:6
[2]=16,	-- depth:7
[10]=2,	-- depth:8
},
week={
{reward_pool_id="0,1",},
{layer=2,reward_pool_id="2,3",},
{layer=3,reward_pool_id="4,5",},
{layer=4,reward_pool_id="6,7",},
{layer=5,reward_pool_id="8,9",},
{layer=6,reward_pool_id="10,11",},
{layer=7,reward_pool_id="12,13",},
{layer=8,reward_pool_id="14,15",},
{layer=9,reward_pool_id="16,17",},
{layer=10,reward_pool_id="18,19",},
{layer=11,reward_pool_id="20,21",},
{layer=12,reward_pool_id="22,23",},
{layer=13,},
{layer=14,},
{layer=15,},
{layer=16,},
{layer=17,},
{layer=18,},
{layer=19,}
},

week_meta_table_map={
},
cost={
{},
{lower_limit_of_extraction=2,extract_upper_limit=2,draw_consume_item_count=2,},
{lower_limit_of_extraction=3,extract_upper_limit=3,draw_consume_item_count=4,},
{lower_limit_of_extraction=4,extract_upper_limit=4,draw_consume_item_count=8,},
{lower_limit_of_extraction=5,extract_upper_limit=5,draw_consume_item_count=10,},
{lower_limit_of_extraction=6,extract_upper_limit=6,draw_consume_item_count=15,},
{lower_limit_of_extraction=7,extract_upper_limit=7,draw_consume_item_count=20,},
{lower_limit_of_extraction=8,extract_upper_limit=8,draw_consume_item_count=30,},
{lower_limit_of_extraction=9,extract_upper_limit=9,draw_consume_item_count=40,},
{lower_limit_of_extraction=10,extract_upper_limit=10,draw_consume_item_count=50,},
{lower_limit_of_extraction=11,extract_upper_limit=11,draw_consume_item_count=70,},
{lower_limit_of_extraction=12,extract_upper_limit=12,draw_consume_item_count=90,},
{lower_limit_of_extraction=13,extract_upper_limit=13,draw_consume_item_count=110,},
{lower_limit_of_extraction=14,extract_upper_limit=14,draw_consume_item_count=130,},
{lower_limit_of_extraction=15,extract_upper_limit=15,draw_consume_item_count=160,},
{lower_limit_of_extraction=16,extract_upper_limit=16,draw_consume_item_count=200,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=3,},
{layer=3,},
{layer=3,},
{layer=3,},
{layer=3,},
{layer=3,},
{layer=3,},
{layer=3,},
{layer=3,},
{layer=3,},
{layer=3,},
{layer=3,},
{layer=3,},
{layer=3,},
{layer=3,},
{layer=3,},
{layer=4,},
{layer=4,},
{layer=4,},
{layer=4,},
{layer=4,},
{layer=4,},
{layer=4,},
{layer=4,},
{layer=4,},
{layer=4,},
{layer=4,},
{layer=4,},
{layer=4,},
{layer=4,},
{layer=4,},
{layer=4,},
{layer=5,},
{layer=5,},
{layer=5,},
{layer=5,},
{layer=5,},
{layer=5,},
{layer=5,},
{layer=5,},
{layer=5,},
{layer=5,},
{layer=5,},
{layer=5,},
{layer=5,},
{layer=5,},
{layer=5,},
{layer=5,},
{layer=6,},
{layer=6,},
{layer=6,},
{layer=6,},
{layer=6,},
{layer=6,},
{layer=6,},
{layer=6,},
{layer=6,},
{layer=6,},
{layer=6,},
{layer=6,},
{layer=6,},
{layer=6,},
{layer=6,},
{layer=6,},
{layer=7,},
{layer=7,},
{layer=7,},
{layer=7,},
{layer=7,},
{layer=7,},
{layer=7,},
{layer=7,},
{layer=7,},
{layer=7,},
{layer=7,},
{layer=7,},
{layer=7,},
{layer=7,},
{layer=7,},
{layer=7,},
{layer=8,},
{layer=8,},
{layer=8,},
{layer=8,},
{layer=8,},
{layer=8,},
{layer=8,},
{layer=8,},
{layer=8,},
{layer=8,},
{layer=8,},
{layer=8,},
{layer=8,},
{layer=8,},
{layer=8,},
{layer=8,},
{layer=9,},
{layer=9,},
{layer=9,},
{layer=9,},
{layer=9,},
{layer=9,},
{layer=9,},
{layer=9,},
{layer=9,},
{layer=9,},
{layer=9,},
{layer=9,},
{layer=9,},
{layer=9,},
{layer=9,},
{layer=9,},
{layer=10,},
{layer=10,},
{layer=10,},
{layer=10,},
{layer=10,},
{layer=10,},
{layer=10,},
{layer=10,},
{layer=10,},
{layer=10,},
{layer=10,},
{layer=10,},
{layer=10,},
{layer=10,},
{layer=10,},
{layer=10,},
{layer=11,},
{layer=11,},
{layer=11,},
{layer=11,},
{layer=11,},
{layer=11,},
{layer=11,},
{layer=11,},
{layer=11,},
{layer=11,},
{layer=11,},
{layer=11,},
{layer=11,},
{layer=11,},
{layer=11,},
{layer=11,},
{layer=12,},
{layer=12,},
{layer=12,},
{layer=12,},
{layer=12,},
{layer=12,},
{layer=12,},
{layer=12,},
{layer=12,},
{layer=12,},
{layer=12,},
{layer=12,},
{layer=12,},
{layer=12,},
{layer=12,},
{layer=12,},
{layer=13,},
{layer=13,},
{layer=13,},
{layer=13,},
{layer=13,},
{layer=13,},
{layer=13,},
{layer=13,},
{layer=13,},
{layer=13,},
{layer=13,},
{layer=13,},
{layer=13,},
{layer=13,},
{layer=13,},
{layer=13,},
{layer=14,},
{layer=14,},
{layer=14,},
{layer=14,},
{layer=14,},
{layer=14,},
{layer=14,},
{layer=14,},
{layer=14,},
{layer=14,},
{layer=14,},
{layer=14,},
{layer=14,},
{layer=14,},
{layer=14,},
{layer=14,},
{layer=15,},
{layer=15,},
{layer=15,},
{layer=15,},
{layer=15,},
{layer=15,},
{layer=15,},
{layer=15,},
{layer=15,},
{layer=15,},
{layer=15,},
{layer=15,},
{layer=15,},
{layer=15,},
{layer=15,},
{layer=15,},
{layer=16,},
{layer=16,},
{layer=16,},
{layer=16,},
{layer=16,},
{layer=16,},
{layer=16,},
{layer=16,},
{layer=16,},
{layer=16,},
{layer=16,},
{layer=16,},
{layer=16,},
{layer=16,},
{layer=16,},
{layer=16,},
{layer=17,},
{layer=17,},
{layer=17,},
{layer=17,},
{layer=17,},
{layer=17,},
{layer=17,},
{layer=17,},
{layer=17,},
{layer=17,},
{layer=17,},
{layer=17,},
{layer=17,},
{layer=17,},
{layer=17,},
{layer=17,},
{layer=18,},
{layer=18,},
{layer=18,},
{layer=18,},
{layer=18,},
{layer=18,},
{layer=18,},
{layer=18,},
{layer=18,},
{layer=18,},
{layer=18,},
{layer=18,},
{layer=18,},
{layer=18,},
{layer=18,},
{layer=18,},
{layer=19,},
{layer=19,},
{layer=19,},
{layer=19,},
{layer=19,},
{layer=19,},
{layer=19,},
{layer=19,},
{layer=19,},
{layer=19,},
{layer=19,},
{layer=19,},
{layer=19,},
{layer=19,},
{layer=19,},
{layer=19,}
},

cost_meta_table_map={
[211]=3,	-- depth:1
[206]=14,	-- depth:1
[205]=13,	-- depth:1
[207]=15,	-- depth:1
[208]=16,	-- depth:1
[204]=12,	-- depth:1
[210]=2,	-- depth:1
[215]=7,	-- depth:1
[213]=5,	-- depth:1
[214]=6,	-- depth:1
[216]=8,	-- depth:1
[217]=9,	-- depth:1
[218]=10,	-- depth:1
[219]=11,	-- depth:1
[203]=219,	-- depth:2
[220]=204,	-- depth:2
[221]=205,	-- depth:2
[222]=206,	-- depth:2
[223]=207,	-- depth:2
[224]=208,	-- depth:2
[226]=210,	-- depth:2
[227]=211,	-- depth:2
[212]=4,	-- depth:1
[202]=218,	-- depth:2
[192]=224,	-- depth:3
[200]=216,	-- depth:2
[167]=215,	-- depth:2
[168]=200,	-- depth:3
[169]=217,	-- depth:2
[170]=202,	-- depth:3
[171]=203,	-- depth:3
[172]=220,	-- depth:3
[173]=221,	-- depth:3
[174]=222,	-- depth:3
[175]=223,	-- depth:3
[176]=192,	-- depth:4
[178]=226,	-- depth:3
[179]=227,	-- depth:3
[180]=212,	-- depth:2
[181]=213,	-- depth:2
[182]=214,	-- depth:2
[183]=167,	-- depth:3
[184]=168,	-- depth:4
[199]=183,	-- depth:4
[198]=182,	-- depth:3
[197]=181,	-- depth:3
[196]=180,	-- depth:3
[195]=179,	-- depth:4
[194]=178,	-- depth:4
[201]=169,	-- depth:3
[228]=196,	-- depth:4
[190]=174,	-- depth:4
[189]=173,	-- depth:4
[188]=172,	-- depth:4
[187]=171,	-- depth:4
[186]=170,	-- depth:4
[185]=201,	-- depth:4
[191]=175,	-- depth:4
[229]=197,	-- depth:4
[242]=194,	-- depth:5
[231]=199,	-- depth:5
[270]=190,	-- depth:5
[271]=191,	-- depth:5
[272]=176,	-- depth:5
[274]=242,	-- depth:6
[275]=195,	-- depth:5
[276]=228,	-- depth:5
[277]=229,	-- depth:5
[278]=198,	-- depth:4
[279]=231,	-- depth:6
[280]=184,	-- depth:5
[281]=185,	-- depth:5
[282]=186,	-- depth:5
[283]=187,	-- depth:5
[284]=188,	-- depth:5
[285]=189,	-- depth:5
[286]=270,	-- depth:6
[287]=271,	-- depth:6
[302]=286,	-- depth:7
[301]=285,	-- depth:6
[300]=284,	-- depth:6
[299]=283,	-- depth:6
[298]=282,	-- depth:6
[297]=281,	-- depth:6
[269]=301,	-- depth:7
[296]=280,	-- depth:6
[294]=278,	-- depth:5
[293]=277,	-- depth:6
[292]=276,	-- depth:6
[291]=275,	-- depth:6
[290]=274,	-- depth:7
[288]=272,	-- depth:6
[295]=279,	-- depth:7
[268]=300,	-- depth:7
[267]=299,	-- depth:7
[266]=298,	-- depth:7
[246]=294,	-- depth:6
[245]=293,	-- depth:7
[244]=292,	-- depth:7
[243]=291,	-- depth:7
[166]=246,	-- depth:7
[240]=288,	-- depth:7
[247]=295,	-- depth:8
[239]=287,	-- depth:7
[237]=269,	-- depth:8
[236]=268,	-- depth:8
[235]=267,	-- depth:8
[234]=266,	-- depth:8
[233]=297,	-- depth:7
[232]=296,	-- depth:7
[238]=302,	-- depth:8
[230]=166,	-- depth:8
[248]=232,	-- depth:8
[250]=234,	-- depth:9
[265]=233,	-- depth:8
[264]=248,	-- depth:9
[263]=247,	-- depth:9
[262]=230,	-- depth:9
[261]=245,	-- depth:8
[260]=244,	-- depth:8
[249]=265,	-- depth:9
[259]=243,	-- depth:8
[256]=240,	-- depth:8
[255]=239,	-- depth:8
[254]=238,	-- depth:9
[253]=237,	-- depth:9
[252]=236,	-- depth:9
[251]=235,	-- depth:9
[258]=290,	-- depth:8
[165]=261,	-- depth:9
[152]=264,	-- depth:10
[163]=259,	-- depth:9
[55]=263,	-- depth:10
[56]=152,	-- depth:11
[57]=249,	-- depth:10
[58]=250,	-- depth:10
[59]=251,	-- depth:10
[60]=252,	-- depth:10
[61]=253,	-- depth:10
[62]=254,	-- depth:10
[63]=255,	-- depth:9
[64]=256,	-- depth:9
[66]=258,	-- depth:9
[67]=163,	-- depth:10
[68]=260,	-- depth:9
[69]=165,	-- depth:10
[70]=262,	-- depth:10
[71]=55,	-- depth:11
[72]=56,	-- depth:12
[87]=71,	-- depth:12
[86]=70,	-- depth:11
[85]=69,	-- depth:11
[84]=68,	-- depth:10
[83]=67,	-- depth:11
[82]=66,	-- depth:10
[54]=86,	-- depth:12
[80]=64,	-- depth:10
[78]=62,	-- depth:11
[77]=61,	-- depth:11
[76]=60,	-- depth:11
[75]=59,	-- depth:11
[74]=58,	-- depth:11
[73]=57,	-- depth:11
[79]=63,	-- depth:10
[88]=72,	-- depth:13
[53]=85,	-- depth:12
[51]=83,	-- depth:12
[18]=82,	-- depth:11
[19]=51,	-- depth:13
[20]=84,	-- depth:11
[21]=53,	-- depth:13
[22]=54,	-- depth:13
[23]=87,	-- depth:13
[24]=88,	-- depth:14
[25]=73,	-- depth:12
[26]=74,	-- depth:12
[27]=75,	-- depth:12
[28]=76,	-- depth:12
[29]=77,	-- depth:12
[30]=78,	-- depth:12
[31]=79,	-- depth:11
[32]=80,	-- depth:11
[34]=18,	-- depth:12
[35]=19,	-- depth:14
[50]=34,	-- depth:13
[48]=32,	-- depth:12
[47]=31,	-- depth:12
[46]=30,	-- depth:13
[45]=29,	-- depth:13
[44]=28,	-- depth:13
[52]=20,	-- depth:12
[43]=27,	-- depth:13
[41]=25,	-- depth:13
[40]=24,	-- depth:15
[39]=23,	-- depth:14
[38]=22,	-- depth:14
[37]=21,	-- depth:14
[36]=52,	-- depth:13
[42]=26,	-- depth:13
[164]=36,	-- depth:14
[89]=41,	-- depth:14
[91]=43,	-- depth:14
[130]=50,	-- depth:14
[131]=35,	-- depth:15
[132]=164,	-- depth:15
[133]=37,	-- depth:15
[134]=38,	-- depth:15
[135]=39,	-- depth:15
[136]=40,	-- depth:16
[137]=89,	-- depth:15
[138]=42,	-- depth:14
[139]=91,	-- depth:15
[140]=44,	-- depth:14
[141]=45,	-- depth:14
[142]=46,	-- depth:14
[143]=47,	-- depth:13
[144]=48,	-- depth:13
[146]=130,	-- depth:15
[147]=131,	-- depth:16
[162]=146,	-- depth:16
[160]=144,	-- depth:14
[159]=143,	-- depth:14
[158]=142,	-- depth:15
[157]=141,	-- depth:15
[156]=140,	-- depth:15
[128]=160,	-- depth:15
[155]=139,	-- depth:16
[153]=137,	-- depth:16
[303]=159,	-- depth:15
[151]=135,	-- depth:16
[150]=134,	-- depth:16
[149]=133,	-- depth:16
[148]=132,	-- depth:16
[154]=138,	-- depth:15
[90]=154,	-- depth:16
[127]=303,	-- depth:16
[125]=157,	-- depth:16
[92]=156,	-- depth:16
[93]=125,	-- depth:17
[94]=158,	-- depth:16
[95]=127,	-- depth:17
[96]=128,	-- depth:16
[98]=162,	-- depth:17
[99]=147,	-- depth:17
[100]=148,	-- depth:17
[101]=149,	-- depth:17
[102]=150,	-- depth:17
[103]=151,	-- depth:17
[104]=136,	-- depth:17
[105]=153,	-- depth:17
[106]=90,	-- depth:17
[107]=155,	-- depth:17
[108]=92,	-- depth:17
[109]=93,	-- depth:18
[124]=108,	-- depth:18
[123]=107,	-- depth:18
[122]=106,	-- depth:18
[121]=105,	-- depth:18
[120]=104,	-- depth:18
[119]=103,	-- depth:18
[126]=94,	-- depth:17
[118]=102,	-- depth:18
[116]=100,	-- depth:18
[115]=99,	-- depth:18
[114]=98,	-- depth:18
[112]=96,	-- depth:17
[111]=95,	-- depth:18
[110]=126,	-- depth:18
[117]=101,	-- depth:18
[304]=112,	-- depth:18
},
reward={
{backgroud="a2_zxtfgx_kp1",x=78.66,y=-216.13,rewrad_rare_show=0.205,},
{reward_id=2,pow="1,1,60|2,2,75|3,3,60|4,16,50",reward_item=item_table[1],show_name="装备合鸣石",backgroud="a2_zxtfgx_kp2",x=-226.5,y=39.94,rewrad_rare_show=0.1308,},
{reward_id=3,pow="1,2,50|3,3,75|4,16,50",reward_item=item_table[2],show_name="太玄九天玉",x=-78.66,y=-216.13,rewrad_rare_show=0.1012,},
{reward_id=4,pow="1,3,5|4,4,75|5,16,50",reward_item=item_table[3],show_name="3级攻击玄晶",backgroud="a2_zxtfgx_kp4",x=147.84,y=176.18,rewrad_rare_show=0.0952,},
{reward_id=5,pow="1,3,5|4,4,50|5,5,75|6,16,50",reward_item=item_table[4],show_name="3级防御玄晶",backgroud="a2_zxtfgx_kp5",x=199.19,y=-115,rewrad_rare_show=0.0902,},
{reward_id=6,pow="1,3,1|4,5,5|6,6,75|7,16,50",reward_item=item_table[5],show_name="完美合鸣石自选包",backgroud="a2_zxtfgx_kp6",x=-199.19,y=-123.5,rewrad_rare_show=0.0902,},
{reward_id=7,pow="1,3,1|4,5,5|6,6,50|7,7,75|8,16,50",reward_item=item_table[6],show_name="红神灵神格随机",backgroud="a2_zxtfgx_kp7",x=226.5,y=39.94,rewrad_rare_show=0.1243,},
{reward_id=8,pow="4,5,1|6,7,5|8,8,75|9,16,50",reward_item=item_table[7],show_name="龙神神格",backgroud="a2_zxtfgx_kp8",x=77.94,rewrad_rare_show=0.0389,},
{reward_id=9,pow="4,5,1|6,7,5|8,8,50|9,9,75|10,16,50",reward_item=item_table[8],show_name="倚栏听风盔",backgroud="a2_zxtfgx_kp9",y=230,rewrad_rare_show=0.0389,},
{reward_id=10,pow="6,7,1|8,9,5|10,10,75|11,16,50",reward_item=item_table[9],show_name="图腾祭炼石·宠",backgroud="a2_zxtfgx_kp10",x=-77.94,y=-45,rewrad_rare_show=0.0361,},
{reward_id=11,pow="6,7,1|8,9,5|10,10,50|11,11,75|12,16,50",reward_item=item_table[10],show_name="济世灵花",backgroud="a2_zxtfgx_kp11",x=-147.84,y=176.18,rewrad_rare_show=0.0156,},
{reward_id=12,pow="8,9,1|10,11,5|12,16,50",reward_item=item_table[11],show_name="深渊神龙·宠",backgroud="a2_zxtfgx_kp12",is_best_reward=1,is_best=1,rank=1,},
{reward_id=13,pow="11,12,1|13,16,50",reward_item=item_table[12],show_name="倚栏听风衣",backgroud="a2_zxtfgx_kp1",is_best_reward=1,is_best=1,rank=1,},
{reward_id=14,pow="12,13,1|14,16,50",reward_item=item_table[13],show_name="倚栏听风裤",backgroud="a2_zxtfgx_kp2",is_best_reward=1,is_best=1,rank=1,},
{reward_id=15,pow="13,14,1|15,16,50",reward_item=item_table[14],show_name="倚栏听风链",is_best_reward=1,is_best=1,rank=1,},
{reward_id=16,pow="14,15,1|16,16,50",reward_item=item_table[15],show_name="东海太子",is_best_reward=1,is_best=1,rank=1,rewrad_rare_show=0.0036,},
{reward_pool_id=1,reward_id=17,},
{reward_pool_id=1,reward_id=18,},
{reward_pool_id=1,reward_id=19,},
{reward_pool_id=1,reward_id=20,},
{reward_pool_id=1,reward_id=21,},
{reward_pool_id=1,reward_id=22,},
{reward_pool_id=1,reward_id=23,},
{reward_pool_id=1,reward_id=24,reward_item=item_table[16],show_name="倚栏听风鞋",},
{reward_pool_id=1,reward_id=25,reward_item=item_table[17],show_name="混沌冥虎·宠",},
{reward_pool_id=1,reward_id=26,},
{reward_pool_id=1,reward_id=27,},
{reward_pool_id=1,reward_id=28,},
{reward_pool_id=1,reward_id=29,reward_item=item_table[18],show_name="倚栏听风武",},
{reward_pool_id=1,reward_id=30,reward_item=item_table[19],show_name="倚栏听风坠",},
{reward_pool_id=1,reward_id=31,},
{reward_pool_id=1,reward_id=32,reward_item=item_table[20],show_name="倚栏听风腕",},
{reward_pool_id=2,reward_id=33,},
{reward_pool_id=2,reward_id=34,},
{reward_pool_id=2,reward_id=35,},
{reward_pool_id=2,reward_id=36,},
{reward_pool_id=2,reward_id=37,},
{reward_pool_id=2,reward_id=38,},
{reward_pool_id=2,reward_id=39,},
{reward_pool_id=2,reward_id=40,},
{reward_pool_id=2,reward_id=41,reward_item=item_table[21],show_name="夜观天象盔",},
{reward_pool_id=2,reward_id=42,},
{reward_pool_id=2,reward_id=43,},
{reward_pool_id=2,reward_id=44,reward_item=item_table[22],show_name="轩辕白泽·宠",},
{reward_pool_id=2,reward_id=45,},
{reward_pool_id=2,reward_id=46,},
{reward_pool_id=2,reward_id=47,reward_item=item_table[23],show_name="夜观天象链",},
{reward_pool_id=2,reward_id=48,},
{reward_pool_id=3,reward_id=49,},
{reward_pool_id=3,reward_id=50,},
{reward_pool_id=3,reward_id=51,},
{reward_pool_id=3,reward_id=52,},
{reward_pool_id=3,reward_id=53,},
{reward_pool_id=3,reward_id=54,},
{reward_pool_id=3,reward_id=55,},
{reward_pool_id=3,reward_id=56,},
{reward_pool_id=3,reward_id=57,},
{reward_pool_id=3,reward_id=58,},
{reward_pool_id=3,reward_id=59,},
{reward_pool_id=3,reward_id=60,reward_item=item_table[24],show_name="玲珑之狐·宠",},
{reward_pool_id=3,reward_id=61,},
{reward_pool_id=3,reward_id=62,},
{reward_pool_id=3,reward_id=63,},
{reward_pool_id=3,reward_id=64,},
{reward_pool_id=4,reward_id=65,},
{reward_pool_id=4,reward_id=66,},
{reward_pool_id=4,reward_id=67,},
{reward_pool_id=4,reward_id=68,},
{reward_pool_id=4,reward_id=69,},
{reward_pool_id=4,reward_id=70,},
{reward_pool_id=4,reward_id=71,},
{reward_pool_id=4,reward_id=72,},
{reward_pool_id=4,reward_id=73,},
{reward_pool_id=4,reward_id=74,},
{reward_pool_id=4,reward_id=75,},
{reward_pool_id=4,reward_id=76,reward_item=item_table[25],show_name="雷霆巨鹿·宠",},
{reward_pool_id=4,reward_id=77,},
{reward_pool_id=4,reward_id=78,},
{reward_pool_id=4,reward_id=79,},
{reward_pool_id=4,reward_id=80,},
{reward_pool_id=5,reward_id=81,},
{reward_pool_id=5,reward_id=82,},
{reward_pool_id=5,reward_id=83,},
{reward_pool_id=5,reward_id=84,},
{reward_pool_id=5,reward_id=85,},
{reward_pool_id=5,reward_id=86,},
{reward_pool_id=5,reward_id=87,},
{reward_pool_id=5,reward_id=88,},
{reward_pool_id=5,reward_id=89,reward_item=item_table[26],show_name="上古灵鱼·宠",},
{reward_pool_id=5,reward_id=90,},
{reward_pool_id=5,reward_id=91,},
{reward_pool_id=5,reward_id=92,},
{reward_pool_id=5,reward_id=93,},
{reward_pool_id=5,reward_id=94,},
{reward_pool_id=5,reward_id=95,reward_item=item_table[27],show_name="圣精灵",},
{reward_pool_id=5,reward_id=96,reward_item=item_table[28],show_name="济世破妄腕",},
{reward_pool_id=6,reward_id=97,},
{reward_pool_id=6,reward_id=98,},
{reward_pool_id=6,reward_id=99,},
{reward_pool_id=6,reward_id=100,},
{reward_pool_id=6,reward_id=101,},
{reward_pool_id=6,reward_id=102,},
{reward_pool_id=6,reward_id=103,},
{reward_pool_id=6,reward_id=104,},
{reward_pool_id=6,reward_id=105,reward_item=item_table[29],show_name="神王大乘盔",},
{reward_pool_id=6,reward_id=106,},
{reward_pool_id=6,reward_id=107,},
{reward_pool_id=6,reward_id=108,reward_item=item_table[30],},
{reward_pool_id=6,reward_id=109,reward_item=item_table[31],show_name="神王大乘衣",},
{reward_pool_id=6,reward_id=110,reward_item=item_table[32],show_name="神王大乘裤",},
{reward_pool_id=6,reward_id=111,reward_item=item_table[33],show_name="神王大乘链",},
{reward_pool_id=6,reward_id=112,},
{reward_pool_id=7,reward_id=113,},
{reward_pool_id=7,reward_id=114,},
{reward_pool_id=7,reward_id=115,},
{reward_pool_id=7,reward_id=116,},
{reward_pool_id=7,reward_id=117,},
{reward_pool_id=7,reward_id=118,},
{reward_pool_id=7,reward_id=119,},
{reward_pool_id=7,reward_id=120,},
{reward_pool_id=7,reward_id=121,reward_item=item_table[34],show_name="神王大乘鞋",},
{reward_pool_id=7,reward_id=122,},
{reward_pool_id=7,reward_id=123,},
{reward_pool_id=7,reward_id=124,reward_item=item_table[35],show_name="混沌冥虎·宠",},
{reward_pool_id=7,reward_id=125,reward_item=item_table[36],show_name="神王大乘武",},
{reward_pool_id=7,reward_id=126,reward_item=item_table[37],show_name="神王大乘坠",},
{reward_pool_id=7,reward_id=127,reward_item=item_table[38],show_name="齐天大圣",},
{reward_pool_id=7,reward_id=128,reward_item=item_table[39],show_name="神王大乘腕",},
{reward_pool_id=8,reward_id=129,},
{reward_pool_id=8,reward_id=130,},
{reward_pool_id=8,reward_id=131,},
{reward_pool_id=8,reward_id=132,},
{reward_pool_id=8,reward_id=133,},
{reward_pool_id=8,reward_id=134,reward_item=item_table[40],show_name="神帝羽化盔",},
{reward_pool_id=8,reward_id=135,},
{reward_pool_id=8,reward_id=136,},
{reward_pool_id=8,reward_id=137,reward_item=item_table[41],show_name="轩辕白泽·宠",},
{reward_pool_id=8,reward_id=138,},
{reward_pool_id=8,reward_id=139,},
{reward_pool_id=8,reward_id=140,},
{reward_pool_id=8,reward_id=141,reward_item=item_table[42],show_name="神帝羽化衣",},
{reward_pool_id=8,reward_id=142,reward_item=item_table[43],show_name="神帝羽化裤",},
{reward_pool_id=8,reward_id=143,reward_item=item_table[44],show_name="神帝羽化链",},
{reward_pool_id=8,reward_id=144,},
{reward_pool_id=9,reward_id=145,},
{reward_pool_id=9,reward_id=146,},
{reward_pool_id=9,reward_id=147,},
{reward_pool_id=9,reward_id=148,},
{reward_pool_id=9,reward_id=149,},
{reward_pool_id=9,reward_id=150,},
{reward_pool_id=9,reward_id=151,},
{reward_pool_id=9,reward_id=152,},
{reward_pool_id=9,reward_id=153,reward_item=item_table[45],show_name="神帝羽化鞋",},
{reward_pool_id=9,reward_id=154,},
{reward_pool_id=9,reward_id=155,},
{reward_pool_id=9,reward_id=156,reward_item=item_table[46],},
{reward_pool_id=9,reward_id=157,reward_item=item_table[47],show_name="神帝羽化武",},
{reward_pool_id=9,reward_id=158,reward_item=item_table[48],show_name="神帝羽化坠",},
{reward_pool_id=9,reward_id=159,},
{reward_pool_id=9,reward_id=160,reward_item=item_table[49],show_name="神帝羽化腕",},
{reward_pool_id=10,reward_id=161,},
{reward_pool_id=10,reward_id=162,},
{reward_pool_id=10,reward_id=163,},
{reward_pool_id=10,reward_id=164,},
{reward_pool_id=10,reward_id=165,},
{reward_pool_id=10,reward_id=166,},
{reward_pool_id=10,reward_id=167,},
{reward_pool_id=10,reward_id=168,},
{reward_pool_id=10,reward_id=169,reward_item=item_table[50],show_name="紫薇登仙盔",},
{reward_pool_id=10,reward_id=170,},
{reward_pool_id=10,reward_id=171,},
{reward_pool_id=10,reward_id=172,reward_item=item_table[51],},
{reward_pool_id=10,reward_id=173,reward_item=item_table[52],show_name="紫薇登仙衣",},
{reward_pool_id=10,reward_id=174,reward_item=item_table[53],show_name="紫薇登仙裤",},
{reward_pool_id=10,reward_id=175,reward_item=item_table[54],show_name="紫薇登仙链",},
{reward_pool_id=10,reward_id=176,},
{reward_pool_id=11,reward_id=177,},
{reward_pool_id=11,reward_id=178,},
{reward_pool_id=11,reward_id=179,},
{reward_pool_id=11,reward_id=180,},
{reward_pool_id=11,reward_id=181,},
{reward_pool_id=11,reward_id=182,},
{reward_pool_id=11,reward_id=183,},
{reward_pool_id=11,reward_id=184,reward_item=item_table[55],show_name="紫薇登仙鞋",},
{reward_pool_id=11,reward_id=185,reward_item=item_table[56],},
{reward_pool_id=11,reward_id=186,},
{reward_pool_id=11,reward_id=187,},
{reward_pool_id=11,reward_id=188,},
{reward_pool_id=11,reward_id=189,reward_item=item_table[57],show_name="紫薇登仙武",},
{reward_pool_id=11,reward_id=190,reward_item=item_table[58],show_name="紫薇登仙坠",},
{reward_pool_id=11,reward_id=191,},
{reward_pool_id=11,reward_id=192,reward_item=item_table[59],show_name="紫薇登仙腕",},
{reward_pool_id=12,reward_id=193,},
{reward_pool_id=12,reward_id=194,},
{reward_pool_id=12,reward_id=195,},
{reward_pool_id=12,reward_id=196,},
{reward_pool_id=12,reward_id=197,},
{reward_pool_id=12,reward_id=198,},
{reward_pool_id=12,reward_id=199,},
{reward_pool_id=12,reward_id=200,},
{reward_pool_id=12,reward_id=201,reward_item=item_table[60],show_name="长生涅槃盔",},
{reward_pool_id=12,reward_id=202,},
{reward_pool_id=12,reward_id=203,},
{reward_pool_id=12,reward_id=204,reward_item=item_table[61],show_name="深渊神龙·骑",},
{reward_pool_id=12,reward_id=205,reward_item=item_table[62],show_name="长生涅槃衣",},
{reward_pool_id=12,reward_id=206,reward_item=item_table[63],show_name="长生涅槃裤",},
{reward_pool_id=12,reward_id=207,reward_item=item_table[64],show_name="长生涅槃链",},
{reward_pool_id=12,reward_id=208,},
{reward_pool_id=13,reward_id=209,},
{reward_pool_id=13,reward_id=210,},
{reward_pool_id=13,reward_id=211,},
{reward_pool_id=13,reward_id=212,},
{reward_pool_id=13,reward_id=213,},
{reward_pool_id=13,reward_id=214,},
{reward_pool_id=13,reward_id=215,},
{reward_pool_id=13,reward_id=216,},
{reward_pool_id=13,reward_id=217,reward_item=item_table[65],show_name="长生涅槃鞋",},
{reward_pool_id=13,reward_id=218,},
{reward_pool_id=13,reward_id=219,},
{reward_pool_id=13,reward_id=220,reward_item=item_table[66],show_name="混沌冥虎·骑",},
{reward_pool_id=13,reward_id=221,reward_item=item_table[67],show_name="长生涅槃武",},
{reward_pool_id=13,reward_id=222,reward_item=item_table[68],show_name="长生涅槃坠",},
{reward_pool_id=13,reward_id=223,reward_item=item_table[69],show_name="天道石",},
{reward_pool_id=13,reward_id=224,reward_item=item_table[70],show_name="长生涅槃腕",},
{reward_pool_id=14,reward_id=225,},
{reward_pool_id=14,reward_id=226,},
{reward_pool_id=14,reward_id=227,},
{reward_pool_id=14,reward_id=228,},
{reward_pool_id=14,reward_id=229,},
{reward_pool_id=14,reward_id=230,},
{reward_pool_id=14,reward_id=231,},
{reward_pool_id=14,reward_id=232,},
{reward_pool_id=14,reward_id=233,reward_item=item_table[71],show_name="承神灵灵盔",},
{reward_pool_id=14,reward_id=234,},
{reward_pool_id=14,reward_id=235,},
{reward_pool_id=14,reward_id=236,reward_item=item_table[72],show_name="轩辕白泽·骑",},
{reward_pool_id=14,reward_id=237,reward_item=item_table[73],show_name="承神灵灵衣",},
{reward_pool_id=14,reward_id=238,reward_item=item_table[74],show_name="承神灵灵裤",},
{reward_pool_id=14,reward_id=239,reward_item=item_table[75],show_name="承神灵灵链",},
{reward_pool_id=14,reward_id=240,},
{reward_pool_id=15,reward_id=241,},
{reward_pool_id=15,reward_id=242,},
{reward_pool_id=15,reward_id=243,},
{reward_pool_id=15,reward_id=244,},
{reward_pool_id=15,reward_id=245,},
{reward_pool_id=15,reward_id=246,},
{reward_pool_id=15,reward_id=247,},
{reward_pool_id=15,reward_id=248,reward_item=item_table[76],show_name="承神灵灵鞋",},
{reward_pool_id=15,reward_id=249,reward_item=item_table[77],show_name="玲珑之狐·骑",},
{reward_pool_id=15,reward_id=250,},
{reward_pool_id=15,reward_id=251,},
{reward_pool_id=15,reward_id=252,},
{reward_pool_id=15,reward_id=253,reward_item=item_table[78],show_name="承神灵专武",},
{reward_pool_id=15,reward_id=254,reward_item=item_table[79],show_name="承神灵灵坠",},
{reward_pool_id=15,reward_id=255,},
{reward_pool_id=15,reward_id=256,reward_item=item_table[80],show_name="承神灵灵腕",},
{reward_pool_id=16,reward_id=257,},
{reward_pool_id=16,reward_id=258,},
{reward_pool_id=16,reward_id=259,},
{reward_pool_id=16,reward_id=260,},
{reward_pool_id=16,reward_id=261,},
{reward_pool_id=16,reward_id=262,},
{reward_pool_id=16,reward_id=263,},
{reward_pool_id=16,reward_id=264,},
{reward_pool_id=16,reward_id=265,reward_item=item_table[81],show_name="道德伪仙盔",},
{reward_pool_id=16,reward_id=266,},
{reward_pool_id=16,reward_id=267,},
{reward_pool_id=16,reward_id=268,reward_item=item_table[82],show_name="雷霆巨鹿·骑",},
{reward_pool_id=16,reward_id=269,reward_item=item_table[83],show_name="道德伪仙衣",},
{reward_pool_id=16,reward_id=270,reward_item=item_table[84],show_name="道德伪仙裤",},
{reward_pool_id=16,reward_id=271,reward_item=item_table[85],show_name="道德伪仙链",},
{reward_pool_id=16,reward_id=272,reward_item=item_table[86],show_name="花仙使者",},
{reward_pool_id=17,reward_id=273,},
{reward_pool_id=17,reward_id=274,},
{reward_pool_id=17,reward_id=275,},
{reward_pool_id=17,reward_id=276,},
{reward_pool_id=17,reward_id=277,},
{reward_pool_id=17,reward_id=278,},
{reward_pool_id=17,reward_id=279,},
{reward_pool_id=17,reward_id=280,},
{reward_pool_id=17,reward_id=281,reward_item=item_table[87],show_name="道德伪仙鞋",},
{reward_pool_id=17,reward_id=282,},
{reward_pool_id=17,reward_id=283,},
{reward_pool_id=17,reward_id=284,reward_item=item_table[88],show_name="上古灵鱼·骑",},
{reward_pool_id=17,reward_id=285,reward_item=item_table[89],show_name="道德伪仙武",},
{reward_pool_id=17,reward_id=286,reward_item=item_table[90],show_name="道德伪仙坠",},
{reward_pool_id=17,reward_id=287,},
{reward_pool_id=17,reward_id=288,reward_item=item_table[91],show_name="道德伪仙腕",},
{reward_pool_id=18,reward_id=289,},
{reward_pool_id=18,reward_id=290,},
{reward_pool_id=18,reward_id=291,},
{reward_pool_id=18,reward_id=292,},
{reward_pool_id=18,reward_id=293,},
{reward_pool_id=18,reward_id=294,reward_item=item_table[92],show_name="鸿蒙真仙盔",},
{reward_pool_id=18,reward_id=295,},
{reward_pool_id=18,reward_id=296,},
{reward_pool_id=18,reward_id=297,reward_item=item_table[93],show_name="上古灵鱼·骑",},
{reward_pool_id=18,reward_id=298,},
{reward_pool_id=18,reward_id=299,},
{reward_pool_id=18,reward_id=300,reward_item=item_table[5],show_name="完美合鸣石自选包",},
{reward_pool_id=18,reward_id=301,reward_item=item_table[94],show_name="鸿蒙真仙衣",},
{reward_pool_id=18,reward_id=302,reward_item=item_table[95],show_name="鸿蒙真仙裤",},
{reward_pool_id=18,reward_id=303,reward_item=item_table[96],show_name="鸿蒙真仙链",},
{reward_pool_id=18,reward_id=304,reward_item=item_table[97],show_name="火灵使者",},
{reward_pool_id=19,reward_id=305,},
{reward_pool_id=19,reward_id=306,},
{reward_pool_id=19,reward_id=307,},
{reward_pool_id=19,reward_id=308,},
{reward_pool_id=19,reward_id=309,},
{reward_pool_id=19,reward_id=310,},
{reward_pool_id=19,reward_id=311,},
{reward_pool_id=19,reward_id=312,},
{reward_pool_id=19,reward_id=313,reward_item=item_table[98],show_name="鸿蒙真仙鞋",},
{reward_pool_id=19,reward_id=314,},
{reward_pool_id=19,reward_id=315,},
{reward_pool_id=19,reward_id=316,reward_item=item_table[99],},
{reward_pool_id=19,reward_id=317,reward_item=item_table[100],show_name="鸿蒙真仙武",},
{reward_pool_id=19,reward_id=318,reward_item=item_table[101],show_name="鸿蒙真仙坠",},
{reward_pool_id=19,reward_id=319,reward_item=item_table[102],show_name="裁判所",},
{reward_pool_id=19,reward_id=320,reward_item=item_table[103],show_name="鸿蒙真仙腕",},
{reward_pool_id=20,reward_id=321,},
{reward_pool_id=20,reward_id=322,},
{reward_pool_id=20,reward_id=323,},
{reward_pool_id=20,reward_id=324,},
{reward_pool_id=20,reward_id=325,},
{reward_pool_id=20,reward_id=326,},
{reward_pool_id=20,reward_id=327,},
{reward_pool_id=20,reward_id=328,},
{reward_pool_id=20,reward_id=329,reward_item=item_table[104],show_name="元始上仙盔",},
{reward_pool_id=20,reward_id=330,},
{reward_pool_id=20,reward_id=331,},
{reward_pool_id=20,reward_id=332,},
{reward_pool_id=20,reward_id=333,reward_item=item_table[105],show_name="元始上仙衣",},
{reward_pool_id=20,reward_id=334,reward_item=item_table[106],show_name="元始上仙裤",},
{reward_pool_id=20,reward_id=335,reward_item=item_table[107],show_name="元始上仙链",},
{reward_pool_id=20,reward_id=336,},
{reward_pool_id=21,reward_id=337,},
{reward_pool_id=21,reward_id=338,},
{reward_pool_id=21,reward_id=339,},
{reward_pool_id=21,reward_id=340,},
{reward_pool_id=21,reward_id=341,},
{reward_pool_id=21,reward_id=342,},
{reward_pool_id=21,reward_id=343,},
{reward_pool_id=21,reward_id=344,reward_item=item_table[108],show_name="元始上仙鞋",},
{reward_pool_id=21,reward_id=345,reward_item=item_table[109],show_name="红3星天魂法器自选包",},
{reward_pool_id=21,reward_id=346,},
{reward_pool_id=21,reward_id=347,},
{reward_pool_id=21,reward_id=348,},
{reward_pool_id=21,reward_id=349,reward_item=item_table[110],show_name="元始上仙武",},
{reward_pool_id=21,reward_id=350,reward_item=item_table[111],show_name="元始上仙坠",},
{reward_pool_id=21,reward_id=351,reward_item=item_table[112],show_name="柠檬甜",},
{reward_pool_id=21,reward_id=352,reward_item=item_table[113],show_name="元始上仙腕",},
{reward_pool_id=22,reward_id=353,},
{reward_pool_id=22,reward_id=354,},
{reward_pool_id=22,reward_id=355,},
{reward_pool_id=22,reward_id=356,},
{reward_pool_id=22,reward_id=357,},
{reward_pool_id=22,reward_id=358,},
{reward_pool_id=22,reward_id=359,},
{reward_pool_id=22,reward_id=360,},
{reward_pool_id=22,reward_id=361,},
{reward_pool_id=22,reward_id=362,},
{reward_pool_id=22,reward_id=363,},
{reward_pool_id=22,reward_id=364,},
{reward_pool_id=22,reward_id=365,reward_item=item_table[114],show_name="夜观天象衣",},
{reward_pool_id=22,reward_id=366,reward_item=item_table[115],show_name="夜观天象裤",},
{reward_pool_id=22,reward_id=367,},
{reward_pool_id=22,reward_id=368,reward_item=item_table[116],show_name="道门小狐",},
{reward_pool_id=23,reward_id=369,},
{reward_pool_id=23,reward_id=370,},
{reward_pool_id=23,reward_id=371,},
{reward_pool_id=23,reward_id=372,},
{reward_pool_id=23,reward_id=373,},
{reward_pool_id=23,reward_id=374,},
{reward_pool_id=23,reward_id=375,},
{reward_pool_id=23,reward_id=376,},
{reward_pool_id=23,reward_id=377,reward_item=item_table[117],show_name="夜观天象鞋",},
{reward_pool_id=23,reward_id=378,},
{reward_pool_id=23,reward_id=379,},
{reward_pool_id=23,reward_id=380,},
{reward_pool_id=23,reward_id=381,reward_item=item_table[118],show_name="夜观天象武",},
{reward_pool_id=23,reward_id=382,reward_item=item_table[119],show_name="夜观天象坠",},
{reward_pool_id=23,reward_id=383,},
{reward_pool_id=23,reward_id=384,reward_item=item_table[120],show_name="夜观天象腕",},
{reward_pool_id=24,reward_id=385,},
{reward_pool_id=24,reward_id=386,},
{reward_pool_id=24,reward_id=387,},
{reward_pool_id=24,reward_id=388,},
{reward_pool_id=24,reward_id=389,},
{reward_pool_id=24,reward_id=390,},
{reward_pool_id=24,reward_id=391,},
{reward_pool_id=24,reward_id=392,},
{reward_pool_id=24,reward_id=393,reward_item=item_table[121],show_name="济世破妄盔",},
{reward_pool_id=24,reward_id=394,},
{reward_pool_id=24,reward_id=395,},
{reward_pool_id=24,reward_id=396,reward_item=item_table[109],show_name="红3星天魂法器自选包",},
{reward_pool_id=24,reward_id=397,reward_item=item_table[122],show_name="济世破妄衣",},
{reward_pool_id=24,reward_id=398,reward_item=item_table[123],show_name="济世破妄裤",},
{reward_pool_id=24,reward_id=399,reward_item=item_table[124],show_name="济世破妄链",},
{reward_pool_id=24,reward_id=400,reward_item=item_table[125],show_name="花灵使者",},
{reward_pool_id=25,reward_id=401,},
{reward_pool_id=25,reward_id=402,},
{reward_pool_id=25,reward_id=403,},
{reward_pool_id=25,reward_id=404,reward_item=item_table[126],show_name="高级灵骑神石",},
{reward_pool_id=25,reward_id=405,},
{reward_pool_id=25,reward_id=406,},
{reward_pool_id=25,reward_id=407,},
{reward_pool_id=25,reward_id=408,reward_item=item_table[127],show_name="济世破妄鞋",},
{reward_pool_id=25,reward_id=409,},
{reward_pool_id=25,reward_id=410,},
{reward_pool_id=25,reward_id=411,},
{reward_pool_id=25,reward_id=412,reward_item=item_table[7],show_name="龙神神格",},
{reward_pool_id=25,reward_id=413,reward_item=item_table[128],show_name="济世破妄武",},
{reward_pool_id=25,reward_id=414,reward_item=item_table[129],show_name="济世破妄坠",},
{reward_pool_id=25,reward_id=415,},
{reward_pool_id=25,reward_id=416,}
},

reward_meta_table_map={
[401]=1,	-- depth:1
[177]=401,	-- depth:2
[33]=401,	-- depth:2
[161]=401,	-- depth:2
[289]=401,	-- depth:2
[145]=401,	-- depth:2
[385]=401,	-- depth:2
[49]=401,	-- depth:2
[305]=401,	-- depth:2
[129]=401,	-- depth:2
[321]=401,	-- depth:2
[113]=401,	-- depth:2
[65]=401,	-- depth:2
[369]=401,	-- depth:2
[337]=401,	-- depth:2
[97]=401,	-- depth:2
[353]=401,	-- depth:2
[273]=401,	-- depth:2
[193]=401,	-- depth:2
[81]=401,	-- depth:2
[257]=401,	-- depth:2
[241]=401,	-- depth:2
[225]=401,	-- depth:2
[209]=401,	-- depth:2
[17]=401,	-- depth:2
[121]=9,	-- depth:1
[239]=15,	-- depth:1
[319]=15,	-- depth:1
[323]=3,	-- depth:1
[233]=9,	-- depth:1
[127]=15,	-- depth:1
[329]=9,	-- depth:1
[217]=9,	-- depth:1
[195]=3,	-- depth:1
[335]=15,	-- depth:1
[131]=3,	-- depth:1
[223]=15,	-- depth:1
[105]=9,	-- depth:1
[339]=3,	-- depth:1
[99]=3,	-- depth:1
[95]=15,	-- depth:1
[345]=9,	-- depth:1
[89]=9,	-- depth:1
[351]=15,	-- depth:1
[227]=3,	-- depth:1
[355]=3,	-- depth:1
[111]=15,	-- depth:1
[313]=9,	-- depth:1
[243]=3,	-- depth:1
[307]=3,	-- depth:1
[191]=351,	-- depth:2
[265]=9,	-- depth:1
[185]=89,	-- depth:2
[271]=15,	-- depth:1
[255]=95,	-- depth:2
[201]=9,	-- depth:1
[179]=3,	-- depth:1
[275]=3,	-- depth:1
[249]=9,	-- depth:1
[175]=15,	-- depth:1
[281]=9,	-- depth:1
[169]=9,	-- depth:1
[287]=127,	-- depth:2
[163]=3,	-- depth:1
[207]=15,	-- depth:1
[83]=3,	-- depth:1
[291]=3,	-- depth:1
[153]=9,	-- depth:1
[297]=9,	-- depth:1
[147]=3,	-- depth:1
[415]=95,	-- depth:2
[303]=15,	-- depth:1
[211]=3,	-- depth:1
[143]=15,	-- depth:1
[259]=3,	-- depth:1
[137]=9,	-- depth:1
[159]=319,	-- depth:2
[115]=3,	-- depth:1
[409]=345,	-- depth:2
[399]=15,	-- depth:1
[387]=3,	-- depth:1
[371]=3,	-- depth:1
[63]=223,	-- depth:2
[31]=351,	-- depth:2
[47]=15,	-- depth:1
[19]=3,	-- depth:1
[51]=3,	-- depth:1
[393]=9,	-- depth:1
[383]=223,	-- depth:2
[377]=9,	-- depth:1
[403]=3,	-- depth:1
[57]=377,	-- depth:2
[367]=47,	-- depth:2
[67]=3,	-- depth:1
[79]=399,	-- depth:2
[41]=9,	-- depth:1
[361]=41,	-- depth:2
[25]=9,	-- depth:1
[73]=393,	-- depth:2
[35]=3,	-- depth:1
[283]=11,	-- depth:1
[404]=4,	-- depth:1
[269]=13,	-- depth:1
[270]=14,	-- depth:1
[398]=14,	-- depth:1
[278]=6,	-- depth:1
[286]=14,	-- depth:1
[268]=12,	-- depth:1
[279]=7,	-- depth:1
[397]=13,	-- depth:1
[402]=2,	-- depth:1
[400]=16,	-- depth:1
[276]=404,	-- depth:2
[285]=13,	-- depth:1
[274]=2,	-- depth:1
[284]=12,	-- depth:1
[272]=16,	-- depth:1
[277]=5,	-- depth:1
[357]=5,	-- depth:1
[263]=7,	-- depth:1
[235]=11,	-- depth:1
[236]=12,	-- depth:1
[237]=13,	-- depth:1
[238]=14,	-- depth:1
[240]=400,	-- depth:2
[414]=14,	-- depth:1
[242]=2,	-- depth:1
[413]=13,	-- depth:1
[244]=404,	-- depth:2
[245]=5,	-- depth:1
[246]=6,	-- depth:1
[247]=7,	-- depth:1
[267]=11,	-- depth:1
[251]=11,	-- depth:1
[253]=13,	-- depth:1
[254]=14,	-- depth:1
[412]=12,	-- depth:1
[256]=16,	-- depth:1
[411]=11,	-- depth:1
[258]=2,	-- depth:1
[407]=7,	-- depth:1
[260]=404,	-- depth:2
[406]=6,	-- depth:1
[261]=5,	-- depth:1
[405]=5,	-- depth:1
[262]=6,	-- depth:1
[252]=412,	-- depth:2
[396]=12,	-- depth:1
[292]=404,	-- depth:2
[288]=16,	-- depth:1
[324]=4,	-- depth:1
[325]=5,	-- depth:1
[326]=6,	-- depth:1
[327]=7,	-- depth:1
[372]=404,	-- depth:2
[331]=11,	-- depth:1
[370]=2,	-- depth:1
[332]=396,	-- depth:2
[333]=13,	-- depth:1
[334]=14,	-- depth:1
[336]=16,	-- depth:1
[368]=16,	-- depth:1
[338]=2,	-- depth:1
[366]=14,	-- depth:1
[340]=4,	-- depth:1
[341]=5,	-- depth:1
[365]=13,	-- depth:1
[342]=6,	-- depth:1
[343]=7,	-- depth:1
[364]=396,	-- depth:2
[363]=11,	-- depth:1
[347]=11,	-- depth:1
[348]=396,	-- depth:2
[349]=13,	-- depth:1
[350]=14,	-- depth:1
[359]=7,	-- depth:1
[352]=16,	-- depth:1
[358]=6,	-- depth:1
[354]=2,	-- depth:1
[373]=5,	-- depth:1
[374]=6,	-- depth:1
[322]=2,	-- depth:1
[375]=7,	-- depth:1
[391]=7,	-- depth:1
[290]=2,	-- depth:1
[390]=6,	-- depth:1
[356]=404,	-- depth:2
[293]=5,	-- depth:1
[294]=6,	-- depth:1
[389]=5,	-- depth:1
[295]=7,	-- depth:1
[388]=404,	-- depth:2
[299]=11,	-- depth:1
[300]=12,	-- depth:1
[301]=13,	-- depth:1
[302]=14,	-- depth:1
[386]=2,	-- depth:1
[395]=11,	-- depth:1
[384]=16,	-- depth:1
[306]=2,	-- depth:1
[308]=404,	-- depth:2
[382]=14,	-- depth:1
[309]=5,	-- depth:1
[310]=6,	-- depth:1
[311]=7,	-- depth:1
[381]=13,	-- depth:1
[315]=11,	-- depth:1
[380]=396,	-- depth:2
[316]=268,	-- depth:2
[379]=11,	-- depth:1
[317]=13,	-- depth:1
[318]=14,	-- depth:1
[320]=16,	-- depth:1
[304]=16,	-- depth:1
[231]=7,	-- depth:1
[208]=368,	-- depth:2
[229]=5,	-- depth:1
[102]=6,	-- depth:1
[101]=5,	-- depth:1
[100]=404,	-- depth:2
[98]=2,	-- depth:1
[96]=16,	-- depth:1
[94]=414,	-- depth:2
[93]=413,	-- depth:2
[92]=412,	-- depth:2
[91]=11,	-- depth:1
[87]=7,	-- depth:1
[86]=6,	-- depth:1
[85]=5,	-- depth:1
[84]=404,	-- depth:2
[82]=2,	-- depth:1
[80]=400,	-- depth:2
[103]=7,	-- depth:1
[107]=11,	-- depth:1
[108]=12,	-- depth:1
[109]=13,	-- depth:1
[133]=5,	-- depth:1
[132]=404,	-- depth:2
[130]=2,	-- depth:1
[128]=16,	-- depth:1
[126]=14,	-- depth:1
[125]=13,	-- depth:1
[124]=12,	-- depth:1
[78]=398,	-- depth:2
[123]=11,	-- depth:1
[119]=7,	-- depth:1
[118]=6,	-- depth:1
[117]=5,	-- depth:1
[116]=404,	-- depth:2
[114]=2,	-- depth:1
[112]=272,	-- depth:2
[110]=14,	-- depth:1
[230]=6,	-- depth:1
[134]=6,	-- depth:1
[77]=397,	-- depth:2
[75]=11,	-- depth:1
[39]=7,	-- depth:1
[38]=6,	-- depth:1
[37]=5,	-- depth:1
[36]=404,	-- depth:2
[34]=2,	-- depth:1
[32]=16,	-- depth:1
[30]=14,	-- depth:1
[29]=13,	-- depth:1
[28]=396,	-- depth:2
[27]=11,	-- depth:1
[23]=7,	-- depth:1
[22]=6,	-- depth:1
[21]=5,	-- depth:1
[20]=4,	-- depth:1
[18]=2,	-- depth:1
[43]=11,	-- depth:1
[44]=12,	-- depth:1
[45]=365,	-- depth:2
[46]=366,	-- depth:2
[71]=7,	-- depth:1
[70]=6,	-- depth:1
[69]=5,	-- depth:1
[68]=404,	-- depth:2
[66]=2,	-- depth:1
[64]=384,	-- depth:2
[62]=382,	-- depth:2
[76]=12,	-- depth:1
[61]=381,	-- depth:2
[59]=11,	-- depth:1
[55]=7,	-- depth:1
[54]=6,	-- depth:1
[53]=5,	-- depth:1
[52]=404,	-- depth:2
[50]=2,	-- depth:1
[48]=368,	-- depth:2
[60]=12,	-- depth:1
[135]=7,	-- depth:1
[416]=96,	-- depth:2
[203]=11,	-- depth:1
[167]=7,	-- depth:1
[222]=14,	-- depth:1
[221]=13,	-- depth:1
[220]=12,	-- depth:1
[219]=11,	-- depth:1
[199]=7,	-- depth:1
[196]=404,	-- depth:2
[171]=11,	-- depth:1
[172]=76,	-- depth:2
[173]=13,	-- depth:1
[215]=7,	-- depth:1
[214]=6,	-- depth:1
[166]=6,	-- depth:1
[174]=14,	-- depth:1
[178]=2,	-- depth:1
[180]=4,	-- depth:1
[181]=5,	-- depth:1
[182]=6,	-- depth:1
[183]=7,	-- depth:1
[213]=5,	-- depth:1
[194]=2,	-- depth:1
[212]=404,	-- depth:2
[192]=16,	-- depth:1
[187]=11,	-- depth:1
[190]=14,	-- depth:1
[189]=13,	-- depth:1
[176]=16,	-- depth:1
[210]=2,	-- depth:1
[165]=5,	-- depth:1
[198]=6,	-- depth:1
[139]=11,	-- depth:1
[140]=300,	-- depth:2
[141]=13,	-- depth:1
[142]=14,	-- depth:1
[144]=304,	-- depth:2
[146]=2,	-- depth:1
[148]=404,	-- depth:2
[149]=5,	-- depth:1
[150]=6,	-- depth:1
[204]=12,	-- depth:1
[197]=5,	-- depth:1
[151]=7,	-- depth:1
[228]=404,	-- depth:2
[206]=14,	-- depth:1
[155]=11,	-- depth:1
[226]=2,	-- depth:1
[156]=60,	-- depth:2
[224]=16,	-- depth:1
[157]=13,	-- depth:1
[158]=14,	-- depth:1
[160]=16,	-- depth:1
[162]=2,	-- depth:1
[164]=4,	-- depth:1
[205]=13,	-- depth:1
[188]=396,	-- depth:2
[10]=16,	-- depth:1
[8]=10,	-- depth:2
[250]=10,	-- depth:2
[218]=10,	-- depth:2
[42]=10,	-- depth:2
[40]=8,	-- depth:3
[392]=8,	-- depth:3
[394]=10,	-- depth:2
[216]=8,	-- depth:3
[26]=10,	-- depth:2
[24]=8,	-- depth:3
[408]=8,	-- depth:3
[234]=10,	-- depth:2
[410]=10,	-- depth:2
[232]=8,	-- depth:3
[248]=8,	-- depth:3
[136]=8,	-- depth:3
[362]=10,	-- depth:2
[200]=8,	-- depth:3
[312]=8,	-- depth:3
[314]=10,	-- depth:2
[298]=10,	-- depth:2
[122]=10,	-- depth:2
[296]=8,	-- depth:3
[152]=8,	-- depth:3
[154]=10,	-- depth:2
[328]=8,	-- depth:3
[330]=10,	-- depth:2
[106]=10,	-- depth:2
[104]=8,	-- depth:3
[168]=8,	-- depth:3
[282]=10,	-- depth:2
[170]=10,	-- depth:2
[344]=8,	-- depth:3
[280]=8,	-- depth:3
[346]=10,	-- depth:2
[56]=8,	-- depth:3
[378]=10,	-- depth:2
[58]=10,	-- depth:2
[376]=8,	-- depth:3
[264]=8,	-- depth:3
[266]=10,	-- depth:2
[202]=10,	-- depth:2
[72]=8,	-- depth:3
[138]=10,	-- depth:2
[186]=10,	-- depth:2
[360]=8,	-- depth:3
[184]=8,	-- depth:3
[88]=408,	-- depth:4
[90]=10,	-- depth:2
[74]=10,	-- depth:2
[120]=8,	-- depth:3
},
record={
{},
{prop_id=43805,},
{prop_id=39142,},
{prop_id=26667,},
{prop_id=26682,},
{prop_id=48071,},
{prop_id=44050,},
{prop_id=27838,},
{prop_id=27050,},
{prop_id=47097,},
{prop_id=29621,},
{prop_id=47052,},
{prop_id=27051,},
{prop_id=27052,},
{prop_id=27053,},
{prop_id=38727,},
{prop_id=27054,},
{prop_id=47060,},
{prop_id=48117,},
{prop_id=27055,},
{prop_id=27056,},
{prop_id=38656,},
{prop_id=27057,},
{prop_id=26345,},
{prop_id=27058,},
{prop_id=47068,},
{prop_id=27059,},
{prop_id=27060,},
{prop_id=27061,},
{prop_id=38728,},
{prop_id=27062,},
{prop_id=47076,},
{prop_id=27063,},
{prop_id=27064,},
{prop_id=38665,},
{prop_id=27065,},
{prop_id=27066,},
{prop_id=47084,},
{prop_id=27067,},
{prop_id=27068,},
{prop_id=27069,},
{prop_id=38729,},
{prop_id=27070,},
{prop_id=47092,},
{prop_id=27071,},
{prop_id=27072,},
{prop_id=38666,},
{prop_id=27073,},
{prop_id=27074,},
{prop_id=47053,},
{prop_id=27075,},
{prop_id=27076,},
{prop_id=27077,},
{prop_id=38739,},
{prop_id=27078,},
{prop_id=47061,},
{prop_id=27079,},
{prop_id=27080,},
{prop_id=38667,},
{prop_id=27081,},
{prop_id=27082,},
{prop_id=47069,},
{prop_id=27083,},
{prop_id=27084,},
{prop_id=27085,},
{prop_id=38734,},
{prop_id=27086,},
{prop_id=47077,},
{prop_id=27087,},
{prop_id=27088,},
{prop_id=38668,},
{prop_id=27089,},
{prop_id=27090,},
{prop_id=47085,},
{prop_id=27091,},
{prop_id=27092,},
{prop_id=27093,},
{prop_id=27094,},
{prop_id=47093,},
{prop_id=27095,},
{prop_id=27096,},
{prop_id=27097,},
{prop_id=27098,},
{prop_id=47005,},
{prop_id=27099,},
{prop_id=27100,},
{prop_id=27101,},
{prop_id=27102,},
{prop_id=47013,},
{prop_id=27103,},
{prop_id=27104,},
{prop_id=27105,},
{prop_id=27106,},
{prop_id=47023,},
{prop_id=27107,},
{prop_id=27108,},
{prop_id=27109,},
{prop_id=27110,},
{prop_id=47029,},
{prop_id=27111,},
{prop_id=27112,},
{prop_id=27113,},
{prop_id=27114,},
{prop_id=47036,},
{prop_id=27115,},
{prop_id=27116,},
{prop_id=27117,},
{prop_id=27118,},
{prop_id=47044,},
{prop_id=27119,},
{prop_id=27120,},
{prop_id=27121,},
{prop_id=27122,},
{prop_id=47045,},
{prop_id=27123,},
{prop_id=27124,},
{prop_id=27125,},
{prop_id=27126,},
{prop_id=47037,},
{prop_id=27127,},
{prop_id=27128,},
{prop_id=27129,},
{prop_id=27130,},
{prop_id=27131,},
{prop_id=27132,},
{prop_id=27133,},
{prop_id=27134,},
{prop_id=27135,},
{prop_id=27136,},
{prop_id=27137,}
},

record_meta_table_map={
},
other_default_table={open_role_level=100,rot_time=6,},

config_param_default_table={start_server_day=10,end_server_day=17,week_index=2,layer=14,},

layer_default_table={layer=1,name="翻牌好礼",round_count=0,round_continue_time_h=48,remaining_incentive_limit=0,is_open_one_key_lottery=0,is_open_one_key_vip=6,draw_consume_item_id=46556,conmsum_xianyu=40,is_auto_refresh=1,color_outline_nor="#b43449",color_nor_gradient1="#c49fa7",color_nor_gradient2="#c49a83",color_outline_high="#ec3c2a",color_high_gradient1="#ffffff",color_high_gradient2="#fff2a7",layer_btn_nor="",layer_btn_high="",turntable_circle_1="turntable_8_2",turntable_circle_2="turntable_8_1",turntable_circle_3="",yaogan="layer_gan_3",renwu="layer_renwu_1",pole="turntable_sp_gan",x=-326,y=251.1,left_decorate="",reward_record_title="bg_1",botton_plate="flower_record_bg",air_bubble="desc",tips="已抽取到得奖励，将从牌库中移除，奖励概率公示：",main_ca="fx_07",sp_title="turntable_sp_title",tips_di="bg_tip_di",},

week_default_table={layer=1,round_count=0,reward_pool_id="24,25",continue_time="24,24",},

cost_default_table={layer=1,lower_limit_of_extraction=1,extract_upper_limit=1,draw_consume_item_count=1,},

reward_default_table={reward_pool_id=0,reward_id=1,pow="1,1,75|2,2,60|3,16,50",reward_item=item_table[130],show_name="玉魄附魂礼包",backgroud="a2_zxtfgx_kp3",show_id=10,is_best_reward=0,is_best=0,x=0,y=90,rank=2,rewrad_rare_show=0.0075,},

record_default_table={prop_id=28718,record_copy="<color=#bb01c3>%s</color>在<color=#ff5a01>%s</color>中探得<color=%s>%s</color>*%d个！可喜可贺！",push_copy="恭喜{r;%d;%s;%d}在{wordsColor;ff5a00;%s}中获得{i;%d}！{Link_name;【我也试试】} {openLink;545}",}

}

