-- Y-运营活动-灵力之种.xls
local item_table={
[1]={item_id=48071,num=1,is_bind=1},
[2]={item_id=44183,num=1,is_bind=1},
[3]={item_id=48500,num=1,is_bind=1},
[4]={item_id=48501,num=1,is_bind=1},
[5]={item_id=26501,num=1,is_bind=1},
[6]={item_id=26516,num=1,is_bind=1},
[7]={item_id=26372,num=1,is_bind=1},
[8]={item_id=26371,num=1,is_bind=1},
[9]={item_id=26370,num=1,is_bind=1},
[10]={item_id=26375,num=1,is_bind=1},
[11]={item_id=26374,num=1,is_bind=1},
[12]={item_id=26200,num=2,is_bind=1},
[13]={item_id=26203,num=2,is_bind=1},
[14]={item_id=44182,num=1,is_bind=1},
[15]={item_id=47586,num=1,is_bind=1},
[16]={item_id=26517,num=1,is_bind=1},
[17]={item_id=26502,num=1,is_bind=1},
[18]={item_id=26373,num=2,is_bind=1},
[19]={item_id=28448,num=1,is_bind=1},
[20]={item_id=28666,num=1,is_bind=1},
[21]={item_id=28665,num=1,is_bind=1},
[22]={item_id=28446,num=1,is_bind=1},
[23]={item_id=28447,num=1,is_bind=1},
[24]={item_id=56317,num=1,is_bind=1},
[25]={item_id=56316,num=1,is_bind=1},
[26]={item_id=37223,num=1,is_bind=1},
[27]={item_id=37672,num=1,is_bind=1},
[28]={item_id=37856,num=1,is_bind=1},
[29]={item_id=38787,num=1,is_bind=1},
[30]={item_id=37426,num=1,is_bind=1},
[31]={item_id=48509,num=1,is_bind=1},
[32]={item_id=48508,num=1,is_bind=1},
[33]={item_id=48507,num=1,is_bind=1},
[34]={item_id=48505,num=5,is_bind=1},
[35]={item_id=48506,num=5,is_bind=1},
[36]={item_id=22009,num=1,is_bind=1},
[37]={item_id=38456,num=1,is_bind=1},
[38]={item_id=47587,num=5,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_day={
{}
},

open_day_meta_table_map={
},
mode={
{},
{mode=2,times=10,cost_item_num=10,add_lucky=10,},
{mode=3,times=50,cost_item_num=50,add_lucky=50,}
},

mode_meta_table_map={
},
reward_pool={
{show_item=0,},
{seq=1,item=item_table[1],},
{seq=2,item=item_table[2],},
{seq=3,item=item_table[3],},
{seq=4,item=item_table[4],},
{seq=5,item=item_table[5],},
{seq=6,item=item_table[6],},
{seq=7,item=item_table[7],},
{seq=8,item=item_table[8],},
{seq=9,item=item_table[9],},
{seq=10,item=item_table[10],},
{seq=11,item=item_table[11],},
{seq=12,item=item_table[12],},
{seq=13,item=item_table[13],},
{seq=14,item=item_table[14],},
{seq=15,item=item_table[15],},
{seq=16,item=item_table[16],},
{seq=17,item=item_table[17],},
{seq=18,item=item_table[18],},
{seq=19,item=item_table[19],},
{seq=20,item=item_table[20],},
{seq=21,item=item_table[21],},
{seq=22,item=item_table[22],},
{seq=23,item=item_table[23],is_rare=1,},
{seq=24,item=item_table[24],},
{seq=25,item=item_table[25],}
},

reward_pool_meta_table_map={
[22]=1,	-- depth:1
[13]=22,	-- depth:2
[15]=22,	-- depth:2
[25]=24,	-- depth:1
[10]=22,	-- depth:2
[9]=22,	-- depth:2
[7]=22,	-- depth:2
[6]=22,	-- depth:2
[5]=22,	-- depth:2
[3]=24,	-- depth:1
[16]=24,	-- depth:1
[26]=22,	-- depth:2
},
baodi={
{}
},

baodi_meta_table_map={
},
convert={
{stuff_num_1=15,},
{seq=1,item=item_table[26],stuff_num_1=2,},
{seq=2,item=item_table[27],},
{seq=3,item=item_table[28],},
{seq=4,item=item_table[29],stuff_num_1=4,},
{seq=5,item=item_table[30],stuff_num_1=3,},
{seq=6,item=item_table[31],},
{seq=7,item=item_table[32],},
{seq=8,time_limit=2,item=item_table[33],}
},

convert_meta_table_map={
[7]=6,	-- depth:1
[8]=2,	-- depth:1
},
times_reward={
{},
{seq=1,need_draw_times=100,item=item_table[34],},
{seq=2,need_draw_times=150,},
{seq=3,need_draw_times=200,item=item_table[35],},
{seq=4,need_draw_times=888,item=item_table[30],}
},

times_reward_meta_table_map={
},
item_random_desc={
{random_count=9.5,},
{number=1,item_id=48071,random_count=0.6,},
{number=2,item_id=44183,},
{number=3,item_id=48500,},
{number=4,item_id=48501,},
{number=5,item_id=26501,},
{number=6,item_id=26516,},
{number=7,item_id=26372,random_count=0.4,},
{number=8,item_id=26371,},
{number=9,item_id=26370,random_count=12.7,},
{number=10,item_id=26375,},
{number=11,item_id=26374,},
{number=12,item_id=26200,random_count=7.6,},
{number=13,item_id=26203,random_count=3.8,},
{number=14,item_id=44182,random_count=1.3,},
{number=15,item_id=47586,},
{number=16,item_id=26517,},
{number=17,item_id=26502,},
{number=18,item_id=26373,},
{number=19,item_id=28448,random_count=0.1,},
{number=20,item_id=28666,},
{number=21,item_id=28665,},
{number=22,item_id=28446,},
{number=23,item_id=28447,},
{number=24,item_id=56317,},
{number=25,item_id=56316,}
},

item_random_desc_meta_table_map={
[3]=2,	-- depth:1
[22]=1,	-- depth:1
[21]=8,	-- depth:1
[18]=2,	-- depth:1
[17]=2,	-- depth:1
[16]=20,	-- depth:1
[25]=20,	-- depth:1
[11]=20,	-- depth:1
[6]=2,	-- depth:1
[7]=2,	-- depth:1
},
other_default_table={cost_item_id=47587,cost_gold=40,},

open_day_default_table={start_day=1,end_day=9999,grade=1,},

mode_default_table={mode=1,times=1,cost_item_num=1,add_lucky=1,},

reward_pool_default_table={grade=1,seq=0,item=item_table[36],is_rare=0,show_item=1,},

baodi_default_table={grade=1,need_lucky=360,item=item_table[15],},

convert_default_table={grade=1,seq=0,time_limit=1,item=item_table[37],stuff_id_1=47586,stuff_num_1=1,stuff_id_2=0,stuff_num_2=0,stuff_id_3=0,stuff_num_3=0,},

times_reward_default_table={grade=1,seq=0,need_draw_times=50,item=item_table[38],},

item_random_desc_default_table={grade=1,number=0,item_id=22009,random_count=6.3,}

}

