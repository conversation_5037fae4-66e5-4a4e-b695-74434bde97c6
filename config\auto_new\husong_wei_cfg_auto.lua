-- H-护送配置(伪护送).xls

return {
task_color_flush_prob={
{},
{task_color=2,},
{task_color=3,need_flush_item_num=1,},
{task_color=4,need_flush_item_num=2,},
{task_color=5,need_flush_item_num=3,}
},

task_color_flush_prob_meta_table_map={
},
other={
{}
},

other_meta_table_map={
},
task_reward_list={
{}
},

task_reward_list_meta_table_map={
},
task_reward_factor_list={
[1]={task_color=1,scale="",},
[2]={task_color=2,task_name="灵儿",exp_factor=40,coin_factor=40,resource_ID=5022001,},
[3]={task_color=3,task_name="熏儿",exp_factor=60,coin_factor=60,resource_ID=3045001,},
[4]={task_color=4,task_name="雪琪",exp_factor=80,coin_factor=80,resource_ID=3040001,},
[5]={task_color=5,task_name="玄女",exp_factor=100,coin_factor=100,resource_ID=4005001,}
},

task_reward_factor_list_meta_table_map={
},
buy_times_cfg={
[1]={buy_times=1,},
[2]={buy_times=2,gold_cost=100,}
},

buy_times_cfg_meta_table_map={
},
task_cfg={
{refresh_comsume_num=-1,max_guarantee_time=1,model_scale="",qipao_start="气泡文字显示",qipao_end_pos="",qipao_end="气泡文字显示",obj_scale="",},
{task_color=2,refresh_weight="0|60|20|20",task_name="小灵儿",exp_factor=60,coin_factor=60,model_bundle_name="model/npc/2059_prefab",model_asset_name=2059,npc_head=330510,model_scale=0.9,whole_display_pos="0,-60",husong_desc="小灵儿",qipao_desc="好久不见，要与我回宗门看看吗？",model_rot="0,25,0",},
{task_color=3,refresh_weight="0|40|35|25",task_name="云汐师姐",exp_factor=40,coin_factor=40,model_bundle_name="model/npc/2062_prefab",model_asset_name=2062,npc_head=330474,whole_display_pos="10,-40",husong_desc="云熙师姐",qipao_desc="我要成为猫猫治安队长",model_rot="0,15,0",},
{task_color=4,refresh_weight="0|0|45|55",task_name="人鱼公主",exp_factor=80,coin_factor=80,model_bundle_name="model/yushou/4046_prefab",model_asset_name=4046,npc_head=338667,whole_display_pos="20,-60",husong_desc="人鱼公主",qipao_desc="守护正义与和平！",model_rot="0,0,0",},
{task_color=5,max_guarantee_time=0,task_name="扶苏花神",exp_factor=100,coin_factor=100,model_bundle_name="model/tianshen/10103_prefab",model_asset_name=10103,npc_head=327803,model_scale=1.2,whole_display_pos="80,0",husong_desc="扶苏花神",qipao_desc="兰陵竟如此壮阔...",model_rot="0,20,0",}
},

task_cfg_meta_table_map={
},
task_reward_item={
{},
{min_limit_level=201,max_limit_level=300,},
{min_limit_level=301,max_limit_level=400,},
{min_limit_level=401,max_limit_level=500,},
{min_limit_level=501,max_limit_level=600,},
{min_limit_level=601,max_limit_level=700,},
{min_limit_level=701,max_limit_level=800,},
{min_limit_level=801,max_limit_level=1000,},
{task_color=2,},
{task_color=2,},
{task_color=2,},
{task_color=2,},
{task_color=2,},
{task_color=2,},
{task_color=2,},
{task_color=2,},
{task_color=3,},
{task_color=3,},
{task_color=3,},
{task_color=3,},
{task_color=3,},
{task_color=3,},
{task_color=3,},
{task_color=3,},
{task_color=4,},
{task_color=4,},
{task_color=4,},
{task_color=4,},
{task_color=4,},
{task_color=4,},
{task_color=4,},
{task_color=4,},
{task_color=5,},
{task_color=5,},
{task_color=5,},
{task_color=5,},
{task_color=5,},
{task_color=5,},
{task_color=5,},
{task_color=5,}
},

task_reward_item_meta_table_map={
[38]=6,	-- depth:1
[37]=5,	-- depth:1
[26]=2,	-- depth:1
[35]=3,	-- depth:1
[34]=26,	-- depth:2
[32]=8,	-- depth:1
[31]=7,	-- depth:1
[30]=38,	-- depth:2
[29]=37,	-- depth:2
[28]=4,	-- depth:1
[27]=35,	-- depth:2
[36]=28,	-- depth:2
[20]=36,	-- depth:3
[23]=31,	-- depth:2
[22]=30,	-- depth:3
[21]=29,	-- depth:3
[39]=23,	-- depth:3
[19]=27,	-- depth:3
[18]=34,	-- depth:3
[16]=32,	-- depth:2
[15]=39,	-- depth:4
[14]=22,	-- depth:4
[13]=21,	-- depth:4
[12]=20,	-- depth:4
[11]=19,	-- depth:4
[10]=18,	-- depth:4
[24]=16,	-- depth:3
[40]=24,	-- depth:4
},
task_color_flush_prob_default_table={task_color=1,need_flush_item_num=0,},

refresh_tocolor_maxtimes_default_table={},

other_default_table={flush_itemid=26165,pretask_id=10,max_rob_count=3,free_refresh_times=3,husong_times=3,insurance_gold=10,insurance_percent=120,orange_reward_gold_bind=30,protect_skill_last_time=6,protect_skill_dec_hurt_per=50,npc_ed1=10249,npc_scene1=102,npc_id=10303,npc_scene2=103,task_id=825,pos_x=193,pos_y=464,price_type=3,},

task_reward_list_default_table={min_limit_level=1,max_limit_level=9999,commit_exp=440000000,commit_bind_coin=78000,},

task_reward_factor_list_default_table={task_color=1,task_name=1,exp_factor=1,coin_factor=1,resource_ID=1,scale=2,},

buy_times_cfg_default_table={buy_times=1,gold_cost=50,},

task_cfg_default_table={task_color=1,refresh_comsume_id=26165,refresh_comsume_num=1,refresh_weight="0|0|0|0",max_guarantee_time=3,task_name=1,exp_factor=1,coin_factor=1,model_bundle_name=1,model_asset_name=1,npc_head="",model_scale=1,whole_display_pos="",husong_desc="巡哨至青衣姑娘处",seq=20008,qipao_desc="气泡文字显示",model_rot="",qipao_start="出发气泡",qipao_end_pos="0,20",qipao_end="出发气泡",obj_scale=1,},

task_reward_item_default_table={task_color=1,min_limit_level=1,max_limit_level=200,reward_item_id=0,reward_item_num=0,}

}

