-- G-冠绝征战.xls
local item_table={
[1]={item_id=28665,num=1,is_bind=1},
[2]={item_id=56316,num=1,is_bind=1},
[3]={item_id=26200,num=1,is_bind=1},
[4]={item_id=26203,num=1,is_bind=1},
[5]={item_id=26415,num=1,is_bind=1},
[6]={item_id=27830,num=1,is_bind=1},
[7]={item_id=26444,num=1,is_bind=1},
[8]={item_id=26421,num=1,is_bind=1},
[9]={item_id=26450,num=1,is_bind=1},
[10]={item_id=28666,num=1,is_bind=1},
[11]={item_id=56317,num=1,is_bind=1},
[12]={item_id=26455,num=1,is_bind=1},
[13]={item_id=57854,num=4,is_bind=1},
[14]={item_id=26462,num=1,is_bind=1},
[15]={item_id=28454,num=2,is_bind=1},
[16]={item_id=48507,num=2,is_bind=1},
[17]={item_id=29478,num=2,is_bind=1},
[18]={item_id=57854,num=3,is_bind=1},
[19]={item_id=26459,num=1,is_bind=1},
[20]={item_id=28454,num=1,is_bind=1},
[21]={item_id=48506,num=2,is_bind=1},
[22]={item_id=29478,num=1,is_bind=1},
[23]={item_id=57854,num=2,is_bind=1},
[24]={item_id=26460,num=1,is_bind=1},
[25]={item_id=28453,num=2,is_bind=1},
[26]={item_id=48505,num=2,is_bind=1},
[27]={item_id=29477,num=2,is_bind=1},
[28]={item_id=27830,num=5,is_bind=1},
[29]={item_id=57999,num=5,is_bind=1},
[30]={item_id=27741,num=200,is_bind=1},
[31]={item_id=27831,num=1,is_bind=1},
[32]={item_id=26505,num=1,is_bind=1},
[33]={item_id=28454,num=5,is_bind=1},
[34]={item_id=27830,num=3,is_bind=1},
[35]={item_id=57999,num=3,is_bind=1},
[36]={item_id=27741,num=100,is_bind=1},
[37]={item_id=26504,num=1,is_bind=1},
[38]={item_id=28454,num=3,is_bind=1},
[39]={item_id=26437,num=1,is_bind=1},
[40]={item_id=57854,num=5,is_bind=1},
[41]={item_id=26463,num=1,is_bind=1},
[42]={item_id=48508,num=2,is_bind=1},
[43]={item_id=29478,num=3,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
camp={
[0]={seq=0,},
[1]={seq=1,born_pos="14,103",camp_name="虹琉域",camp_icon="a3_gjzz_bq1",}
},

camp_meta_table_map={
},
gather={
{type=1,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{gather_id=2055,gather_refresh_pos="74,150|62,69|141,54|151,137",},
{gather_id=2056,gather_refresh_pos="66,141|71,59|148,65|144,144",},
{gather_id=2057,param1=1024,param2=120,gather_refresh_pos="59,135|79,52|155,75|134,155",},
{gather_id=2058,param1=1025,param2=129,gather_refresh_pos="72,143|144,142|71,60|141,63",},
{gather_id=2059,param1=1026,param2=130,gather_refresh_pos="78,147|161,132|132,51|61,74",},
{gather_id=2060,param1=1027,gather_time=2,gather_refresh_pos="107,145|106,72",},
{gather_id=2061,type=3,param1=1000,gather_refresh_pos="109,104|66,104",},
{gather_id=2062,type=4,param1=5000,gather_time=10,duration_time=70,gather_refresh_pos="96,114|119,94",}
},

gather_meta_table_map={
[2]=1,	-- depth:1
[3]=2,	-- depth:2
[4]=7,	-- depth:1
[5]=7,	-- depth:1
[6]=7,	-- depth:1
},
gather_refresh={
{},
{time=140,gather_id="2054|2055|2061",refresh_num=5,},
{time=160,gather_id=2062,refresh_num=1,},
{time=240,},
{time=380,},
{time=400,}
},

gather_refresh_meta_table_map={
[5]=2,	-- depth:1
[6]=3,	-- depth:1
},
contend={
{},
{seq=1,pos="107,103",score_added_per=15000,contend_name="鹤归地",camp_effect_pos="-0.5,-3,0.5",contend_icon="a3_ty_icon_jz2",},
{seq=2,pos="108,158",contend_name="清镜池",camp_effect_pos="-0.5,-3,55",contend_icon="a3_ty_icon_jz3",}
},

contend_meta_table_map={
},
person_score_reward={
{},
{seq=1,need_score=800,},
{seq=2,need_score=1200,},
{seq=3,need_score=1600,reward_item={[0]=item_table[6],[1]=item_table[7],[2]=item_table[8],[3]=item_table[1],[4]=item_table[2]},},
{seq=4,need_score=2000,},
{seq=5,need_score=2400,},
{seq=6,need_score=2800,reward_item={[0]=item_table[6],[1]=item_table[9],[2]=item_table[8],[3]=item_table[10],[4]=item_table[11]},},
{seq=7,need_score=3200,},
{seq=8,need_score=3600,reward_item={[0]=item_table[6],[1]=item_table[12],[2]=item_table[8],[3]=item_table[10],[4]=item_table[11]},},
{seq=9,need_score=4200,}
},

person_score_reward_meta_table_map={
[5]=4,	-- depth:1
[6]=4,	-- depth:1
[8]=7,	-- depth:1
[10]=9,	-- depth:1
},
person_score_rank_reward={
{},
{min_rank=2,max_rank=2,reward_item={[0]=item_table[13],[1]=item_table[14],[2]=item_table[15],[3]=item_table[16],[4]=item_table[17]},},
{min_rank=3,max_rank=3,reward_item={[0]=item_table[18],[1]=item_table[19],[2]=item_table[20],[3]=item_table[21],[4]=item_table[22]},},
{min_rank=4,max_rank=10,reward_item={[0]=item_table[23],[1]=item_table[24],[2]=item_table[25],[3]=item_table[26],[4]=item_table[27]},}
},

person_score_rank_reward_meta_table_map={
},
person_kill_reward={
{},
{seq=1,need_kill=4,},
{seq=2,need_kill=6,},
{seq=3,need_kill=10,reward_item={[0]=item_table[6],[1]=item_table[7],[2]=item_table[8],[3]=item_table[1],[4]=item_table[2]},},
{seq=4,need_kill=15,},
{seq=5,need_kill=20,},
{seq=6,need_kill=25,reward_item={[0]=item_table[6],[1]=item_table[9],[2]=item_table[8],[3]=item_table[10],[4]=item_table[11]},},
{seq=7,need_kill=30,},
{seq=8,need_kill=35,reward_item={[0]=item_table[6],[1]=item_table[12],[2]=item_table[8],[3]=item_table[10],[4]=item_table[11]},},
{seq=9,need_kill=40,}
},

person_kill_reward_meta_table_map={
[5]=4,	-- depth:1
[6]=4,	-- depth:1
[8]=7,	-- depth:1
[10]=9,	-- depth:1
},
other_default_table={open_day=21,open_level=250,scene_id=9210,room_time=480,room_stop_match_time=120,room_player_num_max=30,room_player_num_min=10,match_robot_time=25,calc_contend_time=5,contend_max_add_value=3,auto_get_score_time=5,auto_get_score=10,kill_score=40,win_reward_item={[0]=item_table[28],[1]=item_table[29],[2]=item_table[30],[3]=item_table[31],[4]=item_table[32],[5]=item_table[33]},fail_reward_item={[0]=item_table[34],[1]=item_table[35],[2]=item_table[36],[3]=item_table[31],[4]=item_table[37],[5]=item_table[38]},robot_relive_interval_time_s=8,auto_enter_time=30,auto_fohuo_time=5,explain_tip_show_time=10,},

camp_default_table={seq=0,born_pos="201,103",camp_name="澜月界",camp_icon="a3_gjzz_bq2",},

gather_default_table={gather_id=2054,type=2,param1=0,param2=0,gather_time=4,duration_time=110,reward_item={},gather_refresh_pos="80,155|56,75|136,53|159,128",},

gather_refresh_default_table={time=20,gather_id="2057|2058|2059|2060",refresh_num=6,},

contend_default_table={seq=0,pos="108,48",range=12,max_capture_value=10,score_added_per=10000,contend_name="摘星台",not_camp_gather_id=2051,camp0_gather_id=2053,camp1_gather_id=2052,camp_effect_pos="-0.5,-3,-55",contend_icon="a3_ty_icon_jz1",},

person_score_reward_default_table={seq=0,need_score=400,reward_item={[0]=item_table[6],[1]=item_table[39],[2]=item_table[8],[3]=item_table[1],[4]=item_table[2]},},

person_score_rank_reward_default_table={min_rank=1,max_rank=1,reward_item={[0]=item_table[40],[1]=item_table[41],[2]=item_table[38],[3]=item_table[42],[4]=item_table[43]},},

person_kill_reward_default_table={seq=0,need_kill=2,reward_item={[0]=item_table[6],[1]=item_table[39],[2]=item_table[8],[3]=item_table[1],[4]=item_table[2]},}

}

