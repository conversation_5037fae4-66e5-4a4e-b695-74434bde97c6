-- D-地藏礼包.xls
local item_table={
[1]={item_id=22119,num=2,is_bind=1},
[2]={item_id=22119,num=3,is_bind=1},
[3]={item_id=22119,num=6,is_bind=1},
[4]={item_id=22119,num=10,is_bind=1},
[5]={item_id=22119,num=14,is_bind=1},
[6]={item_id=22119,num=20,is_bind=1},
[7]={item_id=22119,num=48,is_bind=1},
[8]={item_id=22119,num=58,is_bind=1},
[9]={item_id=22119,num=78,is_bind=1},
[10]={item_id=22119,num=168,is_bind=1},
[11]={item_id=22119,num=328,is_bind=1},
[12]={item_id=22119,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
reward={
{weight=60,task_ids="11|14|22|21",title="a1_dzhb_gg1",},
{seq=1,weight=30,task_ids="11|14|29|30",reward_item=item_table[1],start_num=40,finish_num=60,title="a1_dzhb_gg1",},
{seq=2,task_ids="1|12|15|22",reward_item=item_table[2],start_num=60,finish_num=85,title="a1_dzhb_gg1",},
{seq=3,task_ids="2|13|15|21",reward_item=item_table[3],start_num=85,finish_num=110,title="a1_dzhb_gg1",real_recharge_num=3,},
{seq=4,reward_item=item_table[4],start_num=110,finish_num=135,real_recharge_num=6,},
{seq=5,task_ids="4|13|15|21",reward_item=item_table[5],start_num=135,finish_num=160,real_recharge_num=9,},
{seq=6,task_ids="5|12|16|32",reward_item=item_table[6],start_num=160,finish_num=185,real_recharge_num=15,},
{seq=7,task_ids="6|13|15|21",reward_item=item_table[7],start_num=185,finish_num=210,real_recharge_num=34,},
{seq=8,task_ids="7|12|16|32",reward_item=item_table[8],start_num=210,finish_num=235,real_recharge_num=49,},
{seq=9,task_ids="8|13|15|21",reward_item=item_table[9],start_num=235,finish_num=260,real_recharge_num=64,},
{seq=10,task_ids="9|12|16|32",reward_item=item_table[10],start_num=260,finish_num=285,real_recharge_num=164,},
{seq=11,task_ids="10|13|15|21",reward_item=item_table[11],start_num=285,finish_num=310,real_recharge_num=324,},
{seq=12,start_day=5,end_day=7,task_ids="11|14|22|23",},
{seq=13,start_day=5,end_day=7,},
{seq=14,start_day=5,end_day=7,},
{seq=15,start_day=5,end_day=7,task_ids="2|13|15|26",},
{seq=16,start_day=5,end_day=7,},
{seq=17,start_day=5,end_day=7,},
{seq=18,start_day=5,end_day=7,task_ids="5|12|16|23",},
{seq=19,start_day=5,end_day=7,task_ids="6|13|15|25",},
{seq=20,start_day=5,end_day=7,task_ids="7|12|16|26",},
{seq=21,start_day=5,end_day=7,task_ids="8|13|15|22",},
{seq=22,start_day=5,end_day=7,task_ids="9|12|16|21",},
{seq=23,start_day=5,end_day=7,task_ids="10|13|15|23",},
{seq=24,start_day=8,end_day=9999,real_recharge_num=0.5,},
{seq=25,start_day=8,end_day=9999,task_ids="11|14|25|26",},
{seq=26,start_day=8,end_day=9999,task_ids="1|12|15|25",},
{seq=27,start_day=8,end_day=9999,task_ids="2|12|16|26",reward_item=item_table[3],start_num=85,finish_num=110,title="a1_dzhb_gg1",},
{seq=28,start_day=8,end_day=9999,task_ids="3|13|15|22",},
{seq=29,start_day=8,end_day=9999,task_ids="4|12|16|21",},
{seq=30,start_day=8,end_day=9999,task_ids="5|13|15|23",},
{seq=31,start_day=8,end_day=9999,task_ids="6|12|16|25",},
{seq=32,start_day=8,end_day=9999,task_ids="7|13|15|26",},
{seq=33,start_day=8,end_day=9999,task_ids="8|12|16|22",},
{seq=34,start_day=8,end_day=9999,task_ids="9|13|15|21",},
{seq=35,start_day=8,end_day=9999,task_ids="10|12|16|23",}
},

reward_meta_table_map={
[13]=1,	-- depth:1
[17]=5,	-- depth:1
[25]=13,	-- depth:2
[27]=3,	-- depth:1
[34]=10,	-- depth:1
[33]=9,	-- depth:1
[32]=8,	-- depth:1
[31]=7,	-- depth:1
[30]=6,	-- depth:1
[29]=5,	-- depth:1
[18]=6,	-- depth:1
[23]=11,	-- depth:1
[22]=10,	-- depth:1
[21]=9,	-- depth:1
[20]=8,	-- depth:1
[19]=7,	-- depth:1
[35]=11,	-- depth:1
[15]=27,	-- depth:2
[24]=12,	-- depth:1
[36]=12,	-- depth:1
[26]=2,	-- depth:1
[16]=4,	-- depth:1
[14]=26,	-- depth:2
},
task={
{task_type=3,add_num=10,},
{task_id=2,param1=6,add_num=10,desc="累积充值<color=%s>%s/</color>6元（+10提现额度）",},
{task_id=3,param1=12,add_num=10,desc="累积充值<color=%s>%s/</color>12元（+10提现额度）",},
{task_id=4,param1=18,add_num=10,desc="累积充值<color=%s>%s/</color>18元（+10提现额度）",},
{task_id=5,param1=30,add_num=10,desc="累积充值<color=%s>%s/</color>30元（+10提现额度）",},
{task_id=6,param1=68,add_num=10,desc="累积充值<color=%s>%s/</color>68元（+10提现额度）",},
{task_id=7,param1=98,add_num=10,desc="累积充值<color=%s>%s/</color>98元（+10提现额度）",},
{task_id=8,param1=128,add_num=10,desc="累积充值<color=%s>%s/</color>128元（+10提现额度）",},
{task_id=9,param1=328,add_num=10,desc="累积充值<color=%s>%s/</color>328元（+10提现额度）",},
{task_id=10,param1=648,add_num=10,desc="累积充值<color=%s>%s/</color>648元（+10提现额度）",},
{task_id=11,param1=300,desc="在线时间达到<color=%s>%s/</color>5分钟（+5提现额度）",},
{task_id=12,param1=900,desc="在线时间达到<color=%s>%s/</color>15分钟（+5提现额度）",},
{task_id=13,task_type=1,param1=1800,desc="在线时间达到<color=%s>%s/</color>30分钟（+5提现额度）",},
{task_id=14,param1=500,desc="消耗<color=%s>%s/</color>500灵玉（+5提现额度）",},
{task_id=15,param1=1000,desc="消耗<color=%s>%s/</color>1000灵玉（+5提现额度）",},
{task_id=16,task_type=4,param1=3000,desc="消耗<color=%s>%s/</color>3000灵玉（+5提现额度）",open_panel="market",},
{task_id=17,param2=68,desc="完成<color=%s>%s/</color>1次昆仑玉虚（+5提现额度）",open_panel="fubenpanel#fubenpanel_control_beasts",},
{task_id=18,task_type=5,param2=69,desc="完成<color=%s>%s/</color>1次神女无梦（+5提现额度）",open_panel="fubenpanel#fubenpanel_beauty",},
{task_id=19,task_type=18,param2=44,desc="参与<color=%s>%s/</color>1次天峰夺宝（+5提现额度）",open_panel="fubenpanel#fubenpanel_equip_high",},
{task_id=20,param2=70,desc="完成<color=%s>%s/</color>1次山海五行（+5提现额度）",open_panel="fubenpanel#fubenpanel_wuhun",},
{task_id=21,task_type=6,desc="击杀灵妖奇脉BOSS<color=%s>%s/</color>1次（+5提现额度）",open_panel="boss#boss_personal",},
{task_id=22,task_type=7,param1=5,desc="击杀仙遗洞天BOSS<color=%s>%s/</color>5次（+5提现额度）",open_panel="boss#boss_vip",},
{task_id=23,task_type=8,desc="击杀伏魔战场BOSS<color=%s>%s/</color>1次（+5提现额度）",open_panel="boss#boss_world",},
{task_id=24,task_type=9,desc="击杀赤霄魔域BOSS<color=%s>%s/</color>1次（+5提现额度）",open_panel="",},
{task_id=25,task_type=10,desc="击杀谪仙之境BOSS<color=%s>%s/</color>1次（+5提现额度）",open_panel="WorldServer#world_new_shenyuan_boss",},
{task_id=26,task_type=11,desc="击杀荒天炎窟BOSS<color=%s>%s/</color>1次（+5提现额度）",open_panel="WorldServer#worserv_boss_mh",},
{task_id=27,task_type=12,desc="击杀异域魂界BOSS<color=%s>%s/</color>1次（+5提现额度）",open_panel="",},
{task_id=28,task_type=13,desc="参与<color=%s>%s/</color>1次暗翼之巢（+5提现额度）",open_panel="fubenpanel#fubenpanel_pet",},
{task_id=29,task_type=14,desc="参与<color=%s>%s/</color>1次日月修行（+5提现额度）",open_panel="fubenpanel#fubenpanel_exp",},
{task_id=30,task_type=15,desc="参与<color=%s>%s/</color>1次熔火之心（+5提现额度）",open_panel="fubenpanel#fubenpanel_copper",},
{task_id=31,task_type=16,desc="参与<color=%s>%s/</color>1次神灵仙岛（+5提现额度）",open_panel="fubenpanel#fubenpanel_bagua",},
{task_id=32,param1=10,desc="击杀仙遗洞天BOSS<color=%s>%s/</color>10次（+5提现额度）",}
},

task_meta_table_map={
[17]=18,	-- depth:1
[15]=16,	-- depth:1
[14]=16,	-- depth:1
[13]=27,	-- depth:1
[12]=13,	-- depth:2
[11]=13,	-- depth:2
[20]=18,	-- depth:1
[32]=22,	-- depth:1
},
other_default_table={open_level=180,open_daily_work_exp=80,task_time=0,audio=40001,},

reward_default_table={seq=0,start_day=1,end_day=4,weight=10,task_ids="3|12|16|22",reward_item=item_table[12],start_num=20,finish_num=40,title="a1_dzhb_gg",real_recharge_num=0,},

task_default_table={task_id=1,task_type=17,param1=1,param2=0,add_num=5,desc="充值任意金额（+10提现额度）",open_panel="vip",}

}

