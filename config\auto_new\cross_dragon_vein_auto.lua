-- K-跨服龙脉.xls
local item_table={
[1]={item_id=26346,num=1,is_bind=1},
[2]={item_id=26349,num=1,is_bind=1},
[3]={item_id=29476,num=1,is_bind=1},
[4]={item_id=27907,num=1,is_bind=1},
[5]={item_id=26166,num=2,is_bind=1},
[6]={item_id=26167,num=1,is_bind=1},
[7]={item_id=26168,num=1,is_bind=1},
[8]={item_id=26465,num=1,is_bind=1},
[9]={item_id=27741,num=2,is_bind=1},
[10]={item_id=26120,num=1,is_bind=1},
[11]={item_id=26167,num=2,is_bind=1},
[12]={item_id=26168,num=2,is_bind=1},
[13]={item_id=26466,num=1,is_bind=1},
[14]={item_id=57986,num=1,is_bind=1},
[15]={item_id=26169,num=1,is_bind=1},
[16]={item_id=26467,num=1,is_bind=1},
[17]={item_id=57986,num=2,is_bind=1},
[18]={item_id=26169,num=2,is_bind=1},
[19]={item_id=26170,num=1,is_bind=1},
[20]={item_id=57986,num=3,is_bind=1},
[21]={item_id=46522,num=1,is_bind=1},
[22]={item_id=26170,num=2,is_bind=1},
[23]={item_id=26465,num=2,is_bind=1},
[24]={item_id=26466,num=2,is_bind=1},
[25]={item_id=26467,num=2,is_bind=1},
[26]={item_id=57986,num=5,is_bind=1},
[27]={item_id=46522,num=2,is_bind=1},
[28]={item_id=26346,num=5,is_bind=1},
[29]={item_id=26349,num=5,is_bind=1},
[30]={item_id=29476,num=4,is_bind=1},
[31]={item_id=27907,num=5,is_bind=1},
[32]={item_id=26120,num=9,is_bind=1},
[33]={item_id=26166,num=5,is_bind=1},
[34]={item_id=26167,num=3,is_bind=1},
[35]={item_id=26465,num=5,is_bind=1},
[36]={item_id=26466,num=5,is_bind=1},
[37]={item_id=26467,num=5,is_bind=1},
[38]={item_id=26468,num=5,is_bind=1},
[39]={item_id=26469,num=5,is_bind=1},
[40]={item_id=50075,num=1,is_bind=0},
[41]={item_id=46501,num=1,is_bind=0},
[42]={item_id=46502,num=1,is_bind=0},
[43]={item_id=27814,num=1,is_bind=0},
[44]={item_id=26166,num=1,is_bind=0},
[45]={item_id=26167,num=1,is_bind=0},
[46]={item_id=26168,num=1,is_bind=0},
[47]={item_id=26169,num=1,is_bind=0},
[48]={item_id=26170,num=1,is_bind=0},
[49]={item_id=26465,num=1,is_bind=0},
[50]={item_id=26466,num=1,is_bind=0},
[51]={item_id=26467,num=1,is_bind=0},
[52]={item_id=26346,num=1,is_bind=0},
}

return {
other={
{}
},

other_meta_table_map={
},
boss_refresh={
{}
},

boss_refresh_meta_table_map={
},
gather={
{gather_refresh_pos="26,115|39,98|91,108|115,89|120,61|120,50|120,32|140,82|154,97|186,104|214,98|224,112|156,111|140,129|124,181|108,132|126,159",},
{gather_id=2032,quality=2,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7],[7]=item_table[8],[8]=item_table[9]},refresh_boss_id=44103,gather_refresh_pos="39,98|91,108|115,89|120,61|120,50|120,32|140,82|154,97|186,104|214,98|224,112|156,111|140,129|124,181|108,132|126,159",},
{gather_id=2033,quality=3,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[10],[5]=item_table[5],[6]=item_table[11],[7]=item_table[12],[8]=item_table[13],[9]=item_table[9]},refresh_boss_id=44104,gather_refresh_pos="91,108|115,89|120,61|120,50|120,32|140,82|154,97|186,104|214,98",gather_time=3,},
{gather_id=2034,quality=4,reward_item={[0]=item_table[14],[1]=item_table[1],[2]=item_table[2],[3]=item_table[3],[4]=item_table[4],[5]=item_table[10],[6]=item_table[5],[7]=item_table[11],[8]=item_table[12],[9]=item_table[15],[10]=item_table[16],[11]=item_table[9]},refresh_boss_id=44102,gather_refresh_pos="154,97|186,104|214,98|224,112|156,111|140,129|124,181",gather_time=5,},
{gather_id=2035,quality=5,reward_item={[0]=item_table[17],[1]=item_table[1],[2]=item_table[2],[3]=item_table[3],[4]=item_table[4],[5]=item_table[10],[6]=item_table[5],[7]=item_table[11],[8]=item_table[12],[9]=item_table[18],[10]=item_table[19],[11]=item_table[8],[12]=item_table[13],[13]=item_table[9]},refresh_boss_id=44105,gather_time=5,},
{gather_id=2036,quality=6,reward_item={[0]=item_table[20],[1]=item_table[21],[2]=item_table[1],[3]=item_table[2],[4]=item_table[3],[5]=item_table[4],[6]=item_table[10],[7]=item_table[5],[8]=item_table[11],[9]=item_table[12],[10]=item_table[18],[11]=item_table[22],[12]=item_table[23],[13]=item_table[24],[14]=item_table[25],[15]=item_table[9]},refresh_boss_id=44106,gather_time=6,}
},

gather_meta_table_map={
},
shop_refresh_cost={
{},
{refresh_times=2,refresh_cost=30,},
{refresh_times=3,refresh_cost=40,},
{refresh_times=999,refresh_cost=50,}
},

shop_refresh_cost_meta_table_map={
},
shop_refresh={
{},
{},
{}
},

shop_refresh_meta_table_map={
},
shop={
{product_id=26349,stuff_id_1=26166,},
{id=2,product_id=26352,},
{id=3,product_id=26346,},
{id=4,product_id=26344,},
{id=5,product_id=26376,},
{id=6,product_id=26122,},
{id=7,stuff_id_1=26167,},
{id=8,product_id=50094,},
{id=9,product_id=26501,},
{id=10,product_id=26516,},
{id=11,product_id=50044,},
{id=12,product_id=46598,},
{id=13,product_id=46599,},
{id=14,product_id=46600,},
{id=15,stuff_count_1=2,},
{id=16,product_id=26914,},
{id=17,product_id=50085,},
{id=18,product_id=50089,},
{id=19,product_id=44184,stuff_id_1=26169,stuff_count_1=3,},
{id=20,product_id=44183,},
{id=21,product_id=26357,},
{id=22,product_id=26359,},
{id=23,product_id=26361,stuff_id_1=26169,stuff_count_1=2,},
{id=24,product_id=46570,},
{id=25,product_id=26503,},
{id=26,product_id=44182,},
{id=27,product_id=46571,stuff_id_1=26170,stuff_count_1=3,},
{id=28,product_id=26518,},
{id=29,product_id=26360,},
{id=30,product_id=26380,stuff_id_1=26170,stuff_count_1=2,},
{id=31,product_id=26943,stuff_id_1=26166,stuff_id_2=26167,},
{id=32,product_id=26363,},
{id=33,product_id=26372,},
{id=34,product_id=26375,stuff_count_1=15,},
{id=35,product_id=22012,stuff_count_1=20,stuff_count_2=15,},
{id=36,product_id=44058,stuff_id_1=26167,stuff_count_1=20,},
{id=37,product_id=44059,stuff_id_1=26167,stuff_count_1=15,stuff_id_2=26168,stuff_count_2=5,},
{id=38,product_id=44060,},
{id=39,product_id=46577,},
{id=40,product_id=46578,},
{id=41,product_id=46579,},
{id=42,product_id=46580,},
{id=43,product_id=46581,stuff_count_2=3,},
{id=44,product_id=48145,stuff_count_1=15,},
{id=45,product_id=48146,stuff_count_2=10,},
{id=46,product_id=48147,stuff_id_3=26168,stuff_count_3=10,},
{id=47,product_id=44180,stuff_count_1=20,stuff_id_2=26168,},
{id=48,product_id=46572,},
{id=49,product_id=46573,},
{id=50,product_id=50045,stuff_id_1=26169,},
{id=51,product_id=50043,},
{id=52,product_id=46591,},
{id=53,product_id=46592,},
{id=54,product_id=46593,},
{id=55,product_id=46594,},
{id=56,product_id=46595,},
{id=57,product_id=46574,stuff_count_1=10,},
{id=58,product_id=46575,},
{id=59,product_id=46576,},
{id=60,product_id=46582,},
{id=61,product_id=46583,},
{id=62,product_id=46596,stuff_id_3=26170,stuff_count_3=20,},
{id=63,product_id=50443,stuff_count_2=30,stuff_count_3=40,},
{id=64,product_id=50442,stuff_count_1=30,stuff_id_2=26169,stuff_count_2=40,stuff_id_3=26170,stuff_count_3=60,},
{id=65,product_id=48106,stuff_count_2=10,},
{id=66,product_id=48107,},
{id=67,product_id=44073,stuff_count_3=5,},
{id=68,product_id=28920,stuff_count_2=30,stuff_count_3=15,},
{id=69,product_id=46048,stuff_id_1=26169,stuff_id_2=26170,},
{id=70,product_id=50076,stuff_count_2=20,},
{id=71,product_id=46561,},
{id=72,product_id=50075,},
{id=73,product_id=46501,stuff_id_2=26169,stuff_count_2=5,},
{id=74,product_id=46502,},
{id=75,product_id=27814,}
},

shop_meta_table_map={
[4]=1,	-- depth:1
[51]=50,	-- depth:1
[52]=50,	-- depth:1
[53]=50,	-- depth:1
[54]=50,	-- depth:1
[55]=50,	-- depth:1
[56]=50,	-- depth:1
[71]=7,	-- depth:1
[3]=1,	-- depth:1
[16]=15,	-- depth:1
[13]=15,	-- depth:1
[12]=15,	-- depth:1
[11]=15,	-- depth:1
[10]=7,	-- depth:1
[9]=7,	-- depth:1
[8]=7,	-- depth:1
[2]=1,	-- depth:1
[6]=7,	-- depth:1
[5]=1,	-- depth:1
[14]=15,	-- depth:1
[72]=73,	-- depth:1
[74]=73,	-- depth:1
[75]=73,	-- depth:1
[17]=23,	-- depth:1
[18]=23,	-- depth:1
[28]=30,	-- depth:1
[20]=19,	-- depth:1
[21]=19,	-- depth:1
[22]=23,	-- depth:1
[29]=30,	-- depth:1
[24]=27,	-- depth:1
[25]=30,	-- depth:1
[26]=30,	-- depth:1
[57]=73,	-- depth:1
[49]=57,	-- depth:2
[48]=57,	-- depth:2
[65]=57,	-- depth:2
[61]=57,	-- depth:2
[58]=57,	-- depth:2
[66]=65,	-- depth:3
[43]=57,	-- depth:2
[42]=43,	-- depth:3
[41]=43,	-- depth:3
[40]=43,	-- depth:3
[39]=43,	-- depth:3
[60]=57,	-- depth:2
[59]=57,	-- depth:2
[69]=57,	-- depth:2
[70]=69,	-- depth:3
[31]=65,	-- depth:3
[35]=31,	-- depth:4
[34]=31,	-- depth:4
[33]=31,	-- depth:4
[32]=34,	-- depth:5
[47]=31,	-- depth:4
[38]=37,	-- depth:1
[62]=65,	-- depth:3
[63]=62,	-- depth:4
[67]=62,	-- depth:4
[68]=64,	-- depth:1
[46]=35,	-- depth:5
[45]=46,	-- depth:6
[44]=46,	-- depth:6
},
rule_tips={
{img_id=1,},
{rule_index=2,child_title="宝箱boss",content="采集高品质宝箱，有机会刷新<color=#007f18>宝箱BOSS</color>，首刀的玩家获得归属，当归属玩家死亡时，归属会重置，击败可获得大量奖励。其他参与攻击的玩家均可领取一份协助奖励。",img_id=2,},
{rule_index=3,child_title="龙脉之灵",content="活动开启后5分钟，将会刷新<color=#007f18>龙脉BOSS</color>，首刀的玩家获得归属，当归属玩家死亡时，归属会重置。击败后奖励归属者可获得大量个人奖励。归属玩家的同仙境玩家均可领取一份协助奖励。",},
{rule_index=4,child_title="龙脉姻缘树",content="获得的龙脉姻缘树，在活动结束后可前往<color=#007f18>龙脉使者</color>处兑换珍稀奖励。",img_id=3,},
{rule_index=5,child_title="宝物掉落",content="在龙脉场景中被敌对玩家击败，<color=#007f18>龙脉姻缘树</color>将会随机从背包中掉落一个，越高品质的姻缘树掉率越高。",}
},

rule_tips_meta_table_map={
},
dead_drop_item={
{},
{item_id=26167,},
{item_id=26168,},
{item_id=26169,},
{item_id=26170,}
},

dead_drop_item_meta_table_map={
},
reward_show={
{}
},

reward_show_meta_table_map={
},
camera_view={
{},
{server_index=2,},
{server_index=3,},
{server_index=4,}
},

camera_view_meta_table_map={
},
ai_xunlu={
{},
{},
{},
{},
{},
{},
{}
},

ai_xunlu_meta_table_map={
},
other_default_table={scene_id=6009,born_pos="216,107|123,183|31,106|122,32",boss_refresh_time="300|750|1200",max_gather_times=20,gather_refresh_count=40,shop_free_refresh_times=3,npc_scene=1003,npcid=10307,npc_dialog_close="每天17:05准时开启龙脉争霸活动，采集宝箱收集姻缘树，可以换取登神派秘品。（地图深处藏有高级宝箱，可以拿到高品姻缘树）",npc_dialog_open="参与龙脉争霸，采集宝箱收集姻缘树，可以换取登神派秘品。（地图深处藏有高级宝箱，可以拿到高品姻缘树）",gather_refresh_time="1|121|241|361|481|601|721|841|961|1081|1201|1321|1441|1561|1681",gather_boss_max_count=2,act_des="占据着真龙之气的地脉被人们无意间寻得，据说只要在龙脉中夺得龙气，便可自此气运加身，逢凶化吉。",},

boss_refresh_default_table={monster_id=44100,refresh_pos="117,89",gather_id=2037,reward_item={[0]=item_table[26],[1]=item_table[27],[2]=item_table[28],[3]=item_table[29],[4]=item_table[30],[5]=item_table[31],[6]=item_table[32],[7]=item_table[33],[8]=item_table[34],[9]=item_table[12],[10]=item_table[18],[11]=item_table[22],[12]=item_table[35],[13]=item_table[36],[14]=item_table[37],[15]=item_table[38],[16]=item_table[39]},gather_time=6,},

gather_default_table={gather_id=2031,quality=1,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[5],[4]=item_table[6],[5]=item_table[8],[6]=item_table[9]},refresh_boss_id=44101,gather_refresh_pos="140,82|154,97",gather_time=2,},

shop_refresh_cost_default_table={refresh_times=1,refresh_cost=20,},

shop_refresh_default_table={},

shop_default_table={id=1,product_id=50011,stuff_id_1=26168,stuff_count_1=5,stuff_id_2=0,stuff_count_2=0,stuff_id_3=0,stuff_count_3=0,stuff_id_4=0,stuff_count_4=0,stuff_id_5=0,stuff_count_5=0,},

rule_tips_default_table={rule_perent_index=1,rule_index=1,title="玩法说明",child_title="采集规则",content="每人每次活动有20次采集<color=#007f18>宝箱</color>的机会，<color=#007f18>宝箱</color>有品质区分，采集高品质宝箱可获得大量奖励。",img_id=0,},

dead_drop_item_default_table={item_id=26166,},

reward_show_default_table={rare_item={[0]=item_table[40],[1]=item_table[41],[2]=item_table[42],[3]=item_table[43]},reward_item={[0]=item_table[44],[1]=item_table[45],[2]=item_table[46],[3]=item_table[47],[4]=item_table[48],[5]=item_table[49],[6]=item_table[50],[7]=item_table[51],[8]=item_table[52]},},

camera_view_default_table={scene_id=6009,server_index=1,rotation_x=32.8,rotation_y=0,angle_distance=13,lock_rotation=0,is_recover=1,pos_x=99,pos_y=133,},

ai_xunlu_default_table={}

}

