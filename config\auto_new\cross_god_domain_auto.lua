-- K-跨服神域.xls
local item_table={
[1]={item_id=26177,num=85,is_bind=1},
[2]={item_id=26177,num=70,is_bind=1},
[3]={item_id=26177,num=55,is_bind=1},
[4]={item_id=26177,num=40,is_bind=1},
[5]={item_id=48509,num=10,is_bind=1},
[6]={item_id=37049,num=1,is_bind=1},
[7]={item_id=37059,num=1,is_bind=1},
[8]={item_id=37747,num=1,is_bind=1},
[9]={item_id=37646,num=1,is_bind=1},
[10]={item_id=38747,num=1,is_bind=1},
[11]={item_id=37215,num=1,is_bind=1},
[12]={item_id=37239,num=1,is_bind=1},
[13]={item_id=37435,num=1,is_bind=1},
[14]={item_id=36422,num=1000,is_bind=1},
[15]={item_id=36422,num=50,is_bind=1},
[16]={item_id=26177,num=100,is_bind=1},
[17]={item_id=48508,num=10,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
city={
{},
{seq=1,city_name="丑",},
{seq=2,city_name="寅",},
{seq=3,connect_city="20|17",city_name="卯",},
{seq=4,city_name="辰",},
{seq=5,connect_city="17|21",city_name="巳",},
{seq=6,city_name="午",},
{seq=7,connect_city="21|18",city_name="未",},
{seq=8,city_name="申",},
{seq=9,connect_city="18|22",city_name="酉",},
{seq=10,connect_city="22|19",city_name="戌",},
{seq=11,city_name="亥",},
{seq=12,city_name="汉",},
{seq=13,connect_city="19|23",city_name="唐",},
{seq=14,city_name="元",},
{seq=15,connect_city="23|16",city_name="明",},
{seq=16,type=1,connect_city="0|1|14|15|31|24",city_name="落日",},
{seq=17,type=1,connect_city="2|3|4|5|25|28",city_name="九霄",},
{seq=18,type=1,connect_city="6|7|8|9|29|27",city_name="碧落",},
{seq=19,type=1,connect_city="10|11|12|13|30|26",city_name="飞星",},
{seq=20,type=1,connect_city="0|1|2|3|24|25",city_name="星陨",},
{seq=21,type=1,connect_city="4|5|6|7|28|29",city_name="苍茫",},
{seq=22,type=1,connect_city="8|9|10|11|26|27",city_name="逐日",},
{seq=23,type=1,connect_city="12|13|14|15|30|31",city_name="玄天",},
{seq=24,type=2,connect_city="16|20|32|33",city_name="青龙城",},
{seq=25,type=2,connect_city="17|20|32|33",city_name="白虎城",},
{seq=26,type=2,connect_city="19|22|32|33",city_name="朱雀城",},
{seq=27,type=2,connect_city="18|22|32|33",city_name="玄武城",},
{seq=28,type=2,connect_city="17|21|32|33",city_name="苍龙城",},
{seq=29,type=2,connect_city="18|21|32|33",city_name="圣狮城",},
{seq=30,type=2,connect_city="19|23|32|33",city_name="毕方城",},
{seq=31,type=2,connect_city="16|23|32|33",city_name="麒麟城",},
{seq=32,type=3,connect_city="24|25|26|27|28|29|30|31",city_name="圣诸神域",},
{seq=33,city_name="圣天都城",}
},

city_meta_table_map={
[15]=16,	-- depth:1
[13]=14,	-- depth:1
[12]=11,	-- depth:1
[7]=8,	-- depth:1
[5]=6,	-- depth:1
[3]=4,	-- depth:1
[9]=10,	-- depth:1
[34]=33,	-- depth:1
},
boss={
{},
{boss_seq=1,},
{city_seq=1,},
{boss_seq=1,}
},

boss_meta_table_map={
[4]=3,	-- depth:1
},
boss_reward={
{},
{boss_seq=1,}
},

boss_reward_meta_table_map={
},
tired_reduce={
{}
},

tired_reduce_meta_table_map={
},
score_reward={
{},
{seq=1,need_score=100,},
{seq=2,need_score=200,}
},

score_reward_meta_table_map={
},
score_rank_reward={
{},
{min_rank=2,max_rank=2,reward_item={[0]=item_table[1]},},
{min_rank=3,max_rank=3,reward_item={[0]=item_table[2]},},
{min_rank=4,max_rank=10,reward_item={[0]=item_table[3]},},
{min_rank=11,max_rank=50,reward_item={[0]=item_table[4]},}
},

score_rank_reward_meta_table_map={
},
country_rank_reward={
{},
{min_rank=2,max_rank=2,reward_item={[0]=item_table[1]},},
{min_rank=3,max_rank=3,reward_item={[0]=item_table[2]},},
{min_rank=4,max_rank=10,reward_item={[0]=item_table[3]},},
{min_rank=11,max_rank=50,reward_item={[0]=item_table[4]},}
},

country_rank_reward_meta_table_map={
},
convert={
{},
{seq=1,item=item_table[5],},
{seq=2,item=item_table[6],stuff_num_1=30,},
{seq=3,item=item_table[7],stuff_num_1=5,},
{seq=4,item=item_table[8],},
{seq=5,item=item_table[9],stuff_num_1=4,},
{seq=6,item=item_table[10],},
{seq=7,item=item_table[11],stuff_num_1=2,},
{seq=8,item=item_table[12],stuff_num_1=3,},
{seq=9,item=item_table[13],}
},

convert_meta_table_map={
[10]=9,	-- depth:1
},
other_default_table={open_level=150,max_xianli=100,timed_add_xianli_time=600,timed_add_xianli_value=1,rmb_buy_type=144,rmb_buy_price=6666,rmb_buy_day=30,rmb_buy_max_xianli=200,rmb_buy_timed_add_xianli_time=60,rmb_buy_timed_add_xianli_value=1,rmb_buy_score_added=10000,max_worship_times=3,worship_reward={[0]=item_table[14]},be_worship_reward={[0]=item_table[15]},be_kill_monster_hurt_dec_per=1000,active_day=3,},

city_default_table={seq=0,scene_id=3100,type=0,base_durable=50,connect_city="16|20",capture_score=10,capture_reward_item={[0]=item_table[16]},capture_tired=10,city_name="子",},

boss_default_table={city_seq=0,boss_seq=0,monster_id=3000,refresh_pos="80,191",refresh_interval=600,owner_reduce_xianli=30,},

boss_reward_default_table={city_seq=0,boss_seq=0,min_rank="",max_rank="",add_score="",drop_id="",},

tired_reduce_default_table={min_tired=200,max_tired=2000,reduce_scale=5000,},

score_reward_default_table={seq=0,need_score=50,reward_item={[0]=item_table[16]},},

score_rank_reward_default_table={min_rank=1,max_rank=1,reward_item={[0]=item_table[16]},},

country_rank_reward_default_table={min_rank=1,max_rank=1,reward_item={[0]=item_table[16]},},

convert_default_table={seq=0,time_limit=1,item=item_table[17],stuff_id_1=48503,stuff_num_1=1,stuff_id_2=0,stuff_num_2=0,stuff_id_3=0,stuff_num_3=0,}

}

