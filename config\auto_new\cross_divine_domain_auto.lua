-- K-跨服圣天神域.xls
local item_table={
[1]={item_id=26181,num=2,is_bind=1},
[2]={item_id=28449,num=1,is_bind=1},
[3]={item_id=28447,num=1,is_bind=1},
[4]={item_id=26181,num=3,is_bind=1},
[5]={item_id=28450,num=1,is_bind=1},
[6]={item_id=28446,num=1,is_bind=1},
[7]={item_id=26181,num=5,is_bind=1},
[8]={item_id=28451,num=1,is_bind=1},
[9]={item_id=57848,num=5,is_bind=1},
[10]={item_id=26602,num=5,is_bind=1},
[11]={item_id=26450,num=2,is_bind=1},
[12]={item_id=26519,num=2,is_bind=1},
[13]={item_id=26602,num=4,is_bind=1},
[14]={item_id=26450,num=1,is_bind=1},
[15]={item_id=26519,num=1,is_bind=1},
[16]={item_id=26602,num=3,is_bind=1},
[17]={item_id=26444,num=3,is_bind=1},
[18]={item_id=26518,num=2,is_bind=1},
[19]={item_id=57848,num=4,is_bind=1},
[20]={item_id=26444,num=2,is_bind=1},
[21]={item_id=26518,num=1,is_bind=1},
[22]={item_id=26602,num=2,is_bind=1},
[23]={item_id=26437,num=3,is_bind=1},
[24]={item_id=26517,num=2,is_bind=1},
[25]={item_id=57848,num=3,is_bind=1},
[26]={item_id=26437,num=2,is_bind=1},
[27]={item_id=26517,num=1,is_bind=1},
[28]={item_id=26516,num=2,is_bind=1},
[29]={item_id=57848,num=2,is_bind=1},
[30]={item_id=26602,num=1,is_bind=1},
[31]={item_id=26437,num=1,is_bind=1},
[32]={item_id=26516,num=1,is_bind=1},
[33]={item_id=57848,num=9,is_bind=1},
[34]={item_id=26601,num=9,is_bind=1},
[35]={item_id=26455,num=2,is_bind=1},
[36]={item_id=26504,num=2,is_bind=1},
[37]={item_id=57848,num=8,is_bind=1},
[38]={item_id=26601,num=8,is_bind=1},
[39]={item_id=26455,num=1,is_bind=1},
[40]={item_id=26504,num=1,is_bind=1},
[41]={item_id=57848,num=7,is_bind=1},
[42]={item_id=26601,num=7,is_bind=1},
[43]={item_id=26503,num=2,is_bind=1},
[44]={item_id=57848,num=6,is_bind=1},
[45]={item_id=26601,num=6,is_bind=1},
[46]={item_id=26503,num=1,is_bind=1},
[47]={item_id=26601,num=5,is_bind=1},
[48]={item_id=26502,num=2,is_bind=1},
[49]={item_id=26601,num=4,is_bind=1},
[50]={item_id=26502,num=1,is_bind=1},
[51]={item_id=26601,num=3,is_bind=1},
[52]={item_id=26444,num=1,is_bind=1},
[53]={item_id=26501,num=3,is_bind=1},
[54]={item_id=26601,num=2,is_bind=1},
[55]={item_id=26501,num=2,is_bind=1},
[56]={item_id=26601,num=1,is_bind=1},
[57]={item_id=26501,num=1,is_bind=1},
[58]={item_id=28665,num=2,is_bind=1},
[59]={item_id=39989,num=2,is_bind=1},
[60]={item_id=56316,num=2,is_bind=1},
[61]={item_id=50311,num=2,is_bind=1},
[62]={item_id=28665,num=3,is_bind=1},
[63]={item_id=56316,num=3,is_bind=1},
[64]={item_id=57848,num=1,is_bind=1},
[65]={item_id=39990,num=2,is_bind=1},
[66]={item_id=56316,num=4,is_bind=1},
[67]={item_id=50311,num=3,is_bind=1},
[68]={item_id=28666,num=1,is_bind=1},
[69]={item_id=39990,num=1,is_bind=1},
[70]={item_id=56317,num=1,is_bind=1},
[71]={item_id=28666,num=2,is_bind=1},
[72]={item_id=39991,num=2,is_bind=1},
[73]={item_id=56317,num=2,is_bind=1},
[74]={item_id=50311,num=4,is_bind=1},
[75]={item_id=39992,num=3,is_bind=1},
[76]={item_id=56317,num=3,is_bind=1},
[77]={item_id=50311,num=5,is_bind=1},
[78]={item_id=37253,num=1,is_bind=1},
[79]={item_id=37318,num=1,is_bind=1},
[80]={item_id=37756,num=1,is_bind=1},
[81]={item_id=50381,num=1,is_bind=1},
[82]={item_id=50382,num=1,is_bind=1},
[83]={item_id=50383,num=1,is_bind=1},
[84]={item_id=50384,num=1,is_bind=1},
[85]={item_id=50333,num=1,is_bind=1},
[86]={item_id=50334,num=1,is_bind=1},
[87]={item_id=50335,num=1,is_bind=1},
[88]={item_id=50336,num=1,is_bind=1},
[89]={item_id=37028,num=1,is_bind=1},
[90]={item_id=37411,num=1,is_bind=1},
[91]={item_id=37045,num=1,is_bind=1},
[92]={item_id=38772,num=1,is_bind=1},
[93]={item_id=38740,num=1,is_bind=1},
[94]={item_id=37032,num=1,is_bind=1},
[95]={item_id=38415,num=1,is_bind=1},
[96]={item_id=37022,num=1,is_bind=1},
[97]={item_id=38748,num=1,is_bind=1},
[98]={item_id=28452,num=1,is_bind=1},
[99]={item_id=28453,num=1,is_bind=1},
[100]={item_id=28454,num=1,is_bind=1},
[101]={item_id=47424,num=1,is_bind=1},
[102]={item_id=47425,num=1,is_bind=1},
[103]={item_id=47426,num=1,is_bind=1},
[104]={item_id=47427,num=1,is_bind=1},
[105]={item_id=47428,num=1,is_bind=1},
[106]={item_id=47429,num=1,is_bind=1},
[107]={item_id=47430,num=1,is_bind=1},
[108]={item_id=47431,num=1,is_bind=1},
[109]={item_id=47408,num=1,is_bind=1},
[110]={item_id=47409,num=1,is_bind=1},
[111]={item_id=47410,num=1,is_bind=1},
[112]={item_id=47411,num=1,is_bind=1},
[113]={item_id=47412,num=1,is_bind=1},
[114]={item_id=47413,num=1,is_bind=1},
[115]={item_id=47414,num=1,is_bind=1},
[116]={item_id=47415,num=1,is_bind=1},
[117]={item_id=47576,num=1,is_bind=1},
[118]={item_id=47577,num=1,is_bind=1},
[119]={item_id=47578,num=1,is_bind=1},
[120]={item_id=47579,num=1,is_bind=1},
[121]={item_id=47580,num=1,is_bind=1},
[122]={item_id=47581,num=1,is_bind=1},
[123]={item_id=47582,num=1,is_bind=1},
[124]={item_id=47583,num=1,is_bind=1},
[125]={item_id=57849,num=9,is_bind=1},
[126]={item_id=57851,num=18,is_bind=1},
[127]={item_id=26463,num=1,is_bind=1},
[128]={item_id=48441,num=1,is_bind=1},
[129]={item_id=57849,num=8,is_bind=1},
[130]={item_id=26462,num=1,is_bind=1},
[131]={item_id=57849,num=7,is_bind=1},
[132]={item_id=57851,num=16,is_bind=1},
[133]={item_id=26459,num=1,is_bind=1},
[134]={item_id=57849,num=6,is_bind=1},
[135]={item_id=26461,num=1,is_bind=1},
[136]={item_id=57851,num=14,is_bind=1},
[137]={item_id=26460,num=1,is_bind=1},
[138]={item_id=57849,num=5,is_bind=1},
[139]={item_id=57851,num=12,is_bind=1},
[140]={item_id=57849,num=4,is_bind=1},
[141]={item_id=57851,num=10,is_bind=1},
[142]={item_id=57850,num=10,is_bind=1},
[143]={item_id=26570,num=2,is_bind=1},
[144]={item_id=48441,num=2,is_bind=1},
[145]={item_id=57850,num=9,is_bind=1},
[146]={item_id=26570,num=3,is_bind=1},
[147]={item_id=57849,num=3,is_bind=1},
[148]={item_id=57850,num=8,is_bind=1},
[149]={item_id=26569,num=4,is_bind=1},
[150]={item_id=48441,num=3,is_bind=1},
[151]={item_id=57850,num=7,is_bind=1},
[152]={item_id=57849,num=2,is_bind=1},
[153]={item_id=57850,num=6,is_bind=1},
[154]={item_id=26563,num=4,is_bind=1},
[155]={item_id=26564,num=4,is_bind=1},
[156]={item_id=37043,num=1,is_bind=1},
[157]={item_id=28666,num=5,is_bind=1},
[158]={item_id=56317,num=5,is_bind=1},
[159]={item_id=57849,num=1,is_bind=1},
[160]={item_id=39988,num=1,is_bind=1},
[161]={item_id=56316,num=1,is_bind=1},
[162]={item_id=45017,num=1,is_bind=1},
[163]={item_id=50311,num=1,is_bind=1},
[164]={item_id=50312,num=1,is_bind=1},
[165]={item_id=22009,num=1,is_bind=1},
[166]={item_id=26200,num=1,is_bind=1},
[167]={item_id=26181,num=1,is_bind=1},
[168]={item_id=55583,num=1,is_bind=1},
[169]={item_id=55591,num=1,is_bind=1},
[170]={item_id=55599,num=1,is_bind=1},
[171]={item_id=55607,num=1,is_bind=1},
[172]={item_id=55615,num=1,is_bind=1},
[173]={item_id=55623,num=1,is_bind=1},
[174]={item_id=55631,num=1,is_bind=1},
[175]={item_id=55639,num=1,is_bind=1},
[176]={item_id=26602,num=6,is_bind=1},
[177]={item_id=26520,num=1,is_bind=1},
[178]={item_id=57848,num=10,is_bind=1},
[179]={item_id=26601,num=10,is_bind=1},
[180]={item_id=26455,num=3,is_bind=1},
[181]={item_id=26505,num=1,is_bind=1},
[182]={item_id=28665,num=1,is_bind=1},
[183]={item_id=37446,num=1,is_bind=1},
[184]={item_id=57849,num=10,is_bind=1},
[185]={item_id=48540,num=1,is_bind=1},
[186]={item_id=26464,num=1,is_bind=1},
[187]={item_id=48539,num=1,is_bind=1},
[188]={item_id=26570,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
camp={
[0]={seq=0,camp_icon=0,},
[1]={seq=1,camp_name="灵虚·境",camp_icon=1,},
[2]={seq=2,camp_name="灵虚·域",camp_icon=3,},
[3]={seq=3,camp_name="灵虚·城",camp_icon=2,},
[4]={seq=4,camp_name="太古·界",},
[5]={seq=5,camp_name="太古·境",camp_icon=9,},
[6]={seq=6,camp_name="太古·域",camp_icon=11,},
[7]={seq=7,camp_name="太古·城",camp_icon=10,},
[8]={seq=8,camp_name="苍茫·界",camp_icon=4,},
[9]={seq=9,camp_name="苍茫·境",camp_icon=5,},
[10]={seq=10,camp_name="苍茫·域",camp_icon=7,},
[11]={seq=11,camp_name="苍茫·城",},
[12]={seq=12,camp_name="荒芜·界",camp_icon=12,},
[13]={seq=13,camp_name="荒芜·境",camp_icon=13,},
[14]={seq=14,camp_name="荒芜·域",camp_icon=15,},
[15]={seq=15,camp_name="荒芜·城",camp_icon=14,}
},

camp_meta_table_map={
},
city={
[0]={seq=0,type=1,connect_city="18|19|32|33|8|15",capture_tired=25,capture_reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},city_icon_type=2,},
[1]={seq=1,connect_city="18|19|20|21|8|9",scene_id=3201,city_name="灵神域",},
[2]={seq=2,connect_city="20|21|22|23|9|10",scene_id=3202,city_name="灵天域",},
[3]={seq=3,connect_city="22|23|24|25|10|11",scene_id=3203,city_name="灵玄域",},
[4]={seq=4,connect_city="24|25|26|27|11|12",scene_id=3204,city_name="灵日域",},
[5]={seq=5,connect_city="26|27|28|29|12|13",scene_id=3205,city_name="灵月域",},
[6]={seq=6,connect_city="28|29|30|31|13|14",scene_id=3206,city_name="灵星域",},
[7]={seq=7,connect_city="30|31|32|33|14|15",scene_id=3207,city_name="灵宿域",},
[8]={seq=8,connect_city="0|1|15|9|16|17",scene_id=3208,city_name="圣灵光域",},
[9]={seq=9,connect_city="1|2|8|10|16|17",scene_id=3209,city_name="圣灵神域",},
[10]={seq=10,connect_city="2|3|9|11|16|17",scene_id=3210,city_name="圣灵天域",},
[11]={seq=11,connect_city="3|4|10|12|16|17",scene_id=3211,city_name="圣灵玄域",},
[12]={seq=12,connect_city="4|5|11|13|16|17",scene_id=3212,city_name="圣灵日域",},
[13]={seq=13,type=2,connect_city="5|6|12|14|16|17",scene_id=3213,capture_tired=50,capture_reward_item={[0]=item_table[4],[1]=item_table[2],[2]=item_table[5],[3]=item_table[6],[4]=item_table[3]},city_name="圣灵月域",city_icon_type=3,},
[14]={seq=14,connect_city="6|7|13|15|16|17",scene_id=3214,city_name="圣灵星域",},
[15]={seq=15,connect_city="7|0|14|8|16|17",scene_id=3215,city_name="圣灵宿域",},
[16]={seq=16,scene_id=3216,city_name="圣天太上域",},
[17]={seq=17,type=3,scene_id=3217,capture_tired=100,capture_reward_item={[0]=item_table[7],[1]=item_table[2],[2]=item_table[5],[3]=item_table[8],[4]=item_table[6],[5]=item_table[3]},city_name="圣天九霄域",city_icon_type=4,},
[18]={seq=18,scene_id=3218,city_name="天域",relegation_camp=0,},
[19]={seq=19,connect_city="0|1",scene_id=3219,city_name="地域",relegation_camp=1,},
[20]={seq=20,scene_id=3220,city_name="玄域",relegation_camp=2,},
[21]={seq=21,connect_city="1|2",scene_id=3221,city_name="黄域",relegation_camp=3,},
[22]={seq=22,scene_id=3222,city_name="子域",relegation_camp=4,},
[23]={seq=23,connect_city="2|3",scene_id=3223,city_name="丑域",relegation_camp=5,},
[24]={seq=24,scene_id=3224,city_name="寅域",relegation_camp=6,},
[25]={seq=25,connect_city="3|4",scene_id=3225,city_name="卯域",relegation_camp=7,},
[26]={seq=26,scene_id=3226,city_name="辰域",relegation_camp=8,},
[27]={seq=27,connect_city="4|5",scene_id=3227,city_name="巳域",relegation_camp=9,},
[28]={seq=28,scene_id=3228,city_name="午域",relegation_camp=10,},
[29]={seq=29,connect_city="5|6",scene_id=3229,city_name="未域",relegation_camp=11,},
[30]={seq=30,scene_id=3230,city_name="申域",relegation_camp=12,},
[31]={seq=31,connect_city="6|7",scene_id=3231,city_name="酉域",relegation_camp=13,},
[32]={seq=32,scene_id=3232,city_name="戌域",relegation_camp=14,},
[33]={seq=33,connect_city="7|0",scene_id=3233,city_name="亥域",relegation_camp=15,}
},

city_meta_table_map={
[30]=31,	-- depth:1
[28]=29,	-- depth:1
[26]=27,	-- depth:1
[24]=25,	-- depth:1
[22]=23,	-- depth:1
[20]=21,	-- depth:1
[18]=19,	-- depth:1
[32]=33,	-- depth:1
[16]=17,	-- depth:1
[12]=13,	-- depth:1
[11]=13,	-- depth:1
[10]=13,	-- depth:1
[9]=13,	-- depth:1
[8]=13,	-- depth:1
[15]=13,	-- depth:1
[6]=0,	-- depth:1
[5]=0,	-- depth:1
[4]=0,	-- depth:1
[3]=0,	-- depth:1
[2]=0,	-- depth:1
[1]=0,	-- depth:1
[14]=13,	-- depth:1
[7]=0,	-- depth:1
},
monster={
{city_seq=0,},
{city_seq=0,monster_id=63501,},
{city_seq=0,monster_id=63502,},
{city_seq=0,monster_id=63503,},
{city_seq=0,monster_id=63504,},
{city_seq=1,monster_id=63505,},
{city_seq=1,monster_id=63506,},
{city_seq=1,monster_id=63507,},
{city_seq=1,monster_id=63508,},
{city_seq=1,monster_id=63509,},
{city_seq=2,monster_id=63510,},
{city_seq=2,monster_id=63511,},
{city_seq=2,monster_id=63512,},
{city_seq=2,monster_id=63513,},
{city_seq=2,monster_id=63514,},
{city_seq=3,monster_id=63515,},
{city_seq=3,monster_id=63516,},
{city_seq=3,monster_id=63517,},
{city_seq=3,monster_id=63518,},
{city_seq=3,monster_id=63519,},
{city_seq=4,monster_id=63520,},
{city_seq=4,monster_id=63521,},
{city_seq=4,monster_id=63522,},
{city_seq=4,monster_id=63523,},
{city_seq=4,monster_id=63524,},
{city_seq=5,monster_id=63525,},
{city_seq=5,monster_id=63526,},
{city_seq=5,monster_id=63527,},
{city_seq=5,monster_id=63528,},
{city_seq=5,monster_id=63529,},
{city_seq=6,monster_id=63530,},
{city_seq=6,monster_id=63531,},
{city_seq=6,monster_id=63532,},
{city_seq=6,monster_id=63533,},
{city_seq=6,monster_id=63534,},
{city_seq=7,monster_id=63535,},
{city_seq=7,monster_id=63536,},
{city_seq=7,monster_id=63537,},
{city_seq=7,monster_id=63538,},
{city_seq=7,monster_id=63539,},
{monster_id=63540,},
{seq=1,monster_id=63541,monster_pos="61,73",},
{seq=2,monster_id=63542,monster_pos="76,160",},
{seq=3,monster_id=63543,monster_pos="49,256",},
{seq=4,monster_id=63544,monster_pos="156,282",},
{seq=5,monster_id=63545,need_power=20,kill_score=150,monster_refresh_interval=450,monster_pos="278,283",bg_type=3,},
{seq=6,monster_id=63546,monster_pos="279,169",},
{seq=7,monster_id=63547,need_power=30,kill_score=250,monster_refresh_interval=600,monster_pos="259,72",bg_type=5,},
{seq=8,monster_id=63548,monster_pos="158,161",},
{city_seq=9,monster_id=63549,},
{city_seq=9,monster_id=63550,},
{city_seq=9,monster_id=63551,},
{city_seq=9,monster_id=63552,},
{city_seq=9,monster_id=63553,},
{city_seq=9,monster_id=63554,},
{city_seq=9,monster_id=63555,},
{city_seq=9,monster_id=63556,},
{city_seq=9,monster_id=63557,},
{city_seq=10,monster_id=63558,},
{city_seq=10,monster_id=63559,},
{city_seq=10,monster_id=63560,},
{city_seq=10,monster_id=63561,},
{city_seq=10,monster_id=63562,},
{city_seq=10,monster_id=63563,},
{city_seq=10,monster_id=63564,},
{city_seq=10,monster_id=63565,},
{city_seq=10,monster_id=63566,},
{city_seq=11,monster_id=63567,},
{city_seq=11,monster_id=63568,},
{city_seq=11,monster_id=63569,},
{city_seq=11,monster_id=63570,},
{city_seq=11,monster_id=63571,},
{city_seq=11,monster_id=63572,},
{city_seq=11,monster_id=63573,},
{city_seq=11,monster_id=63574,},
{city_seq=11,monster_id=63575,},
{city_seq=12,monster_id=63576,},
{city_seq=12,monster_id=63577,},
{city_seq=12,monster_id=63578,},
{city_seq=12,monster_id=63579,},
{city_seq=12,monster_id=63580,},
{city_seq=12,monster_id=63581,},
{city_seq=12,monster_id=63582,},
{city_seq=12,monster_id=63583,},
{city_seq=12,monster_id=63584,},
{city_seq=13,monster_id=63585,},
{city_seq=13,monster_id=63586,},
{city_seq=13,monster_id=63587,},
{city_seq=13,monster_id=63588,},
{city_seq=13,monster_id=63589,},
{city_seq=13,monster_id=63590,},
{city_seq=13,monster_id=63591,},
{city_seq=13,monster_id=63592,},
{city_seq=13,monster_id=63593,},
{city_seq=14,monster_id=63594,},
{city_seq=14,monster_id=63595,},
{city_seq=14,monster_id=63596,},
{city_seq=14,monster_id=63597,},
{city_seq=14,monster_id=63598,},
{city_seq=14,monster_id=63599,},
{city_seq=14,monster_id=63600,},
{city_seq=14,monster_id=63601,},
{city_seq=14,monster_id=63602,},
{city_seq=15,monster_id=63603,},
{city_seq=15,monster_id=63604,},
{city_seq=15,monster_id=63605,},
{city_seq=15,monster_id=63606,},
{city_seq=15,monster_id=63607,},
{city_seq=15,monster_id=63608,},
{city_seq=15,monster_id=63609,},
{city_seq=15,monster_id=63610,monster_refresh_interval=450,bg_type=3,},
{city_seq=15,monster_id=63611,},
{city_seq=16,monster_id=63612,},
{city_seq=16,monster_id=63613,},
{city_seq=16,monster_id=63614,},
{city_seq=16,monster_id=63615,},
{city_seq=16,monster_id=63616,},
{city_seq=16,monster_id=63617,},
{city_seq=16,monster_id=63618,},
{city_seq=16,monster_id=63619,},
{city_seq=16,monster_id=63620,},
{city_seq=17,monster_id=63621,},
{seq=1,monster_id=63622,monster_pos="61,73",},
{seq=2,monster_id=63623,monster_pos="76,160",},
{city_seq=17,monster_id=63624,},
{city_seq=17,monster_id=63625,},
{city_seq=17,monster_id=63626,},
{seq=6,monster_id=63627,monster_pos="279,169",},
{city_seq=17,monster_id=63628,},
{city_seq=17,monster_id=63629,},
{city_seq=18,monster_id=63630,},
{city_seq=18,monster_id=63631,},
{city_seq=18,monster_id=63632,},
{city_seq=18,monster_id=63633,},
{city_seq=19,monster_id=63634,},
{city_seq=19,monster_id=63635,},
{city_seq=19,monster_id=63636,},
{city_seq=19,monster_id=63637,},
{city_seq=20,monster_id=63638,},
{city_seq=20,monster_id=63639,},
{city_seq=20,monster_id=63640,},
{city_seq=20,monster_id=63641,},
{city_seq=21,monster_id=63642,},
{city_seq=21,monster_id=63643,},
{city_seq=21,monster_id=63644,},
{city_seq=21,monster_id=63645,},
{city_seq=22,monster_id=63646,},
{city_seq=22,monster_id=63647,},
{city_seq=22,monster_id=63648,},
{city_seq=22,monster_id=63649,},
{city_seq=23,monster_id=63650,},
{city_seq=23,monster_id=63651,},
{city_seq=23,monster_id=63652,},
{city_seq=23,monster_id=63653,},
{city_seq=24,monster_id=63654,},
{city_seq=24,monster_id=63655,},
{city_seq=24,monster_id=63656,},
{city_seq=24,monster_id=63657,},
{city_seq=25,monster_id=63658,},
{city_seq=25,monster_id=63659,},
{city_seq=25,monster_id=63660,},
{city_seq=25,monster_id=63661,},
{city_seq=26,monster_id=63662,},
{city_seq=26,monster_id=63663,},
{city_seq=26,monster_id=63664,},
{city_seq=26,monster_id=63665,},
{city_seq=27,monster_id=63666,},
{city_seq=27,monster_id=63667,},
{city_seq=27,monster_id=63668,},
{city_seq=27,monster_id=63669,},
{city_seq=28,monster_id=63670,},
{city_seq=28,monster_id=63671,},
{city_seq=28,monster_id=63672,},
{city_seq=28,monster_id=63673,},
{city_seq=29,monster_id=63674,},
{city_seq=29,monster_id=63675,},
{city_seq=29,monster_id=63676,},
{city_seq=29,monster_id=63677,},
{city_seq=30,monster_id=63678,},
{city_seq=30,monster_id=63679,},
{city_seq=30,monster_id=63680,},
{city_seq=30,monster_id=63681,},
{city_seq=31,monster_id=63682,},
{city_seq=31,monster_id=63683,},
{city_seq=31,monster_id=63684,},
{city_seq=31,monster_id=63685,},
{city_seq=32,monster_id=63686,},
{city_seq=32,monster_id=63687,},
{city_seq=32,monster_id=63688,},
{city_seq=32,monster_id=63689,},
{city_seq=33,monster_id=63690,},
{city_seq=33,monster_id=63691,},
{city_seq=33,monster_id=63692,},
{city_seq=33,monster_id=63693,}
},

monster_meta_table_map={
[133]=43,	-- depth:1
[185]=43,	-- depth:1
[136]=42,	-- depth:1
[132]=42,	-- depth:1
[184]=42,	-- depth:1
[106]=43,	-- depth:1
[105]=42,	-- depth:1
[188]=42,	-- depth:1
[193]=43,	-- depth:1
[96]=42,	-- depth:1
[189]=43,	-- depth:1
[88]=43,	-- depth:1
[87]=42,	-- depth:1
[192]=42,	-- depth:1
[79]=43,	-- depth:1
[137]=43,	-- depth:1
[169]=43,	-- depth:1
[181]=43,	-- depth:1
[141]=43,	-- depth:1
[165]=43,	-- depth:1
[164]=42,	-- depth:1
[161]=43,	-- depth:1
[172]=42,	-- depth:1
[160]=42,	-- depth:1
[173]=43,	-- depth:1
[157]=43,	-- depth:1
[156]=42,	-- depth:1
[140]=42,	-- depth:1
[176]=42,	-- depth:1
[152]=42,	-- depth:1
[177]=43,	-- depth:1
[149]=43,	-- depth:1
[148]=42,	-- depth:1
[145]=43,	-- depth:1
[144]=42,	-- depth:1
[180]=42,	-- depth:1
[168]=42,	-- depth:1
[78]=42,	-- depth:1
[153]=43,	-- depth:1
[97]=43,	-- depth:1
[69]=42,	-- depth:1
[37]=42,	-- depth:1
[28]=43,	-- depth:1
[27]=42,	-- depth:1
[38]=43,	-- depth:1
[23]=43,	-- depth:1
[22]=42,	-- depth:1
[61]=43,	-- depth:1
[18]=43,	-- depth:1
[17]=42,	-- depth:1
[32]=42,	-- depth:1
[13]=43,	-- depth:1
[12]=42,	-- depth:1
[33]=43,	-- depth:1
[70]=43,	-- depth:1
[8]=43,	-- depth:1
[60]=42,	-- depth:1
[7]=42,	-- depth:1
[2]=42,	-- depth:1
[3]=43,	-- depth:1
[51]=42,	-- depth:1
[52]=43,	-- depth:1
[45]=46,	-- depth:1
[49]=48,	-- depth:1
[44]=46,	-- depth:1
[47]=46,	-- depth:1
[34]=44,	-- depth:2
[35]=45,	-- depth:2
[40]=45,	-- depth:2
[39]=44,	-- depth:2
[142]=44,	-- depth:2
[150]=44,	-- depth:2
[146]=44,	-- depth:2
[154]=44,	-- depth:2
[75]=48,	-- depth:1
[166]=44,	-- depth:2
[30]=45,	-- depth:2
[190]=44,	-- depth:2
[4]=44,	-- depth:2
[5]=45,	-- depth:2
[186]=44,	-- depth:2
[9]=44,	-- depth:2
[10]=45,	-- depth:2
[182]=44,	-- depth:2
[14]=44,	-- depth:2
[178]=44,	-- depth:2
[15]=45,	-- depth:2
[174]=44,	-- depth:2
[19]=44,	-- depth:2
[20]=45,	-- depth:2
[170]=44,	-- depth:2
[24]=44,	-- depth:2
[25]=45,	-- depth:2
[53]=44,	-- depth:2
[29]=44,	-- depth:2
[162]=44,	-- depth:2
[158]=44,	-- depth:2
[138]=44,	-- depth:2
[56]=47,	-- depth:2
[55]=46,	-- depth:1
[65]=47,	-- depth:2
[103]=49,	-- depth:2
[102]=48,	-- depth:1
[101]=47,	-- depth:2
[100]=46,	-- depth:1
[99]=45,	-- depth:2
[98]=44,	-- depth:2
[66]=48,	-- depth:1
[67]=49,	-- depth:2
[94]=49,	-- depth:2
[93]=48,	-- depth:1
[92]=47,	-- depth:2
[91]=46,	-- depth:1
[90]=45,	-- depth:2
[89]=44,	-- depth:2
[71]=44,	-- depth:2
[72]=45,	-- depth:2
[85]=49,	-- depth:2
[84]=48,	-- depth:1
[83]=47,	-- depth:2
[82]=46,	-- depth:1
[81]=45,	-- depth:2
[80]=44,	-- depth:2
[73]=46,	-- depth:1
[74]=47,	-- depth:2
[64]=46,	-- depth:1
[54]=45,	-- depth:2
[63]=45,	-- depth:2
[108]=45,	-- depth:2
[76]=49,	-- depth:2
[134]=44,	-- depth:2
[57]=48,	-- depth:1
[58]=49,	-- depth:2
[130]=49,	-- depth:2
[129]=48,	-- depth:1
[128]=129,	-- depth:2
[127]=46,	-- depth:1
[126]=45,	-- depth:2
[125]=44,	-- depth:2
[124]=127,	-- depth:2
[123]=127,	-- depth:2
[121]=49,	-- depth:2
[120]=48,	-- depth:1
[119]=128,	-- depth:3
[118]=46,	-- depth:1
[117]=45,	-- depth:2
[116]=44,	-- depth:2
[115]=124,	-- depth:3
[114]=123,	-- depth:3
[62]=44,	-- depth:2
[112]=49,	-- depth:2
[111]=48,	-- depth:1
[110]=47,	-- depth:2
[109]=46,	-- depth:1
[107]=44,	-- depth:2
[194]=44,	-- depth:2
},
tired_hurt_reduce={
{},
{min_tired=100,max_tired=199,reduce_scale=5000,},
{min_tired=200,max_tired=9999,reduce_scale=8000,text="疲劳值较高，效率较低，请减少攻略低级城池",}
},

tired_hurt_reduce_meta_table_map={
},
person_score_rank_reward={
{},
{min_rank=2,max_rank=2,reward_item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11],[3]=item_table[12],},},
{min_rank=3,max_rank=3,reward_item={[0]=item_table[9],[1]=item_table[13],[2]=item_table[14],[3]=item_table[15],},},
{min_rank=4,max_rank=6,reward_item={[0]=item_table[9],[1]=item_table[16],[2]=item_table[17],[3]=item_table[18],},},
{min_rank=7,max_rank=10,reward_item={[0]=item_table[19],[1]=item_table[16],[2]=item_table[20],[3]=item_table[21],},},
{min_rank=11,max_rank=15,reward_item={[0]=item_table[19],[1]=item_table[22],[2]=item_table[23],[3]=item_table[24],},},
{min_rank=16,max_rank=20,reward_item={[0]=item_table[25],[1]=item_table[22],[2]=item_table[26],[3]=item_table[27],},},
{min_rank=21,max_rank=50,reward_item={[0]=item_table[25],[1]=item_table[22],[2]=item_table[26],[3]=item_table[28],},},
{min_rank=51,max_rank=100,reward_item={[0]=item_table[29],[1]=item_table[30],[2]=item_table[31],[3]=item_table[32],},}
},

person_score_rank_reward_meta_table_map={
},
server_score_rank_reward={
{},
{min_rank=2,max_rank=2,reward_item={[0]=item_table[33],[1]=item_table[34],[2]=item_table[35],[3]=item_table[36],},},
{min_rank=3,max_rank=3,reward_item={[0]=item_table[37],[1]=item_table[38],[2]=item_table[39],[3]=item_table[40],},},
{min_rank=4,max_rank=4,reward_item={[0]=item_table[41],[1]=item_table[42],[2]=item_table[11],[3]=item_table[43],},},
{min_rank=5,max_rank=5,reward_item={[0]=item_table[44],[1]=item_table[45],[2]=item_table[14],[3]=item_table[46],},},
{min_rank=6,max_rank=6,reward_item={[0]=item_table[9],[1]=item_table[47],[2]=item_table[17],[3]=item_table[48],},},
{min_rank=7,max_rank=7,reward_item={[0]=item_table[9],[1]=item_table[49],[2]=item_table[20],[3]=item_table[50],},},
{min_rank=8,max_rank=8,reward_item={[0]=item_table[19],[1]=item_table[51],[2]=item_table[52],[3]=item_table[53],},},
{min_rank=9,max_rank=9,reward_item={[0]=item_table[19],[1]=item_table[54],[2]=item_table[52],[3]=item_table[55],},},
{min_rank=10,max_rank=10,reward_item={[0]=item_table[25],[1]=item_table[56],[2]=item_table[52],[3]=item_table[57],},}
},

server_score_rank_reward_meta_table_map={
},
score_reward={
[0]={seq=0,},
[1]={seq=1,need_score=850,reward_item={[0]=item_table[58],[1]=item_table[59],[2]=item_table[60],[3]=item_table[61],},},
[2]={seq=2,need_score=1150,reward_item={[0]=item_table[62],[1]=item_table[59],[2]=item_table[63],[3]=item_table[61],},},
[3]={seq=3,need_score=1450,reward_item={[0]=item_table[64],[1]=item_table[65],[2]=item_table[66],[3]=item_table[67],},},
[4]={seq=4,need_score=1750,reward_item={[0]=item_table[68],[1]=item_table[69],[2]=item_table[70],[3]=item_table[67],},},
[5]={seq=5,need_score=2050,reward_item={[0]=item_table[71],[1]=item_table[72],[2]=item_table[73],[3]=item_table[74],},},
[6]={seq=6,need_score=2350,reward_item={[0]=item_table[64],[1]=item_table[75],[2]=item_table[76],[3]=item_table[77],},}
},

score_reward_meta_table_map={
},
convert={
[0]={seq=0,times_limit=1,stuff_num_1=600,},
[1]={seq=1,item=item_table[78],},
[2]={seq=2,times_limit=1,item=item_table[79],stuff_num_1=500,},
[3]={seq=3,item=item_table[80],},
[4]={seq=4,times_limit=1,item=item_table[81],stuff_num_1=178,},
[5]={seq=5,item=item_table[82],},
[6]={seq=6,item=item_table[83],},
[7]={seq=7,item=item_table[84],},
[8]={seq=8,item=item_table[85],},
[9]={seq=9,item=item_table[86],},
[10]={seq=10,item=item_table[87],},
[11]={seq=11,item=item_table[88],},
[12]={seq=12,times_limit=1,item=item_table[89],stuff_num_1=520,},
[13]={seq=13,times_limit=1,item=item_table[90],stuff_num_1=400,},
[14]={seq=14,times_limit=1,item=item_table[91],stuff_num_1=320,},
[15]={seq=15,times_limit=1,item=item_table[92],stuff_num_1=190,},
[16]={seq=16,times_limit=1,item=item_table[93],stuff_num_1=300,},
[17]={seq=17,times_limit=1,item=item_table[94],stuff_num_1=80,},
[18]={seq=18,times_limit=1,item=item_table[95],stuff_num_1=250,},
[19]={seq=19,times_limit=1,item=item_table[96],stuff_num_1=210,},
[20]={seq=20,times_limit=1,item=item_table[97],stuff_num_1=270,},
[21]={seq=21,item=item_table[98],},
[22]={seq=22,times_limit=0,item=item_table[99],stuff_num_1=3,},
[23]={seq=23,times_limit=0,item=item_table[100],stuff_num_1=20,},
[24]={seq=24,item=item_table[101],},
[25]={seq=25,item=item_table[102],},
[26]={seq=26,item=item_table[103],},
[27]={seq=27,item=item_table[104],},
[28]={seq=28,item=item_table[105],},
[29]={seq=29,item=item_table[106],},
[30]={seq=30,item=item_table[107],},
[31]={seq=31,item=item_table[108],},
[32]={seq=32,item=item_table[109],},
[33]={seq=33,item=item_table[110],},
[34]={seq=34,item=item_table[111],},
[35]={seq=35,item=item_table[112],},
[36]={seq=36,item=item_table[113],},
[37]={seq=37,item=item_table[114],},
[38]={seq=38,item=item_table[115],},
[39]={seq=39,item=item_table[116],},
[40]={seq=40,item=item_table[117],stuff_num_1=60,},
[41]={seq=41,item=item_table[118],},
[42]={seq=42,item=item_table[119],},
[43]={seq=43,item=item_table[120],},
[44]={seq=44,item=item_table[121],},
[45]={seq=45,item=item_table[122],},
[46]={seq=46,item=item_table[123],},
[47]={seq=47,item=item_table[124],}
},

convert_meta_table_map={
[41]=40,	-- depth:1
[42]=40,	-- depth:1
[43]=40,	-- depth:1
[44]=40,	-- depth:1
[45]=40,	-- depth:1
[47]=40,	-- depth:1
[46]=40,	-- depth:1
[1]=0,	-- depth:1
[3]=2,	-- depth:1
[5]=4,	-- depth:1
[6]=4,	-- depth:1
[7]=4,	-- depth:1
[8]=4,	-- depth:1
[9]=4,	-- depth:1
[10]=4,	-- depth:1
[11]=4,	-- depth:1
[21]=22,	-- depth:1
},
hof_server_rank_reward={
{},
{min_rank=2,max_rank=2,reward_item={[0]=item_table[125],[1]=item_table[126],[2]=item_table[127],[3]=item_table[128],},},
{min_rank=3,max_rank=3,reward_item={[0]=item_table[129],[1]=item_table[126],[2]=item_table[130],[3]=item_table[128],},},
{min_rank=4,max_rank=4,reward_item={[0]=item_table[131],[1]=item_table[132],[2]=item_table[133],[3]=item_table[128],},},
{min_rank=5,max_rank=5,},
{min_rank=6,max_rank=6,reward_item={[0]=item_table[134],[1]=item_table[132],[2]=item_table[135],[3]=item_table[128],},},
{min_rank=7,max_rank=7,reward_item={[0]=item_table[134],[1]=item_table[136],[2]=item_table[135],[3]=item_table[128],},},
{min_rank=8,max_rank=8,reward_item={[0]=item_table[134],[1]=item_table[136],[2]=item_table[137],[3]=item_table[128],},},
{min_rank=9,max_rank=9,},
{min_rank=10,max_rank=10,},
{min_rank=11,max_rank=11,reward_item={[0]=item_table[138],[1]=item_table[139],[2]=item_table[39],[3]=item_table[128],},},
{min_rank=12,max_rank=12,},
{min_rank=13,max_rank=13,},
{min_rank=14,max_rank=14,reward_item={[0]=item_table[140],[1]=item_table[141],[2]=item_table[39],[3]=item_table[128],},},
{min_rank=15,max_rank=15,},
{min_rank=16,max_rank=16,}
},

hof_server_rank_reward_meta_table_map={
[5]=4,	-- depth:1
[9]=8,	-- depth:1
[10]=8,	-- depth:1
[12]=11,	-- depth:1
[13]=11,	-- depth:1
[15]=14,	-- depth:1
[16]=14,	-- depth:1
},
hof_person_rank_reward={
{show_title=9006,},
{min_rank=2,max_rank=2,reward_item={[0]=item_table[138],[1]=item_table[142],[2]=item_table[143],[3]=item_table[144],},show_title=9005,},
{min_rank=3,max_rank=3,reward_item={[0]=item_table[140],[1]=item_table[145],[2]=item_table[146],[3]=item_table[144],},show_title=9004,},
{min_rank=4,max_rank=10,reward_item={[0]=item_table[147],[1]=item_table[148],[2]=item_table[149],[3]=item_table[150],},},
{min_rank=11,max_rank=20,reward_item={[0]=item_table[147],[1]=item_table[151],[2]=item_table[149],[3]=item_table[150],},},
{min_rank=21,max_rank=50,reward_item={[0]=item_table[152],[1]=item_table[153],[2]=item_table[154],[3]=item_table[150],},},
{min_rank=51,max_rank=200,reward_item={[0]=item_table[152],[1]=item_table[153],[2]=item_table[155],[3]=item_table[150],},}
},

hof_person_rank_reward_meta_table_map={
},
worship_pos={
[1]={index=1,pos_y=113,},
[2]={index=2,pos_x=44,gather_pos_x=45,},
[3]={index=3,pos_x=58,gather_pos_x=58,},
[4]={index=4,pos_x=38,pos_y=108,gather_pos_x=40,},
[5]={index=5,pos_x=64,pos_y=109,gather_pos_x=63,}
},

worship_pos_meta_table_map={
},
other_default_table={open_day=11,open_world_level=1,open_level=500,summary_day=1,match_day=2,base_max_power=216,base_power_recover_time=300,rmb_buy_max_power=360,rmb_buy_power_recover_time=180,rmb_buy_siege_value_addition=5000,rmb_buy_type=157,rmb_buy_price=30,rmb_buy_time=30,rmb_buy_reward={[0]=item_table[156],[1]=item_table[157],[2]=item_table[158],[3]=item_table[46],},worship_reward={[0]=item_table[159],[1]=item_table[160],[2]=item_table[161],[3]=item_table[162],},be_worship_reward={[0]=item_table[163],[1]=item_table[164],[2]=item_table[165],[3]=item_table[166],},convene_cd=5,assemble_cd=5,model_show_type=3,model_bundle_name="uis/rawimages/a2_gnkq_shenbing1",model_asset_name="a2_gnkq_shenbing1.png",model_show_itemid="",display_pos="-230|-30",display_scale=0.7,rotation="0|0|0",convert_itemid=26181,open_time=1230,close_time=1800,tired_value_flag=2000,worship_scene_id=3299,worship_scene_pos="197,375",show_reward_item={[0]=item_table[167],[1]=item_table[8],[2]=item_table[168],[3]=item_table[169],[4]=item_table[170],[5]=item_table[171],[6]=item_table[172],[7]=item_table[173],[8]=item_table[174],[9]=item_table[175]},},

camp_default_table={seq=0,camp_name="灵虚·界",camp_icon=8,},

city_default_table={seq=0,type=4,connect_city="8|9|10|11|12|13|14|15",scene_id=3200,capture_tired=0,born_pos="160,160",capture_reward_item={[0]=item_table[167],[1]=item_table[6],[2]=item_table[3]},city_name="灵光域",city_icon_type=1,relegation_camp=-1,},

monster_default_table={city_seq=8,seq=0,monster_id=63500,need_power=15,kill_score=100,monster_refresh_interval=300,monster_pos="146,41",bg_type=2,boss_reward_item={[0]=item_table[167],[1]=item_table[8],[2]=item_table[168],[3]=item_table[169],[4]=item_table[170],[5]=item_table[171],[6]=item_table[172],[7]=item_table[173],[8]=item_table[174],[9]=item_table[175]},},

tired_hurt_reduce_default_table={min_tired=0,max_tired=99,reduce_scale=0,text="疲劳值较低，效率较高，请大胆攻城，占领越多奖励越丰厚",},

person_score_rank_reward_default_table={min_rank=1,max_rank=1,reward_item={[0]=item_table[44],[1]=item_table[176],[2]=item_table[39],[3]=item_table[177],},},

server_score_rank_reward_default_table={min_rank=1,max_rank=1,reward_item={[0]=item_table[178],[1]=item_table[179],[2]=item_table[180],[3]=item_table[181],},},

score_reward_default_table={seq=0,need_score=550,reward_item={[0]=item_table[182],[1]=item_table[160],[2]=item_table[161],[3]=item_table[163],},},

convert_default_table={seq=0,times_limit=10,item=item_table[183],stuff_id_1=26181,stuff_num_1=45,stuff_id_2=0,stuff_num_2=0,stuff_id_3=0,stuff_num_3=0,},

hof_server_rank_reward_default_table={min_rank=1,max_rank=1,reward_item={[0]=item_table[184],[1]=item_table[185],[2]=item_table[186],[3]=item_table[128],},},

hof_person_rank_reward_default_table={min_rank=1,max_rank=1,reward_item={[0]=item_table[134],[1]=item_table[187],[2]=item_table[188],[3]=item_table[128],},show_title=0,},

worship_pos_default_table={index=1,pos_x=51,pos_y=112,gather_pos_x=51,gather_pos_y=97,rotation="0|180|0",name_scale=0.5,name_pos="0|1.5|-3",name_rotation="30|0|0",}

}

