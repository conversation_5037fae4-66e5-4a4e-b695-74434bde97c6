-- Y-运营活动-幸福狂欢.xls
local item_table={
[1]={item_id=46560,num=3,is_bind=1},
[2]={item_id=27658,num=1,is_bind=1},
[3]={item_id=26356,num=2,is_bind=1},
[4]={item_id=26516,num=1,is_bind=1},
[5]={item_id=27909,num=2,is_bind=1},
[6]={item_id=30424,num=2,is_bind=1},
[7]={item_id=26358,num=2,is_bind=1},
[8]={item_id=26502,num=1,is_bind=1},
[9]={item_id=22074,num=2,is_bind=1},
[10]={item_id=32284,num=2,is_bind=1},
[11]={item_id=26359,num=2,is_bind=1},
[12]={item_id=26517,num=1,is_bind=1},
[13]={item_id=56321,num=1,is_bind=1},
[14]={item_id=56317,num=2,is_bind=1},
[15]={item_id=26357,num=2,is_bind=1},
[16]={item_id=26503,num=1,is_bind=1},
[17]={item_id=56322,num=1,is_bind=1},
[18]={item_id=39151,num=2,is_bind=1},
[19]={item_id=26360,num=2,is_bind=1},
[20]={item_id=26518,num=1,is_bind=1},
[21]={item_id=46560,num=1,is_bind=1},
[22]={item_id=48092,num=1,is_bind=1},
[23]={item_id=29622,num=1,is_bind=1},
[24]={item_id=46559,num=3,is_bind=1},
[25]={item_id=39988,num=1,is_bind=1},
[26]={item_id=26355,num=2,is_bind=1},
[27]={item_id=26501,num=1,is_bind=1},
}

return {
config_param={
{rewardlist=1,},
{start_server_day=17,end_server_day=24,},
{start_server_day=24,end_server_day=31,},
{start_server_day=31,end_server_day=38,},
{start_server_day=38,end_server_day=45,},
{start_server_day=45,end_server_day=52,},
{start_server_day=52,end_server_day=59,},
{start_server_day=59,end_server_day=66,},
{start_server_day=66,end_server_day=73,},
{start_server_day=73,end_server_day=80,},
{start_server_day=80,end_server_day=87,},
{start_server_day=87,end_server_day=94,tasklist=2,rewardlist=4,},
{start_server_day=94,end_server_day=101,},
{start_server_day=101,end_server_day=108,tasklist=2,rewardlist=3,},
{start_server_day=108,end_server_day=115,},
{start_server_day=115,end_server_day=122,},
{start_server_day=122,end_server_day=129,},
{start_server_day=129,end_server_day=136,},
{start_server_day=136,end_server_day=143,},
{start_server_day=143,end_server_day=150,},
{start_server_day=150,end_server_day=157,},
{start_server_day=157,end_server_day=164,},
{start_server_day=164,end_server_day=171,},
{start_server_day=171,end_server_day=178,},
{start_server_day=178,end_server_day=185,},
{start_server_day=185,end_server_day=999,}
},

config_param_meta_table_map={
[17]=1,	-- depth:1
[15]=17,	-- depth:2
[13]=17,	-- depth:2
[5]=17,	-- depth:2
[3]=17,	-- depth:2
[8]=12,	-- depth:1
[6]=14,	-- depth:1
[16]=14,	-- depth:1
[18]=14,	-- depth:1
[4]=14,	-- depth:1
[20]=12,	-- depth:1
[22]=12,	-- depth:1
[2]=14,	-- depth:1
[24]=12,	-- depth:1
[10]=12,	-- depth:1
[26]=12,	-- depth:1
},
reward={
{},
{bonus_id=1,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4]},bonus_points=25,},
{bonus_id=2,reward_item={[0]=item_table[5],[1]=item_table[6],[2]=item_table[7],[3]=item_table[8]},bonus_points=40,},
{bonus_id=3,reward_item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11],[3]=item_table[12]},bonus_points=50,},
{bonus_id=4,reward_item={[0]=item_table[13],[1]=item_table[14],[2]=item_table[15],[3]=item_table[16]},bonus_points=80,},
{bonus_id=5,reward_item={[0]=item_table[17],[1]=item_table[18],[2]=item_table[19],[3]=item_table[20]},bonus_points=110,},
{rewardlist=2,},
{rewardlist=2,},
{rewardlist=2,},
{rewardlist=2,},
{rewardlist=2,},
{rewardlist=2,},
{rewardlist=3,},
{rewardlist=3,},
{rewardlist=3,},
{rewardlist=3,},
{rewardlist=3,},
{rewardlist=3,},
{rewardlist=4,},
{rewardlist=4,},
{rewardlist=4,},
{rewardlist=4,},
{rewardlist=4,},
{rewardlist=4,}
},

reward_meta_table_map={
[22]=4,	-- depth:1
[21]=3,	-- depth:1
[20]=2,	-- depth:1
[18]=6,	-- depth:1
[17]=5,	-- depth:1
[16]=22,	-- depth:2
[12]=18,	-- depth:2
[14]=20,	-- depth:2
[23]=17,	-- depth:2
[11]=23,	-- depth:3
[10]=16,	-- depth:3
[9]=21,	-- depth:2
[8]=14,	-- depth:3
[15]=9,	-- depth:3
[24]=12,	-- depth:3
},
task={
{progress=10,task_icon="a1_btn_hundunmoyu",},
{task_id=2,task_type=2,task_sort=2,progress=10000,open_panel="counrty_map_task_view",task_icon="a1_btn_bantu",task_describe="夺仙宝图获得<color=#0f9c1c>%s</color><color=#0f9c1c>星币</color>|点狂欢值",},
{task_id=3,task_type=3,task_sort=3,progress=6,open_panel="boss#boss_personal",task_icon="a1_btn_xinmohuanjing",task_describe="击杀<color=#0f9c1c>%s</color>次<color=#0f9c1c>灵妖奇脉</color>|点狂欢值",},
{task_id=4,task_type=4,task_sort=4,open_panel="national_war",task_icon="a1_btn_bantu",task_describe="参与<color=#0f9c1c>%s</color>次<color=#0f9c1c>夺仙宝图</color>|点狂欢值",},
{task_id=5,task_type=5,task_sort=5,open_panel="ConquestWarView#conquest_war_zxzc",task_icon="a1_btn_eternal_night",task_describe="参与<color=#0f9c1c>%s</color>次<color=#0f9c1c>诛仙战场</color>|点狂欢值",},
{task_id=6,task_type=6,task_sort=6,open_panel="guild#guild_War",task_icon="a1_btn_guild_war",task_describe="参与<color=#0f9c1c>%s</color>次<color=#0f9c1c>仙盟争霸</color>|点狂欢值",},
{task_id=7,task_type=7,task_sort=7,progress=4,bonus_points=10,open_panel="fubenpanel#fubenpanel_exp",task_icon="a1_btn_fb",task_describe="参与<color=#0f9c1c>%s</color>次<color=#0f9c1c>副本日月修行</color>|点狂欢值",},
{task_id=8,task_type=8,task_sort=8,progress=60,bonus_points=10,open_panel="recharge",task_describe="充值<color=#0f9c1c>%s</color>灵玉|点狂欢值",},
{task_id=9,task_type=9,task_sort=9,progress=12,bonus_points=10,open_panel="boss#boss_world",task_icon="a1_btn_world_boss",task_describe="击杀<color=#0f9c1c>%s</color>次<color=#0f9c1c>伏魔战场</color>|点狂欢值",},
{task_id=10,task_type=10,task_sort=10,progress=500,bonus_points=10,open_panel="shop#Tab_Shop50",task_describe="花费<color=#0f9c1c>%s</color>元宝|点狂欢值",},
{task_id=11,task_type=11,task_sort=11,progress=300,bonus_points=20,open_panel="shop#Tab_Shop20",task_describe="花费<color=#0f9c1c>%s</color>灵玉|点狂欢值",},
{task_id=12,task_type=12,task_sort=12,bonus_points=20,open_panel="",task_icon="a1_btn_bizuo",task_describe="登录<color=#0f9c1c>%s</color>天|点狂欢值",},
{tasklist=2,task_describe="<color=#A23B28>击杀<color=#007F18>%s</color>次<color=#007F18>仙遗洞天</color></color>|<color=#A23B28>点狂欢值</color>",},
{tasklist=2,task_describe="<color=#A23B28>夺仙宝图获得<color=#007F18>%s</color><color=#007F18>星币</color></color>|<color=#A23B28>点狂欢值</color>",},
{tasklist=2,task_describe="<color=#A23B28>击杀<color=#007F18>%s</color>次<color=#007F18>灵妖奇脉</color></color>|<color=#A23B28>点狂欢值</color>",},
{tasklist=2,task_describe="<color=#A23B28>参与<color=#007F18>%s</color>次<color=#007F18>夺仙宝图</color></color>|<color=#A23B28>点狂欢值</color>",},
{tasklist=2,open_panel="bizuo#bizuo_act_hall",task_describe="<color=#A23B28>参与<color=#007F18>%s</color>次<color=#007F18>诛仙战场</color></color>|<color=#A23B28>点狂欢值</color>",},
{tasklist=2,task_describe="<color=#A23B28>参与<color=#007F18>%s</color>次<color=#007F18>仙盟争霸</color></color>|<color=#A23B28>点狂欢值</color>",},
{tasklist=2,task_describe="<color=#A23B28>参与<color=#007F18>%s</color>次<color=#007F18>副本日月修行</color></color>|<color=#A23B28>点狂欢值</color>",},
{tasklist=2,task_describe="<color=#A23B28>充值<color=#007F18>%s</color>灵玉</color>|<color=#A23B28>点狂欢值</color>",},
{tasklist=2,task_describe="<color=#A23B28>击杀<color=#007F18>%s</color>次<color=#007F18>伏魔战场</color></color>|<color=#A23B28>点狂欢值</color>",},
{tasklist=2,task_describe="<color=#A23B28>花费<color=#007F18>%s</color>元宝</color>|<color=#A23B28>点狂欢值</color>",},
{tasklist=2,task_describe="<color=#A23B28>花费<color=#007F18>%s</color>灵玉</color>|<color=#A23B28>点狂欢值</color>",},
{tasklist=2,task_describe="<color=#A23B28>登录<color=#007F18>%s</color>天</color>|<color=#A23B28>点狂欢值</color>",}
},

task_meta_table_map={
[13]=1,	-- depth:1
[18]=6,	-- depth:1
[17]=5,	-- depth:1
[16]=4,	-- depth:1
[23]=11,	-- depth:1
[14]=2,	-- depth:1
[15]=3,	-- depth:1
[20]=8,	-- depth:1
[22]=10,	-- depth:1
[24]=12,	-- depth:1
[19]=7,	-- depth:1
[21]=9,	-- depth:1
},
btn_config={
{},
{index=2,btn_name="狂欢获取",},
{rewardlist=2,},
{rewardlist=2,},
{rewardlist=3,},
{rewardlist=3,},
{rewardlist=4,},
{rewardlist=4,},
{rewardlist=5,},
{rewardlist=5,},
{rewardlist=6,},
{rewardlist=6,},
{rewardlist=7,},
{rewardlist=7,},
{rewardlist=8,},
{rewardlist=8,},
{rewardlist=9,},
{rewardlist=9,},
{rewardlist=10,},
{rewardlist=10,},
{rewardlist=11,},
{rewardlist=11,},
{rewardlist=12,},
{rewardlist=12,}
},

btn_config_meta_table_map={
[22]=2,	-- depth:1
[20]=22,	-- depth:2
[18]=20,	-- depth:3
[12]=18,	-- depth:4
[14]=12,	-- depth:5
[10]=14,	-- depth:6
[8]=10,	-- depth:7
[6]=8,	-- depth:8
[4]=6,	-- depth:9
[16]=4,	-- depth:10
[24]=16,	-- depth:11
},
interface={
{},
{rewardlist=2,},
{rewardlist=3,title_name="happy_kh_title_2",huanlezhi_bg="happy_kh_bg_2",model_dizuo="total_dizuo_1",btn_bg="flower_toggle_bg",btn_hight_bg="flower_toggle_bg_hl",normal_bg="total_reward_bg_1",special_bg="total_reward_bg_1",reward_color="#A23B28",reward_value_color="#007F18",},
{rewardlist=4,},
{rewardlist=5,},
{rewardlist=6,},
{rewardlist=7,},
{rewardlist=8,},
{rewardlist=9,},
{rewardlist=10,},
{rewardlist=11,},
{rewardlist=12,}
},

interface_meta_table_map={
[4]=3,	-- depth:1
},
config_param_default_table={start_server_day=10,end_server_day=17,week_index=5,tasklist=1,rewardlist=2,open_level=100,render_type=1,render_int_param1=0,render_string_param1="uis/view/operation_happy_kuanghuan_prefab",render_string_param2="kuanghuan_model_1",mw_scale=1,mw_position="235|58|0",mw_rotation="0|0|0",reward_position="-234|100#-368|-20#-100|-20",reward_item={[0]=item_table[21],[1]=item_table[22],[2]=item_table[23]},},

reward_default_table={rewardlist=1,bonus_id=0,reward_item={[0]=item_table[24],[1]=item_table[25],[2]=item_table[26],[3]=item_table[27]},bonus_points=10,if_show=0,exchange_description="达到|狂欢值可领取",},

task_default_table={tasklist=1,pre_task_id=-1,task_id=1,task_type=1,task_sort=1,if_re=0,progress=1,param_0_0=0,param_1=0,bonus_points=5,if_open=1,select_type=1,open_panel="boss#boss_vip",open_param="",is_close_panel=0,task_icon_path=1,task_icon="a1_btn_market",task_describe="击杀<color=#0f9c1c>%s</color>次<color=#0f9c1c>仙遗洞天</color>|点狂欢值",},

btn_config_default_table={rewardlist=1,index=1,btn_name="狂欢有礼",},

interface_default_table={rewardlist=1,raw_bg="happy_kh_title_bg_1",title_name="happy_kh_title_1",huanlezhi_bg="happy_kh_bg_1",model_dizuo="total_dizuo",btn_bg="xianshi_btn_1",btn_hight_bg="xianshi_btn_2",qiqiu="icon_exchange_shop",normal_bg="total_reward_bg",normal_title_bg="normal_title_bg",special_bg="total_reward_bg",special_title_bg="special_title_bg",tip_label="获取狂欢值，领活动大礼！",rule_1="1.通过完成任务可获得幸福狂欢值\n2.狂欢值达到一定进度可获得对应奖励",reward_color="#ffffff",reward_value_color="#95d12b",}

}

