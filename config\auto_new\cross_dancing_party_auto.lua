return {
	["other"]={
		{cake_get_qingyuan_val=1000,cake_get_score=300,scene_id=6125,answer_extra_reward_fac=50,answer_score=50,answer_start_time_s=60,answer_count=20,dancing_last_time_s=240,answer_time_s=10,is_open=0,answer_qingyuan_val=100,dancing_start_time_s=60,couple_online_reward_fac=20,cake_get_exp_fac=3,cake_feed_times=1,cake_init_num=10,},},
	["question_default_table"]={answer_A="会",answer_B="不会",question="恋人喜欢吃什么类型的菜？",},
	["exp_cfg_default_table"]={},
	["time_rank_reward"]={
		{reward_score=1000,},
		{reward_score=800,reward_item={item_id=22000,num=1,is_bind=1,},min_rank=2,max_rank=2,},
		{reward_item={item_id=22000,num=1,is_bind=1,},min_rank=3,max_rank=3,},
		{reward_score=400,reward_item={item_id=22000,num=1,is_bind=1,},min_rank=4,max_rank=10,},
		{reward_score=200,reward_item={item_id=22000,num=1,is_bind=1,},min_rank=11,max_rank=20,},},
	["exp_cfg"]={
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},},
	["activity_open_time"]={
		{},
		{},
		{},
		{},
		{},
		{},
		{},},
	["time_rank_reward_default_table"]={
		reward_item={item_id=22000,num=1,is_bind=1,},reward_score=600,max_rank=1,min_rank=1,},
	["activity_open_time_default_table"]={},
	["song_cfg_default_table"]={total_time_ms=1516,type=3,song_id=1,time_ms=13086,},
	["song_cfg"]={
		{total_time_ms=742,type=1,time_ms=442,},
		{total_time_ms=778,type=2,time_ms=478,},
		{total_time_ms=819,time_ms=519,},
		{total_time_ms=861,type=2,time_ms=561,},
		{total_time_ms=945,time_ms=645,},
		{total_time_ms=980,type=1,time_ms=680,},
		{total_time_ms=1022,time_ms=722,},
		{total_time_ms=1104,type=1,time_ms=804,},
		{total_time_ms=1144,type=2,time_ms=844,},
		{total_time_ms=1193,time_ms=893,},
		{total_time_ms=1390,type=4,time_ms=1090,},
		{total_time_ms=1432,time_ms=1132,},
		{total_time_ms=1475,type=4,time_ms=1175,},
		{time_ms=1216,},
		{total_time_ms=1600,type=1,time_ms=1300,},
		{total_time_ms=1640,type=4,time_ms=1340,},
		{total_time_ms=1680,type=2,time_ms=1380,},
		{total_time_ms=1758,type=1,time_ms=1458,},
		{total_time_ms=1842,type=4,time_ms=1542,},
		{total_time_ms=1921,type=2,time_ms=1621,},
		{total_time_ms=1961,type=4,time_ms=1661,},
		{total_time_ms=2001,type=4,time_ms=1701,},
		{total_time_ms=2083,type=1,time_ms=1783,},
		{total_time_ms=2153,time_ms=1853,},
		{total_time_ms=2247,type=1,time_ms=1947,},
		{total_time_ms=2326,type=4,time_ms=2026,},
		{total_time_ms=2359,type=2,time_ms=2059,},
		{total_time_ms=2400,type=4,time_ms=2100,},
		{total_time_ms=2483,type=1,time_ms=2183,},
		{total_time_ms=2585,time_ms=2285,},
		{total_time_ms=2669,type=2,time_ms=2369,},
		{total_time_ms=2726,type=1,time_ms=2426,},
		{total_time_ms=2756,time_ms=2456,},
		{total_time_ms=2802,type=4,time_ms=2502,},
		{total_time_ms=2843,type=1,time_ms=2543,},
		{total_time_ms=2924,type=2,time_ms=2624,},
		{total_time_ms=2964,type=1,time_ms=2664,},
		{total_time_ms=3006,type=4,time_ms=2706,},
		{total_time_ms=3086,time_ms=2786,},
		{total_time_ms=3125,type=2,time_ms=2825,},
		{total_time_ms=3176,type=1,time_ms=2876,},
		{total_time_ms=3226,time_ms=2926,},
		{total_time_ms=3259,type=4,time_ms=2959,},
		{total_time_ms=3295,type=2,time_ms=2995,},
		{total_time_ms=3333,type=1,time_ms=3033,},
		{total_time_ms=3384,time_ms=3084,},
		{total_time_ms=3412,type=4,time_ms=3112,},
		{total_time_ms=3453,type=2,time_ms=3153,},
		{total_time_ms=3492,type=2,time_ms=3192,},
		{total_time_ms=3572,type=4,time_ms=3272,},
		{total_time_ms=3615,type=1,time_ms=3315,},
		{total_time_ms=3656,time_ms=3356,},
		{total_time_ms=3739,type=2,time_ms=3439,},
		{total_time_ms=3775,type=1,time_ms=3475,},
		{total_time_ms=3835,type=2,time_ms=3535,},
		{total_time_ms=3911,type=1,time_ms=3611,},
		{total_time_ms=3992,time_ms=3692,},
		{total_time_ms=4071,type=4,time_ms=3771,},
		{total_time_ms=4117,type=1,time_ms=3817,},
		{total_time_ms=4160,time_ms=3860,},
		{total_time_ms=4240,time_ms=3940,},
		{total_time_ms=4286,type=4,time_ms=3986,},
		{total_time_ms=4322,type=2,time_ms=4022,},
		{total_time_ms=4361,type=1,time_ms=4061,},
		{total_time_ms=4401,type=2,time_ms=4101,},
		{total_time_ms=4440,type=2,time_ms=4140,},
		{total_time_ms=4482,type=4,time_ms=4182,},
		{total_time_ms=4559,time_ms=4259,},
		{total_time_ms=4604,type=1,time_ms=4304,},
		{total_time_ms=4641,type=1,time_ms=4341,},
		{total_time_ms=4727,time_ms=4427,},
		{total_time_ms=4776,time_ms=4476,},
		{total_time_ms=4815,type=2,time_ms=4515,},
		{total_time_ms=4912,type=4,time_ms=4612,},
		{total_time_ms=4987,type=1,time_ms=4687,},
		{total_time_ms=5071,time_ms=4771,},
		{total_time_ms=5151,type=2,time_ms=4851,},
		{total_time_ms=5248,type=1,time_ms=4948,},
		{total_time_ms=5334,type=2,time_ms=5034,},
		{total_time_ms=5377,time_ms=5077,},
		{total_time_ms=5423,type=1,time_ms=5123,},
		{total_time_ms=5463,type=4,time_ms=5163,},
		{total_time_ms=5506,type=2,time_ms=5206,},
		{total_time_ms=5586,type=1,time_ms=5286,},
		{total_time_ms=5675,time_ms=5375,},
		{total_time_ms=5754,type=2,time_ms=5454,},
		{total_time_ms=5791,type=4,time_ms=5491,},
		{total_time_ms=5836,time_ms=5536,},
		{total_time_ms=6030,type=1,time_ms=5730,},
		{total_time_ms=6066,type=1,time_ms=5766,},
		{total_time_ms=6113,type=4,time_ms=5813,},
		{total_time_ms=6154,type=1,time_ms=5854,},
		{total_time_ms=6243,type=2,time_ms=5943,},
		{total_time_ms=6319,type=2,time_ms=6019,},
		{total_time_ms=6405,time_ms=6105,},
		{total_time_ms=6496,type=4,time_ms=6196,},
		{total_time_ms=6586,type=1,time_ms=6286,},
		{total_time_ms=6670,type=1,time_ms=6370,},
		{total_time_ms=6751,type=2,time_ms=6451,},
		{total_time_ms=6831,type=4,time_ms=6531,},
		{total_time_ms=6923,type=1,time_ms=6623,},
		{total_time_ms=7000,type=2,time_ms=6700,},
		{total_time_ms=7087,type=1,time_ms=6787,},
		{total_time_ms=7167,type=4,time_ms=6867,},
		{total_time_ms=7254,type=2,time_ms=6954,},
		{total_time_ms=7331,type=4,time_ms=7031,},
		{total_time_ms=7418,type=4,time_ms=7118,},
		{total_time_ms=7509,type=2,time_ms=7209,},
		{total_time_ms=7596,type=1,time_ms=7296,},
		{total_time_ms=7680,type=1,time_ms=7380,},
		{total_time_ms=7761,type=2,time_ms=7461,},
		{total_time_ms=7847,type=4,time_ms=7547,},
		{total_time_ms=8196,time_ms=7896,},
		{total_time_ms=8226,time_ms=7926,},
		{total_time_ms=8259,time_ms=7959,},
		{total_time_ms=8345,type=2,time_ms=8045,},
		{total_time_ms=8426,type=4,time_ms=8126,},
		{total_time_ms=8475,type=2,time_ms=8175,},
		{total_time_ms=8520,type=2,time_ms=8220,},
		{total_time_ms=8551,type=4,time_ms=8251,},
		{total_time_ms=8596,type=1,time_ms=8296,},
		{total_time_ms=8670,time_ms=8370,},
		{total_time_ms=8750,type=1,time_ms=8450,},
		{total_time_ms=8840,type=2,time_ms=8540,},
		{total_time_ms=8877,type=2,time_ms=8577,},
		{total_time_ms=8920,type=4,time_ms=8620,},
		{total_time_ms=8993,type=1,time_ms=8693,},
		{total_time_ms=9077,type=4,time_ms=8777,},
		{total_time_ms=9162,time_ms=8862,},
		{total_time_ms=9242,type=2,time_ms=8942,},
		{total_time_ms=9328,time_ms=9028,},
		{total_time_ms=9365,time_ms=9065,},
		{total_time_ms=9407,type=1,time_ms=9107,},
		{total_time_ms=9531,time_ms=9231,},
		{total_time_ms=9593,type=4,time_ms=9293,},
		{total_time_ms=9754,type=1,time_ms=9454,},
		{total_time_ms=9812,type=2,time_ms=9512,},
		{total_time_ms=9861,type=2,time_ms=9561,},
		{total_time_ms=9944,time_ms=9644,},
		{total_time_ms=10045,time_ms=9745,},
		{total_time_ms=10112,type=4,time_ms=9812,},
		{total_time_ms=10201,type=2,time_ms=9901,},
		{total_time_ms=10292,type=1,time_ms=9992,},
		{total_time_ms=10382,time_ms=10082,},
		{total_time_ms=10463,type=4,time_ms=10163,},
		{total_time_ms=10550,time_ms=10250,},
		{total_time_ms=10636,type=1,time_ms=10336,},
		{total_time_ms=10722,type=2,time_ms=10422,},
		{total_time_ms=10808,time_ms=10508,},
		{total_time_ms=10942,type=1,time_ms=10642,},
		{total_time_ms=10993,type=2,time_ms=10693,},
		{total_time_ms=11035,time_ms=10735,},
		{total_time_ms=11096,type=4,time_ms=10796,},
		{total_time_ms=11173,time_ms=10873,},
		{total_time_ms=11253,type=4,time_ms=10953,},
		{total_time_ms=11341,type=2,time_ms=11041,},
		{total_time_ms=11427,time_ms=11127,},
		{total_time_ms=11509,type=2,time_ms=11209,},
		{total_time_ms=11586,type=2,time_ms=11286,},
		{total_time_ms=11661,type=1,time_ms=11361,},
		{total_time_ms=11741,type=4,time_ms=11441,},
		{total_time_ms=11825,type=1,time_ms=11525,},
		{total_time_ms=11909,type=4,time_ms=11609,},
		{total_time_ms=11992,type=4,time_ms=11692,},
		{total_time_ms=12078,type=2,time_ms=11778,},
		{total_time_ms=12159,type=2,time_ms=11859,},
		{total_time_ms=12244,time_ms=11944,},
		{total_time_ms=12344,type=1,time_ms=12044,},
		{total_time_ms=12443,type=4,time_ms=12143,},
		{total_time_ms=12527,time_ms=12227,},
		{total_time_ms=12566,type=2,time_ms=12266,},
		{total_time_ms=12613,time_ms=12313,},
		{total_time_ms=12707,type=4,time_ms=12407,},
		{total_time_ms=12810,type=2,time_ms=12510,},
		{total_time_ms=12897,type=1,time_ms=12597,},
		{total_time_ms=12978,time_ms=12678,},
		{total_time_ms=13080,time_ms=12780,},
		{total_time_ms=13184,type=2,time_ms=12884,},
		{total_time_ms=13217,time_ms=12917,},
		{total_time_ms=13268,type=1,time_ms=12968,},
		{total_time_ms=13386,type=2,},
		{total_time_ms=13502,time_ms=13202,},
		{total_time_ms=14006,type=1,time_ms=13706,},
		{total_time_ms=14035,type=1,time_ms=13735,},
		{total_time_ms=14063,type=2,time_ms=13763,},
		{total_time_ms=14096,time_ms=13796,},
		{total_time_ms=14170,time_ms=13870,},
		{total_time_ms=14213,type=4,time_ms=13913,},
		{total_time_ms=14260,type=1,time_ms=13960,},
		{total_time_ms=14337,time_ms=14037,},
		{total_time_ms=14376,type=2,time_ms=14076,},
		{total_time_ms=14420,time_ms=14120,},
		{total_time_ms=14456,type=4,time_ms=14156,},
		{total_time_ms=14509,time_ms=14209,},
		{total_time_ms=14640,type=1,time_ms=14340,},
		{total_time_ms=14666,type=4,time_ms=14366,},
		{total_time_ms=14702,type=4,time_ms=14402,},
		{total_time_ms=14746,type=2,time_ms=14446,},
		{total_time_ms=14827,type=4,time_ms=14527,},
		{total_time_ms=14867,type=2,time_ms=14567,},
		{total_time_ms=14907,type=1,time_ms=14607,},
		{total_time_ms=14998,time_ms=14698,},
		{total_time_ms=15031,time_ms=14731,},
		{total_time_ms=15078,type=4,time_ms=14778,},
		{total_time_ms=15289,type=2,time_ms=14989,},
		{total_time_ms=15322,time_ms=15022,},
		{total_time_ms=15367,type=4,time_ms=15067,},
		{total_time_ms=15408,type=2,time_ms=15108,},
		{total_time_ms=15497,time_ms=15197,},
		{total_time_ms=15533,type=1,time_ms=15233,},
		{total_time_ms=15575,type=4,time_ms=15275,},
		{total_time_ms=15659,time_ms=15359,},
		{total_time_ms=15740,time_ms=15440,},
		{total_time_ms=15829,type=2,time_ms=15529,},
		{total_time_ms=15866,type=1,time_ms=15566,},
		{total_time_ms=15909,time_ms=15609,},
		{total_time_ms=16000,time_ms=15700,},
		{total_time_ms=16042,type=1,time_ms=15742,},
		{total_time_ms=16089,type=1,time_ms=15789,},
		{total_time_ms=16179,type=2,time_ms=15879,},
		{total_time_ms=16261,time_ms=15961,},
		{total_time_ms=16344,type=4,time_ms=16044,},
		{total_time_ms=16425,type=1,time_ms=16125,},
		{total_time_ms=16645,time_ms=16345,},
		{total_time_ms=16682,type=2,time_ms=16382,},
		{total_time_ms=16732,type=1,time_ms=16432,},
		{total_time_ms=16772,type=2,time_ms=16472,},
		{total_time_ms=16859,time_ms=16559,},
		{total_time_ms=16900,type=1,time_ms=16600,},
		{total_time_ms=16940,type=4,time_ms=16640,},
		{total_time_ms=17023,type=4,time_ms=16723,},
		{total_time_ms=17063,time_ms=16763,},
		{total_time_ms=17112,type=4,time_ms=16812,},
		{total_time_ms=17206,time_ms=16906,},
		{total_time_ms=17241,type=1,time_ms=16941,},
		{total_time_ms=17278,type=2,time_ms=16978,},
		{total_time_ms=17444,type=2,time_ms=17144,},
		{total_time_ms=17525,type=1,time_ms=17225,},
		{total_time_ms=17563,type=1,time_ms=17263,},
		{total_time_ms=17605,time_ms=17305,},
		{total_time_ms=17686,type=2,time_ms=17386,},
		{total_time_ms=17722,type=4,time_ms=17422,},
		{total_time_ms=17784,time_ms=17484,},
		{total_time_ms=17861,type=4,time_ms=17561,},
		{total_time_ms=17944,type=2,time_ms=17644,},
		{total_time_ms=18024,type=4,time_ms=17724,},
		{total_time_ms=18070,type=1,time_ms=17770,},
		{total_time_ms=18113,type=4,time_ms=17813,},
		{total_time_ms=18206,time_ms=17906,},
		{total_time_ms=18283,type=1,time_ms=17983,},
		{total_time_ms=18324,type=1,time_ms=18024,},
		{total_time_ms=18365,time_ms=18065,},
		{total_time_ms=18410,type=1,time_ms=18110,},
		{total_time_ms=18450,type=4,time_ms=18150,},
		{total_time_ms=18539,time_ms=18239,},
		{total_time_ms=18572,time_ms=18272,},
		{total_time_ms=18615,type=2,time_ms=18315,},
		{total_time_ms=18698,type=1,time_ms=18398,},
		{total_time_ms=18787,type=4,time_ms=18487,},
		{total_time_ms=18878,type=4,time_ms=18578,},
		{total_time_ms=18963,type=2,time_ms=18663,},
		{total_time_ms=19040,time_ms=18740,},
		{total_time_ms=19125,type=1,time_ms=18825,},
		{total_time_ms=19219,type=4,time_ms=18919,},
		{total_time_ms=19254,time_ms=18954,},
		{total_time_ms=19292,type=4,time_ms=18992,},
		{total_time_ms=19388,time_ms=19088,},
		{total_time_ms=19428,type=1,time_ms=19128,},
		{total_time_ms=19471,type=4,time_ms=19171,},
		{total_time_ms=19551,type=2,time_ms=19251,},
		{total_time_ms=19638,type=1,time_ms=19338,},
		{total_time_ms=19721,type=2,time_ms=19421,},
		{total_time_ms=19776,time_ms=19476,},
		{total_time_ms=19807,type=1,time_ms=19507,},
		{total_time_ms=19860,type=2,time_ms=19560,},
		{total_time_ms=19894,type=4,time_ms=19594,},
		{total_time_ms=19934,time_ms=19634,},
		{total_time_ms=19984,type=1,time_ms=19684,},
		{total_time_ms=20048,type=4,time_ms=19748,},
		{total_time_ms=20074,type=2,time_ms=19774,},
		{total_time_ms=20119,time_ms=19819,},
		{total_time_ms=20178,type=1,time_ms=19878,},
		{total_time_ms=20258,type=1,time_ms=19958,},
		{total_time_ms=20371,type=2,time_ms=20071,},
		{total_time_ms=20475,type=1,time_ms=20175,},
		{total_time_ms=20585,time_ms=20285,},
		{total_time_ms=20702,type=1,time_ms=20402,},
		{total_time_ms=20954,type=4,time_ms=20654,},},
	["question"]={
		{answer_A="敢",answer_B="不敢",question="敢跟恋人互换微信来看吗？",},
		{answer_A="是的",answer_B="认识少量",question="你熟识各种车的标志吗？",},
		{answer_A="愿意",answer_B="不愿意",question="愿意用每个月10%的工资交换干净的空气和放心的食物吗？",},
		{question="在家里会随手关掉多余的灯吗？",},
		{question="你会因恋人偶尔温暖的小举动而感动吗？",},
		{answer_A="情感剧",answer_B="喜剧",question="假如可以一起看场电影，会选择什么类型的片子？",},
		{answer_A="有",answer_B="没有",question="恋人有口头禅吗？",},
		{answer_A="有",answer_B="没有",question="恋人有挑食的习惯吗？",},
		{question="恋人吃苹果会削皮吗？",},
		{answer_A="清淡",answer_B="麻辣",},
		{answer_A="黑色",answer_B="白色",question="黑色和白色相比你更喜欢哪个？",},
		{question="晚饭过后会适当做些运动吗？",},
		{question="休假的时候会坚持早起吗？",},
		{answer_A="粥",answer_B="汉堡",question="汉堡和粥会选择哪一样？",},
		{question="会因为刚看过鬼片而害怕夜晚吗？",},
		{answer_A="牛奶",answer_B="酸奶",question="牛奶和酸奶更喜欢哪一种？",},
		{question="会因为偶遇偶像而紧张兴奋吗？",},
		{answer_A="火锅",answer_B="麻辣烫",question="火锅或者麻辣烫喜欢哪种？",},
		{answer_A="先刷牙",answer_B="先洗脸",question="早上先刷牙还是先洗脸？",},
		{question="晚上12点前会睡觉吗？",},
		{answer_A="咖啡",answer_B="茶",question="喜欢喝咖啡还是喝茶？",},
		{question="如果吵架了，会主动找对方认错吗？",},
		{answer_A="是",answer_B="不是",question="你认为女生是否可以向男生主动表白？",},
		{answer_A="喜欢",answer_B="不喜欢",question="喜欢吃油炸食品吗？",},
		{answer_A="是",answer_B="不是",question="洗衣服是否全部都用洗衣机？",},
		{answer_A="是",answer_B="不是",question="你是否是个浪漫的人？",},
		{answer_A="丢人",answer_B="不丢人",question="你认为男生给女朋友买卫生巾丢人吗？",},
		{answer_A="是",answer_B="不是",question="是否喜欢喝碳酸饮料？",},
		{answer_A="喜欢",answer_B="不喜欢",question="喜欢看综艺节目吗？",},
		{question="晚上睡觉时会把手机关机吗？",},
		{question="看情感剧时会因为剧情变化而喜怒哀乐吗？",},
		{answer_A="雨",answer_B="雪",question="喜欢雨还是喜欢雪？",},
		{answer_A="是",answer_B="不是",question="你是一个颜值控吗？",},
		{answer_A="宠物狗",answer_B="宠物猫",question="宠物狗或宠物猫你更喜欢养哪个？",},
		{answer_A="丰盛",answer_B="简单",question="你认为晚餐应该丰盛一点还是应该简单一点？",},
		{answer_A="在意",answer_B="不在意",question="是否会在意自己鞋子是否干净？",},
		{answer_A="喜欢",answer_B="不喜欢",question="喜欢看新闻联播吗？",},
		{answer_A="应该",answer_B="不应该",question="你觉得女生应该强势一点吗？",},
		{answer_A="应该",answer_B="不应该",question="你觉得男生应该主动一点吗？",},
		{answer_A="有必要",answer_B="没必要",question="你认为有必要刻意去买奢侈品吗？",},
		{answer_A="海边",answer_B="山巅",question="更喜欢海边还是山巅？",},
		{answer_A="喜欢",answer_B="不喜欢",question="喜欢看动漫吗？",},
		{answer_A="喜欢",answer_B="不喜欢",question="喜欢看推理电影吗？",},
		{answer_A="喜欢",answer_B="不喜欢",question="喜欢看鬼片或悬疑片吗？",},
		{answer_A="百合",answer_B="玫瑰",question="喜欢百合还是玫瑰？",},
		{question="同学聚会会喝酒吗？",},
		{answer_A="面试",answer_B="打团",question="当你在街上看到一个男生西装革履，你认为他要去干嘛？",},
		{question="闲暇时间会选择读书吗？",},
		{answer_A="相信",answer_B="不相信",question="你相信一见钟情吗？",},
		{answer_A="坚信",answer_B="不坚信",question="你坚信爱情可以从一而终吗？",},},
}
