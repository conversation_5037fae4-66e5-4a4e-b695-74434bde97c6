-- Y-运营活动-招财猫.xls

return {
other={
{}
},

other_meta_table_map={
},
recharge_cfg={
{recharge_value=6,},
{min_count=2,max_count=2,},
{min_count=3,max_count=3,},
{min_count=4,max_count=4,recharge_value=12,},
{min_count=5,max_count=5,recharge_value=30,},
{min_count=6,max_count=6,recharge_value=60,},
{min_count=7,max_count=7,recharge_value=98,},
{min_count=8,max_count=8,recharge_value=128,},
{min_count=9,max_count=9,recharge_value=198,},
{min_count=10,max_count=10,recharge_value=328,},
{min_count=11,max_count=11,},
{min_count=12,max_count=12,},
{min_count=13,max_count=13,},
{min_count=14,max_count=14,},
{min_count=15,max_count=15,},
{min_count=16,max_count=16,},
{min_count=17,max_count=17,},
{min_count=18,max_count=18,},
{min_count=19,max_count=19,},
{min_count=20,max_count=20,},
{min_count=21,max_count=21,},
{min_count=22,max_count=22,},
{min_count=23,max_count=23,},
{min_count=24,max_count=24,},
{min_count=25,max_count=25,},
{min_count=26,max_count=26,},
{min_count=27,max_count=27,},
{min_count=28,max_count=28,},
{min_count=29,max_count=29,},
{min_count=30,max_count=9999,}
},

recharge_cfg_meta_table_map={
[3]=4,	-- depth:1
[2]=1,	-- depth:1
},
consume_cfg={
{consume_value=60,},
{min_count=2,max_count=2,},
{min_count=3,max_count=3,},
{min_count=4,max_count=4,consume_value=120,},
{min_count=5,max_count=5,consume_value=300,},
{min_count=6,max_count=6,consume_value=600,},
{min_count=7,max_count=7,consume_value=980,},
{min_count=8,max_count=8,consume_value=1280,},
{min_count=9,max_count=9,consume_value=1980,},
{min_count=10,max_count=10,consume_value=3280,},
{min_count=11,max_count=11,},
{min_count=12,max_count=12,},
{min_count=13,max_count=13,},
{min_count=14,max_count=14,},
{min_count=15,max_count=15,},
{min_count=16,max_count=16,},
{min_count=17,max_count=17,},
{min_count=18,max_count=18,},
{min_count=19,max_count=19,},
{min_count=20,max_count=20,},
{min_count=21,max_count=21,},
{min_count=22,max_count=22,},
{min_count=23,max_count=23,},
{min_count=24,max_count=24,},
{min_count=25,max_count=25,},
{min_count=26,max_count=26,},
{min_count=27,max_count=27,},
{min_count=28,max_count=28,},
{min_count=29,max_count=29,},
{min_count=30,max_count=9999,}
},

consume_cfg_meta_table_map={
[3]=4,	-- depth:1
[2]=1,	-- depth:1
},
reward_grade_cfg={
{},
{multiple_value=150,},
{multiple_value=200,},
{multiple_value=250,},
{multiple_value=300,},
{multiple_value=350,},
{multiple_value=400,},
{multiple_value=600,}
},

reward_grade_cfg_meta_table_map={
},
other_default_table={open_level=1,server_draw_count=9999,news_count="50|20|5",show_num=1,show_item=91497,},

recharge_cfg_default_table={min_count=1,max_count=1,recharge_value=648,},

consume_cfg_default_table={min_count=1,max_count=1,consume_value=6480,money_type=1,},

reward_grade_cfg_default_table={multiple_value=110,}

}

