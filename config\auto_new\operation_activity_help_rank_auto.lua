-- Y-运营活动-冲榜助力.xls
local item_table={
[1]={item_id=26130,num=30,is_bind=1},
[2]={item_id=22099,num=980,is_bind=1},
[3]={item_id=26369,num=10,is_bind=1},
[4]={item_id=26345,num=50,is_bind=1},
[5]={item_id=26130,num=60,is_bind=1},
[6]={item_id=22099,num=1980,is_bind=1},
[7]={item_id=26369,num=20,is_bind=1},
[8]={item_id=26345,num=100,is_bind=1},
[9]={item_id=26130,num=10,is_bind=1},
[10]={item_id=26191,num=1,is_bind=1},
[11]={item_id=22099,num=300,is_bind=1},
[12]={item_id=28847,num=1,is_bind=1},
[13]={item_id=26193,num=1,is_bind=1},
[14]={item_id=28848,num=1,is_bind=1},
[15]={item_id=26193,num=2,is_bind=1},
[16]={item_id=28849,num=1,is_bind=1},
[17]={item_id=62004,num=1,is_bind=1},
[18]={item_id=30442,num=10,is_bind=1},
[19]={item_id=62004,num=2,is_bind=1},
[20]={item_id=30442,num=30,is_bind=1},
[21]={item_id=22850,num=1,is_bind=1},
[22]={item_id=29615,num=10,is_bind=1},
[23]={item_id=23273,num=1,is_bind=1},
[24]={item_id=29615,num=30,is_bind=1},
[25]={item_id=23270,num=1,is_bind=1},
[26]={item_id=29615,num=80,is_bind=1},
[27]={item_id=27410,num=1,is_bind=1},
[28]={item_id=27410,num=2,is_bind=1},
[29]={item_id=29767,num=1,is_bind=1},
[30]={item_id=39478,num=1,is_bind=1},
[31]={item_id=29768,num=1,is_bind=1},
[32]={item_id=39195,num=1,is_bind=1},
[33]={item_id=29769,num=1,is_bind=1},
[34]={item_id=39193,num=1,is_bind=1},
[35]={item_id=26078,num=1,is_bind=1},
[36]={item_id=26199,num=5,is_bind=1},
[37]={item_id=26079,num=1,is_bind=1},
[38]={item_id=26199,num=10,is_bind=1},
[39]={item_id=26080,num=1,is_bind=1},
[40]={item_id=26199,num=20,is_bind=1},
[41]={item_id=22531,num=1,is_bind=1},
[42]={item_id=26200,num=5,is_bind=1},
[43]={item_id=62000,num=5,is_bind=1},
[44]={item_id=30423,num=5,is_bind=1},
[45]={item_id=22010,num=1,is_bind=1},
[46]={item_id=27657,num=5,is_bind=1},
[47]={item_id=22072,num=5,is_bind=1},
[48]={item_id=26391,num=5,is_bind=1},
[49]={item_id=26367,num=6,is_bind=1},
[50]={item_id=26368,num=6,is_bind=1},
[51]={item_id=26345,num=6,is_bind=1},
[52]={item_id=26344,num=60,is_bind=1},
[53]={item_id=26367,num=18,is_bind=1},
[54]={item_id=26368,num=18,is_bind=1},
[55]={item_id=26345,num=18,is_bind=1},
[56]={item_id=26344,num=180,is_bind=1},
[57]={item_id=48071,num=1,is_bind=1},
[58]={item_id=28853,num=1,is_bind=1},
[59]={item_id=26415,num=10,is_bind=1},
[60]={item_id=43077,num=2,is_bind=1},
[61]={item_id=28853,num=2,is_bind=1},
[62]={item_id=26415,num=30,is_bind=1},
[63]={item_id=43078,num=1,is_bind=1},
[64]={item_id=48071,num=2,is_bind=1},
[65]={item_id=28853,num=3,is_bind=1},
[66]={item_id=26415,num=50,is_bind=1},
[67]={item_id=43078,num=2,is_bind=1},
[68]={item_id=30443,num=10,is_bind=1},
[69]={item_id=22531,num=10,is_bind=1},
[70]={item_id=22615,num=3,is_bind=1},
[71]={item_id=62000,num=20,is_bind=1},
[72]={item_id=30443,num=20,is_bind=1},
[73]={item_id=22531,num=20,is_bind=1},
[74]={item_id=22615,num=4,is_bind=1},
[75]={item_id=62000,num=40,is_bind=1},
[76]={item_id=30443,num=30,is_bind=1},
[77]={item_id=22531,num=30,is_bind=1},
[78]={item_id=22615,num=5,is_bind=1},
[79]={item_id=62000,num=80,is_bind=1},
[80]={item_id=39109,num=1,is_bind=1},
[81]={item_id=30425,num=1,is_bind=1},
[82]={item_id=29615,num=1,is_bind=1},
[83]={item_id=30424,num=2,is_bind=1},
[84]={item_id=39109,num=2,is_bind=1},
[85]={item_id=29615,num=5,is_bind=1},
[86]={item_id=39109,num=3,is_bind=1},
[87]={item_id=30425,num=2,is_bind=1},
[88]={item_id=29615,num=8,is_bind=1},
[89]={item_id=30424,num=3,is_bind=1},
[90]={item_id=27659,num=1,is_bind=1},
[91]={item_id=27658,num=1,is_bind=1},
[92]={item_id=36366,num=3,is_bind=1},
[93]={item_id=27658,num=2,is_bind=1},
[94]={item_id=27657,num=15,is_bind=1},
[95]={item_id=27658,num=3,is_bind=1},
[96]={item_id=27657,num=25,is_bind=1},
[97]={item_id=22073,num=1,is_bind=1},
[98]={item_id=22072,num=1,is_bind=1},
[99]={item_id=26449,num=1,is_bind=1},
[100]={item_id=22073,num=2,is_bind=1},
[101]={item_id=22072,num=3,is_bind=1},
[102]={item_id=26454,num=1,is_bind=1},
[103]={item_id=22074,num=1,is_bind=1},
[104]={item_id=22073,num=3,is_bind=1},
[105]={item_id=26393,num=1,is_bind=1},
[106]={item_id=26392,num=5,is_bind=1},
[107]={item_id=26391,num=20,is_bind=1},
[108]={item_id=26391,num=30,is_bind=1},
[109]={item_id=26392,num=10,is_bind=1},
[110]={item_id=26391,num=50,is_bind=1},
[111]={item_id=26130,num=8,is_bind=1},
[112]={item_id=26369,num=2,is_bind=1},
[113]={item_id=26368,num=10,is_bind=1},
[114]={item_id=26345,num=20,is_bind=1},
[115]={item_id=26130,num=15,is_bind=1},
[116]={item_id=26369,num=5,is_bind=1},
[117]={item_id=26368,num=20,is_bind=1},
[118]={item_id=26345,num=30,is_bind=1},
[119]={item_id=26130,num=4,is_bind=1},
[120]={item_id=26230,num=1,is_bind=1},
[121]={item_id=48118,num=1,is_bind=1},
[122]={item_id=26231,num=1,is_bind=1},
[123]={item_id=48118,num=3,is_bind=1},
[124]={item_id=26234,num=1,is_bind=1},
[125]={item_id=26233,num=1,is_bind=1},
[126]={item_id=62001,num=1,is_bind=1},
[127]={item_id=30442,num=5,is_bind=1},
[128]={item_id=62000,num=10,is_bind=1},
[129]={item_id=30447,num=10,is_bind=1},
[130]={item_id=62000,num=30,is_bind=1},
[131]={item_id=30447,num=20,is_bind=1},
[132]={item_id=62000,num=50,is_bind=1},
[133]={item_id=39109,num=8,is_bind=1},
[134]={item_id=29615,num=18,is_bind=1},
[135]={item_id=30425,num=5,is_bind=1},
[136]={item_id=39109,num=18,is_bind=1},
[137]={item_id=29615,num=38,is_bind=1},
[138]={item_id=30425,num=10,is_bind=1},
[139]={item_id=39109,num=38,is_bind=1},
[140]={item_id=48073,num=1,is_bind=1},
[141]={item_id=26172,num=10,is_bind=1},
[142]={item_id=36366,num=5,is_bind=1},
[143]={item_id=26172,num=20,is_bind=1},
[144]={item_id=36366,num=10,is_bind=1},
[145]={item_id=48073,num=3,is_bind=1},
[146]={item_id=26172,num=50,is_bind=1},
[147]={item_id=22074,num=3,is_bind=1},
[148]={item_id=26197,num=10,is_bind=1},
[149]={item_id=22074,num=5,is_bind=1},
[150]={item_id=26197,num=20,is_bind=1},
[151]={item_id=22074,num=10,is_bind=1},
[152]={item_id=26197,num=50,is_bind=1},
[153]={item_id=26462,num=1,is_bind=1},
[154]={item_id=26393,num=3,is_bind=1},
[155]={item_id=26392,num=20,is_bind=1},
[156]={item_id=26393,num=5,is_bind=1},
[157]={item_id=26392,num=30,is_bind=1},
[158]={item_id=26369,num=3,is_bind=1},
[159]={item_id=26344,num=5,is_bind=1},
[160]={item_id=26367,num=3,is_bind=1},
[161]={item_id=26368,num=3,is_bind=1},
[162]={item_id=26345,num=3,is_bind=1},
[163]={item_id=26344,num=30,is_bind=1},
[164]={item_id=26369,num=1,is_bind=1},
[165]={item_id=26368,num=5,is_bind=1},
[166]={item_id=26345,num=10,is_bind=1},
}

return {
open_day={
{},
{start_day=8,end_day=9999,grade=2,}
},

open_day_meta_table_map={
},
shop={
{model_show_itemid=37014,},
{seq=1,rmb_seq=1101,price=98,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4]},discount_gift=4,model_show_itemid=37014,gift_name="天域密藏",},
{seq=2,rmb_seq=1102,price=198,reward_item={[0]=item_table[5],[1]=item_table[6],[2]=item_table[7],[3]=item_table[8]},discount_gift=3,model_show_itemid=37014,gift_name="灵境神赐",},
{activity_day=2,rmb_seq=1200,reward_item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11],[3]=item_table[12]},model_show_type=3,model_bundle_name="uis/rawimages/a3_26191",model_asset_name="a3_26191.png",model_show_itemid=26193,open_item_id=26193,sign_des1="装备极速冲榜",rank_name="装备冲榜",image_effect_bundle="effects/prefab/ui/ui_sdbs_icon_prefab",image_effect_asset="UI_sdbs_icon",},
{seq=1,rmb_seq=1201,price=98,reward_item={[0]=item_table[1],[1]=item_table[13],[2]=item_table[2],[3]=item_table[14]},discount_gift=4,gift_name="天域密藏",},
{seq=2,rmb_seq=1202,price=198,reward_item={[0]=item_table[5],[1]=item_table[15],[2]=item_table[6],[3]=item_table[16]},discount_gift=3,gift_name="灵境神赐",},
{activity_day=3,rmb_seq=1300,reward_item={[0]=item_table[9],[1]=item_table[11],[2]=item_table[17],[3]=item_table[18]},model_show_itemid=38123,open_item_id=38123,sign_des1="雷法极速冲榜",rank_name="雷法冲榜",},
{seq=1,rmb_seq=1301,price=98,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[17],[3]=item_table[18]},discount_gift=4,gift_name="天域密藏",},
{seq=2,rmb_seq=1302,price=198,reward_item={[0]=item_table[5],[1]=item_table[6],[2]=item_table[19],[3]=item_table[20]},discount_gift=3,gift_name="灵境神赐",},
{activity_day=4,rmb_seq=1400,reward_item={[0]=item_table[9],[1]=item_table[11],[2]=item_table[21],[3]=item_table[22]},model_show_type=2,model_show_itemid=30045,open_item_id=30045,sign_des1="万魂幡极速冲榜",rank_name="万魂幡冲榜",},
{seq=1,rmb_seq=1401,price=98,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[23],[3]=item_table[24]},discount_gift=4,gift_name="天域密藏",},
{seq=2,rmb_seq=1402,price=198,reward_item={[0]=item_table[5],[1]=item_table[6],[2]=item_table[25],[3]=item_table[26]},discount_gift=3,gift_name="灵境神赐",},
{activity_day=5,rmb_seq=1500,reward_item={[0]=item_table[9],[1]=item_table[11],[2]=item_table[21],[3]=item_table[27]},model_show_type=5,soul_ring_id="9|18|27|36|45|54|63|72",open_item_id="",display_pos="0|-30|0",display_scale=0.9,sign_des1="魂环极速冲榜",rank_name="魂环冲榜",},
{seq=1,rmb_seq=1501,price=98,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[23],[3]=item_table[27]},discount_gift=4,gift_name="天域密藏",},
{seq=2,rmb_seq=1502,price=198,reward_item={[0]=item_table[5],[1]=item_table[6],[2]=item_table[25],[3]=item_table[28]},discount_gift=3,gift_name="灵境神赐",},
{activity_day=6,rmb_seq=1600,reward_item={[0]=item_table[9],[1]=item_table[11],[2]=item_table[29],[3]=item_table[30]},model_show_type=3,model_bundle_name="uis/rawimages/a2_shj_tu329",model_asset_name="a2_shj_tu329.png",model_show_itemid=29767,open_item_id=29767,sign_des1="万图谱极速冲榜",rank_name="万图谱冲榜",},
{activity_day=6,seq=1,rmb_seq=1601,price=98,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[31],[3]=item_table[32]},discount_gift=4,model_show_type=3,model_bundle_name="uis/rawimages/a2_shj_tu330",model_asset_name="a2_shj_tu330.png",model_show_itemid=29768,open_item_id=29768,sign_des1="万图谱极速冲榜",rank_name="万图谱冲榜",gift_name="天域密藏",},
{activity_day=6,seq=2,rmb_seq=1602,price=198,reward_item={[0]=item_table[5],[1]=item_table[6],[2]=item_table[33],[3]=item_table[34]},discount_gift=3,model_show_type=3,model_bundle_name="uis/rawimages/a2_shj_tu331",model_asset_name="a2_shj_tu331.png",model_show_itemid=29769,open_item_id=29769,sign_des1="万图谱极速冲榜",rank_name="万图谱冲榜",gift_name="灵境神赐",},
{activity_day=7,rmb_seq=1700,reward_item={[0]=item_table[9],[1]=item_table[11],[2]=item_table[35],[3]=item_table[36]},model_show_type=2,model_bundle_name="uis/view/artifact_ui/spineanimations/sx_spine_10005_prefab",model_asset_name="sx_spine_tip_10005",open_item_id=26071,display_scale=0.9,sign_des1="双修极速冲榜",rank_name="双修冲榜",},
{seq=1,rmb_seq=1701,price=98,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[37],[3]=item_table[38]},discount_gift=4,gift_name="天域密藏",},
{seq=2,rmb_seq=1702,price=198,reward_item={[0]=item_table[5],[1]=item_table[6],[2]=item_table[39],[3]=item_table[40]},discount_gift=3,gift_name="灵境神赐",}
},

shop_meta_table_map={
[8]=7,	-- depth:1
[9]=7,	-- depth:1
[12]=10,	-- depth:1
[11]=10,	-- depth:1
[14]=13,	-- depth:1
[15]=13,	-- depth:1
[20]=19,	-- depth:1
[21]=19,	-- depth:1
[6]=4,	-- depth:1
[5]=4,	-- depth:1
},
every_day_reward={
{},
{activity_day=2,reward_item={[0]=item_table[41],[1]=item_table[42]},},
{activity_day=3,reward_item={[0]=item_table[41],[1]=item_table[43]},},
{activity_day=4,reward_item={[0]=item_table[44],[1]=item_table[45]},},
{activity_day=5,reward_item={[0]=item_table[46],[1]=item_table[45]},},
{activity_day=6,reward_item={[0]=item_table[47],[1]=item_table[45]},},
{activity_day=7,reward_item={[0]=item_table[48],[1]=item_table[45]},}
},

every_day_reward_meta_table_map={
},
buy_all={
{},
{rank_seq=2,rmb_seq=1102,},
{activity_day=2,rmb_seq=1201,},
{activity_day=3,rmb_seq=1301,},
{activity_day=4,rmb_seq=1401,},
{activity_day=5,rmb_seq=1501,},
{activity_day=6,rmb_seq=1601,},
{activity_day=7,rmb_seq=1701,}
},

buy_all_meta_table_map={
},
lingyu_shop={
{model_show_itemid=37014,},
{seq=1,price=2888,reward_item={[0]=item_table[49],[1]=item_table[50],[2]=item_table[51],[3]=item_table[52]},discount_gift=4,model_show_itemid=37014,gift_name="归墟神藏",unlock_desc="（完购凤栖道柩后解锁）",},
{seq=2,price=8888,reward_item={[0]=item_table[53],[1]=item_table[54],[2]=item_table[55],[3]=item_table[56]},discount_gift=3,model_show_itemid=37014,gift_name="九寰神匣",unlock_desc="（完购归墟神藏后解锁）",},
{activity_day=2,reward_item={[0]=item_table[57],[1]=item_table[58],[2]=item_table[59],[3]=item_table[60]},model_show_type=3,model_bundle_name="uis/rawimages/a3_26191",model_asset_name="a3_26191.png",model_show_itemid=26193,open_item_id=26193,sign_des1="装备强化提升",rank_name="装备冲榜",image_effect_bundle="effects/prefab/ui/ui_sdbs_icon_prefab",image_effect_asset="UI_sdbs_icon",},
{seq=1,price=2888,reward_item={[0]=item_table[57],[1]=item_table[61],[2]=item_table[62],[3]=item_table[63]},discount_gift=4,gift_name="归墟神藏",unlock_desc="（完购凤栖道柩后解锁）",},
{seq=2,price=8888,reward_item={[0]=item_table[64],[1]=item_table[65],[2]=item_table[66],[3]=item_table[67]},discount_gift=3,gift_name="九寰神匣",unlock_desc="（完购归墟神藏后解锁）",},
{activity_day=3,reward_item={[0]=item_table[68],[1]=item_table[69],[2]=item_table[70],[3]=item_table[71]},model_show_itemid=38123,open_item_id=38123,sign_des1="雷法器提升",rank_name="雷法冲榜",},
{activity_day=3,reward_item={[0]=item_table[72],[1]=item_table[73],[2]=item_table[74],[3]=item_table[75]},model_show_itemid=38123,open_item_id=38123,sign_des1="雷法器提升",rank_name="雷法冲榜",},
{activity_day=3,reward_item={[0]=item_table[76],[1]=item_table[77],[2]=item_table[78],[3]=item_table[79]},model_show_itemid=38123,open_item_id=38123,sign_des1="雷法器提升",rank_name="雷法冲榜",},
{activity_day=4,reward_item={[0]=item_table[80],[1]=item_table[81],[2]=item_table[82],[3]=item_table[83]},model_show_type=2,model_show_itemid=30045,open_item_id=30045,sign_des1="万魂幡提升",rank_name="万魂幡冲榜",},
{seq=1,price=2888,reward_item={[0]=item_table[84],[1]=item_table[81],[2]=item_table[85],[3]=item_table[83]},discount_gift=4,gift_name="归墟神藏",unlock_desc="（完购凤栖道柩后解锁）",},
{seq=2,price=8888,reward_item={[0]=item_table[86],[1]=item_table[87],[2]=item_table[88],[3]=item_table[89]},discount_gift=3,gift_name="九寰神匣",unlock_desc="（完购归墟神藏后解锁）",},
{activity_day=5,reward_item={[0]=item_table[90],[1]=item_table[91],[2]=item_table[92],[3]=item_table[46]},model_show_type=5,soul_ring_id="9|18|27|36|45|54|63|72",open_item_id="",display_pos="0|-30|0",display_scale=0.9,sign_des1="魂环觉醒",rank_name="魂环冲榜",},
{seq=1,price=2888,reward_item={[0]=item_table[90],[1]=item_table[93],[2]=item_table[92],[3]=item_table[94]},discount_gift=4,gift_name="归墟神藏",unlock_desc="（完购凤栖道柩后解锁）",},
{seq=2,price=8888,reward_item={[0]=item_table[90],[1]=item_table[95],[2]=item_table[92],[3]=item_table[96]},discount_gift=3,gift_name="九寰神匣",unlock_desc="（完购归墟神藏后解锁）",},
{activity_day=6,reward_item={[0]=item_table[97],[1]=item_table[97],[2]=item_table[98],[3]=item_table[99]},model_show_type=3,model_bundle_name="uis/rawimages/a2_shj_tu329",model_asset_name="a2_shj_tu329.png",model_show_itemid=29767,open_item_id=29767,sign_des1="图鉴收集",rank_name="万图谱冲榜",},
{activity_day=6,seq=1,price=2888,reward_item={[0]=item_table[97],[1]=item_table[100],[2]=item_table[101],[3]=item_table[102]},discount_gift=4,model_show_type=3,model_bundle_name="uis/rawimages/a2_shj_tu330",model_asset_name="a2_shj_tu330.png",model_show_itemid=29768,open_item_id=29768,sign_des1="图鉴收集",rank_name="万图谱冲榜",gift_name="归墟神藏",unlock_desc="（完购凤栖道柩后解锁）",},
{activity_day=6,seq=2,price=8888,reward_item={[0]=item_table[103],[1]=item_table[104],[2]=item_table[47],[3]=item_table[102]},discount_gift=3,model_show_type=3,model_bundle_name="uis/rawimages/a2_shj_tu331",model_asset_name="a2_shj_tu331.png",model_show_itemid=29769,open_item_id=29769,sign_des1="图鉴收集",rank_name="万图谱冲榜",gift_name="九寰神匣",unlock_desc="（完购归墟神藏后解锁）",},
{activity_day=7,reward_item={[0]=item_table[105],[1]=item_table[106],[2]=item_table[107],[3]=item_table[99]},model_show_type=2,model_bundle_name="uis/view/artifact_ui/spineanimations/sx_spine_10005_prefab",model_asset_name="sx_spine_tip_10005",open_item_id=26071,display_scale=0.9,sign_des1="女神相伴",sign_des2="强力技能",rank_name="双修冲榜",},
{seq=1,price=2888,reward_item={[0]=item_table[105],[1]=item_table[106],[2]=item_table[108],[3]=item_table[102]},discount_gift=4,gift_name="归墟神藏",unlock_desc="（完购凤栖道柩后解锁）",},
{seq=2,price=8888,reward_item={[0]=item_table[105],[1]=item_table[109],[2]=item_table[110],[3]=item_table[102]},discount_gift=3,gift_name="九寰神匣",unlock_desc="（完购归墟神藏后解锁）",}
},

lingyu_shop_meta_table_map={
[8]=2,	-- depth:1
[9]=3,	-- depth:1
[11]=10,	-- depth:1
[12]=10,	-- depth:1
[14]=13,	-- depth:1
[15]=13,	-- depth:1
[20]=19,	-- depth:1
[21]=19,	-- depth:1
[6]=4,	-- depth:1
[5]=4,	-- depth:1
},
score_shop={
{model_show_itemid=37014,},
{seq=1,price=888,reward_item={[0]=item_table[111],[1]=item_table[112],[2]=item_table[113],[3]=item_table[114]},discount_gift=4,model_show_itemid=37014,gift_name="玄渊灵椟",unlock_desc="（完购赦劫天礼后解锁）",},
{seq=2,price=1888,reward_item={[0]=item_table[115],[1]=item_table[116],[2]=item_table[117],[3]=item_table[118]},discount_gift=3,model_show_itemid=37014,gift_name="烛龙时匣",unlock_desc="（完购玄渊灵椟后解锁）",},
{activity_day=2,reward_item={[0]=item_table[119],[1]=item_table[10],[2]=item_table[120],[3]=item_table[121]},model_show_type=3,model_bundle_name="uis/rawimages/a3_26191",model_asset_name="a3_26191.png",model_show_itemid=26193,open_item_id=26193,sign_des1="装备强化提升",rank_name="装备冲榜",image_effect_bundle="effects/prefab/ui/ui_sdbs_icon_prefab",image_effect_asset="UI_sdbs_icon",},
{seq=1,price=888,reward_item={[0]=item_table[111],[1]=item_table[10],[2]=item_table[122],[3]=item_table[123]},discount_gift=4,gift_name="玄渊灵椟",unlock_desc="（完购赦劫天礼后解锁）",},
{seq=2,price=1888,reward_item={[0]=item_table[115],[1]=item_table[10],[2]=item_table[124],[3]=item_table[125]},discount_gift=3,gift_name="烛龙时匣",unlock_desc="（完购玄渊灵椟后解锁）",},
{activity_day=3,reward_item={[0]=item_table[119],[1]=item_table[126],[2]=item_table[127],[3]=item_table[128]},model_show_itemid=38123,open_item_id=38123,sign_des1="雷法器提升",rank_name="雷法冲榜",},
{activity_day=3,reward_item={[0]=item_table[111],[1]=item_table[126],[2]=item_table[129],[3]=item_table[130]},model_show_itemid=38123,open_item_id=38123,sign_des1="雷法器提升",rank_name="雷法冲榜",},
{activity_day=3,reward_item={[0]=item_table[115],[1]=item_table[126],[2]=item_table[131],[3]=item_table[132]},model_show_itemid=38123,open_item_id=38123,sign_des1="雷法器提升",rank_name="雷法冲榜",},
{activity_day=4,reward_item={[0]=item_table[119],[1]=item_table[88],[2]=item_table[87],[3]=item_table[133]},model_show_type=2,model_show_itemid=30045,open_item_id=30045,sign_des1="万魂幡提升",rank_name="万魂幡冲榜",},
{seq=1,price=888,reward_item={[0]=item_table[111],[1]=item_table[134],[2]=item_table[135],[3]=item_table[136]},discount_gift=4,gift_name="玄渊灵椟",unlock_desc="（完购赦劫天礼后解锁）",},
{seq=2,price=1888,reward_item={[0]=item_table[115],[1]=item_table[137],[2]=item_table[138],[3]=item_table[139]},discount_gift=3,gift_name="烛龙时匣",unlock_desc="（完购玄渊灵椟后解锁）",},
{activity_day=5,reward_item={[0]=item_table[119],[1]=item_table[140],[2]=item_table[141],[3]=item_table[142]},model_show_type=5,soul_ring_id="9|18|27|36|45|54|63|72",open_item_id="",display_pos="0|-30|0",display_scale=0.9,sign_des1="魂环觉醒",rank_name="魂环冲榜",},
{seq=1,price=888,reward_item={[0]=item_table[111],[1]=item_table[140],[2]=item_table[143],[3]=item_table[144]},discount_gift=4,gift_name="玄渊灵椟",unlock_desc="（完购赦劫天礼后解锁）",},
{seq=2,price=1888,reward_item={[0]=item_table[115],[1]=item_table[145],[2]=item_table[146],[3]=item_table[144]},discount_gift=3,gift_name="烛龙时匣",unlock_desc="（完购玄渊灵椟后解锁）",},
{activity_day=6,reward_item={[0]=item_table[119],[1]=item_table[147],[2]=item_table[148],[3]=item_table[99]},model_show_type=3,model_bundle_name="uis/rawimages/a2_shj_tu329",model_asset_name="a2_shj_tu329.png",model_show_itemid=29767,open_item_id=29767,sign_des1="图鉴收集",rank_name="万图谱冲榜",},
{activity_day=6,seq=1,price=888,reward_item={[0]=item_table[111],[1]=item_table[149],[2]=item_table[150],[3]=item_table[102]},discount_gift=4,model_show_type=3,model_bundle_name="uis/rawimages/a2_shj_tu330",model_asset_name="a2_shj_tu330.png",model_show_itemid=29768,open_item_id=29768,sign_des1="图鉴收集",rank_name="万图谱冲榜",gift_name="玄渊灵椟",unlock_desc="（完购赦劫天礼后解锁）",},
{activity_day=6,seq=2,price=1888,reward_item={[0]=item_table[115],[1]=item_table[151],[2]=item_table[152],[3]=item_table[153]},discount_gift=3,model_show_type=3,model_bundle_name="uis/rawimages/a2_shj_tu331",model_asset_name="a2_shj_tu331.png",model_show_itemid=29769,open_item_id=29769,sign_des1="图鉴收集",rank_name="万图谱冲榜",gift_name="烛龙时匣",unlock_desc="（完购玄渊灵椟后解锁）",},
{activity_day=7,reward_item={[0]=item_table[119],[1]=item_table[35],[2]=item_table[105],[3]=item_table[109]},model_show_type=2,model_bundle_name="uis/view/artifact_ui/spineanimations/sx_spine_10005_prefab",model_asset_name="sx_spine_tip_10005",open_item_id=26071,display_scale=0.9,sign_des1="女神相伴",sign_des2="强力技能",rank_name="双修冲榜",},
{seq=1,price=888,reward_item={[0]=item_table[111],[1]=item_table[35],[2]=item_table[154],[3]=item_table[155]},discount_gift=4,gift_name="玄渊灵椟",unlock_desc="（完购赦劫天礼后解锁）",},
{seq=2,price=1888,reward_item={[0]=item_table[115],[1]=item_table[37],[2]=item_table[156],[3]=item_table[157]},discount_gift=3,gift_name="烛龙时匣",unlock_desc="（完购玄渊灵椟后解锁）",}
},

score_shop_meta_table_map={
[8]=2,	-- depth:1
[9]=3,	-- depth:1
[11]=10,	-- depth:1
[12]=10,	-- depth:1
[14]=13,	-- depth:1
[15]=13,	-- depth:1
[20]=19,	-- depth:1
[21]=19,	-- depth:1
[6]=4,	-- depth:1
[5]=4,	-- depth:1
},
open_day_default_table={start_day=1,end_day=7,grade=1,},

shop_default_table={grade=1,activity_day=1,rank_seq=1,seq=0,rmb_type=124,rmb_seq=1100,type=1,price=30,reward_item={[0]=item_table[9],[1]=item_table[11],[2]=item_table[158],[3]=item_table[114]},buy_limit=3,discount_gift=5,discount=5,model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid="",soul_ring_id="",open_item_id=37014,special_show_name="",display_pos="",display_scale=1,show_special_effect="",special_img="",sign_des1="灵骑极速冲榜",sign_des2="战力飙升",sign_des3="",rank_show_image="a2_kfcb_icom_1",rank_name="灵骑冲榜",gift_name="奇缘赠礼",image_effect_bundle="",image_effect_asset="",},

every_day_reward_default_table={grade=1,activity_day=1,reward_item={[0]=item_table[41],[1]=item_table[159]},},

buy_all_default_table={grade=1,activity_day=1,price=888,rank_seq=1,rmb_seq=1101,rmb_type=165,is_show=0,},

lingyu_shop_default_table={grade=1,activity_day=1,rank_seq=1,seq=0,price=888,type=2,buy_type=1,reward_item={[0]=item_table[160],[1]=item_table[161],[2]=item_table[162],[3]=item_table[163]},buy_limit=5,discount_gift=5,model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid="",soul_ring_id="",open_item_id=37014,special_show_name="",display_pos="",display_scale=1,show_special_effect="",special_img="",sign_des1="灵骑进阶提升",sign_des2="战力飙升",sign_des3="",rank_show_image="a2_kfcb_icom_1",rank_name="灵骑冲榜",gift_name="凤栖道柩",unlock_desc="",image_effect_bundle="",image_effect_asset="",},

score_shop_default_table={grade=1,activity_day=1,rank_seq=1,seq=0,price=388,type=3,reward_item={[0]=item_table[119],[1]=item_table[164],[2]=item_table[165],[3]=item_table[166]},buy_limit=5,discount_gift=5,model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid="",soul_ring_id="",open_item_id=37014,special_show_name="",display_pos="",display_scale=1,show_special_effect="",special_img="",sign_des1="灵骑进阶提升",sign_des2="战力飙升",sign_des3="",rank_show_image="a2_kfcb_icom_1",rank_name="灵骑冲榜",gift_name="赦劫天礼",unlock_desc="",image_effect_bundle="",image_effect_asset="",}

}

