return {
    other={
        {
            open_times=1,
            assist_reward_times=1,
            role_level=82
        }
    },
    wave={
        {
            wave=0,
            monster_id=26600,
            boss_id=26630,
            open_reward={
                item_id=28520,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28560,
                num=1,
                is_bind=1
            }
        },
        {
            wave=1,
            monster_id=26601,
            boss_id=26631,
            open_reward={
                item_id=28521,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28561,
                num=1,
                is_bind=1
            }
        },
        {
            wave=2,
            monster_id=26602,
            boss_id=26632,
            open_reward={
                item_id=28522,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28562,
                num=1,
                is_bind=1
            }
        },
        {
            wave=3,
            monster_id=26603,
            boss_id=26633,
            open_reward={
                item_id=28523,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28563,
                num=1,
                is_bind=1
            }
        },
        {
            wave=4,
            monster_id=26604,
            boss_id=26634,
            open_reward={
                item_id=28524,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28564,
                num=1,
                is_bind=1
            }
        },
        {
            wave=5,
            monster_id=26605,
            boss_id=26635,
            open_reward={
                item_id=28525,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28565,
                num=1,
                is_bind=1
            }
        },
        {
            wave=6,
            monster_id=26606,
            boss_id=26636,
            open_reward={
                item_id=28526,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28566,
                num=1,
                is_bind=1
            }
        },
        {
            wave=7,
            monster_id=26607,
            boss_id=26637,
            open_reward={
                item_id=28527,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28567,
                num=1,
                is_bind=1
            }
        },
        {
            wave=8,
            monster_id=26608,
            boss_id=26638,
            open_reward={
                item_id=28528,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28568,
                num=1,
                is_bind=1
            }
        },
        {
            wave=9,
            monster_id=26609,
            boss_id=26639,
            open_reward={
                item_id=28529,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28569,
                num=1,
                is_bind=1
            }
        },
        {
            wave=10,
            monster_id=26610,
            boss_id=26640,
            open_reward={
                item_id=28530,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28570,
                num=1,
                is_bind=1
            }
        },
        {
            wave=11,
            monster_id=26611,
            boss_id=26641,
            open_reward={
                item_id=28531,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28571,
                num=1,
                is_bind=1
            }
        },
        {
            wave=12,
            monster_id=26612,
            boss_id=26642,
            open_reward={
                item_id=28532,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28572,
                num=1,
                is_bind=1
            }
        },
        {
            wave=13,
            monster_id=26613,
            boss_id=26643,
            open_reward={
                item_id=28533,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28573,
                num=1,
                is_bind=1
            }
        },
        {
            wave=14,
            monster_id=26614,
            boss_id=26644,
            open_reward={
                item_id=28534,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28574,
                num=1,
                is_bind=1
            }
        },
        {
            wave=15,
            monster_id=26615,
            boss_id=26645,
            open_reward={
                item_id=28535,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28575,
                num=1,
                is_bind=1
            }
        },
        {
            wave=16,
            monster_id=26616,
            boss_id=26646,
            open_reward={
                item_id=28536,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28576,
                num=1,
                is_bind=1
            }
        },
        {
            wave=17,
            monster_id=26617,
            boss_id=26647,
            open_reward={
                item_id=28537,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28577,
                num=1,
                is_bind=1
            }
        },
        {
            wave=18,
            monster_id=26618,
            boss_id=26648,
            open_reward={
                item_id=28538,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28578,
                num=1,
                is_bind=1
            }
        },
        {
            wave=19,
            monster_id=26619,
            boss_id=26649,
            open_reward={
                item_id=28539,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28579,
                num=1,
                is_bind=1
            }
        },
        {
            wave=20,
            monster_id=26620,
            boss_id=26650,
            open_reward={
                item_id=28540,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28580,
                num=1,
                is_bind=1
            }
        },
        {
            wave=21,
            monster_id=26621,
            boss_id=26651,
            open_reward={
                item_id=28541,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28581,
                num=1,
                is_bind=1
            }
        },
        {
            wave=22,
            monster_id=26622,
            boss_id=26652,
            open_reward={
                item_id=28542,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28582,
                num=1,
                is_bind=1
            }
        },
        {
            wave=23,
            monster_id=26623,
            boss_id=26653,
            open_reward={
                item_id=28543,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28583,
                num=1,
                is_bind=1
            }
        },
        {
            wave=24,
            monster_id=26624,
            boss_id=26654,
            open_reward={
                item_id=28544,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28584,
                num=1,
                is_bind=1
            }
        },
        {
            wave=25,
            monster_id=26625,
            boss_id=26655,
            open_reward={
                item_id=28545,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28585,
                num=1,
                is_bind=1
            }
        },
        {
            wave=26,
            monster_id=26626,
            boss_id=26656,
            open_reward={
                item_id=28546,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28586,
                num=1,
                is_bind=1
            }
        },
        {
            wave=27,
            monster_id=26627,
            boss_id=26657,
            open_reward={
                item_id=28547,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28587,
                num=1,
                is_bind=1
            }
        },
        {
            wave=28,
            monster_id=26628,
            boss_id=26658,
            open_reward={
                item_id=28548,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28588,
                num=1,
                is_bind=1
            }
        },
        {
            wave=29,
            monster_id=26629,
            boss_id=26659,
            open_reward={
                item_id=28549,
                num=1,
                is_bind=1
            },
            assist_reward={
                item_id=28589,
                num=1,
                is_bind=1
            }
        }
    },
    monster_pos={
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {}
    },
    test={
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {}
    }
}