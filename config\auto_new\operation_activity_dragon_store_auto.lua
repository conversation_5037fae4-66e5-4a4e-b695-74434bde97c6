-- Y-运营活动-神龙秘藏.xls
local item_table={
[1]={item_id=26463,num=1,is_bind=1},
[2]={item_id=26194,num=1,is_bind=1},
[3]={item_id=44185,num=1,is_bind=1},
[4]={item_id=26193,num=1,is_bind=1},
[5]={item_id=45017,num=1,is_bind=1},
[6]={item_id=26191,num=1,is_bind=1},
[7]={item_id=44180,num=1,is_bind=1},
[8]={item_id=44184,num=1,is_bind=1},
[9]={item_id=27836,num=1,is_bind=1},
[10]={item_id=27837,num=1,is_bind=1},
[11]={item_id=26503,num=1,is_bind=1},
[12]={item_id=26518,num=1,is_bind=1},
[13]={item_id=44183,num=1,is_bind=1},
[14]={item_id=29615,num=1,is_bind=1},
[15]={item_id=27833,num=1,is_bind=1},
[16]={item_id=27834,num=1,is_bind=1},
[17]={item_id=27835,num=1,is_bind=1},
[18]={item_id=48071,num=1,is_bind=1},
[19]={item_id=43804,num=1,is_bind=1},
[20]={item_id=26200,num=1,is_bind=1},
[21]={item_id=26203,num=1,is_bind=1},
[22]={item_id=26409,num=1,is_bind=1},
[23]={item_id=22009,num=1,is_bind=1},
[24]={item_id=22576,num=1,is_bind=1},
[25]={item_id=27838,num=1,is_bind=1},
[26]={item_id=26462,num=1,is_bind=1},
[27]={item_id=26464,num=1,is_bind=1},
[28]={item_id=26504,num=1,is_bind=1},
[29]={item_id=26519,num=1,is_bind=1},
[30]={item_id=56280,num=1,is_bind=1},
[31]={item_id=56323,num=1,is_bind=1},
[32]={item_id=56324,num=1,is_bind=1},
[33]={item_id=48507,num=1,is_bind=1},
[34]={item_id=26421,num=1,is_bind=1},
[35]={item_id=26422,num=1,is_bind=1},
[36]={item_id=26423,num=1,is_bind=1},
[37]={item_id=26424,num=1,is_bind=1},
[38]={item_id=26425,num=1,is_bind=1},
[39]={item_id=56319,num=1,is_bind=1},
[40]={item_id=56320,num=1,is_bind=1},
[41]={item_id=56321,num=1,is_bind=1},
[42]={item_id=56402,num=1,is_bind=1},
[43]={item_id=56401,num=1,is_bind=1},
[44]={item_id=50019,num=1,is_bind=1},
[45]={item_id=39151,num=1,is_bind=1},
[46]={item_id=50094,num=1,is_bind=1},
[47]={item_id=26410,num=1,is_bind=1},
[48]={item_id=26121,num=1,is_bind=1},
[49]={item_id=56290,num=1,is_bind=1},
[50]={item_id=38743,num=1,is_bind=1},
[51]={item_id=38123,num=1,is_bind=1},
[52]={item_id=37250,num=1,is_bind=1},
[53]={item_id=38744,num=1,is_bind=1},
[54]={item_id=38125,num=1,is_bind=1},
[55]={item_id=37251,num=1,is_bind=1},
[56]={item_id=44182,num=1,is_bind=1},
[57]={item_id=48441,num=10,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_day={
{},
{start_day=21,end_day=9999,grade=2,}
},

open_day_meta_table_map={
},
reward_pool={
{max_times=1,item=item_table[1],is_rare=1,probability=0.03,},
{seq=1,max_times=1,item=item_table[2],probability=0.005,},
{seq=2,max_times=3,item=item_table[3],probability=0.01,},
{seq=3,max_times=3,item=item_table[4],probability=0.02,},
{seq=4,max_times=5,item=item_table[5],probability=0.02,},
{seq=5,item=item_table[6],},
{seq=6,max_times=8,item=item_table[7],probability=0.025,},
{seq=7,item=item_table[8],},
{seq=8,max_times=10,item=item_table[9],probability=0.025,},
{seq=9,item=item_table[10],},
{seq=10,max_times=10,item=item_table[11],probability=0.035,},
{seq=11,item=item_table[12],},
{seq=12,item=item_table[13],},
{seq=13,item=item_table[14],probability=0.04,},
{seq=14,item=item_table[15],},
{seq=15,item=item_table[16],},
{seq=16,item=item_table[17],},
{seq=17,probability=0.06,},
{seq=18,item=item_table[18],},
{seq=19,item=item_table[19],},
{seq=20,item=item_table[20],},
{seq=21,item=item_table[21],},
{seq=22,item=item_table[22],},
{seq=23,max_times=50,item=item_table[23],probability=0.06,},
{seq=24,item=item_table[24],},
{round=1,},
{round=1,probability=0.03,},
{round=1,},
{round=1,probability=0.035,},
{round=1,probability=0.035,},
{round=1,},
{round=1,max_times=15,},
{round=1,max_times=15,},
{round=1,},
{round=1,},
{round=1,},
{round=1,},
{round=1,},
{round=1,},
{round=1,max_times=80,},
{seq=15,item=item_table[16],},
{round=1,max_times=80,},
{round=1,max_times=80,},
{round=1,max_times=80,},
{seq=19,item=item_table[19],},
{seq=20,item=item_table[20],},
{seq=21,item=item_table[21],},
{seq=22,item=item_table[22],},
{seq=23,item=item_table[23],},
{round=1,max_times=100,},
{round=2,},
{round=2,},
{round=2,},
{round=2,},
{round=2,seq=4,item=item_table[25],},
{round=2,max_times=15,},
{round=2,max_times=25,},
{round=2,},
{round=2,},
{round=2,},
{round=2,},
{round=2,},
{round=2,},
{round=2,},
{round=2,},
{round=2,},
{round=2,},
{round=2,max_times=100,},
{round=2,},
{round=2,max_times=100,},
{round=2,max_times=180,},
{round=2,max_times=180,},
{round=2,max_times=180,},
{round=2,},
{round=2,},
{round=3,item=item_table[26],},
{round=3,probability=0.03,},
{round=3,probability=0.03,},
{round=3,max_times=5,probability=0.035,},
{round=3,},
{seq=5,item=item_table[6],},
{round=3,seq=6,item=item_table[7],probability=0.04,},
{seq=7,item=item_table[8],},
{seq=8,item=item_table[9],},
{round=3,seq=9,item=item_table[10],probability=0.025,},
{seq=10,item=item_table[11],},
{round=3,seq=11,item=item_table[12],probability=0.035,},
{round=3,max_times=50,},
{round=3,max_times=50,},
{round=3,max_times=100,},
{seq=15,item=item_table[16],},
{round=3,},
{round=3,},
{seq=18,item=item_table[18],},
{seq=19,item=item_table[19],},
{seq=20,item=item_table[20],},
{seq=21,item=item_table[21],},
{seq=22,item=item_table[22],},
{seq=23,item=item_table[23],},
{round=3,seq=24,max_times=200,item=item_table[24],},
{round=4,item=item_table[27],probability=0.025,},
{round=4,max_times=3,},
{round=4,probability=0.03,},
{round=4,},
{round=4,seq=4,item=item_table[5],probability=0.035,},
{round=4,},
{round=4,},
{round=4,},
{round=4,},
{round=4,},
{round=4,},
{round=4,},
{round=4,max_times=100,},
{round=4,max_times=100,},
{round=4,},
{round=4,},
{round=4,seq=16,max_times=100,item=item_table[17],},
{round=4,seq=17,max_times=250,},
{seq=18,item=item_table[18],},
{seq=19,item=item_table[19],},
{seq=20,item=item_table[20],},
{round=4,max_times=300,},
{seq=22,item=item_table[22],},
{seq=23,item=item_table[23],},
{round=4,max_times=300,},
{round=5,probability=0.005,},
{round=5,max_times=2,probability=0.01,},
{round=5,max_times=4,},
{round=5,max_times=10,probability=0.03,},
{round=5,probability=0.03,},
{seq=5,item=item_table[6],},
{round=5,probability=0.025,},
{round=5,},
{round=5,},
{round=5,},
{round=5,item=item_table[28],},
{round=5,item=item_table[29],},
{round=5,max_times=200,},
{round=5,max_times=200,},
{seq=14,item=item_table[15],},
{round=5,max_times=200,},
{seq=16,item=item_table[17],},
{round=5,max_times=400,},
{seq=18,item=item_table[18],},
{seq=19,item=item_table[19],},
{seq=20,item=item_table[20],},
{seq=21,item=item_table[21],},
{seq=22,item=item_table[22],},
{round=5,max_times=400,},
{round=5,max_times=400,},
{grade=2,item=item_table[30],probability=0.02,},
{grade=2,item=item_table[31],probability=0.015,},
{seq=2,item=item_table[32],},
{grade=2,max_times=5,item=item_table[33],probability=0.03,},
{seq=4,item=item_table[34],},
{seq=5,item=item_table[35],},
{seq=6,item=item_table[36],},
{seq=7,item=item_table[37],},
{seq=8,item=item_table[38],},
{grade=2,item=item_table[39],probability=0.04,},
{seq=10,item=item_table[40],},
{seq=11,item=item_table[41],},
{seq=12,item=item_table[19],},
{seq=13,item=item_table[42],},
{seq=14,item=item_table[43],},
{seq=15,item=item_table[44],},
{grade=2,seq=16,},
{seq=17,item=item_table[45],},
{seq=18,item=item_table[46],},
{seq=19,item=item_table[47],},
{seq=20,item=item_table[20],},
{seq=21,item=item_table[21],},
{seq=22,item=item_table[48],},
{seq=23,item=item_table[23],},
{seq=24,item=item_table[24],},
{round=1,},
{round=1,},
{round=1,},
{round=1,},
{round=1,},
{round=1,},
{round=1,},
{round=1,},
{round=1,},
{round=1,},
{round=1,},
{round=1,},
{grade=2,item=item_table[19],},
{grade=2,item=item_table[42],},
{round=1,max_times=50,},
{round=1,},
{round=1,max_times=50,},
{seq=17,item=item_table[45],},
{seq=18,item=item_table[46],},
{seq=19,item=item_table[47],},
{grade=2,},
{grade=2,},
{seq=22,item=item_table[48],},
{grade=2,},
{grade=2,},
{round=2,},
{round=2,max_times=2,},
{round=2,max_times=2,},
{seq=3,item=item_table[33],},
{seq=4,item=item_table[34],},
{seq=5,item=item_table[35],},
{grade=2,round=2,item=item_table[36],},
{grade=2,item=item_table[37],},
{grade=2,item=item_table[38],},
{grade=2,item=item_table[39],},
{grade=2,item=item_table[40],},
{grade=2,item=item_table[41],},
{round=2,max_times=80,},
{round=2,max_times=50,},
{round=2,max_times=80,},
{seq=15,item=item_table[44],},
{round=2,},
{round=2,},
{round=2,max_times=200,},
{round=2,},
{round=2,},
{round=2,},
{round=2,},
{round=2,},
{round=2,},
{grade=2,item=item_table[49],},
{round=3,max_times=3,probability=0.03,},
{grade=2,item=item_table[32],},
{round=3,},
{round=3,},
{round=3,},
{round=3,},
{round=3,},
{round=3,},
{round=3,},
{round=3,},
{round=3,},
{grade=2,item=item_table[19],},
{round=3,},
{grade=2,item=item_table[43],},
{grade=2,item=item_table[44],},
{grade=2,seq=16,},
{round=3,max_times=200,},
{round=3,},
{grade=2,item=item_table[47],},
{grade=2,},
{grade=2,},
{grade=2,item=item_table[48],},
{grade=2,},
{grade=2,},
{round=4,},
{round=4,max_times=5,},
{seq=2,item=item_table[32],},
{round=4,max_times=15,},
{round=4,},
{round=4,},
{round=4,},
{round=4,},
{round=4,},
{round=4,},
{round=4,},
{round=4,},
{round=4,max_times=150,},
{round=4,max_times=150,},
{seq=14,item=item_table[43],},
{seq=15,item=item_table[44],},
{round=4,max_times=150,},
{seq=17,item=item_table[45],},
{seq=18,item=item_table[46],},
{round=4,max_times=150,},
{round=4,max_times=250,},
{round=4,max_times=250,},
{round=4,max_times=250,},
{seq=23,item=item_table[23],},
{grade=2,max_times=250,},
{grade=2,item=item_table[49],},
{seq=1,item=item_table[31],},
{grade=2,max_times=8,item=item_table[32],},
{round=5,max_times=30,},
{seq=4,item=item_table[34],},
{seq=5,item=item_table[35],probability=0.025,},
{seq=6,item=item_table[36],},
{seq=7,item=item_table[37],},
{seq=8,item=item_table[38],},
{seq=9,item=item_table[39],},
{round=5,max_times=30,},
{round=5,max_times=30,},
{round=5,max_times=300,},
{grade=2,item=item_table[42],},
{seq=14,item=item_table[43],},
{grade=2,item=item_table[44],},
{grade=2,seq=16,max_times=200,},
{seq=17,item=item_table[45],},
{grade=2,max_times=300,item=item_table[46],},
{seq=19,item=item_table[47],},
{grade=2,max_times=300,},
{grade=2,max_times=300,},
{seq=22,item=item_table[48],},
{grade=2,max_times=300,},
{seq=24,item=item_table[24],}
},

reward_pool_meta_table_map={
[43]=118,	-- depth:1
[68]=118,	-- depth:1
[163]=167,	-- depth:1
[165]=167,	-- depth:1
[23]=18,	-- depth:1
[22]=18,	-- depth:1
[21]=18,	-- depth:1
[20]=18,	-- depth:1
[19]=18,	-- depth:1
[17]=18,	-- depth:1
[16]=18,	-- depth:1
[164]=167,	-- depth:1
[175]=167,	-- depth:1
[174]=167,	-- depth:1
[15]=14,	-- depth:1
[172]=167,	-- depth:1
[171]=167,	-- depth:1
[173]=167,	-- depth:1
[169]=167,	-- depth:1
[168]=167,	-- depth:1
[166]=167,	-- depth:1
[13]=14,	-- depth:1
[170]=167,	-- depth:1
[93]=68,	-- depth:2
[242]=93,	-- depth:3
[99]=100,	-- depth:1
[95]=100,	-- depth:1
[98]=100,	-- depth:1
[97]=100,	-- depth:1
[96]=100,	-- depth:1
[91]=93,	-- depth:3
[86]=87,	-- depth:1
[84]=85,	-- depth:1
[83]=85,	-- depth:1
[81]=82,	-- depth:1
[106]=81,	-- depth:2
[80]=105,	-- depth:1
[94]=93,	-- depth:3
[107]=82,	-- depth:1
[122]=97,	-- depth:2
[109]=84,	-- depth:2
[192]=242,	-- depth:4
[217]=192,	-- depth:5
[143]=18,	-- depth:1
[135]=85,	-- depth:1
[134]=109,	-- depth:3
[132]=82,	-- depth:1
[131]=132,	-- depth:2
[130]=105,	-- depth:1
[125]=100,	-- depth:1
[108]=83,	-- depth:2
[124]=125,	-- depth:2
[75]=100,	-- depth:1
[121]=125,	-- depth:2
[120]=125,	-- depth:2
[119]=118,	-- depth:1
[116]=91,	-- depth:4
[112]=87,	-- depth:1
[111]=86,	-- depth:2
[110]=135,	-- depth:2
[123]=125,	-- depth:2
[74]=99,	-- depth:2
[92]=117,	-- depth:1
[72]=97,	-- depth:2
[10]=9,	-- depth:1
[12]=11,	-- depth:1
[25]=24,	-- depth:1
[50]=100,	-- depth:1
[48]=50,	-- depth:2
[47]=50,	-- depth:2
[8]=9,	-- depth:1
[46]=50,	-- depth:2
[44]=119,	-- depth:2
[42]=117,	-- depth:1
[41]=42,	-- depth:2
[39]=14,	-- depth:1
[38]=13,	-- depth:2
[37]=112,	-- depth:2
[36]=111,	-- depth:3
[267]=242,	-- depth:4
[73]=98,	-- depth:2
[45]=42,	-- depth:2
[49]=50,	-- depth:2
[58]=108,	-- depth:3
[71]=96,	-- depth:2
[6]=9,	-- depth:1
[69]=94,	-- depth:4
[67]=92,	-- depth:2
[66]=116,	-- depth:5
[64]=39,	-- depth:2
[63]=38,	-- depth:3
[70]=95,	-- depth:2
[59]=134,	-- depth:4
[60]=110,	-- depth:3
[62]=37,	-- depth:3
[61]=36,	-- depth:4
[209]=59,	-- depth:5
[211]=61,	-- depth:5
[212]=62,	-- depth:4
[186]=211,	-- depth:6
[208]=58,	-- depth:4
[216]=217,	-- depth:6
[207]=82,	-- depth:1
[206]=207,	-- depth:2
[205]=212,	-- depth:5
[204]=212,	-- depth:5
[210]=60,	-- depth:4
[200]=50,	-- depth:2
[197]=47,	-- depth:3
[198]=200,	-- depth:3
[275]=125,	-- depth:2
[187]=212,	-- depth:5
[188]=38,	-- depth:3
[189]=39,	-- depth:2
[196]=46,	-- depth:3
[195]=200,	-- depth:3
[194]=200,	-- depth:3
[193]=192,	-- depth:5
[191]=216,	-- depth:7
[292]=143,	-- depth:2
[199]=49,	-- depth:3
[274]=275,	-- depth:3
[230]=205,	-- depth:6
[219]=194,	-- depth:4
[237]=187,	-- depth:6
[241]=91,	-- depth:4
[260]=210,	-- depth:5
[243]=193,	-- depth:6
[259]=209,	-- depth:6
[258]=208,	-- depth:5
[257]=207,	-- depth:2
[256]=206,	-- depth:3
[185]=260,	-- depth:6
[244]=219,	-- depth:5
[245]=95,	-- depth:2
[246]=96,	-- depth:2
[255]=230,	-- depth:7
[247]=97,	-- depth:2
[248]=98,	-- depth:2
[249]=99,	-- depth:2
[250]=100,	-- depth:1
[261]=186,	-- depth:7
[218]=193,	-- depth:6
[262]=237,	-- depth:7
[235]=185,	-- depth:7
[220]=245,	-- depth:3
[221]=246,	-- depth:3
[222]=247,	-- depth:3
[223]=248,	-- depth:3
[224]=249,	-- depth:3
[273]=248,	-- depth:3
[272]=247,	-- depth:3
[225]=250,	-- depth:2
[271]=246,	-- depth:3
[270]=245,	-- depth:3
[269]=270,	-- depth:4
[231]=256,	-- depth:4
[232]=257,	-- depth:3
[233]=258,	-- depth:6
[268]=270,	-- depth:4
[266]=270,	-- depth:4
[234]=259,	-- depth:7
[236]=261,	-- depth:8
[184]=234,	-- depth:8
[150]=25,	-- depth:2
[182]=232,	-- depth:4
[77]=2,	-- depth:1
[78]=3,	-- depth:1
[79]=4,	-- depth:1
[88]=38,	-- depth:3
[89]=39,	-- depth:2
[90]=15,	-- depth:2
[101]=1,	-- depth:1
[76]=101,	-- depth:2
[183]=233,	-- depth:7
[104]=79,	-- depth:2
[113]=38,	-- depth:3
[114]=39,	-- depth:2
[115]=90,	-- depth:3
[126]=101,	-- depth:2
[127]=2,	-- depth:1
[128]=3,	-- depth:1
[103]=128,	-- depth:2
[129]=4,	-- depth:1
[65]=115,	-- depth:4
[56]=81,	-- depth:2
[26]=76,	-- depth:3
[27]=127,	-- depth:2
[28]=78,	-- depth:2
[29]=4,	-- depth:1
[30]=5,	-- depth:1
[31]=56,	-- depth:3
[32]=82,	-- depth:1
[57]=82,	-- depth:1
[33]=8,	-- depth:2
[35]=10,	-- depth:2
[40]=90,	-- depth:3
[51]=26,	-- depth:4
[52]=27,	-- depth:3
[53]=28,	-- depth:3
[54]=104,	-- depth:3
[55]=11,	-- depth:1
[34]=9,	-- depth:1
[133]=8,	-- depth:2
[102]=77,	-- depth:2
[137]=12,	-- depth:2
[149]=24,	-- depth:1
[151]=1,	-- depth:1
[152]=2,	-- depth:1
[153]=152,	-- depth:2
[154]=4,	-- depth:1
[155]=154,	-- depth:2
[156]=154,	-- depth:2
[148]=149,	-- depth:2
[157]=154,	-- depth:2
[159]=154,	-- depth:2
[160]=10,	-- depth:2
[161]=160,	-- depth:3
[162]=160,	-- depth:3
[179]=204,	-- depth:6
[180]=255,	-- depth:8
[181]=231,	-- depth:5
[136]=11,	-- depth:1
[147]=149,	-- depth:2
[158]=154,	-- depth:2
[144]=149,	-- depth:2
[146]=149,	-- depth:2
[138]=38,	-- depth:3
[139]=39,	-- depth:2
[140]=139,	-- depth:3
[141]=16,	-- depth:2
[142]=141,	-- depth:3
[145]=149,	-- depth:2
[279]=154,	-- depth:2
[280]=279,	-- depth:3
[281]=279,	-- depth:3
[282]=281,	-- depth:4
[283]=281,	-- depth:4
[284]=281,	-- depth:4
[285]=281,	-- depth:4
[213]=188,	-- depth:4
[286]=211,	-- depth:6
[287]=212,	-- depth:5
[288]=188,	-- depth:4
[289]=139,	-- depth:3
[290]=289,	-- depth:4
[291]=141,	-- depth:3
[226]=76,	-- depth:3
[293]=292,	-- depth:3
[294]=144,	-- depth:3
[295]=294,	-- depth:4
[296]=146,	-- depth:3
[297]=147,	-- depth:3
[298]=297,	-- depth:4
[278]=128,	-- depth:2
[277]=278,	-- depth:3
[190]=290,	-- depth:5
[227]=152,	-- depth:2
[214]=189,	-- depth:3
[299]=149,	-- depth:2
[240]=90,	-- depth:3
[239]=214,	-- depth:4
[238]=88,	-- depth:4
[251]=226,	-- depth:4
[252]=227,	-- depth:3
[253]=252,	-- depth:4
[254]=204,	-- depth:6
[215]=240,	-- depth:4
[203]=253,	-- depth:5
[202]=227,	-- depth:3
[201]=251,	-- depth:5
[263]=188,	-- depth:4
[264]=189,	-- depth:3
[265]=264,	-- depth:4
[229]=254,	-- depth:7
[176]=201,	-- depth:6
[177]=202,	-- depth:4
[178]=203,	-- depth:6
[228]=78,	-- depth:2
[276]=126,	-- depth:3
[300]=299,	-- depth:3
},
reset_reward={
{reward_item={[0]=item_table[50]},},
{seq=1,times=2,},
{seq=2,times=3,reward_item={[0]=item_table[51]},},
{seq=3,times=4,reward_item={[0]=item_table[27]},},
{seq=4,times=5,reward_item={[0]=item_table[52]},},
{grade=2,reward_item={[0]=item_table[53]},},
{grade=2,},
{grade=2,reward_item={[0]=item_table[54]},},
{grade=2,},
{grade=2,reward_item={[0]=item_table[55]},}
},

reset_reward_meta_table_map={
[7]=2,	-- depth:1
[8]=3,	-- depth:1
[9]=4,	-- depth:1
[10]=5,	-- depth:1
},
other_default_table={cost_item_id=26179,cost_item_num=1,cost_gold=80,},

open_day_default_table={start_day=1,end_day=20,grade=1,},

reward_pool_default_table={grade=1,round=0,seq=0,max_times=20,item=item_table[56],is_rare=0,probability=0.05,},

reset_reward_default_table={grade=1,seq=0,times=1,reward_item={[0]=item_table[57]},}

}

