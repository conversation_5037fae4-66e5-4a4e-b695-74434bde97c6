return {
	["other"]={
		{boss_id=11555,boss_drop_box_gather_times=1,width_to_p2=4,length_to_p0=4,length_to_p2=3,boss_position_y=171,jump_platform_radius=3,length_to_p1=3,length_to_p3=4,width_to_p3=3,width_to_p1=3,platform_num=4,boss_position_x=39,width_to_p0=2,},},
	["platform"]={
		{box_x=183,platform=0,platform_center_x=183,relive_x=183,box_y=114,platform_center_y=110,},
		{box_y=181,relive_y=172,box_gather_id=151,platform_center_y=176,},
		{box_x=97,platform=2,platform_center_x=102,relive_x=105,box_item_id=29308,relive_y=239,box_gather_id=152,platform_center_y=235,},
		{box_x=0,platform=3,platform_center_x=39,relive_x=54,has_box=0,box_y=0,box_item_id=22000,relive_y=186,box_gather_id=0,},},
	["area_default_table"]={point_x=188,point_y=212,},
	["drop_boss_reward"]={
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},},
	["drop_boss_reward_default_table"]={},
	["safe_arer_default_table"]={y=174,x=183,radius=21,},
	["platform_default_table"]={box_x=153,platform=1,scene_id=1403,platform_center_x=159,relive_x=162,has_box=1,box_y=231,box_item_id=29307,relive_y=109,box_gather_id=150,platform_center_y=160,},
	["safe_arer"]={
		{y=107,radius=18,},
		{x=160,},
		{y=234,x=103,radius=23,},
		{y=185,x=52,radius=15,},},
	["fuben"]={
		{bossdrop_item={[0]={item_id=26979,num=1,is_bind=0,},[2]={item_id=22733,num=1,is_bind=0,},[1]={item_id=26978,num=1,is_bind=0,},},reward_item={[0]={item_id=26978,num=1,is_bind=1,},[2]={item_id=26423,num=1,is_bind=1,},[1]={item_id=22733,num=1,is_bind=1,},},scene_id=1403,pos_y=15,need_role_level=150,name="仙盟禁地",seq=0,pos_x=184,},},
	["trap_effect"]={
		{},
		{},},
	["area"]={
		{point_x=178,point_y=63,},
		{point_x=189,point_y=63,},
		{point_x=178,point_y=72,},
		{point_y=72,},
		{point_x=177,point_y=81,},
		{point_y=81,},
		{point_x=178,point_y=90,},
		{point_y=90,},
		{point_x=173,point_y=128,},
		{point_x=184,point_y=131,},
		{point_x=195,point_y=132,},
		{point_x=170,point_y=138,},
		{point_x=181,point_y=142,},
		{point_x=191,point_y=146,},
		{point_x=168,point_y=147,},
		{point_x=176,point_y=154,},
		{point_x=184,point_y=160,},
		{point_x=133,point_y=184,},
		{point_x=138,point_y=190,},
		{point_x=145,point_y=196,},
		{point_x=151,point_y=202,},
		{point_x=124,point_y=193,},
		{point_x=130,point_y=199,},
		{point_x=136,point_y=205,},
		{point_x=142,},
		{point_x=114,point_y=203,},
		{point_x=120,point_y=209,},
		{point_x=126,point_y=215,},
		{point_x=132,point_y=221,},
		{point_x=94,point_y=209,},
		{point_x=86,point_y=217,},
		{point_x=78,point_y=225,},
		{point_x=88,point_y=203,},
		{point_x=80,},
		{point_x=72,point_y=220,},
		{point_x=82,point_y=197,},
		{point_x=74,point_y=206,},
		{point_x=66,point_y=214,},
		{point_x=76,point_y=191,},
		{point_x=67,point_y=199,},
		{point_x=59,point_y=207,},},
}
