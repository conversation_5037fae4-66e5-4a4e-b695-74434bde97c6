-- J-角色天赋.xls

return {
talent_level_max={
{max_level=20,talent_type=1,tips_x=-168,},
{talent_id=101,name="冥眼·法器之息",solt_index=3,talent_type=1,icon=2,tips_x=-293,},
{talent_id=102,name="幽雀·天武之息",solt_index=4,talent_type=1,icon=3,tips_x=-53,},
{talent_id=103,name="灵虚·无极天道",solt_index=5,max_level=5,talent_type=1,icon=4,tips_x=-165,},
{talent_id=104,name="幽影·折光翼斩",solt_index=2,max_level=20,talent_type=1,icon=5,tips_x=-288,tips_y=-195,},
{talent_id=105,name="冥刻·梵空魂珠",solt_index=6,max_level=20,talent_type=1,icon=6,tips_x=-49,},
{talent_id=106,name="幽魅·百鬼索道",solt_index=7,talent_type=1,icon=7,tips_x=-290,tips_y=-195,},
{talent_id=107,name="冥黎·践踏爆破",solt_index=9,talent_type=1,icon=8,tips_x=-49,},
{talent_id=108,name="幽瞳·深渊凝视",solt_index=12,max_level=5,talent_type=1,icon=9,tips_x=-163,tips_y=-327,},
{talent_id=109,name="幽控·无魂傀儡",solt_index=10,talent_type=1,icon=10,tips_x=-290,},
{talent_id=110,name="冥极·阴阳交替",solt_index=11,talent_type=1,icon=11,tips_x=-52,tips_y=-195,},
{talent_id=111,name="灵阙·阴阳破碎",solt_index=8,max_level=1,talent_type=1,icon=12,tips_x=-167,tips_y=-327,},
{talent_id=200,name="炽烈·道心坚固",max_level=20,icon=13,tips_x=219,tips_y=-195,},
{talent_id=201,name="炽灵·背饰之息",solt_index=4,icon=14,},
{talent_id=202,name="焱锋·珍骑之息",solt_index=6,icon=15,tips_x=340,},
{talent_id=203,name="灵决·万法不侵",solt_index=5,max_level=5,icon=16,tips_x=222,tips_y=-327,},
{talent_id=204,name="灵轮·强制反弹",solt_index=2,max_level=20,icon=17,},
{talent_id=205,name="焱指·掌控之力",solt_index=3,max_level=20,icon=18,tips_x=337,},
{talent_id=206,name="焱锁·乾坤套索",solt_index=7,icon=19,},
{talent_id=207,name="炽涡·百川归海",solt_index=8,icon=20,},
{talent_id=208,name="灵刺·烈火荆棘",solt_index=9,max_level=5,icon=21,tips_x=221,},
{talent_id=209,name="灵爪·百里爆裂",solt_index=10,icon=22,},
{talent_id=210,name="炽符·咒术法盾",solt_index=11,icon=23,tips_x=217,},
{talent_id=211,name="焱龙·九天咆哮",solt_index=12,icon=24,tips_x=336,},
{talent_id=212,name="灵光·十里莲华",solt_index=13,max_level=1,icon=25,tips_x=222,tips_y=-327,},
{talent_id=300,name="凛玉·水滴石穿",max_level=20,talent_type=3,icon=26,tips_x=-1,},
{talent_id=301,name="冽盾·双龙戏珠",solt_index=2,max_level=20,talent_type=3,icon=27,tips_x=241,},
{talent_id=302,name="凛蛟·背饰之息",solt_index=3,talent_type=3,icon=28,tips_x=-1,},
{talent_id=303,name="冽心·灵剑之息",solt_index=5,talent_type=3,icon=29,tips_x=241,},
{talent_id=304,name="冽魄·无息净化",solt_index=12,max_level=1,talent_type=3,icon=30,tips_x=-1,tips_y=-327,},
{talent_id=305,name="冽悟·绝对零度",solt_index=7,max_level=5,talent_type=3,icon=31,tips_x=241,tips_y=-327,},
{talent_id=306,name="凛回·水润万物",solt_index=6,max_level=20,talent_type=3,icon=32,tips_x=119,},
{talent_id=307,name="灵兵·冰息凝剑",solt_index=4,icon=33,},
{talent_id=308,name="凛法·冰玄领域",solt_index=9,icon=34,},
{talent_id=309,name="冽闪·踏空而行",solt_index=10,max_level=5,talent_type=3,icon=35,tips_x=119,},
{talent_id=310,name="灵生·魂归来兮",solt_index=8,icon=36,},
{talent_id=311,name="凛封·冻脉结魄",solt_index=11,max_level=1,talent_type=3,icon=37,tips_x=119,}
},

talent_level_max_meta_table_map={
[20]=24,	-- depth:1
[33]=28,	-- depth:1
[36]=37,	-- depth:1
[34]=31,	-- depth:1
},
talent_level_cfg={
{talent_type=1,pre_talent_type=1,pre_talent_level=0,gongji=675,pojia=225,desc="提升角色攻击<color=#9DF5A7>675</color>,破甲<color=#9DF5A7>225</color>",},
{talent_level=2,gongji=1350,pojia=450,desc="提升角色攻击<color=#9DF5A7>1350</color>,破甲<color=#9DF5A7>450</color>",},
{talent_level=3,gongji=2025,pojia=675,desc="提升角色攻击<color=#9DF5A7>2025</color>,破甲<color=#9DF5A7>675</color>",},
{talent_level=4,gongji=2700,pojia=900,desc="提升角色攻击<color=#9DF5A7>2700</color>,破甲<color=#9DF5A7>900</color>",},
{talent_level=5,gongji=3375,pojia=1125,desc="提升角色攻击<color=#9DF5A7>3375</color>,破甲<color=#9DF5A7>1125</color>",},
{talent_level=6,gongji=4050,pojia=1350,desc="提升角色攻击<color=#9DF5A7>4050</color>,破甲<color=#9DF5A7>1350</color>",},
{talent_level=7,gongji=4725,pojia=1575,desc="提升角色攻击<color=#9DF5A7>4725</color>,破甲<color=#9DF5A7>1575</color>",},
{talent_level=8,gongji=5400,pojia=1800,desc="提升角色攻击<color=#9DF5A7>5400</color>,破甲<color=#9DF5A7>1800</color>",},
{talent_level=9,gongji=6075,pojia=2025,desc="提升角色攻击<color=#9DF5A7>6075</color>,破甲<color=#9DF5A7>2025</color>",},
{talent_level=10,gongji=6750,pojia=2250,desc="提升角色攻击<color=#9DF5A7>6750</color>,破甲<color=#9DF5A7>2250</color>",},
{talent_level=11,gongji=7425,pojia=2475,desc="提升角色攻击<color=#9DF5A7>7425</color>,破甲<color=#9DF5A7>2475</color>",},
{talent_level=12,gongji=8100,pojia=2700,desc="提升角色攻击<color=#9DF5A7>8100</color>,破甲<color=#9DF5A7>2700</color>",},
{talent_level=13,gongji=8775,pojia=2925,desc="提升角色攻击<color=#9DF5A7>8775</color>,破甲<color=#9DF5A7>2925</color>",},
{talent_level=14,gongji=9450,pojia=3150,desc="提升角色攻击<color=#9DF5A7>9450</color>,破甲<color=#9DF5A7>3150</color>",},
{talent_level=15,gongji=10125,pojia=3375,desc="提升角色攻击<color=#9DF5A7>10125</color>,破甲<color=#9DF5A7>3375</color>",},
{talent_level=16,gongji=10800,pojia=3600,desc="提升角色攻击<color=#9DF5A7>10800</color>,破甲<color=#9DF5A7>3600</color>",},
{talent_level=17,gongji=11475,pojia=3825,desc="提升角色攻击<color=#9DF5A7>11475</color>,破甲<color=#9DF5A7>3825</color>",},
{talent_level=18,gongji=12150,pojia=4050,desc="提升角色攻击<color=#9DF5A7>12150</color>,破甲<color=#9DF5A7>4050</color>",},
{talent_level=19,gongji=12825,pojia=4275,desc="提升角色攻击<color=#9DF5A7>12825</color>,破甲<color=#9DF5A7>4275</color>",},
{talent_level=20,gongji=13500,pojia=4500,desc="提升角色攻击<color=#9DF5A7>13500</color>,破甲<color=#9DF5A7>4500</color>",},
{talent_id=101,pre_talent_id=100,param_a=2,desc="提升法器<color=#9DF5A7>3%</color>培养属性(不含化形）",},
{talent_id=101,pre_talent_id=100,param_a=2,desc="提升法器<color=#9DF5A7>6%</color>培养属性(不含化形）",},
{talent_id=101,pre_talent_id=100,param_a=2,desc="提升法器<color=#9DF5A7>9%</color>培养属性(不含化形）",},
{talent_id=101,pre_talent_id=100,param_a=2,desc="提升法器<color=#9DF5A7>12%</color>培养属性(不含化形）",},
{talent_id=101,pre_talent_id=100,param_a=2,desc="提升法器<color=#9DF5A7>15%</color>培养属性(不含化形）",},
{talent_id=101,pre_talent_id=100,param_a=2,desc="提升法器<color=#9DF5A7>18%</color>培养属性(不含化形）",},
{talent_level=7,param_b=2100,capability_inc=70000,desc="提升法器<color=#9DF5A7>21%</color>培养属性(不含化形）",},
{talent_id=101,pre_talent_id=100,param_a=2,desc="提升法器<color=#9DF5A7>24%</color>培养属性(不含化形）",},
{talent_level=9,xiaohao=2,param_b=2700,capability_inc=90000,desc="提升法器<color=#9DF5A7>27%</color>培养属性(不含化形）",},
{talent_level=10,param_b=3000,capability_inc=100000,desc="提升法器<color=#9DF5A7>30%</color>培养属性(不含化形）",},
{talent_id=102,talent_type=1,pre_talent_type=1,pre_talent_id=104,param_a=5,param_b=300,capability_inc=10000,desc="提升天武<color=#9DF5A7>3%</color>培养属性(不含化形）",},
{talent_level=2,param_b=600,capability_inc=20000,desc="提升天武<color=#9DF5A7>6%</color>培养属性(不含化形）",},
{talent_level=3,param_b=900,capability_inc=30000,desc="提升天武<color=#9DF5A7>9%</color>培养属性(不含化形）",},
{talent_level=4,param_b=1200,capability_inc=40000,desc="提升天武<color=#9DF5A7>12%</color>培养属性(不含化形）",},
{talent_level=5,param_b=1500,capability_inc=50000,desc="提升天武<color=#9DF5A7>15%</color>培养属性(不含化形）",},
{talent_level=6,xiaohao=2,param_b=1800,capability_inc=60000,desc="提升天武<color=#9DF5A7>18%</color>培养属性(不含化形）",},
{talent_level=7,param_b=2100,capability_inc=70000,desc="提升天武<color=#9DF5A7>21%</color>培养属性(不含化形）",},
{talent_level=8,param_b=2400,capability_inc=80000,desc="提升天武<color=#9DF5A7>24%</color>培养属性(不含化形）",},
{talent_level=9,param_b=2700,capability_inc=90000,desc="提升天武<color=#9DF5A7>27%</color>培养属性(不含化形）",},
{talent_level=10,param_b=3000,capability_inc=100000,desc="提升天武<color=#9DF5A7>30%</color>培养属性(不含化形）",},
{talent_id=103,talent_type=1,xiaohao=2,pre_talent_type=1,pre_talent_type_level=30,pre_talent_level=0,effect_type=17,cool_down=120,param_a=10,param_b=50,param_c=3,param_d=15000,attack_power=1413,capability_inc=10000,desc="每攻击10下会提升自身全属性增伤<color=#9DF5A7>0.5%</color>,最高叠加<color=#9DF5A7>3</color>层,持续<color=#9DF5A7>15</color>秒,冷却时间<color=#9DF5A7>120</color>秒",},
{talent_level=2,param_b=80,attack_power=2261,capability_inc=20000,desc="每攻击10下会提升自身全属性增伤<color=#9DF5A7>0.8%</color>,最高叠加<color=#9DF5A7>3</color>层,持续<color=#9DF5A7>15</color>秒,冷却时间<color=#9DF5A7>120</color>秒",},
{talent_level=3,param_b=110,attack_power=3109,capability_inc=30000,desc="每攻击10下会提升自身全属性增伤<color=#9DF5A7>1.1%</color>,最高叠加<color=#9DF5A7>3</color>层,持续<color=#9DF5A7>15</color>秒,冷却时间<color=#9DF5A7>120</color>秒",},
{talent_level=4,param_b=140,attack_power=3957,capability_inc=40000,desc="每攻击10下会提升自身全属性增伤<color=#9DF5A7>1.4%</color>,最高叠加<color=#9DF5A7>3</color>层,持续<color=#9DF5A7>15</color>秒,冷却时间<color=#9DF5A7>120</color>秒",},
{talent_level=5,param_b=170,attack_power=4805,capability_inc=50000,desc="每攻击10下会提升自身全属性增伤<color=#9DF5A7>1.7%</color>,最高叠加<color=#9DF5A7>3</color>层,持续<color=#9DF5A7>15</color>秒,冷却时间<color=#9DF5A7>120</color>秒",},
{talent_id=104,talent_type=1,pre_talent_type=1,pre_talent_level=0,shengming_qq=600,desc="提升生命窃取<color=#9DF5A7>600</color>",},
{talent_level=2,shengming_qq=1200,desc="提升生命窃取<color=#9DF5A7>1200</color>",},
{talent_level=3,shengming_qq=1800,desc="提升生命窃取<color=#9DF5A7>1800</color>",},
{talent_level=4,shengming_qq=2400,desc="提升生命窃取<color=#9DF5A7>2400</color>",},
{talent_level=5,shengming_qq=3000,desc="提升生命窃取<color=#9DF5A7>3000</color>",},
{talent_level=6,shengming_qq=3600,desc="提升生命窃取<color=#9DF5A7>3600</color>",},
{talent_level=7,shengming_qq=4200,desc="提升生命窃取<color=#9DF5A7>4200</color>",},
{talent_level=8,shengming_qq=4800,desc="提升生命窃取<color=#9DF5A7>4800</color>",},
{talent_level=9,shengming_qq=5400,desc="提升生命窃取<color=#9DF5A7>5400</color>",},
{talent_level=10,shengming_qq=6000,desc="提升生命窃取<color=#9DF5A7>6000</color>",},
{talent_level=11,shengming_qq=6600,desc="提升生命窃取<color=#9DF5A7>6600</color>",},
{talent_level=12,shengming_qq=7200,desc="提升生命窃取<color=#9DF5A7>7200</color>",},
{talent_level=13,shengming_qq=7800,desc="提升生命窃取<color=#9DF5A7>7800</color>",},
{talent_level=14,shengming_qq=8400,desc="提升生命窃取<color=#9DF5A7>8400</color>",},
{talent_level=15,shengming_qq=9000,desc="提升生命窃取<color=#9DF5A7>9000</color>",},
{talent_level=16,shengming_qq=9600,desc="提升生命窃取<color=#9DF5A7>9600</color>",},
{talent_level=17,shengming_qq=10200,desc="提升生命窃取<color=#9DF5A7>10200</color>",},
{talent_level=18,shengming_qq=10800,desc="提升生命窃取<color=#9DF5A7>10800</color>",},
{talent_level=19,shengming_qq=11400,desc="提升生命窃取<color=#9DF5A7>11400</color>",},
{talent_level=20,shengming_qq=12000,desc="提升生命窃取<color=#9DF5A7>12000</color>",},
{talent_id=105,talent_type=1,pre_talent_type=1,pre_talent_id=101,shanghai_zs=1200,desc="提升真实伤害<color=#9DF5A7>1200</color>",},
{talent_level=2,shanghai_zs=2400,desc="提升真实伤害<color=#9DF5A7>2400</color>",},
{talent_level=3,shanghai_zs=3600,desc="提升真实伤害<color=#9DF5A7>3600</color>",},
{talent_level=4,shanghai_zs=4800,desc="提升真实伤害<color=#9DF5A7>4800</color>",},
{talent_level=5,shanghai_zs=6000,desc="提升真实伤害<color=#9DF5A7>6000</color>",},
{talent_level=6,shanghai_zs=7200,desc="提升真实伤害<color=#9DF5A7>7200</color>",},
{talent_level=7,shanghai_zs=8400,desc="提升真实伤害<color=#9DF5A7>8400</color>",},
{talent_level=8,shanghai_zs=9600,desc="提升真实伤害<color=#9DF5A7>9600</color>",},
{talent_level=9,shanghai_zs=10800,desc="提升真实伤害<color=#9DF5A7>10800</color>",},
{talent_level=10,shanghai_zs=12000,desc="提升真实伤害<color=#9DF5A7>12000</color>",},
{talent_level=11,shanghai_zs=13200,desc="提升真实伤害<color=#9DF5A7>13200</color>",},
{talent_level=12,shanghai_zs=14400,desc="提升真实伤害<color=#9DF5A7>14400</color>",},
{talent_level=13,shanghai_zs=15600,desc="提升真实伤害<color=#9DF5A7>15600</color>",},
{talent_level=14,shanghai_zs=16800,desc="提升真实伤害<color=#9DF5A7>16800</color>",},
{talent_level=15,shanghai_zs=18000,desc="提升真实伤害<color=#9DF5A7>18000</color>",},
{talent_level=16,shanghai_zs=19200,desc="提升真实伤害<color=#9DF5A7>19200</color>",},
{talent_level=17,shanghai_zs=20400,desc="提升真实伤害<color=#9DF5A7>20400</color>",},
{talent_level=18,shanghai_zs=21600,desc="提升真实伤害<color=#9DF5A7>21600</color>",},
{talent_level=19,shanghai_zs=22800,desc="提升真实伤害<color=#9DF5A7>22800</color>",},
{talent_level=20,shanghai_zs=24000,desc="提升真实伤害<color=#9DF5A7>24000</color>",},
{talent_id=106,talent_type=1,xiaohao=2,pre_talent_type=1,pre_talent_id=102,zengshang_per=40,capability_inc=6000,desc="提升角色增伤<color=#9DF5A7>0.4%</color>",},
{talent_level=2,zengshang_per=80,capability_inc=12000,desc="提升角色增伤<color=#9DF5A7>0.8%</color>",},
{talent_level=3,zengshang_per=120,capability_inc=18000,desc="提升角色增伤<color=#9DF5A7>1.2%</color>",},
{talent_level=4,zengshang_per=160,capability_inc=24000,desc="提升角色增伤<color=#9DF5A7>1.6%</color>",},
{talent_level=5,zengshang_per=200,capability_inc=30000,desc="提升角色增伤<color=#9DF5A7>2%</color>",},
{talent_level=6,zengshang_per=240,capability_inc=36000,desc="提升角色增伤<color=#9DF5A7>2.4%</color>",},
{talent_level=7,zengshang_per=280,capability_inc=42000,desc="提升角色增伤<color=#9DF5A7>2.8%</color>",},
{talent_level=8,xiaohao=3,zengshang_per=320,capability_inc=48000,desc="提升角色增伤<color=#9DF5A7>3.2%</color>",},
{talent_level=9,zengshang_per=360,capability_inc=54000,desc="提升角色增伤<color=#9DF5A7>3.6%</color>",},
{talent_level=10,xiaohao=4,zengshang_per=400,capability_inc=60000,desc="提升角色增伤<color=#9DF5A7>4%</color>",},
{talent_id=107,talent_type=1,xiaohao=2,pre_talent_type=1,pre_talent_id=105,pre_talent_level=10,zengshang_guaiwu_per=40,capability_inc=6000,desc="提升对怪物造成的伤害<color=#9DF5A7>0.4%</color>",},
{talent_level=2,zengshang_guaiwu_per=80,capability_inc=12000,desc="提升对怪物造成的伤害<color=#9DF5A7>0.8%</color>",},
{talent_level=3,zengshang_guaiwu_per=120,capability_inc=18000,desc="提升对怪物造成的伤害<color=#9DF5A7>1.2%</color>",},
{talent_level=4,zengshang_guaiwu_per=160,capability_inc=24000,desc="提升对怪物造成的伤害<color=#9DF5A7>1.6%</color>",},
{talent_level=5,zengshang_guaiwu_per=200,capability_inc=30000,desc="提升对怪物造成的伤害<color=#9DF5A7>2%</color>",},
{talent_level=6,zengshang_guaiwu_per=240,capability_inc=36000,desc="提升对怪物造成的伤害<color=#9DF5A7>2.4%</color>",},
{talent_level=7,zengshang_guaiwu_per=280,capability_inc=42000,desc="提升对怪物造成的伤害<color=#9DF5A7>2.8%</color>",},
{talent_level=8,xiaohao=3,zengshang_guaiwu_per=320,capability_inc=48000,desc="提升对怪物造成的伤害<color=#9DF5A7>3.2%</color>",},
{talent_level=9,xiaohao=4,zengshang_guaiwu_per=360,capability_inc=54000,desc="提升对怪物造成的伤害<color=#9DF5A7>3.6%</color>",},
{talent_level=10,zengshang_guaiwu_per=400,capability_inc=60000,desc="提升对怪物造成的伤害<color=#9DF5A7>4%</color>",},
{talent_id=108,talent_type=1,xiaohao=2,pre_talent_type=1,pre_talent_type_level=90,pre_talent_id=109,effect_type=18,cool_down=60,param_a=2000,param_b=670,param_c=670,param_d=5000,attack_power=1472,capability_inc=18000,desc="攻击时,有<color=#99ffbb>20%</color>概率给目标带来恐惧,每秒造成自身攻击的<color=#99ffbb>6.7%</color>流血,且使目标生命回复降低<color=#99ffbb>6.7%</color>,持续<color=#99ffbb>5</color>秒,冷却时间<color=#99ffbb>60</color>秒",},
{talent_level=2,param_b=1000,param_c=1000,attack_power=2197,capability_inc=32500,desc="攻击时,有<color=#99ffbb>20%</color>概率给目标带来恐惧,每秒造成自身攻击的<color=#99ffbb>10%</color>流血,且使目标生命回复降低<color=#99ffbb>10%</color>,持续<color=#99ffbb>5</color>秒,冷却时间<color=#99ffbb>60</color>秒",},
{talent_level=3,param_b=1330,param_c=1330,attack_power=2922,capability_inc=45000,desc="攻击时,有<color=#99ffbb>20%</color>概率给目标带来恐惧,每秒造成自身攻击的<color=#99ffbb>13.3%</color>流血,且使目标生命回复降低<color=#99ffbb>13.3%</color>,持续<color=#99ffbb>5</color>秒,冷却时间<color=#99ffbb>60</color>秒",},
{talent_level=4,xiaohao=3,param_b=1670,param_c=1670,attack_power=3669,capability_inc=57500,desc="攻击时,有<color=#99ffbb>20%</color>概率给目标带来恐惧,每秒造成自身攻击的<color=#99ffbb>16.7%</color>流血,且使目标生命回复降低<color=#99ffbb>16.7%</color>,持续<color=#99ffbb>5</color>秒,冷却时间<color=#99ffbb>60</color>秒",},
{talent_level=5,param_b=2000,param_c=2000,attack_power=4394,capability_inc=70000,desc="攻击时,有<color=#99ffbb>20%</color>概率给目标带来恐惧,每秒造成自身攻击的<color=#99ffbb>20%</color>流血,且使目标生命回复降低<color=#99ffbb>20%</color>,持续<color=#99ffbb>5</color>秒,冷却时间<color=#99ffbb>60</color>秒",},
{talent_id=109,talent_type=1,xiaohao=2,pre_talent_type=1,pre_talent_id=106,mingzhong_per=20,capability_inc=6000,desc="提升命中率<color=#9DF5A7>0.2%</color>",},
{talent_level=2,mingzhong_per=40,capability_inc=12000,desc="提升命中率<color=#9DF5A7>0.4%</color>",},
{talent_level=3,mingzhong_per=60,capability_inc=18000,desc="提升命中率<color=#9DF5A7>0.6%</color>",},
{talent_level=4,mingzhong_per=80,capability_inc=24000,desc="提升命中率<color=#9DF5A7>0.8%</color>",},
{talent_level=5,mingzhong_per=100,capability_inc=30000,desc="提升命中率<color=#9DF5A7>1%</color>",},
{talent_level=6,mingzhong_per=120,capability_inc=36000,desc="提升命中率<color=#9DF5A7>1.2%</color>",},
{talent_level=7,xiaohao=3,mingzhong_per=140,capability_inc=42000,desc="提升命中率<color=#9DF5A7>1.4%</color>",},
{talent_level=8,mingzhong_per=160,capability_inc=48000,desc="提升命中率<color=#9DF5A7>1.6%</color>",},
{talent_level=9,xiaohao=4,mingzhong_per=180,capability_inc=54000,desc="提升命中率<color=#9DF5A7>1.8%</color>",},
{talent_level=10,mingzhong_per=200,capability_inc=60000,desc="提升命中率<color=#9DF5A7>2%</color>",},
{talent_id=110,talent_type=1,xiaohao=2,pre_talent_type=1,pre_talent_type_level=90,pre_talent_id=107,podang_per=40,capability_inc=18000,desc="提升破挡率<color=#9DF5A7>0.4%</color>",},
{talent_level=2,podang_per=80,capability_inc=32500,desc="提升破挡率<color=#9DF5A7>0.8%</color>",},
{talent_level=3,podang_per=120,capability_inc=45000,desc="提升破挡率<color=#9DF5A7>1.2%</color>",},
{talent_level=4,podang_per=160,capability_inc=57500,desc="提升破挡率<color=#9DF5A7>1.6%</color>",},
{talent_level=5,podang_per=200,capability_inc=70000,desc="提升破挡率<color=#9DF5A7>2%</color>",},
{talent_level=6,podang_per=240,capability_inc=82500,desc="提升破挡率<color=#9DF5A7>2.4%</color>",},
{talent_level=7,podang_per=280,capability_inc=95000,desc="提升破挡率<color=#9DF5A7>2.8%</color>",},
{talent_level=8,xiaohao=3,podang_per=320,capability_inc=107500,desc="提升破挡率<color=#9DF5A7>3.2%</color>",},
{talent_level=9,xiaohao=4,podang_per=360,capability_inc=120000,desc="提升破挡率<color=#9DF5A7>3.6%</color>",},
{talent_level=10,podang_per=400,capability_inc=132500,desc="提升破挡率<color=#9DF5A7>4%</color>",},
{talent_id=111,talent_type=1,xiaohao=5,pre_talent_type=1,pre_talent_type_level=110,pre_talent_id=103,effect_type=19,param_a=5,param_b=25000,param_c=1000,param_d=2000,attack_power=4805,capability_inc=40000,desc="强化灵虚·无极天道效果,叠加层数提升至<color=#99ffbb>5</color>层,持续时间增至<color=#99ffbb>25</color>秒,当状态叠加到<color=#99ffbb>5</color>层时,附加伤害提升<color=#99ffbb>10%</color>,持续<color=#99ffbb>2</color>秒",},
{talent_id=200,pre_talent_level=0,fangyu=300,shengming_max=6000,desc="提升防御<color=#9DF5A7>300</color>,生命<color=#9DF5A7>6000</color>",},
{talent_level=2,fangyu=600,shengming_max=12000,desc="提升防御<color=#9DF5A7>600</color>,生命<color=#9DF5A7>12000</color>",},
{talent_level=3,fangyu=900,shengming_max=18000,desc="提升防御<color=#9DF5A7>900</color>,生命<color=#9DF5A7>18000</color>",},
{talent_level=4,fangyu=1200,shengming_max=24000,desc="提升防御<color=#9DF5A7>1200</color>,生命<color=#9DF5A7>24000</color>",},
{talent_level=5,fangyu=1500,shengming_max=30000,desc="提升防御<color=#9DF5A7>1500</color>,生命<color=#9DF5A7>30000</color>",},
{talent_level=6,fangyu=1800,shengming_max=36000,desc="提升防御<color=#9DF5A7>1800</color>,生命<color=#9DF5A7>36000</color>",},
{talent_level=7,fangyu=2100,shengming_max=42000,desc="提升防御<color=#9DF5A7>2100</color>,生命<color=#9DF5A7>42000</color>",},
{talent_level=8,fangyu=2400,shengming_max=48000,desc="提升防御<color=#9DF5A7>2400</color>,生命<color=#9DF5A7>48000</color>",},
{talent_level=9,fangyu=2700,shengming_max=54000,desc="提升防御<color=#9DF5A7>2700</color>,生命<color=#9DF5A7>54000</color>",},
{talent_level=10,fangyu=3000,shengming_max=60000,desc="提升防御<color=#9DF5A7>3000</color>,生命<color=#9DF5A7>60000</color>",},
{talent_level=11,fangyu=3300,shengming_max=66000,desc="提升防御<color=#9DF5A7>3300</color>,生命<color=#9DF5A7>66000</color>",},
{talent_level=12,fangyu=3600,shengming_max=72000,desc="提升防御<color=#9DF5A7>3600</color>,生命<color=#9DF5A7>72000</color>",},
{talent_level=13,fangyu=3900,shengming_max=78000,desc="提升防御<color=#9DF5A7>3900</color>,生命<color=#9DF5A7>78000</color>",},
{talent_level=14,fangyu=4200,shengming_max=84000,desc="提升防御<color=#9DF5A7>4200</color>,生命<color=#9DF5A7>84000</color>",},
{talent_level=15,fangyu=4500,shengming_max=90000,desc="提升防御<color=#9DF5A7>4500</color>,生命<color=#9DF5A7>90000</color>",},
{talent_level=16,fangyu=4800,shengming_max=96000,desc="提升防御<color=#9DF5A7>4800</color>,生命<color=#9DF5A7>96000</color>",},
{talent_level=17,fangyu=5100,shengming_max=102000,desc="提升防御<color=#9DF5A7>5100</color>,生命<color=#9DF5A7>102000</color>",},
{talent_level=18,fangyu=5400,shengming_max=108000,desc="提升防御<color=#9DF5A7>5400</color>,生命<color=#9DF5A7>108000</color>",},
{talent_level=19,fangyu=5700,shengming_max=114000,desc="提升防御<color=#9DF5A7>5700</color>,生命<color=#9DF5A7>114000</color>",},
{talent_level=20,fangyu=6000,shengming_max=120000,desc="提升防御<color=#9DF5A7>6000</color>,生命<color=#9DF5A7>120000</color>",},
{talent_id=201,pre_talent_id=200,param_a=3,param_b=300,capability_inc=12000,},
{talent_level=2,param_b=600,capability_inc=24000,desc="提升背饰<color=#9DF5A7>6%</color>培养属性(不含化形）",},
{talent_level=3,param_b=900,capability_inc=36000,desc="提升背饰<color=#9DF5A7>9%</color>培养属性(不含化形）",},
{talent_level=4,param_b=1200,capability_inc=48000,desc="提升背饰<color=#9DF5A7>12%</color>培养属性(不含化形）",},
{talent_level=5,param_b=1500,capability_inc=60000,desc="提升背饰<color=#9DF5A7>15%</color>培养属性(不含化形）",},
{talent_id=201,pre_talent_id=200,param_a=3,desc="提升背饰<color=#9DF5A7>18%</color>培养属性(不含化形）",},
{talent_level=7,param_b=2100,capability_inc=84000,desc="提升背饰<color=#9DF5A7>21%</color>培养属性(不含化形）",},
{talent_level=8,param_b=2400,capability_inc=96000,desc="提升背饰<color=#9DF5A7>24%</color>培养属性(不含化形）",},
{talent_level=9,param_b=2700,capability_inc=108000,desc="提升背饰<color=#9DF5A7>27%</color>培养属性(不含化形）",},
{talent_id=201,pre_talent_id=200,param_a=3,desc="提升背饰<color=#9DF5A7>30%</color>培养属性(不含化形）",},
{talent_id=202,pre_talent_id=205,param_a=8,desc="提升珍骑<color=#9DF5A7>3%</color>培养属性(不含化形）",},
{talent_level=2,param_b=600,capability_inc=24000,desc="提升珍骑<color=#9DF5A7>6%</color>培养属性(不含化形）",},
{talent_level=3,param_b=900,capability_inc=36000,desc="提升珍骑<color=#9DF5A7>9%</color>培养属性(不含化形）",},
{talent_level=4,param_b=1200,capability_inc=48000,desc="提升珍骑<color=#9DF5A7>12%</color>培养属性(不含化形）",},
{talent_id=202,pre_talent_id=205,param_a=8,desc="提升珍骑<color=#9DF5A7>15%</color>培养属性(不含化形）",},
{talent_level=6,param_b=1800,capability_inc=72000,desc="提升珍骑<color=#9DF5A7>18%</color>培养属性(不含化形）",},
{talent_level=7,param_b=2100,capability_inc=84000,desc="提升珍骑<color=#9DF5A7>21%</color>培养属性(不含化形）",},
{talent_level=8,param_b=2400,capability_inc=96000,desc="提升珍骑<color=#9DF5A7>24%</color>培养属性(不含化形）",},
{talent_level=9,param_b=2700,capability_inc=108000,desc="提升珍骑<color=#9DF5A7>27%</color>培养属性(不含化形）",},
{talent_id=202,talent_level=10,xiaohao=2,pre_talent_id=205,param_a=8,param_b=3000,capability_inc=120000,desc="提升珍骑<color=#9DF5A7>30%</color>培养属性(不含化形）",},
{talent_id=203,xiaohao=2,pre_talent_id=204,pre_talent_level=10,effect_type=20,cool_down=120,param_a=2000,param_b=800,param_c=100,param_d=150,param_e=5000,defence_power=1500,capability_inc=10000,desc="生命低于<color=#9DF5A7>20%</color>,且受到伤害时吸收<color=#9DF5A7>8%</color>伤害<color=#9DF5A7>（PVE中吸收率为1%）</color>,护盾上限为自身生命的<color=#9DF5A7>1.5%</color>,持续<color=#9DF5A7>5</color>秒,每隔<color=#9DF5A7>120</color>秒触发一次",},
{talent_level=2,param_b=1200,param_c=150,param_d=230,defence_power=2300,capability_inc=20000,desc="生命低于<color=#9DF5A7>20%</color>,且受到伤害时吸收<color=#9DF5A7>12%</color>伤害<color=#9DF5A7>（PVE中吸收率为1.5%）</color>,护盾上限为自身生命的<color=#9DF5A7>2.3%</color>,持续<color=#9DF5A7>5</color>秒,每隔<color=#9DF5A7>120</color>秒触发一次",},
{talent_level=3,param_b=1600,param_c=200,param_d=310,defence_power=3100,capability_inc=30000,desc="生命低于<color=#9DF5A7>20%</color>,且受到伤害时吸收<color=#9DF5A7>16%</color>伤害<color=#9DF5A7>（PVE中吸收率为2%）</color>,护盾上限为自身生命的<color=#9DF5A7>3.1%</color>,持续<color=#9DF5A7>5</color>秒,每隔<color=#9DF5A7>120</color>秒触发一次",},
{talent_level=4,param_b=2000,param_c=250,param_d=390,defence_power=3900,capability_inc=40000,desc="生命低于<color=#9DF5A7>20%</color>,且受到伤害时吸收<color=#9DF5A7>20%</color>伤害<color=#9DF5A7>（PVE中吸收率为2.5%）</color>,护盾上限为自身生命的<color=#9DF5A7>3.9%</color>,持续<color=#9DF5A7>5</color>秒,每隔<color=#9DF5A7>120</color>秒触发一次",},
{talent_level=5,param_b=2400,param_c=300,param_d=470,defence_power=4700,capability_inc=50000,desc="生命低于<color=#9DF5A7>20%</color>,且受到伤害时吸收<color=#9DF5A7>24%</color>伤害<color=#9DF5A7>（PVE中吸收率为3%）</color>,护盾上限为自身生命的<color=#9DF5A7>4.7%</color>,持续<color=#9DF5A7>5</color>秒,每隔<color=#9DF5A7>120</color>秒触发一次",},
{talent_id=204,pre_talent_level=0,fangtan=600,desc="提升伤害反弹<color=#9DF5A7>600</color>",},
{talent_level=2,fangtan=1200,desc="提升伤害反弹<color=#9DF5A7>1200</color>",},
{talent_level=3,fangtan=1800,desc="提升伤害反弹<color=#9DF5A7>1800</color>",},
{talent_level=4,fangtan=2400,desc="提升伤害反弹<color=#9DF5A7>2400</color>",},
{talent_level=5,fangtan=3000,desc="提升伤害反弹<color=#9DF5A7>3000</color>",},
{talent_level=6,fangtan=3600,desc="提升伤害反弹<color=#9DF5A7>3600</color>",},
{talent_level=7,fangtan=4200,desc="提升伤害反弹<color=#9DF5A7>4200</color>",},
{talent_level=8,fangtan=4800,desc="提升伤害反弹<color=#9DF5A7>4800</color>",},
{talent_level=9,fangtan=5400,desc="提升伤害反弹<color=#9DF5A7>5400</color>",},
{talent_level=10,fangtan=6000,desc="提升伤害反弹<color=#9DF5A7>6000</color>",},
{talent_level=11,fangtan=6600,desc="提升伤害反弹<color=#9DF5A7>6600</color>",},
{talent_level=12,fangtan=7200,desc="提升伤害反弹<color=#9DF5A7>7200</color>",},
{talent_level=13,fangtan=7800,desc="提升伤害反弹<color=#9DF5A7>7800</color>",},
{talent_level=14,fangtan=8400,desc="提升伤害反弹<color=#9DF5A7>8400</color>",},
{talent_level=15,fangtan=9000,desc="提升伤害反弹<color=#9DF5A7>9000</color>",},
{talent_level=16,fangtan=9600,desc="提升伤害反弹<color=#9DF5A7>9600</color>",},
{talent_level=17,fangtan=10200,desc="提升伤害反弹<color=#9DF5A7>10200</color>",},
{talent_level=18,fangtan=10800,desc="提升伤害反弹<color=#9DF5A7>10800</color>",},
{talent_level=19,fangtan=11400,desc="提升伤害反弹<color=#9DF5A7>11400</color>",},
{talent_level=20,fangtan=12000,desc="提升伤害反弹<color=#9DF5A7>12000</color>",},
{talent_id=205,pre_talent_level=0,fangyu_zs=1200,desc="提升真实防御<color=#9DF5A7>1200</color>",},
{talent_level=2,fangyu_zs=2400,desc="提升真实防御<color=#9DF5A7>2400</color>",},
{talent_level=3,fangyu_zs=3600,desc="提升真实防御<color=#9DF5A7>3600</color>",},
{talent_level=4,fangyu_zs=4800,desc="提升真实防御<color=#9DF5A7>4800</color>",},
{talent_level=5,fangyu_zs=6000,desc="提升真实防御<color=#9DF5A7>6000</color>",},
{talent_level=6,fangyu_zs=7200,desc="提升真实防御<color=#9DF5A7>7200</color>",},
{talent_level=7,fangyu_zs=8400,desc="提升真实防御<color=#9DF5A7>8400</color>",},
{talent_level=8,fangyu_zs=9600,desc="提升真实防御<color=#9DF5A7>9600</color>",},
{talent_level=9,fangyu_zs=10800,desc="提升真实防御<color=#9DF5A7>10800</color>",},
{talent_level=10,fangyu_zs=12000,desc="提升真实防御<color=#9DF5A7>12000</color>",},
{talent_level=11,fangyu_zs=13200,desc="提升真实防御<color=#9DF5A7>13200</color>",},
{talent_level=12,fangyu_zs=14400,desc="提升真实防御<color=#9DF5A7>14400</color>",},
{talent_level=13,fangyu_zs=15600,desc="提升真实防御<color=#9DF5A7>15600</color>",},
{talent_level=14,fangyu_zs=16800,desc="提升真实防御<color=#9DF5A7>16800</color>",},
{talent_level=15,fangyu_zs=18000,desc="提升真实防御<color=#9DF5A7>18000</color>",},
{talent_level=16,fangyu_zs=19200,desc="提升真实防御<color=#9DF5A7>19200</color>",},
{talent_level=17,fangyu_zs=20400,desc="提升真实防御<color=#9DF5A7>20400</color>",},
{talent_level=18,fangyu_zs=21600,desc="提升真实防御<color=#9DF5A7>21600</color>",},
{talent_level=19,fangyu_zs=22800,desc="提升真实防御<color=#9DF5A7>22800</color>",},
{talent_level=20,fangyu_zs=24000,desc="提升真实防御<color=#9DF5A7>24000</color>",},
{talent_id=206,xiaohao=2,pre_talent_id=202,jianshang_per=40,capability_inc=6000,desc="提升角色伤害减免<color=#9DF5A7>0.4%</color>",},
{talent_level=2,jianshang_per=80,capability_inc=12000,desc="提升角色伤害减免<color=#9DF5A7>0.8%</color>",},
{talent_level=3,jianshang_per=120,capability_inc=18000,desc="提升角色伤害减免<color=#9DF5A7>1.2%</color>",},
{talent_level=4,jianshang_per=160,capability_inc=24000,desc="提升角色伤害减免<color=#9DF5A7>1.6%</color>",},
{talent_level=5,jianshang_per=200,capability_inc=30000,desc="提升角色伤害减免<color=#9DF5A7>2%</color>",},
{talent_id=206,talent_level=6,xiaohao=3,pre_talent_id=202,jianshang_per=240,capability_inc=36000,desc="提升角色伤害减免<color=#9DF5A7>2.4%</color>",},
{talent_level=7,jianshang_per=280,capability_inc=42000,desc="提升角色伤害减免<color=#9DF5A7>2.8%</color>",},
{talent_level=8,jianshang_per=320,capability_inc=48000,desc="提升角色伤害减免<color=#9DF5A7>3.2%</color>",},
{talent_level=9,jianshang_per=360,capability_inc=54000,desc="提升角色伤害减免<color=#9DF5A7>3.6%</color>",},
{talent_id=206,talent_level=10,xiaohao=4,pre_talent_id=202,jianshang_per=400,capability_inc=60000,desc="提升角色伤害减免<color=#9DF5A7>4%</color>",},
{talent_id=207,xiaohao=2,pre_talent_id=201,jianshang_guaiwu_per=40,capability_inc=6000,desc="提升对怪物伤害减免<color=#9DF5A7>0.4%</color>",},
{talent_level=2,jianshang_guaiwu_per=80,capability_inc=12000,desc="提升对怪物伤害减免<color=#9DF5A7>0.8%</color>",},
{talent_level=3,jianshang_guaiwu_per=120,capability_inc=18000,desc="提升对怪物伤害减免<color=#9DF5A7>1.2%</color>",},
{talent_level=4,jianshang_guaiwu_per=160,capability_inc=24000,desc="提升对怪物伤害减免<color=#9DF5A7>1.6%</color>",},
{talent_level=5,jianshang_guaiwu_per=200,capability_inc=30000,desc="提升对怪物伤害减免<color=#9DF5A7>2%</color>",},
{talent_level=6,jianshang_guaiwu_per=240,capability_inc=36000,desc="提升对怪物伤害减免<color=#9DF5A7>2.4%</color>",},
{talent_id=207,talent_level=7,xiaohao=3,pre_talent_id=201,jianshang_guaiwu_per=280,capability_inc=42000,desc="提升对怪物伤害减免<color=#9DF5A7>2.8%</color>",},
{talent_level=8,jianshang_guaiwu_per=320,capability_inc=48000,desc="提升对怪物伤害减免<color=#9DF5A7>3.2%</color>",},
{talent_id=207,talent_level=9,xiaohao=4,pre_talent_id=201,jianshang_guaiwu_per=360,capability_inc=54000,desc="提升对怪物伤害减免<color=#9DF5A7>3.6%</color>",},
{talent_level=10,jianshang_guaiwu_per=400,capability_inc=60000,desc="提升对怪物伤害减免<color=#9DF5A7>4%</color>",},
{talent_id=208,xiaohao=2,pre_talent_type_level=80,pre_talent_id=206,effect_type=21,param_a=5000,param_b=1300,param_c=1500,attack_power=1428,capability_inc=18000,desc="灵决释放时,每次受到伤害,会有<color=#9DF5A7>50%</color>概率反射<color=#9DF5A7>13%</color>伤害,反射的伤害量最多不超过自身伤害的<color=#9DF5A7>15%</color>",},
{talent_level=2,param_b=2000,param_c=2200,attack_power=2197,capability_inc=32500,desc="灵决释放时,每次受到伤害,会有<color=#9DF5A7>50%</color>概率反射<color=#9DF5A7>20%</color>伤害,反射的伤害量最多不超过自身伤害的<color=#9DF5A7>22%</color>",},
{talent_level=3,param_b=2700,param_c=2900,attack_power=2966,capability_inc=45000,desc="灵决释放时,每次受到伤害,会有<color=#9DF5A7>50%</color>概率反射<color=#9DF5A7>27%</color>伤害,反射的伤害量最多不超过自身伤害的<color=#9DF5A7>29%</color>",},
{talent_level=4,param_b=3400,param_c=3600,attack_power=3735,capability_inc=57500,desc="灵决释放时,每次受到伤害,会有<color=#9DF5A7>50%</color>概率反射<color=#9DF5A7>34%</color>伤害,反射的伤害量最多不超过自身伤害的<color=#9DF5A7>36%</color>",},
{talent_level=5,xiaohao=3,param_b=4100,param_c=4300,attack_power=4504,capability_inc=70000,desc="灵决释放时,每次受到伤害,会有<color=#9DF5A7>50%</color>概率反射<color=#9DF5A7>41%</color>伤害,反射的伤害量最多不超过自身伤害的<color=#9DF5A7>43%</color>",},
{talent_id=209,xiaohao=2,pre_talent_id=203,kangbao_per=40,capability_inc=6000,desc="提升角色抗暴率<color=#9DF5A7>0.4%</color>",},
{talent_level=2,kangbao_per=80,capability_inc=12000,desc="提升角色抗暴率<color=#9DF5A7>0.8%</color>",},
{talent_level=3,kangbao_per=120,capability_inc=18000,desc="提升角色抗暴率<color=#9DF5A7>1.2%</color>",},
{talent_level=4,kangbao_per=160,capability_inc=24000,desc="提升角色抗暴率<color=#9DF5A7>1.6%</color>",},
{talent_level=5,kangbao_per=200,capability_inc=30000,desc="提升角色抗暴率<color=#9DF5A7>2%</color>",},
{talent_id=209,talent_level=6,xiaohao=3,pre_talent_id=203,kangbao_per=240,capability_inc=36000,desc="提升角色抗暴率<color=#9DF5A7>2.4%</color>",},
{talent_level=7,kangbao_per=280,capability_inc=42000,desc="提升角色抗暴率<color=#9DF5A7>2.8%</color>",},
{talent_level=8,kangbao_per=320,capability_inc=48000,desc="提升角色抗暴率<color=#9DF5A7>3.2%</color>",},
{talent_id=209,talent_level=9,xiaohao=4,pre_talent_id=203,kangbao_per=360,capability_inc=54000,desc="提升角色抗暴率<color=#9DF5A7>3.6%</color>",},
{talent_level=10,kangbao_per=400,capability_inc=60000,desc="提升角色抗暴率<color=#9DF5A7>4%</color>",},
{talent_id=210,xiaohao=2,pre_talent_type_level=90,pre_talent_id=207,lianjikang_per=40,capability_inc=6000,desc="提升角色连击抵抗<color=#9DF5A7>0.4%</color>",},
{talent_level=2,lianjikang_per=80,capability_inc=12000,desc="提升角色连击抵抗<color=#9DF5A7>0.8%</color>",},
{talent_level=3,lianjikang_per=120,capability_inc=18000,desc="提升角色连击抵抗<color=#9DF5A7>1.2%</color>",},
{talent_level=4,lianjikang_per=160,capability_inc=24000,desc="提升角色连击抵抗<color=#9DF5A7>1.6%</color>",},
{talent_level=5,lianjikang_per=200,capability_inc=30000,desc="提升角色连击抵抗<color=#9DF5A7>2%</color>",},
{talent_level=6,lianjikang_per=240,capability_inc=36000,desc="提升角色连击抵抗<color=#9DF5A7>2.4%</color>",},
{talent_level=7,lianjikang_per=280,capability_inc=42000,desc="提升角色连击抵抗<color=#9DF5A7>2.8%</color>",},
{talent_id=210,talent_level=8,xiaohao=3,pre_talent_type_level=90,pre_talent_id=207,lianjikang_per=320,capability_inc=48000,desc="提升角色连击抵抗<color=#9DF5A7>3.2%</color>",},
{talent_level=9,lianjikang_per=360,capability_inc=54000,desc="提升角色连击抵抗<color=#9DF5A7>3.6%</color>",},
{talent_id=210,talent_level=10,xiaohao=4,pre_talent_type_level=90,pre_talent_id=207,lianjikang_per=400,capability_inc=60000,desc="提升角色连击抵抗<color=#9DF5A7>4%</color>",},
{talent_id=211,xiaohao=2,pre_talent_type_level=90,pre_talent_id=209,jichuankang_per=40,capability_inc=6000,desc="提升角色击穿抵抗<color=#9DF5A7>0.4%</color>",},
{talent_level=2,jichuankang_per=80,capability_inc=12000,desc="提升角色击穿抵抗<color=#9DF5A7>0.8%</color>",},
{talent_level=3,jichuankang_per=120,capability_inc=18000,desc="提升角色击穿抵抗<color=#9DF5A7>1.2%</color>",},
{talent_level=4,jichuankang_per=160,capability_inc=24000,desc="提升角色击穿抵抗<color=#9DF5A7>1.6%</color>",},
{talent_level=5,jichuankang_per=200,capability_inc=30000,desc="提升角色击穿抵抗<color=#9DF5A7>2%</color>",},
{talent_level=6,jichuankang_per=240,capability_inc=36000,desc="提升角色击穿抵抗<color=#9DF5A7>2.4%</color>",},
{talent_level=7,jichuankang_per=280,capability_inc=42000,desc="提升角色击穿抵抗<color=#9DF5A7>2.8%</color>",},
{talent_id=211,talent_level=8,xiaohao=3,pre_talent_type_level=90,pre_talent_id=209,jichuankang_per=320,capability_inc=48000,desc="提升角色击穿抵抗<color=#9DF5A7>3.2%</color>",},
{talent_level=9,jichuankang_per=360,capability_inc=54000,desc="提升角色击穿抵抗<color=#9DF5A7>3.6%</color>",},
{talent_id=211,talent_level=10,xiaohao=4,pre_talent_type_level=90,pre_talent_id=209,jichuankang_per=400,capability_inc=60000,desc="提升角色击穿抵抗<color=#9DF5A7>4%</color>",},
{talent_id=212,xiaohao=5,pre_talent_type_level=110,pre_talent_id=208,effect_type=22,param_a=3000,param_b=6000,param_c=1500,param_d=800,param_e=150,defence_power=3955,capability_inc=40000,desc="灵决释放时有<color=#99ffbb>30%</color>将攻击伤害的<color=#99ffbb>60%</color>回炉为自身生命,总吸取量不超过<color=#99ffbb>15%</color>生命上限,PVE状态下回炉率为<color=#99ffbb>8%</color>,上限<color=#99ffbb>1.5%</color>",},
{talent_id=300,talent_type=3,pre_talent_type=3,pre_talent_level=0,yuansu_sh=450,desc="提升角色道法伤害<color=#9DF5A7>450</color>",},
{talent_level=2,yuansu_sh=900,desc="提升角色道法伤害<color=#9DF5A7>900</color>",},
{talent_level=3,yuansu_sh=1350,desc="提升角色道法伤害<color=#9DF5A7>1350</color>",},
{talent_level=4,yuansu_sh=1800,desc="提升角色道法伤害<color=#9DF5A7>1800</color>",},
{talent_level=5,yuansu_sh=2250,desc="提升角色道法伤害<color=#9DF5A7>2250</color>",},
{talent_level=6,yuansu_sh=2700,desc="提升角色道法伤害<color=#9DF5A7>2700</color>",},
{talent_level=7,yuansu_sh=3150,desc="提升角色道法伤害<color=#9DF5A7>3150</color>",},
{talent_level=8,yuansu_sh=3600,desc="提升角色道法伤害<color=#9DF5A7>3600</color>",},
{talent_level=9,yuansu_sh=4050,desc="提升角色道法伤害<color=#9DF5A7>4050</color>",},
{talent_level=10,yuansu_sh=4500,desc="提升角色道法伤害<color=#9DF5A7>4500</color>",},
{talent_level=11,yuansu_sh=4950,desc="提升角色道法伤害<color=#9DF5A7>4950</color>",},
{talent_level=12,yuansu_sh=5400,desc="提升角色道法伤害<color=#9DF5A7>5400</color>",},
{talent_level=13,yuansu_sh=5850,desc="提升角色道法伤害<color=#9DF5A7>5850</color>",},
{talent_level=14,yuansu_sh=6300,desc="提升角色道法伤害<color=#9DF5A7>6300</color>",},
{talent_level=15,yuansu_sh=6750,desc="提升角色道法伤害<color=#9DF5A7>6750</color>",},
{talent_level=16,yuansu_sh=7200,desc="提升角色道法伤害<color=#9DF5A7>7200</color>",},
{talent_level=17,yuansu_sh=7650,desc="提升角色道法伤害<color=#9DF5A7>7650</color>",},
{talent_level=18,yuansu_sh=8100,desc="提升角色道法伤害<color=#9DF5A7>8100</color>",},
{talent_level=19,yuansu_sh=8550,desc="提升角色道法伤害<color=#9DF5A7>8550</color>",},
{talent_level=20,yuansu_sh=9000,desc="提升角色道法伤害<color=#9DF5A7>9000</color>",},
{talent_id=301,talent_type=3,pre_talent_type=3,pre_talent_level=0,yuansu_hj=450,desc="提升角色道法护甲<color=#9DF5A7>450</color>",},
{talent_level=2,yuansu_hj=900,desc="提升角色道法护甲<color=#9DF5A7>900</color>",},
{talent_level=3,yuansu_hj=1350,desc="提升角色道法护甲<color=#9DF5A7>1350</color>",},
{talent_level=4,yuansu_hj=1800,desc="提升角色道法护甲<color=#9DF5A7>1800</color>",},
{talent_level=5,yuansu_hj=2250,desc="提升角色道法护甲<color=#9DF5A7>2250</color>",},
{talent_level=6,yuansu_hj=2700,desc="提升角色道法护甲<color=#9DF5A7>2700</color>",},
{talent_level=7,yuansu_hj=3150,desc="提升角色道法护甲<color=#9DF5A7>3150</color>",},
{talent_level=8,yuansu_hj=3600,desc="提升角色道法护甲<color=#9DF5A7>3600</color>",},
{talent_level=9,yuansu_hj=4050,desc="提升角色道法护甲<color=#9DF5A7>4050</color>",},
{talent_level=10,yuansu_hj=4500,desc="提升角色道法护甲<color=#9DF5A7>4500</color>",},
{talent_level=11,yuansu_hj=4950,desc="提升角色道法护甲<color=#9DF5A7>4950</color>",},
{talent_level=12,yuansu_hj=5400,desc="提升角色道法护甲<color=#9DF5A7>5400</color>",},
{talent_level=13,yuansu_hj=5850,desc="提升角色道法护甲<color=#9DF5A7>5850</color>",},
{talent_level=14,yuansu_hj=6300,desc="提升角色道法护甲<color=#9DF5A7>6300</color>",},
{talent_level=15,yuansu_hj=6750,desc="提升角色道法护甲<color=#9DF5A7>6750</color>",},
{talent_level=16,yuansu_hj=7200,desc="提升角色道法护甲<color=#9DF5A7>7200</color>",},
{talent_level=17,yuansu_hj=7650,desc="提升角色道法护甲<color=#9DF5A7>7650</color>",},
{talent_level=18,yuansu_hj=8100,desc="提升角色道法护甲<color=#9DF5A7>8100</color>",},
{talent_level=19,yuansu_hj=8550,desc="提升角色道法护甲<color=#9DF5A7>8550</color>",},
{talent_level=20,yuansu_hj=9000,desc="提升角色道法护甲<color=#9DF5A7>9000</color>",},
{talent_id=302,talent_type=3,pre_talent_type=3,pre_talent_id=300,param_a=19,param_b=300,capability_inc=10000,},
{talent_level=2,param_b=600,capability_inc=20000,desc="提升背饰<color=#9DF5A7>6%</color>培养属性(不含化形）",},
{talent_level=3,param_b=900,capability_inc=30000,desc="提升背饰<color=#9DF5A7>9%</color>培养属性(不含化形）",},
{talent_level=4,param_b=1200,capability_inc=40000,desc="提升背饰<color=#9DF5A7>12%</color>培养属性(不含化形）",},
{talent_level=5,param_b=1500,capability_inc=50000,desc="提升背饰<color=#9DF5A7>15%</color>培养属性(不含化形）",},
{talent_level=6,param_b=1800,capability_inc=60000,desc="提升背饰<color=#9DF5A7>18%</color>培养属性(不含化形）",},
{talent_level=7,param_b=2100,capability_inc=70000,desc="提升背饰<color=#9DF5A7>21%</color>培养属性(不含化形）",},
{talent_level=8,xiaohao=2,param_b=2400,capability_inc=80000,desc="提升背饰<color=#9DF5A7>24%</color>培养属性(不含化形）",},
{talent_level=9,param_b=2700,capability_inc=90000,desc="提升背饰<color=#9DF5A7>27%</color>培养属性(不含化形）",},
{talent_level=10,param_b=3000,capability_inc=100000,desc="提升背饰<color=#9DF5A7>30%</color>培养属性(不含化形）",},
{talent_id=303,talent_type=3,pre_talent_type=3,pre_talent_id=301,param_a=1,param_b=300,capability_inc=6000,desc="提升灵剑<color=#9DF5A7>3%</color>培养属性(不含化形）",},
{talent_level=2,param_b=600,capability_inc=12000,desc="提升灵剑<color=#9DF5A7>6%</color>培养属性(不含化形）",},
{talent_level=3,param_b=900,capability_inc=18000,desc="提升灵剑<color=#9DF5A7>9%</color>培养属性(不含化形）",},
{talent_level=4,param_b=1200,capability_inc=24000,desc="提升灵剑<color=#9DF5A7>12%</color>培养属性(不含化形）",},
{talent_level=5,param_b=1500,capability_inc=30000,desc="提升灵剑<color=#9DF5A7>15%</color>培养属性(不含化形）",},
{talent_level=6,param_b=1800,capability_inc=36000,desc="提升灵剑<color=#9DF5A7>18%</color>培养属性(不含化形）",},
{talent_level=7,param_b=2100,capability_inc=42000,desc="提升灵剑<color=#9DF5A7>21%</color>培养属性(不含化形）",},
{talent_level=8,param_b=2400,capability_inc=48000,desc="提升灵剑<color=#9DF5A7>24%</color>培养属性(不含化形）",},
{talent_level=9,xiaohao=2,param_b=2700,capability_inc=54000,desc="提升灵剑<color=#9DF5A7>27%</color>培养属性(不含化形）",},
{talent_level=10,param_b=3000,capability_inc=60000,desc="提升灵剑<color=#9DF5A7>30%</color>培养属性(不含化形）",},
{talent_id=304,pre_talent_id=309,effect_type=23,cool_down=60,param_a=5000,param_b=2500,defence_power=1250,desc="受到控制状态时<color=#99ffbb>50%</color>概率触发<color=#99ffbb>免疫控制效果</color>,并<color=#99ffbb>清除当前控制状态</color>,持续<color=#99ffbb>2.5</color>秒,每隔<color=#99ffbb>60</color>秒触发一次",},
{talent_id=305,talent_type=3,xiaohao=2,pre_talent_type=3,pre_talent_id=303,effect_type=24,cool_down=120,param_a=5000,param_b=60,param_c=5000,defence_power=1000,capability_inc=10000,desc="单次受到大于生命上限<color=#99ffbb>50%</color>伤害时,本次伤害最多为自身生命的<color=#99ffbb>50%</color>,同时每秒恢复自身生命<color=#99ffbb>0.6%</color>,持续<color=#99ffbb>5</color>秒,每隔<color=#99ffbb>120</color>秒触发一次",},
{talent_level=2,param_b=90,defence_power=1500,capability_inc=20000,desc="单次受到大于生命上限<color=#99ffbb>50%</color>伤害时,本次伤害最多为自身生命的<color=#99ffbb>50%</color>,同时每秒恢复自身生命<color=#99ffbb>0.9%</color>,持续<color=#99ffbb>5</color>秒,每隔<color=#99ffbb>120</color>秒触发一次",},
{talent_level=3,param_b=120,defence_power=2000,capability_inc=30000,desc="单次受到大于生命上限<color=#99ffbb>50%</color>伤害时,本次伤害最多为自身生命的<color=#99ffbb>50%</color>,同时每秒恢复自身生命<color=#99ffbb>1.2%</color>,持续<color=#99ffbb>5</color>秒,每隔<color=#99ffbb>120</color>秒触发一次",},
{talent_level=4,xiaohao=3,param_b=150,defence_power=2500,capability_inc=40000,desc="单次受到大于生命上限<color=#99ffbb>50%</color>伤害时,本次伤害最多为自身生命的<color=#99ffbb>50%</color>,同时每秒恢复自身生命<color=#99ffbb>1.5%</color>,持续<color=#99ffbb>5</color>秒,每隔<color=#99ffbb>120</color>秒触发一次",},
{talent_level=5,param_b=180,defence_power=3000,capability_inc=50000,desc="单次受到大于生命上限<color=#99ffbb>50%</color>伤害时,本次伤害最多为自身生命的<color=#99ffbb>50%</color>,同时每秒恢复自身生命<color=#99ffbb>1.8%</color>,持续<color=#99ffbb>5</color>秒,每隔<color=#99ffbb>120</color>秒触发一次",},
{talent_id=306,talent_type=3,pre_talent_type=3,pre_talent_id=302,shengming_hf=6000,desc="提升角色生命回复值<color=#9DF5A7>6000</color>",},
{talent_level=2,shengming_hf=12000,desc="提升角色生命回复值<color=#9DF5A7>12000</color>",},
{talent_level=3,shengming_hf=18000,desc="提升角色生命回复值<color=#9DF5A7>18000</color>",},
{talent_level=4,shengming_hf=24000,desc="提升角色生命回复值<color=#9DF5A7>24000</color>",},
{talent_level=5,shengming_hf=30000,desc="提升角色生命回复值<color=#9DF5A7>30000</color>",},
{talent_level=6,shengming_hf=36000,desc="提升角色生命回复值<color=#9DF5A7>36000</color>",},
{talent_level=7,shengming_hf=42000,desc="提升角色生命回复值<color=#9DF5A7>42000</color>",},
{talent_level=8,shengming_hf=48000,desc="提升角色生命回复值<color=#9DF5A7>48000</color>",},
{talent_level=9,shengming_hf=54000,desc="提升角色生命回复值<color=#9DF5A7>54000</color>",},
{talent_level=10,shengming_hf=60000,desc="提升角色生命回复值<color=#9DF5A7>60000</color>",},
{talent_level=11,shengming_hf=66000,desc="提升角色生命回复值<color=#9DF5A7>66000</color>",},
{talent_level=12,shengming_hf=72000,desc="提升角色生命回复值<color=#9DF5A7>72000</color>",},
{talent_level=13,shengming_hf=78000,desc="提升角色生命回复值<color=#9DF5A7>78000</color>",},
{talent_level=14,shengming_hf=84000,desc="提升角色生命回复值<color=#9DF5A7>84000</color>",},
{talent_level=15,shengming_hf=90000,desc="提升角色生命回复值<color=#9DF5A7>90000</color>",},
{talent_level=16,shengming_hf=96000,desc="提升角色生命回复值<color=#9DF5A7>96000</color>",},
{talent_level=17,shengming_hf=102000,desc="提升角色生命回复值<color=#9DF5A7>102000</color>",},
{talent_level=18,shengming_hf=108000,desc="提升角色生命回复值<color=#9DF5A7>108000</color>",},
{talent_level=19,shengming_hf=114000,desc="提升角色生命回复值<color=#9DF5A7>114000</color>",},
{talent_level=20,shengming_hf=120000,desc="提升角色生命回复值<color=#9DF5A7>120000</color>",},
{talent_id=307,talent_type=3,xiaohao=2,pre_talent_type=3,pre_talent_type_level=30,pre_talent_id=302,effect_type=25,param_a=180,attack_power=1207,capability_inc=6000,desc="提升角色普攻伤害<color=#9DF5A7>1.8%</color>",},
{talent_level=2,param_a=360,attack_power=2414,capability_inc=12000,desc="提升角色普攻伤害<color=#9DF5A7>3.6%</color>",},
{talent_level=3,param_a=540,attack_power=3620,capability_inc=18000,desc="提升角色普攻伤害<color=#9DF5A7>5.4%</color>",},
{talent_level=4,param_a=720,attack_power=4827,capability_inc=24000,desc="提升角色普攻伤害<color=#9DF5A7>7.2%</color>",},
{talent_level=5,param_a=900,attack_power=6034,capability_inc=30000,desc="提升角色普攻伤害<color=#9DF5A7>9%</color>",},
{talent_level=6,xiaohao=3,param_a=1080,attack_power=7241,capability_inc=36000,desc="提升角色普攻伤害<color=#9DF5A7>10.8%</color>",},
{talent_level=7,param_a=1260,attack_power=8447,capability_inc=42000,desc="提升角色普攻伤害<color=#9DF5A7>12.6%</color>",},
{talent_level=8,param_a=1440,attack_power=9654,capability_inc=48000,desc="提升角色普攻伤害<color=#9DF5A7>14.4%</color>",},
{talent_level=9,param_a=1620,attack_power=10861,capability_inc=54000,desc="提升角色普攻伤害<color=#9DF5A7>16.2%</color>",},
{talent_level=10,xiaohao=4,param_a=1800,attack_power=12067,capability_inc=60000,desc="提升角色普攻伤害<color=#9DF5A7>18%</color>",},
{talent_id=308,talent_type=3,xiaohao=2,pre_talent_type=3,pre_talent_id=306,pre_talent_level=10,effect_type=26,param_a=100,param_b=30,attack_power=1500,capability_inc=18000,desc="组队战斗时,增强自身伤害,对玩家的伤害提升幅度为<color=#99ffbb>1%</color>乘以组队人数,对怪物的伤害提升幅度为<color=#99ffbb>0.3%</color>乘以组队人数",},
{talent_level=2,param_a=150,param_b=50,attack_power=2250,capability_inc=32500,desc="组队战斗时,增强自身伤害,对玩家的伤害提升幅度为<color=#99ffbb>1.5%</color>乘以组队人数,对怪物的伤害提升幅度为<color=#99ffbb>0.5%</color>乘以组队人数",},
{talent_level=3,param_a=200,param_b=70,attack_power=3000,capability_inc=45000,desc="组队战斗时,增强自身伤害,对玩家的伤害提升幅度为<color=#99ffbb>2%</color>乘以组队人数,对怪物的伤害提升幅度为<color=#99ffbb>0.7%</color>乘以组队人数",},
{talent_level=4,param_a=250,param_b=80,attack_power=3750,capability_inc=57500,desc="组队战斗时,增强自身伤害,对玩家的伤害提升幅度为<color=#99ffbb>2.5%</color>乘以组队人数,对怪物的伤害提升幅度为<color=#99ffbb>0.8%</color>乘以组队人数",},
{talent_level=5,xiaohao=3,param_a=300,param_b=100,attack_power=4500,capability_inc=70000,desc="组队战斗时,增强自身伤害,对玩家的伤害提升幅度为<color=#99ffbb>3%</color>乘以组队人数,对怪物的伤害提升幅度为<color=#99ffbb>1%</color>乘以组队人数",},
{talent_id=309,talent_type=3,xiaohao=2,pre_talent_type=3,pre_talent_id=305,effect_type=27,cool_down=60,param_a=4000,param_b=1330,defence_power=1059,capability_inc=18000,desc="单次受到大于生命上限<color=#99ffbb>50%</color>伤害时,在接下来的<color=#99ffbb>4</color>秒内,受到的伤害减少<color=#99ffbb>13.3%</color>,每隔<color=#99ffbb>60</color>秒触发一次",},
{talent_level=2,param_b=2000,defence_power=1588,capability_inc=32500,desc="单次受到大于生命上限<color=#99ffbb>50%</color>伤害时,在接下来的<color=#99ffbb>4</color>秒内,受到的伤害减少<color=#99ffbb>20%</color>,每隔<color=#99ffbb>60</color>秒触发一次",},
{talent_level=3,param_b=2670,defence_power=2118,capability_inc=45000,desc="单次受到大于生命上限<color=#99ffbb>50%</color>伤害时,在接下来的<color=#99ffbb>4</color>秒内,受到的伤害减少<color=#99ffbb>26.7%</color>,每隔<color=#99ffbb>60</color>秒触发一次",},
{talent_level=4,xiaohao=3,param_b=3330,defence_power=2647,capability_inc=57500,desc="单次受到大于生命上限<color=#99ffbb>50%</color>伤害时,在接下来的<color=#99ffbb>4</color>秒内,受到的伤害减少<color=#99ffbb>33.3%</color>,每隔<color=#99ffbb>60</color>秒触发一次",},
{talent_level=5,param_b=4000,defence_power=3176,capability_inc=70000,desc="单次受到大于生命上限<color=#99ffbb>50%</color>伤害时,在接下来的<color=#99ffbb>4</color>秒内,受到的伤害减少<color=#99ffbb>40%</color>,每隔<color=#99ffbb>60</color>秒触发一次",},
{talent_id=310,talent_type=3,xiaohao=5,pre_talent_type=3,pre_talent_type_level=100,pre_talent_id=307,pre_talent_level=10,effect_type=28,cool_down=30,param_a=0,attack_power=4394,capability_inc=40000,desc="受到令角色死亡的伤害时,必定<color=#99ffbb>免疫此次伤害</color>,每隔<color=#99ffbb>30</color>秒触发一次",},
{talent_id=311,talent_type=3,xiaohao=3,pre_talent_type=3,pre_talent_type_level=80,pre_talent_id=308,effect_type=29,cool_down=30,param_a=1000,param_b=2000,defence_power=2412,capability_inc=25000,desc="攻击时,有<color=#99ffbb>10%</color>概率给目标附加沉默状态,持续<color=#99ffbb>2</color>秒,每隔<color=#99ffbb>30</color>秒触发一次",}
},

talent_level_cfg_meta_table_map={
[190]=177,	-- depth:1
[196]=177,	-- depth:1
[195]=177,	-- depth:1
[194]=177,	-- depth:1
[193]=177,	-- depth:1
[192]=177,	-- depth:1
[191]=177,	-- depth:1
[188]=177,	-- depth:1
[187]=177,	-- depth:1
[186]=177,	-- depth:1
[185]=177,	-- depth:1
[184]=177,	-- depth:1
[183]=177,	-- depth:1
[182]=177,	-- depth:1
[181]=177,	-- depth:1
[180]=177,	-- depth:1
[179]=177,	-- depth:1
[178]=177,	-- depth:1
[198]=197,	-- depth:1
[199]=197,	-- depth:1
[189]=177,	-- depth:1
[201]=197,	-- depth:1
[200]=197,	-- depth:1
[216]=197,	-- depth:1
[215]=197,	-- depth:1
[214]=197,	-- depth:1
[213]=197,	-- depth:1
[212]=197,	-- depth:1
[211]=197,	-- depth:1
[210]=197,	-- depth:1
[209]=197,	-- depth:1
[208]=197,	-- depth:1
[207]=197,	-- depth:1
[206]=197,	-- depth:1
[205]=197,	-- depth:1
[204]=197,	-- depth:1
[203]=197,	-- depth:1
[202]=197,	-- depth:1
[143]=132,	-- depth:1
[142]=132,	-- depth:1
[141]=132,	-- depth:1
[140]=132,	-- depth:1
[139]=132,	-- depth:1
[136]=132,	-- depth:1
[137]=132,	-- depth:1
[144]=132,	-- depth:1
[135]=132,	-- depth:1
[134]=132,	-- depth:1
[138]=132,	-- depth:1
[145]=132,	-- depth:1
[149]=132,	-- depth:1
[147]=132,	-- depth:1
[148]=132,	-- depth:1
[133]=132,	-- depth:1
[150]=132,	-- depth:1
[151]=132,	-- depth:1
[162]=152,	-- depth:1
[146]=132,	-- depth:1
[234]=233,	-- depth:1
[236]=235,	-- depth:1
[5]=1,	-- depth:1
[243]=242,	-- depth:1
[244]=242,	-- depth:1
[245]=242,	-- depth:1
[246]=242,	-- depth:1
[248]=247,	-- depth:1
[249]=247,	-- depth:1
[251]=250,	-- depth:1
[4]=1,	-- depth:1
[274]=273,	-- depth:1
[275]=273,	-- depth:1
[276]=273,	-- depth:1
[277]=273,	-- depth:1
[278]=273,	-- depth:1
[232]=233,	-- depth:1
[231]=227,	-- depth:1
[230]=227,	-- depth:1
[229]=227,	-- depth:1
[19]=1,	-- depth:1
[18]=1,	-- depth:1
[17]=1,	-- depth:1
[16]=1,	-- depth:1
[15]=1,	-- depth:1
[14]=1,	-- depth:1
[13]=1,	-- depth:1
[12]=1,	-- depth:1
[11]=1,	-- depth:1
[10]=1,	-- depth:1
[9]=1,	-- depth:1
[279]=273,	-- depth:1
[8]=1,	-- depth:1
[218]=217,	-- depth:1
[219]=217,	-- depth:1
[220]=217,	-- depth:1
[221]=217,	-- depth:1
[223]=222,	-- depth:1
[224]=222,	-- depth:1
[69]=66,	-- depth:1
[6]=1,	-- depth:1
[228]=227,	-- depth:1
[7]=1,	-- depth:1
[70]=66,	-- depth:1
[280]=273,	-- depth:1
[282]=273,	-- depth:1
[311]=293,	-- depth:1
[312]=293,	-- depth:1
[2]=1,	-- depth:1
[340]=339,	-- depth:1
[341]=339,	-- depth:1
[342]=339,	-- depth:1
[343]=339,	-- depth:1
[344]=339,	-- depth:1
[345]=339,	-- depth:1
[346]=339,	-- depth:1
[347]=339,	-- depth:1
[348]=339,	-- depth:1
[349]=339,	-- depth:1
[350]=339,	-- depth:1
[351]=339,	-- depth:1
[352]=339,	-- depth:1
[353]=339,	-- depth:1
[354]=339,	-- depth:1
[355]=339,	-- depth:1
[356]=339,	-- depth:1
[357]=339,	-- depth:1
[358]=339,	-- depth:1
[310]=293,	-- depth:1
[309]=293,	-- depth:1
[308]=293,	-- depth:1
[307]=293,	-- depth:1
[283]=273,	-- depth:1
[284]=273,	-- depth:1
[285]=273,	-- depth:1
[286]=273,	-- depth:1
[287]=273,	-- depth:1
[288]=273,	-- depth:1
[289]=273,	-- depth:1
[290]=273,	-- depth:1
[291]=273,	-- depth:1
[292]=273,	-- depth:1
[3]=1,	-- depth:1
[281]=273,	-- depth:1
[294]=293,	-- depth:1
[296]=293,	-- depth:1
[297]=293,	-- depth:1
[298]=293,	-- depth:1
[299]=293,	-- depth:1
[300]=293,	-- depth:1
[301]=293,	-- depth:1
[302]=293,	-- depth:1
[303]=293,	-- depth:1
[304]=293,	-- depth:1
[305]=293,	-- depth:1
[306]=293,	-- depth:1
[295]=293,	-- depth:1
[20]=1,	-- depth:1
[225]=226,	-- depth:1
[74]=66,	-- depth:1
[165]=162,	-- depth:2
[164]=162,	-- depth:2
[163]=162,	-- depth:2
[47]=46,	-- depth:1
[67]=66,	-- depth:1
[65]=46,	-- depth:1
[64]=46,	-- depth:1
[63]=46,	-- depth:1
[156]=152,	-- depth:1
[155]=152,	-- depth:1
[154]=152,	-- depth:1
[153]=152,	-- depth:1
[48]=46,	-- depth:1
[49]=46,	-- depth:1
[50]=46,	-- depth:1
[51]=46,	-- depth:1
[52]=46,	-- depth:1
[53]=46,	-- depth:1
[54]=46,	-- depth:1
[55]=46,	-- depth:1
[62]=46,	-- depth:1
[61]=46,	-- depth:1
[60]=46,	-- depth:1
[59]=46,	-- depth:1
[58]=46,	-- depth:1
[166]=156,	-- depth:2
[56]=46,	-- depth:1
[57]=46,	-- depth:1
[82]=66,	-- depth:1
[76]=66,	-- depth:1
[77]=66,	-- depth:1
[78]=66,	-- depth:1
[79]=66,	-- depth:1
[80]=66,	-- depth:1
[68]=66,	-- depth:1
[81]=66,	-- depth:1
[71]=66,	-- depth:1
[83]=66,	-- depth:1
[84]=66,	-- depth:1
[85]=66,	-- depth:1
[75]=66,	-- depth:1
[72]=66,	-- depth:1
[73]=66,	-- depth:1
[270]=271,	-- depth:1
[268]=269,	-- depth:1
[253]=252,	-- depth:1
[266]=262,	-- depth:1
[265]=262,	-- depth:1
[264]=262,	-- depth:1
[263]=262,	-- depth:1
[254]=252,	-- depth:1
[255]=252,	-- depth:1
[256]=252,	-- depth:1
[258]=259,	-- depth:1
[260]=261,	-- depth:1
[257]=259,	-- depth:1
[267]=269,	-- depth:1
[170]=171,	-- depth:1
[169]=171,	-- depth:1
[168]=171,	-- depth:1
[161]=171,	-- depth:1
[160]=161,	-- depth:2
[167]=171,	-- depth:1
[158]=161,	-- depth:2
[157]=167,	-- depth:2
[159]=161,	-- depth:2
[21]=31,	-- depth:1
[119]=111,	-- depth:1
[327]=323,	-- depth:1
[317]=313,	-- depth:1
[95]=86,	-- depth:1
[326]=323,	-- depth:1
[120]=119,	-- depth:2
[113]=111,	-- depth:1
[117]=111,	-- depth:1
[116]=117,	-- depth:2
[115]=111,	-- depth:1
[114]=111,	-- depth:1
[316]=313,	-- depth:1
[112]=111,	-- depth:1
[325]=323,	-- depth:1
[324]=323,	-- depth:1
[118]=117,	-- depth:2
[94]=95,	-- depth:2
[88]=86,	-- depth:1
[32]=31,	-- depth:1
[93]=86,	-- depth:1
[92]=93,	-- depth:2
[91]=93,	-- depth:2
[314]=313,	-- depth:1
[35]=31,	-- depth:1
[34]=31,	-- depth:1
[33]=31,	-- depth:1
[315]=313,	-- depth:1
[87]=86,	-- depth:1
[23]=33,	-- depth:2
[24]=34,	-- depth:2
[25]=35,	-- depth:2
[89]=86,	-- depth:1
[90]=86,	-- depth:1
[22]=32,	-- depth:2
[320]=313,	-- depth:1
[319]=320,	-- depth:2
[318]=320,	-- depth:2
[321]=320,	-- depth:2
[331]=323,	-- depth:1
[29]=21,	-- depth:2
[328]=331,	-- depth:2
[329]=331,	-- depth:2
[330]=331,	-- depth:2
[332]=331,	-- depth:2
[30]=29,	-- depth:3
[36]=31,	-- depth:1
[37]=36,	-- depth:2
[38]=36,	-- depth:2
[39]=36,	-- depth:2
[40]=36,	-- depth:2
[322]=320,	-- depth:2
[104]=96,	-- depth:1
[124]=121,	-- depth:1
[98]=96,	-- depth:1
[97]=96,	-- depth:1
[128]=121,	-- depth:1
[127]=128,	-- depth:2
[126]=128,	-- depth:2
[125]=121,	-- depth:1
[123]=121,	-- depth:1
[122]=121,	-- depth:1
[28]=38,	-- depth:3
[129]=121,	-- depth:1
[26]=36,	-- depth:2
[27]=26,	-- depth:3
[105]=104,	-- depth:2
[103]=96,	-- depth:1
[102]=103,	-- depth:2
[101]=103,	-- depth:2
[100]=96,	-- depth:1
[99]=96,	-- depth:1
[130]=129,	-- depth:2
[238]=237,	-- depth:1
[239]=237,	-- depth:1
[368]=359,	-- depth:1
[361]=359,	-- depth:1
[367]=368,	-- depth:2
[241]=237,	-- depth:1
[362]=359,	-- depth:1
[363]=359,	-- depth:1
[360]=359,	-- depth:1
[240]=241,	-- depth:2
[364]=359,	-- depth:1
[365]=364,	-- depth:2
[366]=364,	-- depth:2
[373]=369,	-- depth:1
[372]=373,	-- depth:2
[370]=369,	-- depth:1
[375]=374,	-- depth:1
[377]=374,	-- depth:1
[378]=377,	-- depth:2
[376]=374,	-- depth:1
[371]=369,	-- depth:1
[333]=380,	-- depth:1
[337]=334,	-- depth:1
[338]=337,	-- depth:2
[335]=334,	-- depth:1
[336]=334,	-- depth:1
[173]=172,	-- depth:1
[174]=172,	-- depth:1
[175]=172,	-- depth:1
[176]=172,	-- depth:1
[109]=106,	-- depth:1
[110]=109,	-- depth:2
[44]=41,	-- depth:1
[107]=106,	-- depth:1
[45]=41,	-- depth:1
[42]=41,	-- depth:1
[108]=106,	-- depth:1
[43]=41,	-- depth:1
},
other={
{}
},

other_meta_table_map={
},
talent_level_max_default_table={talent_id=100,name="冥想·意守丹田",solt_index=1,max_level=10,talent_type=2,icon=1,tips_x=97,tips_y=-194,},

talent_level_cfg_default_table={talent_id=100,talent_level=1,talent_type=2,xiaohao=1,pre_talent_type=2,pre_talent_type_level=0,pre_talent_id=0,pre_talent_level=5,effect_type=1,attr_type_1=0,attr_value_1=0,attr_type_2=0,attr_value_2=0,attr_type_3=0,attr_value_3=0,gongji=0,pojia=0,fangyu=0,shengming_max=0,yuansu_sh=0,yuansu_hj=0,shengming_qq=0,shanghai_zs=0,fangtan=0,fangyu_zs=0,shengming_hf=0,zengshang_per=0,zengshang_guaiwu_per=0,jianshang_guaiwu_per=0,jianshang_per=0,mingzhong_per=0,podang_per=0,kangbao_per=0,lianjikang_per=0,jichuankang_per=0,cool_down=0,add_fix_cap=0,param_a=11,param_b=0,param_c=0,param_d=0,param_e=0,attack_power=0,defence_power=0,capability_inc=0,extra_gongji_jiacheng=0,extra_fangyu_jiacheng=0,desc="提升背饰<color=#9DF5A7>3%</color>培养属性(不含化形）",},

other_default_table={open_talent_level=371,base_talent_point=5,reset_consume_item=26196,proficient_talent_limit=0,open_proficient_talent_level=0,task_id=29019,seq=1757,talent_point_id=91481,}

}

