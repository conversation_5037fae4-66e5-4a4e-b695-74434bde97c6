-- Y-运营活动-登录有礼.xls
local item_table={
[1]={item_id=46556,num=15,is_bind=1},
[2]={item_id=46557,num=2,is_bind=1},
[3]={item_id=39142,num=500,is_bind=1},
[4]={item_id=36366,num=1,is_bind=1},
[5]={item_id=27908,num=5,is_bind=1},
[6]={item_id=46555,num=10,is_bind=1},
[7]={item_id=39144,num=1000,is_bind=1},
[8]={item_id=46559,num=1,is_bind=1},
[9]={item_id=22615,num=1,is_bind=1},
[10]={item_id=46555,num=15,is_bind=1},
[11]={item_id=39142,num=1000,is_bind=1},
[12]={item_id=46559,num=2,is_bind=1},
[13]={item_id=46555,num=20,is_bind=1},
[14]={item_id=22753,num=1,is_bind=1},
[15]={item_id=46556,num=10,is_bind=1},
[16]={item_id=46557,num=1,is_bind=1},
}

return {
activity_param={
{grade=0,},
{week_id=5,grade=1,},
{start_server_day=20,end_server_day=27,grade=0,interface=2,},
{week_id=5,grade=1,},
{start_server_day=27,end_server_day=34,},
{week_id=5,grade=1,},
{start_server_day=34,end_server_day=41,},
{start_server_day=34,end_server_day=41,},
{start_server_day=41,end_server_day=48,},
{week_id=5,grade=1,},
{start_server_day=48,end_server_day=55,},
{start_server_day=48,end_server_day=55,},
{start_server_day=55,end_server_day=62,},
{start_server_day=55,end_server_day=62,},
{start_server_day=62,end_server_day=69,},
{start_server_day=62,end_server_day=69,},
{start_server_day=69,end_server_day=76,},
{start_server_day=69,end_server_day=76,},
{start_server_day=76,end_server_day=83,},
{start_server_day=76,end_server_day=83,},
{start_server_day=83,end_server_day=90,},
{start_server_day=83,end_server_day=90,},
{start_server_day=90,end_server_day=97,},
{start_server_day=90,end_server_day=97,},
{start_server_day=97,end_server_day=104,},
{start_server_day=97,end_server_day=104,},
{start_server_day=104,end_server_day=111,},
{week_id=5,grade=3,},
{start_server_day=111,end_server_day=118,},
{start_server_day=111,end_server_day=118,},
{start_server_day=118,end_server_day=125,},
{start_server_day=118,end_server_day=125,},
{start_server_day=125,end_server_day=132,},
{start_server_day=125,end_server_day=132,},
{start_server_day=132,end_server_day=139,},
{start_server_day=132,end_server_day=139,},
{start_server_day=139,end_server_day=146,},
{week_id=5,grade=3,},
{start_server_day=146,end_server_day=153,},
{start_server_day=146,end_server_day=153,},
{start_server_day=153,end_server_day=160,},
{start_server_day=153,end_server_day=160,},
{start_server_day=160,end_server_day=167,interface=2,},
{start_server_day=160,end_server_day=167,},
{start_server_day=167,end_server_day=174,},
{start_server_day=167,end_server_day=174,},
{start_server_day=174,end_server_day=181,},
{start_server_day=174,end_server_day=181,},
{start_server_day=181,end_server_day=188,},
{start_server_day=181,end_server_day=188,},
{start_server_day=188,end_server_day=999,},
{start_server_day=188,end_server_day=999,}
},

activity_param_meta_table_map={
[27]=43,	-- depth:1
[51]=43,	-- depth:1
[23]=43,	-- depth:1
[35]=43,	-- depth:1
[9]=1,	-- depth:1
[19]=43,	-- depth:1
[47]=43,	-- depth:1
[31]=43,	-- depth:1
[39]=43,	-- depth:1
[5]=9,	-- depth:2
[15]=43,	-- depth:1
[38]=37,	-- depth:1
[34]=38,	-- depth:2
[50]=38,	-- depth:2
[46]=38,	-- depth:2
[42]=38,	-- depth:2
[26]=38,	-- depth:2
[22]=38,	-- depth:2
[6]=5,	-- depth:3
[18]=38,	-- depth:2
[7]=3,	-- depth:1
[14]=38,	-- depth:2
[30]=38,	-- depth:2
[11]=3,	-- depth:1
[10]=9,	-- depth:2
[28]=27,	-- depth:2
[4]=3,	-- depth:1
[48]=28,	-- depth:3
[8]=4,	-- depth:2
[12]=4,	-- depth:2
[40]=28,	-- depth:3
[16]=28,	-- depth:3
[36]=28,	-- depth:3
[20]=28,	-- depth:3
[24]=28,	-- depth:3
[32]=28,	-- depth:3
[44]=28,	-- depth:3
[52]=28,	-- depth:3
},
login_reward={
{grade=0,},
{grade=0,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{reward_item={[0]=item_table[6],[1]=item_table[7],[2]=item_table[8],[3]=item_table[9],[4]=item_table[5]},},
{day_index=2,reward_item={[0]=item_table[10],[1]=item_table[11],[2]=item_table[12],[3]=item_table[4],[4]=item_table[5]},},
{day_index=3,reward_item={[0]=item_table[13],[1]=item_table[14],[2]=item_table[11],[3]=item_table[12],[4]=item_table[5]},},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,}
},

login_reward_meta_table_map={
[8]=3,	-- depth:1
[2]=4,	-- depth:1
[7]=2,	-- depth:2
[9]=4,	-- depth:1
[10]=5,	-- depth:1
},
interface={
{},
{interface=1,},
{interface=2,top_bg="reward_top_bg_1",word_bg="word_songli_1",word_bg1="word_lingqu_1",list_bg="cslc_04_1",list_item_bg="cslc_012",},
{interface=3,}
},

interface_meta_table_map={
[4]=3,	-- depth:1
},
language={
{},
{ID=2,conect="使人变渺小的感情可耻，使人变孩子的感情可贵",},
{ID=3,conect="茶亦醉人何必久，书能香我无需花",},
{ID=4,conect="山川是不卷收的文章，日月为你掌灯伴读",},
{ID=5,conect="当时年少春衫薄，骑马倚斜桥，满楼红袖招",},
{ID=6,conect="未来的路不会比过去更笔直，更平坦，但是我并不恐惧，我眼前还闪动着道路前方野百合的影子",},
{ID=7,conect="世界不再贩卖焦虑，开始流行快乐",},
{ID=8,conect="把烦心事去掉，腾出地方装鲜花",},
{ID=9,conect="把怦然心动和一切热爱都寄予活着也是一种浪漫",},
{ID=10,conect="不辞山路远，踏雪也相过",}
},

language_meta_table_map={
},
activity_param_default_table={start_server_day=10,end_server_day=20,week_id=2,grade=2,open_role_level=100,interface=0,},

login_reward_default_table={grade=1,day_index=1,reward_item={[0]=item_table[15],[1]=item_table[16],[2]=item_table[3],[3]=item_table[9],[4]=item_table[5]},vip_lv=0,special_reward_item={},},

interface_default_table={interface=0,top_bg="reward_top_bg",word_bg="word_songli1",word_bg1="word_lingqu",list_bg="cslc_04",list_item_bg="cslc_011",day_line="login_line",select_lh="lianhua_select",normal_lh="lianhua_normal",ylq_lh="lianhua_yilingqu",ylq_btn="new_btn_2",base_word="基础奖励",base_bg="title_bg1",tip_word="登录即送",reward_bg="day_first_kuang",title_bg="biaotidi",vip_word="贵族专享",vip_bg="title_bg2",big_bg="denglu_reward_bg2",lq_btn_bg="recharge_btn_1",lq_btn_efc="UI_lq_04",day_num="#fcfaf2|#ffe69c|#ca6947",wh_bg="btn_tip",wh_word="每天登录就能领奖哦~",btn_rule_title="活动说明",rule_desc="1.活动期间，每日登录游戏，即可领取超值好礼\n2.登录获得<color=#99ffbb>密钥</color>，可前往<color=#99ffbb>寻宝</color>活动参与抽奖\n3.活动结束后，未领取的奖励将通过邮件发放",},

language_default_table={ID=1,conect="我们走进夜海，去打捞遗失的繁星",}

}

