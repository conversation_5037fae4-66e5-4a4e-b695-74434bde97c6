-- K-跨服国战.xls
local item_table={
[1]={item_id=57990,num=10,is_bind=1},
[2]={item_id=57988,num=10,is_bind=1},
[3]={item_id=27613,num=1,is_bind=1},
[4]={item_id=27612,num=5,is_bind=1},
[5]={item_id=46048,num=5,is_bind=1},
[6]={item_id=44049,num=2,is_bind=1},
[7]={item_id=44051,num=4,is_bind=1},
[8]={item_id=39142,num=8000,is_bind=1},
[9]={item_id=36420,num=80,is_bind=1},
[10]={item_id=36422,num=18000,is_bind=1},
[11]={item_id=36416,num=900000,is_bind=1},
[12]={item_id=57990,num=8,is_bind=1},
[13]={item_id=57988,num=8,is_bind=1},
[14]={item_id=46048,num=3,is_bind=1},
[15]={item_id=44051,num=3,is_bind=1},
[16]={item_id=39142,num=7000,is_bind=1},
[17]={item_id=36420,num=60,is_bind=1},
[18]={item_id=36422,num=16000,is_bind=1},
[19]={item_id=36416,num=800000,is_bind=1},
[20]={item_id=57990,num=5,is_bind=1},
[21]={item_id=57988,num=5,is_bind=1},
[22]={item_id=27612,num=3,is_bind=1},
[23]={item_id=46048,num=2,is_bind=1},
[24]={item_id=44051,num=2,is_bind=1},
[25]={item_id=39142,num=6000,is_bind=1},
[26]={item_id=36420,num=50,is_bind=1},
[27]={item_id=36422,num=14000,is_bind=1},
[28]={item_id=36416,num=700000,is_bind=1},
[29]={item_id=57990,num=3,is_bind=1},
[30]={item_id=57988,num=3,is_bind=1},
[31]={item_id=27612,num=2,is_bind=1},
[32]={item_id=44051,num=1,is_bind=1},
[33]={item_id=39142,num=5000,is_bind=1},
[34]={item_id=36422,num=12000,is_bind=1},
[35]={item_id=36416,num=600000,is_bind=1},
[36]={item_id=57990,num=1,is_bind=1},
[37]={item_id=57988,num=1,is_bind=1},
[38]={item_id=27612,num=1,is_bind=1},
[39]={item_id=46048,num=1,is_bind=1},
[40]={item_id=39142,num=4000,is_bind=1},
[41]={item_id=36422,num=10000,is_bind=1},
[42]={item_id=36416,num=500000,is_bind=1},
[43]={item_id=39142,num=540,is_bind=1},
[44]={item_id=36422,num=1270,is_bind=1},
[45]={item_id=36416,num=60000,is_bind=1},
[46]={item_id=39142,num=1150,is_bind=1},
[47]={item_id=36422,num=2680,is_bind=1},
[48]={item_id=36416,num=120000,is_bind=1},
[49]={item_id=39142,num=2060,is_bind=1},
[50]={item_id=36422,num=4800,is_bind=1},
[51]={item_id=36416,num=210000,is_bind=1},
[52]={item_id=39142,num=3580,is_bind=1},
[53]={item_id=36422,num=8340,is_bind=1},
[54]={item_id=36416,num=360000,is_bind=1},
[55]={item_id=27740,num=150,is_bind=1},
[56]={item_id=39142,num=5300,is_bind=1},
[57]={item_id=36422,num=6000,is_bind=1},
[58]={item_id=36416,num=300000,is_bind=1},
[59]={item_id=27740,num=75,is_bind=1},
[60]={item_id=39142,num=4700,is_bind=1},
[61]={item_id=36422,num=5000,is_bind=1},
[62]={item_id=27740,num=50,is_bind=1},
[63]={item_id=36416,num=200000,is_bind=1},
[64]={item_id=36422,num=1000,is_bind=1},
[65]={item_id=36422,num=50,is_bind=1},
[66]={item_id=57990,num=15,is_bind=1},
[67]={item_id=57988,num=15,is_bind=1},
[68]={item_id=27613,num=2,is_bind=1},
[69]={item_id=44049,num=3,is_bind=1},
[70]={item_id=44051,num=5,is_bind=1},
[71]={item_id=39142,num=9000,is_bind=1},
[72]={item_id=36420,num=100,is_bind=1},
[73]={item_id=36422,num=20000,is_bind=1},
[74]={item_id=36416,num=1000000,is_bind=1},
[75]={item_id=39142,num=180,is_bind=1},
[76]={item_id=36422,num=420,is_bind=1},
[77]={item_id=36416,num=20000,is_bind=1},
[78]={item_id=27740,num=200,is_bind=1},
[79]={item_id=36422,num=7000,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_time={
{},
{activity_week_day=6,}
},

open_time_meta_table_map={
},
city={
[1]={seq=1,fight_des="外部圆环表示据点正在被其他仙门争夺",},
[2]={seq=2,connect_city="5|6|9|10",name="玉英仙境",fight_des="这就是你的仙门占领的据点，在仙门据点内击杀敌城敌方玩家时会获得20的积分加成，同时每连续占领1分钟可获一定争霸积分",},
[3]={seq=3,connect_city="6|7|10|11",name="琼华仙境",fight_des="血条为boss剩余血量，对boss伤害累计最高的仙境获得该剧点占领权",},
[4]={seq=4,connect_city="7|8|11|12",name="朔方仙境",fight_des="占领据点后可以点亮路线点击连线链接的其他据点可进行传送",},
[5]={seq=5,connect_city="1|2",name="瑶池灵台",fight_des="这是你的初始蓝色城池，实战中可能被分配为其他颜色",},
[6]={seq=6,connect_city="2|3",name="幽冥灵台",fight_des="该灰色据点为中立状态，点击据点可以传送到该剧点进行争夺，占领越多奖励越多",},
[7]={seq=7,type=1,connect_city="3|4",monster_id=48801,fight_car_attack_score=21,name="飘渺灵台",},
[8]={seq=8,connect_city="1|4",name="苍梧灵台",},
[9]={seq=9,type=2,connect_city="1|2|13",monster_id=49801,capture_tired=20,capture_power=120,minute_add_power=20,fight_car_attack_score=22,name="蓬莱仙山",},
[10]={seq=10,connect_city="2|3|13",name="九华仙山",},
[11]={seq=11,connect_city="3|4|14",name="方丈仙山",},
[12]={seq=12,connect_city="4|1|14",name="瀛洲仙山",},
[13]={seq=13,type=3,connect_city="9|10|15",monster_id=50801,capture_tired=30,capture_power=240,minute_add_power=40,city_lord_title=5026,fight_car_attack_score=23,name="太上城",},
[14]={seq=14,connect_city="11|12|15",city_lord_title=5024,name="问情城",},
[15]={seq=15,type=4,connect_city="13|14",monster_id=51801,capture_power=480,minute_add_power=80,city_lord_title=5027,name="九霄城",}
},

city_meta_table_map={
[8]=7,	-- depth:1
[5]=7,	-- depth:1
[6]=7,	-- depth:1
[10]=9,	-- depth:1
[11]=9,	-- depth:1
[12]=9,	-- depth:1
[14]=13,	-- depth:1
[15]=13,	-- depth:1
},
monster_level={
{},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,maxhp=80000000,},
{seq=10,},
{seq=11,},
{seq=12,},
{seq=13,maxhp=140000000,},
{seq=14,},
{seq=15,maxhp=230000000,}
},

monster_level_meta_table_map={
[10]=9,	-- depth:1
[11]=10,	-- depth:2
[12]=11,	-- depth:3
[14]=13,	-- depth:1
},
relive_pos={
{},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{seq=10,},
{seq=11,},
{seq=12,},
{seq=13,},
{seq=14,},
{seq=15,}
},

relive_pos_meta_table_map={
},
tired_hurt_reduce={
{}
},

tired_hurt_reduce_meta_table_map={
},
kill_add_score={
{},
{min_rank=2,max_rank=5,add_score=500,},
{min_rank=6,max_rank=20,add_score=300,},
{min_rank=21,max_rank=50,add_score=200,},
{min_rank=51,max_rank=999,add_score=100,}
},

kill_add_score_meta_table_map={
},
score_rank_reward={
{},
{min_rank=2,max_rank=3,client_reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7],[7]=item_table[8],[8]=item_table[9],[9]=item_table[10],[10]=item_table[11]},},
{min_rank=4,max_rank=10,client_reward_item={[0]=item_table[12],[1]=item_table[13],[2]=item_table[4],[3]=item_table[14],[4]=item_table[15],[5]=item_table[16],[6]=item_table[17],[7]=item_table[18],[8]=item_table[19]},},
{min_rank=11,max_rank=20,client_reward_item={[0]=item_table[20],[1]=item_table[21],[2]=item_table[22],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25],[6]=item_table[26],[7]=item_table[27],[8]=item_table[28]},},
{min_rank=21,max_rank=50,client_reward_item={[0]=item_table[29],[1]=item_table[30],[2]=item_table[31],[3]=item_table[23],[4]=item_table[32],[5]=item_table[33],[6]=item_table[26],[7]=item_table[34],[8]=item_table[35]},},
{min_rank=51,max_rank=999,client_reward_item={[0]=item_table[36],[1]=item_table[37],[2]=item_table[38],[3]=item_table[39],[4]=item_table[32],[5]=item_table[40],[6]=item_table[26],[7]=item_table[41],[8]=item_table[42]},}
},

score_rank_reward_meta_table_map={
},
power_treat={
[0]={index=0,},
[1]={index=1,power=6000,reward_item={[0]=item_table[43],[1]=item_table[44],[2]=item_table[45]},name="足兵足食",},
[2]={index=2,power=10000,reward_item={[0]=item_table[46],[1]=item_table[47],[2]=item_table[48]},name="兵精粮足",},
[3]={index=3,power=15000,reward_item={[0]=item_table[49],[1]=item_table[50],[2]=item_table[51]},name="民丰物阜",},
[4]={index=4,power=25000,reward_item={[0]=item_table[52],[1]=item_table[53],[2]=item_table[54]},name="大赏三军",},
[5]={index=5,power=40000,reward_item={[0]=item_table[25],[1]=item_table[27],[2]=item_table[35]},name="举国同庆",}
},

power_treat_meta_table_map={
},
power_rank_reward={
{},
{min_rank=2,max_rank=2,reward_item={[0]=item_table[55],[1]=item_table[56],[2]=item_table[57],[3]=item_table[58]},},
{min_rank=3,max_rank=3,reward_item={[0]=item_table[59],[1]=item_table[60],[2]=item_table[61],[3]=item_table[58]},},
{min_rank=4,max_rank=4,reward_item={[0]=item_table[62],[1]=item_table[40],[2]=item_table[61],[3]=item_table[63]},}
},

power_rank_reward_meta_table_map={
},
season_reward={

},

season_reward_meta_table_map={
},
nation_data={
[1]={index=1,},
[2]={index=2,name="飘渺仙境",icon="feng",},
[3]={index=3,name="昆仑仙境",icon="yun",},
[4]={index=4,name="瑶池仙境",icon="yue",}
},

nation_data_meta_table_map={
},
fight_car={
{},
{id=2,type=2,skill_id="901|903",}
},

fight_car_meta_table_map={
},
fight_car_skill_cd={
{},
{skill_id=903,},
{skill_id=904,}
},

fight_car_skill_cd_meta_table_map={
},
continue_kill={
{type=1,},
{type=1,kill_desc="%s已一骑当千连续击败%s人！",},
{type=1,kill_desc="%s正在无双杀戮连续击败%s人！",},
{type=1,kill_desc="%s风华绝代连续击败%s人！",},
{kill_count=1,kill_desc="%s已完成单杀！",},
{kill_desc="%s已完成双杀！",},
{kill_count=3,kill_desc="%s已完成三杀！",},
{kill_count=4,kill_desc="%s已完成四杀！",},
{kill_count=5,kill_desc="%s已完成五杀！",},
{kill_count=7,kill_desc="%s已完成七连绝世！",},
{kill_count=10,kill_desc="%s已完成以一敌十！",}
},

continue_kill_meta_table_map={
[2]=7,	-- depth:1
[3]=8,	-- depth:1
[4]=9,	-- depth:1
},
camera_view={
{},
{server_index=2,rotation_y=180,},
{server_index=3,rotation_y=90,},
{server_index=4,rotation_y=-90,}
},

camera_view_meta_table_map={
},
other_default_table={attack_war_car_max_num=5,defend_war_car_max_num=5,add_war_car_need_role_num=1,hurt_remove_interval=30,initial_city_added_hurt_scale=5000,attack_monster_add_score=1,attack_monster_add_score_need_hurt=5000,initial_city_added_score_scale=2000,season_country_power_limit=5000,season_country_score_limit=500,player_worship_reward={[0]=item_table[64]},player_be_worship_reward={[0]=item_table[65]},to_fight_car_added_hurt_scale=117,owner_reward_list={[0]={item_id=37627,num=1,is_bind=1,param0=4},[1]={item_id=37737,num=1,is_bind=1,param0=4},[2]={item_id=37727,num=1,is_bind=1,param0=6},[3]={item_id=37738,num=1,is_bind=1,param0=5},[4]={item_id=38917,num=1,is_bind=1,param0=1},[5]={item_id=38916,num=1,is_bind=1,param0=3},[6]={item_id=38914,num=1,is_bind=1,param0=2}},cap_rank_count=10,convene_cd=30,},

open_time_default_table={activity_week_day=3,activity_ready_time=2057,activity_start_time=2100,activity_end_time=2130,},

city_default_table={seq=1,type=0,connect_city="5|8|9|12",scene_id=4598,monster_id=47801,monster_pos="109,109",capture_tired=10,capture_power=60,minute_add_power=10,city_lord_title=0,fight_car_attack_score=10,monster_refresh_interval=10,name="昆仑仙境",reward_desc="仙境占领城池后持续增加仙境<color=#6fbb6f>灵力</color>",bianshen_npc_1=708,bianshen_npc_2=709,bianshen_npc_3=710,bianshen_npc_4=711,guaji_pos1="106,96",guaji_pos2="106,107",guaji_pos3="101,101",guaji_pos4="112,101",fight_car_guaji_pos1="106,91",fight_car_guaji_pos2="106,111",fight_car_guaji_pos3="96,101",fight_car_guaji_pos4="116,101",fight_des="",},

monster_level_default_table={min_avg_level=1,max_avg_level=999,seq=1,maxhp=30000000,},

relive_pos_default_table={seq=1,pos_0="106,26",pos_1="106,176",pos_2="33,101",pos_3="179,101",},

tired_hurt_reduce_default_table={min_tired=200,max_tired=2000,reduce_scale=5000,},

kill_add_score_default_table={min_rank=1,max_rank=1,add_score=1000,},

score_rank_reward_default_table={min_rank=1,max_rank=1,client_reward_item={[0]=item_table[66],[1]=item_table[67],[2]=item_table[68],[3]=item_table[4],[4]=item_table[5],[5]=item_table[69],[6]=item_table[70],[7]=item_table[71],[8]=item_table[72],[9]=item_table[73],[10]=item_table[74]},},

power_treat_default_table={index=0,power=3000,reward_item={[0]=item_table[75],[1]=item_table[76],[2]=item_table[77]},name="小战微赏",},

power_rank_reward_default_table={min_rank=1,max_rank=1,reward_item={[0]=item_table[78],[1]=item_table[25],[2]=item_table[79],[3]=item_table[58]},},

season_reward_default_table={},

nation_data_default_table={index=1,name="蓬莱仙境",icon="guang",},

fight_car_default_table={id=1,type=1,app_id=2039001,skill_id=900,duration=0,max_num=2,base_num=2,add_need_role_num=999,},

fight_car_skill_cd_default_table={skill_id=902,cd_time=30,},

continue_kill_default_table={type=2,kill_count=2,kill_desc="%s已无人能挡连续击败%s人！",},

camera_view_default_table={scene_id=4598,server_index=1,rotation_x=32.8,rotation_y=0,angle_distance=13,lock_rotation=0,is_recover=1,pos_x=1,pos_y=1,}

}

