-- Y-运营活动-烟花抽奖.xls
local item_table={
[1]={item_id=26569,num=1,is_bind=1},
[2]={item_id=37425,num=1,is_bind=1},
[3]={item_id=37229,num=1,is_bind=1},
[4]={item_id=50439,num=1,is_bind=1},
[5]={item_id=50440,num=1,is_bind=1},
[6]={item_id=56317,num=1,is_bind=1},
[7]={item_id=45017,num=1,is_bind=1},
[8]={item_id=28826,num=1,is_bind=1},
[9]={item_id=48088,num=1,is_bind=1},
[10]={item_id=48071,num=1,is_bind=1},
[11]={item_id=26191,num=1,is_bind=1},
[12]={item_id=28666,num=1,is_bind=1},
[13]={item_id=26345,num=1,is_bind=1},
[14]={item_id=26344,num=1,is_bind=1},
[15]={item_id=26369,num=1,is_bind=1},
[16]={item_id=26367,num=1,is_bind=1},
[17]={item_id=26368,num=1,is_bind=1},
[18]={item_id=37422,num=1,is_bind=1},
[19]={item_id=38739,num=1,is_bind=1},
[20]={item_id=50432,num=1,is_bind=1},
[21]={item_id=26377,num=1,is_bind=1},
[22]={item_id=37227,num=1,is_bind=1},
[23]={item_id=38656,num=1,is_bind=1},
[24]={item_id=50435,num=1,is_bind=1},
[25]={item_id=50436,num=1,is_bind=1},
[26]={item_id=26376,num=1,is_bind=1},
[27]={item_id=22072,num=1,is_bind=1},
[28]={item_id=26378,num=1,is_bind=1},
[29]={item_id=26200,num=1,is_bind=1},
[30]={item_id=26203,num=1,is_bind=1},
[31]={item_id=30422,num=1,is_bind=1},
[32]={item_id=38667,num=1,is_bind=1},
[33]={item_id=37218,num=1,is_bind=1},
[34]={item_id=38668,num=1,is_bind=1},
[35]={item_id=37426,num=1,is_bind=1},
[36]={item_id=37034,num=1,is_bind=1},
[37]={item_id=50429,num=1,is_bind=1},
[38]={item_id=50433,num=1,is_bind=1},
[39]={item_id=50430,num=1,is_bind=1},
[40]={item_id=27909,num=1,is_bind=1},
[41]={item_id=27907,num=1,is_bind=1},
[42]={item_id=26380,num=1,is_bind=1},
[43]={item_id=26501,num=1,is_bind=1},
[44]={item_id=26516,num=1,is_bind=1},
[45]={item_id=26354,num=1,is_bind=1},
[46]={item_id=26351,num=1,is_bind=1},
[47]={item_id=26379,num=1,is_bind=1},
[48]={item_id=26371,num=1,is_bind=1},
[49]={item_id=26502,num=1,is_bind=1},
[50]={item_id=26415,num=2,is_bind=1},
[51]={item_id=37052,num=1,is_bind=1},
[52]={item_id=37219,num=1,is_bind=1},
[53]={item_id=26355,num=1,is_bind=1},
[54]={item_id=26356,num=1,is_bind=1},
[55]={item_id=37236,num=1,is_bind=1},
[56]={item_id=37427,num=1,is_bind=1},
[57]={item_id=38127,num=1,is_bind=1},
[58]={item_id=26360,num=1,is_bind=1},
[59]={item_id=26358,num=1,is_bind=1},
[60]={item_id=26359,num=1,is_bind=1},
[61]={item_id=38126,num=1,is_bind=1},
[62]={item_id=37220,num=1,is_bind=1},
[63]={item_id=48521,num=1,is_bind=1},
[64]={item_id=26363,num=1,is_bind=1},
[65]={item_id=44180,num=1,is_bind=1},
[66]={item_id=26361,num=1,is_bind=1},
[67]={item_id=26362,num=1,is_bind=1},
[68]={item_id=27908,num=1,is_bind=1},
[69]={item_id=26519,num=1,is_bind=1},
[70]={item_id=37051,num=1,is_bind=1},
[71]={item_id=37428,num=1,is_bind=1},
[72]={item_id=37515,num=1,is_bind=1},
[73]={item_id=38654,num=1,is_bind=1},
[74]={item_id=38743,num=1,is_bind=1},
[75]={item_id=26375,num=1,is_bind=1},
[76]={item_id=26373,num=1,is_bind=1},
[77]={item_id=26374,num=1,is_bind=1},
[78]={item_id=26517,num=1,is_bind=1},
[79]={item_id=26503,num=1,is_bind=1},
[80]={item_id=26193,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_day={
{},
{start_day=8,end_day=9999,grade=2,}
},

open_day_meta_table_map={
},
mode={
{},
{mode=2,times=50,cost_item_num=50,}
},

mode_meta_table_map={
},
reward_pool={
{activity_day=1,item=item_table[1],},
{seq=1,item=item_table[2],},
{seq=2,item=item_table[3],},
{seq=3,item=item_table[4],},
{seq=4,item=item_table[5],},
{seq=5,item=item_table[6],},
{seq=6,item=item_table[7],},
{seq=7,item=item_table[8],},
{seq=8,item=item_table[9],},
{seq=9,item=item_table[10],},
{activity_day=1,},
{seq=11,item=item_table[11],},
{seq=12,item=item_table[12],},
{activity_day=1,item=item_table[13],},
{activity_day=1,item=item_table[14],},
{seq=15,item=item_table[15],},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{seq=21,item=item_table[16],},
{activity_day=1,item=item_table[17],},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=2,},
{seq=1,item=item_table[18],},
{seq=2,item=item_table[19],},
{seq=3,item=item_table[20],},
{activity_day=2,seq=4,},
{activity_day=2,seq=5,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,seq=10,is_rare=1,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,item=item_table[21],},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=3,},
{activity_day=3,item=item_table[22],},
{activity_day=3,item=item_table[23],},
{seq=3,item=item_table[24],},
{activity_day=3,item=item_table[25],},
{activity_day=3,item=item_table[20],},
{activity_day=3,seq=6,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,item=item_table[26],},
{activity_day=3,seq=15,},
{activity_day=3,seq=16,},
{activity_day=3,seq=17,},
{activity_day=3,item=item_table[27],},
{activity_day=3,seq=19,},
{activity_day=3,seq=20,},
{activity_day=3,seq=21,},
{activity_day=3,item=item_table[28],},
{activity_day=3,item=item_table[29],},
{activity_day=3,item=item_table[30],},
{activity_day=3,seq=25,},
{activity_day=3,item=item_table[31],},
{activity_day=3,seq=27,},
{activity_day=3,seq=28,},
{item=item_table[32],is_rare=1,},
{seq=1,item=item_table[33],},
{seq=2,item=item_table[34],},
{seq=3,item=item_table[18],},
{seq=4,item=item_table[19],},
{seq=5,},
{seq=6,item=item_table[35],},
{seq=7,item=item_table[22],},
{seq=8,item=item_table[23],},
{seq=9,item=item_table[36],},
{seq=10,item=item_table[2],},
{seq=11,item=item_table[3],},
{seq=12,item=item_table[37],},
{seq=13,item=item_table[38],},
{seq=14,item=item_table[39],},
{seq=15,item=item_table[6],},
{seq=16,item=item_table[8],},
{seq=17,item=item_table[9],},
{seq=18,item=item_table[10],},
{seq=19,},
{seq=20,item=item_table[11],},
{seq=21,item=item_table[12],},
{seq=22,item=item_table[40],},
{seq=23,item=item_table[41],},
{seq=24,item=item_table[42],},
{seq=25,item=item_table[43],},
{seq=26,item=item_table[44],},
{seq=27,item=item_table[27],},
{seq=28,item=item_table[45],},
{seq=29,item=item_table[46],},
{seq=30,item=item_table[47],},
{seq=31,item=item_table[28],},
{seq=32,item=item_table[29],},
{seq=33,item=item_table[30],},
{seq=34,item=item_table[48],},
{seq=35,item=item_table[31],},
{seq=36,item=item_table[49],},
{seq=37,item=item_table[50],},
{grade=2,item=item_table[51],},
{activity_day=1,seq=1,},
{activity_day=1,item=item_table[52],},
{activity_day=1,},
{activity_day=1,item=item_table[15],},
{activity_day=1,},
{activity_day=1,item=item_table[53],},
{activity_day=1,item=item_table[54],},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{seq=12,item=item_table[43],},
{grade=2,item=item_table[44],},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=2,item=item_table[55],},
{grade=2,item=item_table[56],},
{grade=2,item=item_table[57],},
{activity_day=2,},
{activity_day=2,item=item_table[58],},
{activity_day=2,},
{activity_day=2,item=item_table[59],},
{activity_day=2,item=item_table[60],},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,seq=24,},
{activity_day=2,seq=25,},
{activity_day=3,item=item_table[61],},
{activity_day=3,seq=1,},
{activity_day=3,item=item_table[62],},
{activity_day=3,item=item_table[63],},
{activity_day=3,item=item_table[64],},
{activity_day=3,item=item_table[65],},
{activity_day=3,item=item_table[66],},
{activity_day=3,item=item_table[67],},
{activity_day=3,seq=8,},
{activity_day=3,item=item_table[11],},
{activity_day=3,seq=10,},
{seq=11,item=item_table[68],},
{activity_day=3,seq=12,},
{activity_day=3,seq=13,},
{grade=2,item=item_table[27],},
{grade=2,item=item_table[45],},
{activity_day=3,seq=16,},
{activity_day=3,seq=17,},
{activity_day=3,seq=18,},
{activity_day=3,item=item_table[29],},
{activity_day=3,seq=20,},
{activity_day=3,item=item_table[48],},
{activity_day=3,item=item_table[31],},
{activity_day=3,item=item_table[69],},
{activity_day=3,},
{activity_day=3,},
{grade=2,item=item_table[70],},
{seq=1,item=item_table[71],},
{seq=2,item=item_table[72],},
{seq=3,item=item_table[61],},
{seq=4,item=item_table[73],},
{seq=5,item=item_table[62],},
{seq=6,item=item_table[55],},
{seq=7,item=item_table[56],},
{seq=8,item=item_table[57],},
{seq=9,item=item_table[51],},
{seq=10,item=item_table[74],},
{seq=11,item=item_table[52],},
{seq=12,item=item_table[63],},
{seq=13,item=item_table[75],},
{seq=14,item=item_table[65],},
{seq=15,item=item_table[76],},
{seq=16,item=item_table[77],},
{grade=2,seq=17,is_rare=1,},
{seq=18,item=item_table[11],},
{grade=2,seq=19,},
{seq=20,item=item_table[68],},
{grade=2,seq=21,},
{grade=2,item=item_table[78],},
{grade=2,item=item_table[27],},
{grade=2,item=item_table[45],},
{grade=2,item=item_table[46],},
{grade=2,item=item_table[21],},
{grade=2,item=item_table[13],},
{grade=2,item=item_table[29],},
{grade=2,item=item_table[30],},
{grade=2,seq=30,},
{grade=2,seq=31,},
{grade=2,item=item_table[69],},
{grade=2,item=item_table[79],},
{grade=2,item=item_table[50],}
},

reward_pool_meta_table_map={
[75]=113,	-- depth:1
[98]=88,	-- depth:1
[97]=88,	-- depth:1
[96]=88,	-- depth:1
[76]=114,	-- depth:1
[77]=106,	-- depth:1
[95]=88,	-- depth:1
[94]=88,	-- depth:1
[78]=116,	-- depth:1
[79]=117,	-- depth:1
[80]=118,	-- depth:1
[81]=110,	-- depth:1
[82]=111,	-- depth:1
[83]=112,	-- depth:1
[84]=122,	-- depth:1
[85]=114,	-- depth:1
[86]=124,	-- depth:1
[93]=88,	-- depth:1
[87]=125,	-- depth:1
[89]=88,	-- depth:1
[90]=88,	-- depth:1
[92]=88,	-- depth:1
[91]=88,	-- depth:1
[109]=88,	-- depth:1
[103]=88,	-- depth:1
[236]=120,	-- depth:1
[235]=123,	-- depth:1
[234]=122,	-- depth:1
[233]=117,	-- depth:1
[232]=116,	-- depth:1
[231]=115,	-- depth:1
[230]=114,	-- depth:1
[229]=113,	-- depth:1
[228]=112,	-- depth:1
[227]=111,	-- depth:1
[226]=110,	-- depth:1
[225]=124,	-- depth:1
[224]=225,	-- depth:2
[223]=110,	-- depth:1
[220]=223,	-- depth:2
[219]=223,	-- depth:2
[204]=88,	-- depth:1
[237]=121,	-- depth:1
[74]=112,	-- depth:1
[108]=88,	-- depth:1
[105]=88,	-- depth:1
[104]=88,	-- depth:1
[99]=88,	-- depth:1
[73]=102,	-- depth:1
[1]=88,	-- depth:1
[238]=122,	-- depth:1
[43]=101,	-- depth:1
[11]=40,	-- depth:1
[14]=101,	-- depth:1
[35]=102,	-- depth:1
[34]=101,	-- depth:1
[33]=34,	-- depth:2
[15]=102,	-- depth:1
[30]=1,	-- depth:2
[29]=87,	-- depth:2
[28]=86,	-- depth:2
[27]=85,	-- depth:2
[26]=84,	-- depth:2
[25]=83,	-- depth:2
[23]=110,	-- depth:1
[22]=23,	-- depth:2
[21]=79,	-- depth:2
[20]=78,	-- depth:2
[16]=23,	-- depth:2
[17]=75,	-- depth:2
[18]=76,	-- depth:2
[19]=77,	-- depth:2
[72]=43,	-- depth:2
[44]=73,	-- depth:2
[45]=74,	-- depth:2
[24]=82,	-- depth:2
[47]=18,	-- depth:3
[46]=17,	-- depth:3
[4]=23,	-- depth:2
[5]=23,	-- depth:2
[6]=23,	-- depth:2
[64]=35,	-- depth:2
[63]=34,	-- depth:2
[69]=11,	-- depth:2
[59]=30,	-- depth:3
[58]=29,	-- depth:3
[57]=28,	-- depth:3
[56]=27,	-- depth:3
[62]=73,	-- depth:2
[54]=25,	-- depth:3
[53]=24,	-- depth:3
[52]=81,	-- depth:2
[51]=80,	-- depth:2
[55]=26,	-- depth:3
[48]=19,	-- depth:3
[49]=20,	-- depth:3
[50]=21,	-- depth:3
[177]=238,	-- depth:2
[195]=230,	-- depth:2
[196]=231,	-- depth:2
[198]=233,	-- depth:2
[194]=229,	-- depth:2
[199]=225,	-- depth:2
[200]=226,	-- depth:2
[201]=227,	-- depth:2
[197]=223,	-- depth:2
[193]=74,	-- depth:2
[190]=225,	-- depth:2
[2]=11,	-- depth:2
[191]=226,	-- depth:2
[189]=191,	-- depth:3
[173]=199,	-- depth:3
[174]=200,	-- depth:3
[175]=201,	-- depth:3
[176]=237,	-- depth:2
[188]=223,	-- depth:2
[186]=221,	-- depth:1
[178]=204,	-- depth:2
[192]=73,	-- depth:2
[202]=176,	-- depth:3
[212]=221,	-- depth:1
[205]=221,	-- depth:1
[9]=11,	-- depth:2
[10]=11,	-- depth:2
[12]=11,	-- depth:2
[13]=11,	-- depth:2
[222]=221,	-- depth:1
[8]=11,	-- depth:2
[7]=11,	-- depth:2
[172]=198,	-- depth:3
[3]=11,	-- depth:2
[218]=221,	-- depth:1
[217]=221,	-- depth:1
[216]=221,	-- depth:1
[215]=221,	-- depth:1
[214]=221,	-- depth:1
[213]=221,	-- depth:1
[211]=221,	-- depth:1
[210]=221,	-- depth:1
[209]=221,	-- depth:1
[208]=221,	-- depth:1
[207]=221,	-- depth:1
[206]=221,	-- depth:1
[203]=177,	-- depth:3
[171]=197,	-- depth:3
[71]=13,	-- depth:3
[169]=195,	-- depth:3
[144]=196,	-- depth:3
[170]=144,	-- depth:4
[142]=194,	-- depth:3
[141]=193,	-- depth:3
[140]=192,	-- depth:3
[139]=14,	-- depth:2
[138]=139,	-- depth:3
[137]=189,	-- depth:4
[136]=188,	-- depth:3
[134]=186,	-- depth:2
[126]=1,	-- depth:2
[31]=40,	-- depth:1
[145]=171,	-- depth:4
[32]=40,	-- depth:1
[37]=8,	-- depth:3
[38]=9,	-- depth:3
[39]=10,	-- depth:3
[41]=12,	-- depth:3
[42]=71,	-- depth:4
[60]=31,	-- depth:2
[61]=32,	-- depth:2
[65]=103,	-- depth:2
[66]=37,	-- depth:4
[67]=38,	-- depth:4
[68]=39,	-- depth:4
[70]=41,	-- depth:4
[36]=7,	-- depth:3
[146]=172,	-- depth:4
[143]=169,	-- depth:4
[148]=174,	-- depth:4
[147]=173,	-- depth:4
[160]=134,	-- depth:3
[162]=136,	-- depth:4
[164]=190,	-- depth:3
[165]=191,	-- depth:3
[166]=140,	-- depth:4
[163]=137,	-- depth:5
[168]=142,	-- depth:4
[152]=204,	-- depth:2
[151]=203,	-- depth:4
[150]=202,	-- depth:4
[149]=175,	-- depth:4
[167]=141,	-- depth:4
[184]=210,	-- depth:2
[179]=208,	-- depth:2
[180]=206,	-- depth:2
[181]=207,	-- depth:2
[182]=208,	-- depth:2
[183]=209,	-- depth:2
[185]=211,	-- depth:2
[128]=206,	-- depth:2
[187]=213,	-- depth:2
[127]=214,	-- depth:2
[129]=181,	-- depth:3
[130]=208,	-- depth:2
[131]=183,	-- depth:3
[132]=210,	-- depth:2
[133]=211,	-- depth:2
[135]=187,	-- depth:3
[159]=211,	-- depth:2
[158]=210,	-- depth:2
[157]=131,	-- depth:4
[155]=129,	-- depth:4
[154]=32,	-- depth:2
[153]=31,	-- depth:2
[161]=135,	-- depth:4
[156]=208,	-- depth:2
},
item_random_desc={
{activity_day=1,item_name="君子贵人丹",item_id=26569,is_rare=1,},
{activity_day=1,number=1,},
{activity_day=1,number=2,},
{number=3,item_name="敦煌佛莲·碎片",item_id=50439,random_count=0.02,},
{number=4,item_name="一路向西翼·碎片",item_id=50440,},
{activity_day=1,number=5,item_name="龙力金丹",item_id=56317,},
{number=6,item_name="史诗合鸣石",item_id=45017,},
{activity_day=1,number=7,},
{activity_day=1,number=8,},
{number=9,item_name="完美合鸣石自选包",item_id=48071,},
{activity_day=1,},
{activity_day=1,number=11,},
{activity_day=1,number=12,},
{number=13,item_name="高级灵骑神石",item_id=26345,random_count=0.08,},
{number=14,item_name="灵骑神石",item_id=26344,random_count=0.32,},
{activity_day=1,number=15,item_name="朱鱼石",item_id=26369,},
{activity_day=1,number=16,random_count=0.08,},
{activity_day=1,random_count=0.08,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{number=21,item_name="碧鱼石",item_id=26367,},
{activity_day=1,item_name="黛鱼石",item_id=26368,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,number=25,item_name="中等饰之阵",item_id=26371,},
{activity_day=1,},
{activity_day=1,number=27,item_name="3级攻击玉魄",item_id=26502,},
{activity_day=1,},
{activity_day=2,},
{activity_day=2,number=1,},
{activity_day=2,number=2,},
{activity_day=2,item_name="朱雀焰·碎片",item_id=50432,},
{activity_day=2,number=4,item_name="花仙使者·碎片",},
{activity_day=2,number=5,item_name="火灵使者·碎片",},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,number=10,is_rare=1,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,random_count=0.34,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,number=17,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,number=23,},
{activity_day=2,number=24,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,random_count=0.17,},
{activity_day=3,},
{activity_day=3,number=1,},
{activity_day=3,number=2,},
{number=3,item_name="冰龙晶芯·碎片",item_id=50435,},
{activity_day=3,item_name="圣火梵天翼·碎片",item_id=50436,},
{activity_day=3,item_name="柠檬甜·碎片",item_id=50432,},
{activity_day=3,number=6,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,number=13,item_id=26377,},
{activity_day=3,number=14,item_id=26376,},
{activity_day=3,number=15,},
{activity_day=3,number=16,},
{activity_day=3,},
{activity_day=3,number=18,},
{activity_day=3,number=19,},
{activity_day=3,number=20,},
{activity_day=3,number=21,},
{activity_day=3,number=22,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,random_count=0.02,},
{activity_day=3,number=26,},
{activity_day=3,random_count=0.02,},
{activity_day=3,number=28,},
{item_name="齐天大圣",item_id=38667,is_rare=1,},
{number=1,item_name="神赐天翼",item_id=37218,is_rare=1,},
{number=2,item_name="裁判所",item_id=38668,is_rare=1,},
{number=3,item_name="朱雀焰",item_id=37422,is_rare=1,},
{number=4,item_name="花仙使者",item_id=38739,is_rare=1,},
{number=5,},
{number=6,item_name="冰龙晶芯",item_id=37426,is_rare=1,},
{number=7,item_name="圣火梵天翼",item_id=37227,is_rare=1,},
{number=8,item_name="柠檬甜",item_id=38656,is_rare=1,},
{number=9,item_name="小飞猪",item_id=37034,is_rare=1,},
{number=10,item_name="敦煌佛莲",item_id=37425,is_rare=1,},
{number=11,item_name="一路向西翼",item_id=37229,is_rare=1,},
{number=12,item_name="神赐天翼·碎片",item_id=50429,random_count=0.02,},
{number=13,item_name="齐天大圣·碎片",item_id=50433,random_count=0.02,},
{number=14,item_name="裁判所·碎片",item_id=50430,random_count=0.02,},
{number=15,item_name="龙力金丹",item_id=56317,is_rare=1,},
{number=16,item_name="合鸣石礼盒",item_id=28826,is_rare=1,},
{number=17,item_name="圣品神纹自选包",item_id=48088,is_rare=1,},
{number=18,item_name="完美合鸣石自选包",item_id=48071,},
{number=19,},
{number=20,item_name="灵缘石·粉",item_id=26191,is_rare=1,},
{number=21,item_name="武魂石·卓",item_id=28666,is_rare=1,},
{number=22,item_name="高级灵剑灵瓶",item_id=27909,random_count=0.08,},
{number=23,item_name="灵剑灵瓶",item_id=27907,random_count=0.32,},
{number=24,item_name="摄月石",item_id=26380,},
{number=25,item_name="2级攻击玉魄",item_id=26501,random_count=0.03,},
{number=26,item_name="2级生命玉魄",item_id=26516,random_count=0.03,},
{number=27,item_name="橙色图鉴经验",item_id=22072,},
{number=28,item_name="高等武之阵",item_id=26354,},
{number=29,item_name="高等器之阵",item_id=26351,},
{number=30,item_name="摇月石",item_id=26379,random_count=0.02,},
{number=31,item_name="辐月石",item_id=26378,random_count=0.02,},
{number=32,item_name="强化水晶",item_id=26200,random_count=0.08,},
{number=33,item_name="强化水晶·饰品",item_id=26203,random_count=0.08,},
{number=34,item_name="中等饰之阵",item_id=26371,random_count=0.02,},
{number=35,item_name="蓝色云篆精华",item_id=30422,random_count=0.02,},
{number=36,item_name="3级攻击玉魄",item_id=26502,random_count=0.02,},
{number=37,item_name="洗炼石",item_id=26415,random_count=0.16,},
{activity_day=1,item_name="新年醒狮",item_id=37052,},
{activity_day=1,number=1,},
{activity_day=1,number=2,},
{activity_day=1,},
{grade=2,number=4,is_rare=1,},
{activity_day=1,number=5,},
{activity_day=1,item_name="灵梦石",item_id=26355,},
{number=7,item_name="幻梦石",item_id=26356,random_count=0.02,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{grade=2,number=12,random_count=0.02,},
{grade=2,number=13,random_count=0.02,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,random_count=0.16,},
{activity_day=1,random_count=0.16,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=1,},
{activity_day=2,item_name="精灵火羽",item_id=37236,},
{activity_day=2,number=1,},
{activity_day=2,number=2,},
{activity_day=2,number=3,},
{activity_day=2,item_name="暗轮石",item_id=26360,},
{activity_day=2,},
{activity_day=2,item_name="光轮石",item_id=26358,},
{activity_day=2,item_name="星轮石",item_id=26359,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=3,item_name="绝天青龙",item_id=38126,},
{activity_day=3,number=1,},
{activity_day=3,number=2,},
{activity_day=3,},
{activity_day=3,item_name="地泽石",item_id=26363,},
{activity_day=3,},
{grade=2,item_name="尘泽石",item_id=26361,},
{activity_day=3,item_name="天泽石",item_id=26362,},
{activity_day=3,number=8,},
{activity_day=3,number=9,},
{activity_day=3,number=10,},
{activity_day=3,number=11,},
{activity_day=3,number=12,},
{activity_day=3,number=13,},
{activity_day=3,number=14,},
{activity_day=3,number=15,},
{activity_day=3,number=16,},
{grade=2,number=17,},
{activity_day=3,number=18,},
{activity_day=3,number=19,},
{activity_day=3,number=20,},
{activity_day=3,number=21,},
{grade=2,number=22,},
{activity_day=3,number=23,},
{activity_day=3,number=24,},
{grade=2,number=25,},
{grade=2,item_name="洪荒龙王",item_id=37051,is_rare=1,},
{number=1,item_name="莺莺燕羽",item_id=37428,},
{number=2,item_name="寒梅雪琼",item_id=37515,},
{number=3,item_name="绝天青龙",item_id=38126,},
{number=4,item_name="小月儿",item_id=38654,},
{number=5,item_name="炎魔魂翼",item_id=37220,},
{number=6,item_name="精灵火羽",item_id=37236,},
{number=7,item_name="元宵花灯",item_id=37427,},
{number=8,item_name="蔽日墨龙",item_id=38127,},
{number=9,item_name="新年醒狮",item_id=37052,},
{number=10,item_name="新春福娃",item_id=38743,},
{number=11,item_name="白羽仙翼",item_id=37219,},
{number=12,item_name="金石自选礼包",item_id=48521,},
{number=13,item_name="云灵石",item_id=26375,},
{number=14,item_name="属性丹随机礼包",item_id=44180,},
{grade=2,number=15,item_name="风灵石",item_id=26373,},
{number=16,item_name="雨灵石",item_id=26374,},
{grade=2,number=17,is_rare=1,},
{grade=2,number=18,},
{grade=2,number=19,item_name="极品聚灵瓶",},
{number=20,item_name="超品聚灵瓶",item_id=27908,},
{grade=2,number=21,random_count=0.16,},
{grade=2,item_name="3级生命玉魄",item_id=26517,random_count=0.16,},
{grade=2,number=23,random_count=0.03,},
{grade=2,number=24,random_count=0.08,},
{grade=2,number=25,random_count=0.08,},
{grade=2,number=26,item_id=26377,},
{number=27,item_name="高级灵骑神石",item_id=26345,},
{grade=2,number=28,},
{grade=2,number=29,},
{grade=2,number=30,random_count=0.08,},
{grade=2,number=31,},
{grade=2,number=32,item_name="5级生命玉魄",item_id=26519,},
{grade=2,item_name="4级攻击玉魄",item_id=26503,random_count=0.06,},
{grade=2,number=34,}
},

item_random_desc_meta_table_map={
[11]=40,	-- depth:1
[69]=11,	-- depth:2
[93]=88,	-- depth:1
[79]=117,	-- depth:1
[78]=116,	-- depth:1
[77]=115,	-- depth:1
[74]=112,	-- depth:1
[186]=221,	-- depth:1
[160]=186,	-- depth:2
[134]=160,	-- depth:3
[59]=1,	-- depth:1
[19]=77,	-- depth:2
[20]=78,	-- depth:2
[49]=20,	-- depth:3
[48]=19,	-- depth:3
[30]=59,	-- depth:2
[50]=79,	-- depth:2
[45]=74,	-- depth:2
[21]=50,	-- depth:3
[205]=204,	-- depth:1
[4]=6,	-- depth:1
[206]=204,	-- depth:1
[207]=204,	-- depth:1
[5]=4,	-- depth:2
[178]=204,	-- depth:1
[126]=204,	-- depth:1
[53]=120,	-- depth:1
[7]=1,	-- depth:1
[8]=104,	-- depth:1
[9]=105,	-- depth:1
[10]=1,	-- depth:1
[12]=108,	-- depth:1
[13]=109,	-- depth:1
[14]=6,	-- depth:1
[237]=121,	-- depth:1
[15]=6,	-- depth:1
[17]=113,	-- depth:1
[152]=204,	-- depth:1
[208]=204,	-- depth:1
[210]=204,	-- depth:1
[211]=204,	-- depth:1
[236]=119,	-- depth:1
[235]=123,	-- depth:1
[234]=122,	-- depth:1
[233]=121,	-- depth:1
[232]=120,	-- depth:1
[231]=232,	-- depth:2
[230]=110,	-- depth:1
[229]=117,	-- depth:1
[228]=116,	-- depth:1
[227]=115,	-- depth:1
[226]=110,	-- depth:1
[225]=124,	-- depth:1
[224]=228,	-- depth:2
[223]=110,	-- depth:1
[222]=108,	-- depth:1
[2]=98,	-- depth:1
[220]=235,	-- depth:2
[3]=99,	-- depth:1
[218]=204,	-- depth:1
[217]=204,	-- depth:1
[216]=204,	-- depth:1
[215]=204,	-- depth:1
[214]=204,	-- depth:1
[213]=204,	-- depth:1
[212]=204,	-- depth:1
[209]=204,	-- depth:1
[54]=121,	-- depth:1
[238]=125,	-- depth:1
[22]=4,	-- depth:2
[70]=12,	-- depth:2
[71]=13,	-- depth:2
[72]=110,	-- depth:1
[37]=8,	-- depth:2
[73]=111,	-- depth:1
[75]=113,	-- depth:1
[47]=114,	-- depth:1
[80]=118,	-- depth:1
[38]=9,	-- depth:2
[81]=119,	-- depth:1
[51]=80,	-- depth:2
[82]=53,	-- depth:2
[84]=26,	-- depth:1
[85]=123,	-- depth:1
[86]=28,	-- depth:1
[87]=125,	-- depth:1
[39]=10,	-- depth:2
[46]=75,	-- depth:2
[44]=73,	-- depth:2
[43]=72,	-- depth:2
[18]=47,	-- depth:2
[41]=70,	-- depth:3
[83]=54,	-- depth:2
[68]=39,	-- depth:3
[76]=47,	-- depth:2
[66]=37,	-- depth:3
[23]=81,	-- depth:2
[24]=82,	-- depth:3
[25]=83,	-- depth:3
[67]=38,	-- depth:3
[29]=87,	-- depth:2
[31]=91,	-- depth:1
[32]=92,	-- depth:1
[33]=4,	-- depth:2
[34]=101,	-- depth:1
[35]=102,	-- depth:1
[55]=84,	-- depth:2
[27]=85,	-- depth:2
[57]=86,	-- depth:2
[65]=103,	-- depth:1
[64]=35,	-- depth:2
[56]=27,	-- depth:3
[63]=34,	-- depth:2
[62]=86,	-- depth:2
[42]=71,	-- depth:3
[61]=96,	-- depth:1
[58]=87,	-- depth:2
[52]=81,	-- depth:2
[60]=95,	-- depth:1
[36]=7,	-- depth:2
[188]=223,	-- depth:2
[189]=224,	-- depth:3
[190]=225,	-- depth:2
[191]=226,	-- depth:2
[192]=227,	-- depth:2
[193]=228,	-- depth:2
[195]=72,	-- depth:2
[196]=231,	-- depth:3
[197]=232,	-- depth:2
[199]=234,	-- depth:2
[200]=85,	-- depth:2
[201]=236,	-- depth:2
[202]=237,	-- depth:2
[203]=87,	-- depth:2
[187]=222,	-- depth:2
[194]=229,	-- depth:2
[198]=233,	-- depth:2
[177]=203,	-- depth:3
[155]=216,	-- depth:2
[153]=211,	-- depth:2
[151]=177,	-- depth:4
[150]=202,	-- depth:3
[149]=201,	-- depth:3
[148]=200,	-- depth:3
[147]=199,	-- depth:3
[145]=197,	-- depth:3
[144]=196,	-- depth:4
[143]=195,	-- depth:3
[142]=194,	-- depth:3
[141]=193,	-- depth:3
[140]=192,	-- depth:3
[139]=18,	-- depth:3
[138]=17,	-- depth:2
[137]=189,	-- depth:4
[136]=188,	-- depth:3
[135]=187,	-- depth:3
[132]=210,	-- depth:2
[131]=218,	-- depth:2
[130]=16,	-- depth:1
[129]=155,	-- depth:3
[128]=215,	-- depth:2
[127]=214,	-- depth:2
[154]=212,	-- depth:2
[184]=65,	-- depth:2
[146]=198,	-- depth:3
[157]=131,	-- depth:3
[183]=157,	-- depth:4
[182]=130,	-- depth:2
[181]=129,	-- depth:4
[180]=209,	-- depth:2
[179]=208,	-- depth:2
[176]=150,	-- depth:4
[175]=149,	-- depth:4
[174]=148,	-- depth:4
[173]=147,	-- depth:4
[172]=198,	-- depth:3
[156]=130,	-- depth:2
[171]=197,	-- depth:3
[169]=143,	-- depth:4
[168]=142,	-- depth:4
[167]=141,	-- depth:4
[166]=140,	-- depth:4
[165]=191,	-- depth:3
[164]=190,	-- depth:3
[163]=137,	-- depth:5
[162]=136,	-- depth:4
[161]=135,	-- depth:4
[158]=210,	-- depth:2
[170]=144,	-- depth:5
[133]=130,	-- depth:2
[159]=133,	-- depth:3
[185]=133,	-- depth:3
},
baodi={
{activity_day=1,times=700,},
{times=1400,itemlist={[0]=item_table[2]},},
{times=2450,itemlist={[0]=item_table[3]},},
{activity_day=1,itemlist={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},},
{activity_day=2,},
{times=1400,itemlist={[0]=item_table[18]},},
{times=2450,itemlist={[0]=item_table[19]},},
{activity_day=2,itemlist={[0]=item_table[1],[1]=item_table[18],[2]=item_table[19]},},
{activity_day=3,},
{times=1400,itemlist={[0]=item_table[22]},},
{times=2450,itemlist={[0]=item_table[23]},},
{activity_day=3,itemlist={[0]=item_table[1],[1]=item_table[22],[2]=item_table[23]},},
{times=1000,},
{times=2000,itemlist={[0]=item_table[33]},},
{times=3500,itemlist={[0]=item_table[34]},},
{times=4500,itemlist={[0]=item_table[18]},},
{times=5500,itemlist={[0]=item_table[19]},},
{times=6500,itemlist={[0]=item_table[32]},},
{times=7500,itemlist={[0]=item_table[35]},},
{times=8500,itemlist={[0]=item_table[22]},},
{times=9500,itemlist={[0]=item_table[23]},},
{times=10500,itemlist={[0]=item_table[36]},},
{times=11500,itemlist={[0]=item_table[2]},},
{times=12500,itemlist={[0]=item_table[3]},},
{times=700,itemlist={[0]=item_table[51]},},
{activity_day=1,times=1400,},
{times=2450,itemlist={[0]=item_table[52]},},
{grade=2,itemlist={[0]=item_table[51],[1]=item_table[74],[2]=item_table[52]},},
{times=800,itemlist={[0]=item_table[55]},},
{times=1600,itemlist={[0]=item_table[56]},},
{times=2800,itemlist={[0]=item_table[57]},},
{grade=2,activity_day=2,times=3600,itemlist={[0]=item_table[55],[1]=item_table[56],[2]=item_table[57]},},
{times=900,itemlist={[0]=item_table[61]},},
{times=1800,itemlist={[0]=item_table[73]},},
{grade=2,itemlist={[0]=item_table[62]},},
{times=4050,itemlist={[0]=item_table[61],[1]=item_table[73],[2]=item_table[62]},},
{grade=2,itemlist={[0]=item_table[70]},},
{grade=2,itemlist={[0]=item_table[71]},},
{grade=2,itemlist={[0]=item_table[72]},},
{grade=2,itemlist={[0]=item_table[61]},},
{grade=2,itemlist={[0]=item_table[73]},},
{grade=2,itemlist={[0]=item_table[62]},},
{grade=2,itemlist={[0]=item_table[55]},},
{grade=2,itemlist={[0]=item_table[56]},},
{grade=2,itemlist={[0]=item_table[57]},},
{grade=2,itemlist={[0]=item_table[51]},},
{grade=2,itemlist={[0]=item_table[74]},},
{grade=2,itemlist={[0]=item_table[52]},}
},

baodi_meta_table_map={
[13]=18,	-- depth:1
[9]=1,	-- depth:1
[5]=9,	-- depth:2
[37]=13,	-- depth:2
[38]=14,	-- depth:1
[39]=15,	-- depth:1
[40]=16,	-- depth:1
[41]=17,	-- depth:1
[43]=19,	-- depth:1
[35]=12,	-- depth:1
[44]=20,	-- depth:1
[45]=21,	-- depth:1
[46]=22,	-- depth:1
[42]=18,	-- depth:1
[11]=12,	-- depth:1
[48]=24,	-- depth:1
[10]=12,	-- depth:1
[7]=8,	-- depth:1
[6]=8,	-- depth:1
[3]=4,	-- depth:1
[28]=4,	-- depth:1
[2]=4,	-- depth:1
[47]=23,	-- depth:1
[25]=28,	-- depth:2
[26]=47,	-- depth:2
[27]=28,	-- depth:2
[36]=35,	-- depth:2
[29]=32,	-- depth:1
[34]=35,	-- depth:2
[33]=35,	-- depth:2
[30]=32,	-- depth:1
[31]=32,	-- depth:1
},
other_default_table={cost_item_id=26171,cost_gold=40,itemlist_display=0,},

open_day_default_table={start_day=1,end_day=7,grade=1,},

mode_default_table={mode=1,times=1,cost_item_num=1,},

reward_pool_default_table={grade=1,activity_day=4,seq=0,item=item_table[80],is_rare=0,},

item_random_desc_default_table={grade=1,activity_day=4,number=0,item_name="灵缘石·金",item_id=26193,random_count=0.01,is_rare=0,},

baodi_default_table={grade=1,activity_day=4,times=3150,itemlist={[0]=item_table[1]},}

}

