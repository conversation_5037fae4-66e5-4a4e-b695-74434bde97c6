-- K-跨服仙侣2v2.xls
local item_table={
[1]={item_id=27741,num=3,is_bind=1},
[2]={item_id=26451,num=1,is_bind=1},
[3]={item_id=26148,num=3,is_bind=1},
[4]={item_id=26121,num=2,is_bind=1},
[5]={item_id=27741,num=2,is_bind=1},
[6]={item_id=26438,num=1,is_bind=1},
[7]={item_id=26148,num=2,is_bind=1},
[8]={item_id=26121,num=1,is_bind=1},
[9]={item_id=57758,num=8,is_bind=1},
[10]={item_id=26569,num=1,is_bind=1},
[11]={item_id=26459,num=1,is_bind=1},
[12]={item_id=26127,num=1,is_bind=1},
[13]={item_id=26123,num=1,is_bind=1},
[14]={item_id=27764,num=2,is_bind=1},
[15]={item_id=57758,num=6,is_bind=1},
[16]={item_id=26455,num=1,is_bind=1},
[17]={item_id=26126,num=2,is_bind=1},
[18]={item_id=27764,num=1,is_bind=1},
[19]={item_id=57758,num=4,is_bind=1},
[20]={item_id=26450,num=1,is_bind=1},
[21]={item_id=26126,num=1,is_bind=1},
[22]={item_id=26122,num=2,is_bind=1},
[23]={item_id=27763,num=1,is_bind=1},
[24]={item_id=57758,num=3,is_bind=1},
[25]={item_id=26444,num=1,is_bind=1},
[26]={item_id=26125,num=2,is_bind=1},
[27]={item_id=26122,num=1,is_bind=1},
[28]={item_id=57758,num=2,is_bind=1},
[29]={item_id=26125,num=1,is_bind=1},
[30]={item_id=26437,num=1,is_bind=1},
[31]={item_id=57760,num=40,is_bind=1},
[32]={item_id=57759,num=8,is_bind=1},
[33]={item_id=26554,num=1,is_bind=1},
[34]={item_id=26570,num=1,is_bind=1},
[35]={item_id=26127,num=4,is_bind=1},
[36]={item_id=26123,num=4,is_bind=1},
[37]={item_id=57760,num=30,is_bind=1},
[38]={item_id=57759,num=6,is_bind=1},
[39]={item_id=26556,num=1,is_bind=1},
[40]={item_id=57760,num=20,is_bind=1},
[41]={item_id=57759,num=5,is_bind=1},
[42]={item_id=26127,num=3,is_bind=1},
[43]={item_id=57760,num=10,is_bind=1},
[44]={item_id=57759,num=4,is_bind=1},
[45]={item_id=36418,num=200,is_bind=1},
[46]={item_id=36418,num=300,is_bind=1},
[47]={item_id=57807,num=1,is_bind=1},
[48]={item_id=57809,num=1,is_bind=1},
[49]={item_id=26463,num=1,is_bind=1},
[50]={item_id=57810,num=1,is_bind=1},
[51]={item_id=26464,num=1,is_bind=1},
[52]={item_id=57811,num=1,is_bind=1},
[53]={item_id=26123,num=3,is_bind=1},
[54]={item_id=57761,num=3,is_bind=1},
[55]={item_id=26452,num=1,is_bind=1},
[56]={item_id=26148,num=5,is_bind=1},
[57]={item_id=57761,num=4,is_bind=1},
[58]={item_id=26453,num=1,is_bind=1},
[59]={item_id=26148,num=10,is_bind=1},
[60]={item_id=57761,num=6,is_bind=1},
[61]={item_id=26454,num=1,is_bind=1},
[62]={item_id=57761,num=10,is_bind=1},
[63]={item_id=26148,num=15,is_bind=1},
[64]={item_id=57806,num=1,is_bind=1},
[65]={item_id=22733,num=15,is_bind=1},
[66]={item_id=57760,num=50,is_bind=1},
[67]={item_id=57759,num=10,is_bind=1},
[68]={item_id=26553,num=1,is_bind=1},
[69]={item_id=26127,num=5,is_bind=1},
[70]={item_id=26123,num=5,is_bind=1},
[71]={item_id=26445,num=1,is_bind=1},
[72]={item_id=27741,num=1,is_bind=1},
[73]={item_id=26148,num=1,is_bind=1},
[74]={item_id=57758,num=10,is_bind=1},
[75]={item_id=26127,num=2,is_bind=1},
[76]={item_id=26123,num=2,is_bind=1},
[77]={item_id=27764,num=3,is_bind=1},
[78]={item_id=36418,num=400,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
born_point={
[0]={seq=0,},
[1]={seq=1,born_pos1="32,107",born_pos2="36,101",}
},

born_point_meta_table_map={
},
standy={
{}
},

standy_meta_table_map={
},
match_reward={
[0]={seq=0,},
[1]={seq=1,win_score=40,lose_score=20,win_reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4]},lose_reward_item={[0]=item_table[5],[1]=item_table[6],[2]=item_table[7],[3]=item_table[8]},}
},

match_reward_meta_table_map={
},
match_rank_reward={
{},
{min_rank=2,max_rank=2,reward_item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11],[3]=item_table[12],[4]=item_table[13],[5]=item_table[14]},},
{min_rank=3,max_rank=4,reward_item={[0]=item_table[15],[1]=item_table[16],[2]=item_table[17],[3]=item_table[13],[4]=item_table[18]},},
{min_rank=5,max_rank=8,reward_item={[0]=item_table[19],[1]=item_table[20],[2]=item_table[21],[3]=item_table[22],[4]=item_table[23]},},
{min_rank=9,max_rank=16,reward_item={[0]=item_table[24],[1]=item_table[25],[2]=item_table[26],[3]=item_table[27],[4]=item_table[23]},},
{min_rank=17,max_rank=25,reward_item={[0]=item_table[28],[1]=item_table[25],[2]=item_table[29],[3]=item_table[8],[4]=item_table[23]},},
{min_rank=26,max_rank=50,reward_item={[0]=item_table[28],[1]=item_table[30],[2]=item_table[29],[3]=item_table[8],[4]=item_table[23]},}
},

match_rank_reward_meta_table_map={
},
knockout_rank_reward={
{},
{min_rank=2,max_rank=2,reward_item={[0]=item_table[31],[1]=item_table[32],[2]=item_table[33],[3]=item_table[34],[4]=item_table[35],[5]=item_table[36]},},
{min_rank=3,max_rank=4,reward_item={[0]=item_table[37],[1]=item_table[38],[2]=item_table[39],[3]=item_table[10],[4]=item_table[35],[5]=item_table[36]},},
{min_rank=5,max_rank=8,reward_item={[0]=item_table[40],[1]=item_table[41],[2]=item_table[39],[3]=item_table[10],[4]=item_table[42]},},
{min_rank=9,max_rank=16,reward_item={[0]=item_table[43],[1]=item_table[44],[2]=item_table[39],[3]=item_table[10],[4]=item_table[42]},}
},

knockout_rank_reward_meta_table_map={
},
knockout={
{}
},

knockout_meta_table_map={
},
knockout_guess={
[0]={round=0,guess_need_gold=100,reward_list={[0]=item_table[45]},},
[1]={round=1,guess_need_gold=200,reward_list={[0]=item_table[46]},},
[2]={round=2,},
[3]={round=3,}
},

knockout_guess_meta_table_map={
},
gather_rewards={
[0]={grade=0,},
[1]={grade=1,flowers_count_begin=10,flowers_count_end=50,reward_item={[0]=item_table[20],[1]=item_table[47],[2]=item_table[29],[3]=item_table[8]},},
[2]={grade=2,flowers_count_begin=51,flowers_count_end=100,reward_item={[0]=item_table[16],[1]=item_table[48],[2]=item_table[26],[3]=item_table[27]},},
[3]={grade=3,flowers_count_begin=101,flowers_count_end=666,reward_item={[0]=item_table[11],[1]=item_table[48],[2]=item_table[21],[3]=item_table[27]},},
[4]={grade=4,flowers_count_begin=667,flowers_count_end=12888,reward_item={[0]=item_table[49],[1]=item_table[50],[2]=item_table[17],[3]=item_table[13]},},
[5]={grade=5,flowers_count_begin=12889,flowers_count_end=999999,reward_item={[0]=item_table[51],[1]=item_table[34],[2]=item_table[52],[3]=item_table[12],[4]=item_table[53]},}
},

gather_rewards_meta_table_map={
},
match_count={
[0]={seq=0,},
[1]={seq=1,match_count=6,reward_list={[0]=item_table[54],[1]=item_table[47],[2]=item_table[55],[3]=item_table[56],[4]=item_table[27]},},
[2]={seq=2,match_count=12,reward_list={[0]=item_table[57],[1]=item_table[48],[2]=item_table[58],[3]=item_table[59],[4]=item_table[27]},},
[3]={seq=3,match_count=20,reward_list={[0]=item_table[60],[1]=item_table[48],[2]=item_table[61],[3]=item_table[59],[4]=item_table[22]},},
[4]={seq=4,match_count=30,reward_list={[0]=item_table[62],[1]=item_table[48],[2]=item_table[20],[3]=item_table[63],[4]=item_table[13]},}
},

match_count_meta_table_map={
},
notifications={
{},
{}
},

notifications_meta_table_map={
},
knockout_monsters={
{},
{seq=1,}
},

knockout_monsters_meta_table_map={
},
knockout_guard_monsters={
{}
},

knockout_guard_monsters_meta_table_map={
},
send_flower_buff={
[0]={seq=0,},
[1]={seq=1,flowers_count_begin=8,flowers_count_end=12,buff_id=273,buff_title="百年好合",buff_txt="生命 + 10%",},
[2]={seq=2,flowers_count_begin=13,flowers_count_end=17,buff_id=274,buff_title="海枯石烂",buff_txt="攻击 + 10%",},
[3]={seq=3,flowers_count_begin=18,flowers_count_end=29,buff_id=275,buff_title="一心一意",buff_txt="防御 + 12%",}
},

send_flower_buff_meta_table_map={
},
other_default_table={open_day=1,open_level=220,scene_id=3300,fight_time_s=60,backto_standby_scene_time_s=15,monster_appear_time_s=60,max_wait_client_ready_s=50,every_day_max_match_count=20,rank_open_wday=6,flower_item_id=26182,consume_for_flower=20,reward_from_send_flower={[0]=item_table[64],[1]=item_table[29],[2]=item_table[7],[3]=item_table[8]},flower_reward_count=10,guess_reward_list={[0]=item_table[65]},get_score_limit=5,fszz_reward_list={[0]=item_table[66],[1]=item_table[67],[2]=item_table[51],[3]=item_table[68],[4]=item_table[34],[5]=item_table[52],[6]=item_table[69],[7]=item_table[70]},fszz_show_title=2090,gather_id=2063,gather_refresh_pos="140,82",gather_count=3,gather_count=3,pos_1_x=112,pos_1_y=35,wait_start_sec=5,send_flowers_count=1,},

born_point_default_table={seq=0,born_pos1="215,106",born_pos2="220,112",},

standy_default_table={ready_scene_id=3301,},

match_reward_default_table={seq=0,win_score=30,lose_score=10,win_reward_item={[0]=item_table[5],[1]=item_table[71],[2]=item_table[7]},lose_reward_item={[0]=item_table[72],[1]=item_table[71],[2]=item_table[73]},},

match_rank_reward_default_table={min_rank=1,max_rank=1,reward_item={[0]=item_table[74],[1]=item_table[34],[2]=item_table[49],[3]=item_table[75],[4]=item_table[76],[5]=item_table[77]},},

knockout_rank_reward_default_table={min_rank=1,max_rank=1,reward_item={[0]=item_table[66],[1]=item_table[67],[2]=item_table[68],[3]=item_table[34],[4]=item_table[69],[5]=item_table[70]},},

knockout_default_table={scene_id=3300,fight_time=60,knockout_ready_time=60,is_invoke_robot=1,},

knockout_guess_default_table={round=0,guess_need_gold=300,reward_list={[0]=item_table[78]},},

gather_rewards_default_table={grade=0,flowers_count_begin=0,flowers_count_end=9,reward_item={[0]=item_table[25],[1]=item_table[64],[2]=item_table[29],[3]=item_table[8]},},

match_count_default_table={seq=0,match_count=3,reward_list={[0]=item_table[64],[1]=item_table[2],[2]=item_table[56],[3]=item_table[27]},},

notifications_default_table={},

knockout_monsters_default_table={seq=0,},

knockout_guard_monsters_default_table={monster_id=5507,pos_x=140,pos_y=93,},

send_flower_buff_default_table={seq=0,flowers_count_begin=0,flowers_count_end=7,buff_id=272,buff_title="情比金坚",buff_txt="生命 + 5%",}

}

