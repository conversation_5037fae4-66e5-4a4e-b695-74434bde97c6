-- Y-运营活动-灵丹宝炉.xls
local item_table={
[1]={item_id=22009,num=1,is_bind=1},
[2]={item_id=48071,num=1,is_bind=1},
[3]={item_id=44183,num=1,is_bind=1},
[4]={item_id=48500,num=1,is_bind=1},
[5]={item_id=48501,num=1,is_bind=1},
[6]={item_id=26501,num=1,is_bind=1},
[7]={item_id=26516,num=1,is_bind=1},
[8]={item_id=26345,num=1,is_bind=1},
[9]={item_id=26344,num=1,is_bind=1},
[10]={item_id=26369,num=1,is_bind=1},
[11]={item_id=26367,num=1,is_bind=1},
[12]={item_id=26368,num=1,is_bind=1},
[13]={item_id=26200,num=2,is_bind=1},
[14]={item_id=26203,num=2,is_bind=1},
[15]={item_id=44182,num=1,is_bind=1},
[16]={item_id=47584,num=1,is_bind=1},
[17]={item_id=26502,num=1,is_bind=1},
[18]={item_id=26415,num=2,is_bind=1},
[19]={item_id=48505,num=1,is_bind=1},
[20]={item_id=48506,num=1,is_bind=1},
[21]={item_id=48507,num=1,is_bind=1},
[22]={item_id=48508,num=1,is_bind=1},
[23]={item_id=48509,num=1,is_bind=1},
[24]={item_id=56317,num=1,is_bind=1},
[25]={item_id=56316,num=1,is_bind=1},
[26]={item_id=26191,num=1,is_bind=1},
[27]={item_id=27909,num=1,is_bind=1},
[28]={item_id=26353,num=1,is_bind=1},
[29]={item_id=26350,num=1,is_bind=1},
[30]={item_id=26377,num=1,is_bind=1},
[31]={item_id=26200,num=3,is_bind=1},
[32]={item_id=26203,num=3,is_bind=1},
[33]={item_id=26371,num=1,is_bind=1},
[34]={item_id=26415,num=4,is_bind=1},
[35]={item_id=27807,num=1,is_bind=1},
[36]={item_id=46394,num=1,is_bind=1},
[37]={item_id=37961,num=1,is_bind=1},
[38]={item_id=37675,num=1,is_bind=1},
[39]={item_id=37859,num=1,is_bind=1},
[40]={item_id=38427,num=1,is_bind=1},
[41]={item_id=48505,num=5,is_bind=1},
[42]={item_id=48506,num=5,is_bind=1},
[43]={item_id=37034,num=1,is_bind=1},
[44]={item_id=57357,num=1,is_bind=1},
[45]={item_id=26517,num=1,is_bind=1},
[46]={item_id=38457,num=1,is_bind=1},
[47]={item_id=47585,num=5,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_day={
{},
{start_day=7,end_day=9999,grade=2,}
},

open_day_meta_table_map={
},
mode={
{},
{mode=2,times=10,cost_item_num=10,add_lucky=10,},
{mode=3,times=50,cost_item_num=50,add_lucky=50,}
},

mode_meta_table_map={
},
reward_pool={
{item=item_table[1],show_item=0,},
{seq=1,item=item_table[2],},
{seq=2,item=item_table[3],},
{seq=3,item=item_table[4],},
{seq=4,item=item_table[5],},
{seq=5,item=item_table[6],},
{seq=6,item=item_table[7],},
{seq=7,item=item_table[8],},
{seq=8,item=item_table[9],},
{seq=9,item=item_table[10],},
{seq=10,item=item_table[11],},
{seq=11,item=item_table[12],},
{seq=12,item=item_table[13],},
{seq=13,item=item_table[14],},
{seq=14,item=item_table[15],},
{seq=15,item=item_table[16],},
{seq=16,show_item=0,},
{seq=17,item=item_table[17],},
{seq=18,item=item_table[18],},
{seq=19,item=item_table[19],},
{seq=20,item=item_table[20],},
{seq=21,item=item_table[21],},
{seq=22,item=item_table[22],},
{seq=23,item=item_table[23],},
{seq=24,item=item_table[24],is_rare=1,},
{seq=25,item=item_table[25],},
{grade=2,},
{grade=2,},
{seq=2,item=item_table[26],},
{grade=2,},
{grade=2,},
{grade=2,item=item_table[17],},
{seq=6,},
{grade=2,item=item_table[27],},
{seq=8,item=item_table[28],},
{seq=9,item=item_table[29],},
{grade=2,item=item_table[30],},
{grade=2,item=item_table[8],},
{grade=2,item=item_table[31],},
{grade=2,item=item_table[32],},
{grade=2,item=item_table[33],},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,item=item_table[34],},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

reward_pool_meta_table_map={
[9]=17,	-- depth:1
[49]=23,	-- depth:1
[48]=22,	-- depth:1
[47]=21,	-- depth:1
[46]=20,	-- depth:1
[44]=18,	-- depth:1
[43]=17,	-- depth:1
[38]=12,	-- depth:1
[37]=11,	-- depth:1
[33]=43,	-- depth:2
[10]=17,	-- depth:1
[30]=4,	-- depth:1
[28]=2,	-- depth:1
[27]=1,	-- depth:1
[52]=26,	-- depth:1
[24]=25,	-- depth:1
[3]=25,	-- depth:1
[8]=17,	-- depth:1
[5]=17,	-- depth:1
[13]=17,	-- depth:1
[14]=17,	-- depth:1
[15]=17,	-- depth:1
[19]=17,	-- depth:1
[6]=17,	-- depth:1
[16]=25,	-- depth:1
[7]=17,	-- depth:1
[50]=24,	-- depth:2
[45]=19,	-- depth:2
[40]=14,	-- depth:2
[41]=15,	-- depth:2
[51]=25,	-- depth:1
[39]=13,	-- depth:2
[36]=27,	-- depth:2
[35]=27,	-- depth:2
[34]=8,	-- depth:2
[32]=6,	-- depth:2
[29]=51,	-- depth:2
[42]=16,	-- depth:2
[31]=5,	-- depth:2
},
baodi={
{},
{grade=2,}
},

baodi_meta_table_map={
},
convert={
{item=item_table[35],stuff_num_1=40,},
{seq=1,item=item_table[36],stuff_num_1=20,},
{seq=2,stuff_num_1=10,},
{seq=3,item=item_table[37],stuff_num_1=6,},
{seq=4,item=item_table[38],},
{seq=5,item=item_table[39],},
{seq=6,item=item_table[23],stuff_num_1=3,},
{seq=7,item=item_table[22],stuff_num_1=2,},
{seq=8,time_limit=2,item=item_table[21],stuff_num_1=1,},
{grade=2,item=item_table[40],},
{grade=2,seq=1,},
{seq=2,item=item_table[37],},
{grade=2,item=item_table[38],},
{grade=2,seq=4,},
{grade=2,item=item_table[23],},
{grade=2,seq=6,},
{grade=2,seq=7,item=item_table[21],stuff_num_1=1,}
},

convert_meta_table_map={
[15]=6,	-- depth:1
[10]=1,	-- depth:1
[11]=3,	-- depth:1
[14]=6,	-- depth:1
[12]=11,	-- depth:2
[16]=8,	-- depth:1
[13]=4,	-- depth:1
},
times_reward={
{},
{seq=1,need_draw_times=100,item=item_table[41],},
{seq=2,need_draw_times=150,},
{seq=3,need_draw_times=200,item=item_table[42],},
{seq=4,need_draw_times=2000,item=item_table[43],},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,seq=4,need_draw_times=1500,item=item_table[44],}
},

times_reward_meta_table_map={
[8]=3,	-- depth:1
[7]=2,	-- depth:1
[9]=4,	-- depth:1
},
item_random_desc={
{item_id=38053,random_count=0.01,},
{number=1,item_id=38734,random_count=0.02,},
{number=2,random_count=0.03,},
{number=3,item_id=26459,random_count=0.13,},
{number=4,item_id=26460,random_count=0.15,},
{number=5,item_id=26461,random_count=0.31,},
{number=6,item_id=26569,},
{number=7,item_id=48441,},
{number=8,item_id=44185,random_count=0.52,},
{number=9,item_id=44184,},
{number=10,item_id=44180,random_count=5.15,},
{number=11,item_id=26193,},
{number=12,item_id=26191,},
{number=13,item_id=26377,random_count=3.09,},
{number=14,item_id=26203,random_count=3.11,},
{number=15,item_id=26380,},
{number=16,item_id=26379,},
{number=17,item_id=26376,},
{number=18,item_id=26378,random_count=11.22,},
{number=19,item_id=26443,random_count=6.19,},
{number=20,item_id=26441,},
{number=21,item_id=26442,},
{number=22,item_id=26448,},
{number=23,item_id=26438,},
{number=24,item_id=26439,random_count=10.31,},
{number=25,item_id=26440,},
{number=26,item_id=26445,},
{number=27,item_id=26446,},
{number=28,item_id=26447,},
{grade=1,item_id=37056,},
{grade=1,item_id=37796,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,}
},

item_random_desc_meta_table_map={
[56]=27,	-- depth:1
[55]=26,	-- depth:1
[53]=24,	-- depth:1
[52]=23,	-- depth:1
[51]=22,	-- depth:1
[37]=8,	-- depth:1
[32]=3,	-- depth:1
[30]=1,	-- depth:1
[57]=28,	-- depth:1
[58]=29,	-- depth:1
[13]=25,	-- depth:1
[12]=25,	-- depth:1
[17]=11,	-- depth:1
[16]=11,	-- depth:1
[18]=11,	-- depth:1
[21]=11,	-- depth:1
[7]=11,	-- depth:1
[10]=11,	-- depth:1
[49]=20,	-- depth:1
[48]=19,	-- depth:1
[54]=25,	-- depth:1
[47]=18,	-- depth:2
[50]=21,	-- depth:2
[46]=17,	-- depth:2
[41]=12,	-- depth:2
[44]=15,	-- depth:1
[43]=14,	-- depth:1
[42]=13,	-- depth:2
[40]=11,	-- depth:1
[39]=10,	-- depth:2
[38]=9,	-- depth:1
[36]=7,	-- depth:2
[35]=6,	-- depth:1
[34]=5,	-- depth:1
[31]=2,	-- depth:1
[45]=16,	-- depth:2
[33]=4,	-- depth:1
},
other_default_table={cost_item_id=47585,cost_gold=40,},

open_day_default_table={start_day=1,end_day=6,grade=1,},

mode_default_table={mode=1,times=1,cost_item_num=1,add_lucky=1,},

reward_pool_default_table={grade=1,seq=0,item=item_table[45],is_rare=0,show_item=1,},

baodi_default_table={grade=1,need_lucky=360,item=item_table[16],},

convert_default_table={grade=1,seq=0,time_limit=1,item=item_table[46],stuff_id_1=47584,stuff_num_1=4,stuff_id_2=0,stuff_num_2=0,stuff_id_3=0,stuff_num_3=0,},

times_reward_default_table={grade=1,seq=0,need_draw_times=50,item=item_table[47],},

item_random_desc_default_table={grade=0,number=0,item_id=26570,random_count=1.03,}

}

