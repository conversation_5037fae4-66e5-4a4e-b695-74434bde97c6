-- F-伏龙翔天.xls
local item_table={
[1]={item_id=37033,num=1,is_bind=1},
[2]={item_id=37923,num=1,is_bind=1},
[3]={item_id=27838,num=1,is_bind=1},
[4]={item_id=27836,num=1,is_bind=1},
[5]={item_id=27837,num=1,is_bind=1},
[6]={item_id=27833,num=1,is_bind=1},
[7]={item_id=27834,num=1,is_bind=1},
[8]={item_id=27835,num=1,is_bind=1},
[9]={item_id=27613,num=1,is_bind=1},
[10]={item_id=27612,num=1,is_bind=1},
[11]={item_id=27611,num=1,is_bind=1},
[12]={item_id=28033,num=1,is_bind=1},
[13]={item_id=26369,num=1,is_bind=1},
[14]={item_id=26357,num=1,is_bind=1},
[15]={item_id=26360,num=1,is_bind=1},
[16]={item_id=26363,num=1,is_bind=1},
[17]={item_id=26122,num=1,is_bind=1},
[18]={item_id=26121,num=1,is_bind=1},
[19]={item_id=26194,num=1,is_bind=1},
[20]={item_id=26193,num=1,is_bind=1},
[21]={item_id=26191,num=1,is_bind=1},
[22]={item_id=26200,num=1,is_bind=1},
[23]={item_id=26203,num=1,is_bind=1},
[24]={item_id=26415,num=1,is_bind=1},
[25]={item_id=26099,num=1,is_bind=1},
[26]={item_id=26505,num=1,is_bind=1},
[27]={item_id=26504,num=1,is_bind=1},
[28]={item_id=26520,num=1,is_bind=1},
[29]={item_id=26519,num=1,is_bind=1},
[30]={item_id=26123,num=1,is_bind=1},
[31]={item_id=39105,num=1,is_bind=1},
[32]={item_id=29624,num=1,is_bind=1},
[33]={item_id=29625,num=1,is_bind=1},
[34]={item_id=30423,num=1,is_bind=1},
[35]={item_id=30424,num=1,is_bind=1},
[36]={item_id=30425,num=1,is_bind=1},
[37]={item_id=29615,num=1,is_bind=1},
[38]={item_id=48096,num=1,is_bind=1},
[39]={item_id=48099,num=1,is_bind=1},
[40]={item_id=48101,num=1,is_bind=1},
[41]={item_id=48102,num=1,is_bind=1},
[42]={item_id=29614,num=1,is_bind=1},
[43]={item_id=30003,num=1,is_bind=1},
[44]={item_id=30013,num=1,is_bind=1},
[45]={item_id=26944,num=1,is_bind=1},
[46]={item_id=48120,num=1,is_bind=1},
[47]={item_id=48117,num=1,is_bind=1},
[48]={item_id=48073,num=1,is_bind=1},
[49]={item_id=44073,num=1,is_bind=1},
[50]={item_id=26161,num=1,is_bind=1},
[51]={item_id=27656,num=1,is_bind=1},
[52]={item_id=27657,num=1,is_bind=1},
[53]={item_id=27658,num=1,is_bind=1},
[54]={item_id=27659,num=1,is_bind=1},
[55]={item_id=26561,num=1,is_bind=1},
[56]={item_id=26568,num=1,is_bind=1},
[57]={item_id=46400,num=1,is_bind=1},
[58]={item_id=46392,num=1,is_bind=1},
[59]={item_id=23103,num=1,is_bind=1},
[60]={item_id=37633,num=1,is_bind=1},
[61]={item_id=26502,num=10,is_bind=1},
[62]={item_id=26517,num=10,is_bind=1},
[63]={item_id=22618,num=2,is_bind=1},
[64]={item_id=23104,num=1,is_bind=1},
[65]={item_id=37291,num=1,is_bind=1},
[66]={item_id=26193,num=2,is_bind=1},
[67]={item_id=26504,num=10,is_bind=1},
[68]={item_id=26519,num=10,is_bind=1},
[69]={item_id=22618,num=4,is_bind=1},
[70]={item_id=38765,num=1,is_bind=1},
[71]={item_id=23105,num=1,is_bind=1},
[72]={item_id=37477,num=1,is_bind=1},
[73]={item_id=26505,num=10,is_bind=1},
[74]={item_id=26520,num=10,is_bind=1},
[75]={item_id=22618,num=5,is_bind=1},
[76]={item_id=23102,num=1,is_bind=1},
[77]={item_id=39464,num=1,is_bind=1},
[78]={item_id=26558,num=1,is_bind=1},
[79]={item_id=23100,num=1,is_bind=1},
[80]={item_id=44183,num=1,is_bind=1},
[81]={item_id=26501,num=10,is_bind=1},
[82]={item_id=26516,num=10,is_bind=1},
[83]={item_id=22618,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_day={
{}
},

open_day_meta_table_map={
},
grade={
{},
{mode=2,times=10,need_cost=1000,add_lucky=10,},
{mode=3,times=50,need_cost=5000,add_lucky=50,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=4,},
{seq=4,},
{seq=4,}
},

grade_meta_table_map={
[5]=2,	-- depth:1
[6]=3,	-- depth:1
[8]=5,	-- depth:2
[9]=6,	-- depth:2
[11]=8,	-- depth:3
[12]=9,	-- depth:3
},
reward_pool={
{seq=1,gift_name="诸神伏龙枪",},
{item=item_table[1],},
{item=item_table[2],},
{item=item_table[3],},
{item=item_table[4],},
{item=item_table[5],},
{item=item_table[6],},
{item=item_table[7],},
{item=item_table[8],},
{item=item_table[9],},
{item=item_table[10],},
{item=item_table[11],},
{item=item_table[12],},
{item=item_table[13],},
{item=item_table[14],},
{item=item_table[15],},
{item=item_table[16],},
{seq=1,gift_name="诸神伏龙枪",},
{item=item_table[17],},
{item=item_table[18],},
{seq=2,gift_name="装备伏龙枪",},
{item=item_table[1],},
{item=item_table[2],},
{item=item_table[19],},
{item=item_table[20],},
{item=item_table[21],},
{item=item_table[22],},
{item=item_table[23],},
{item=item_table[24],},
{item=item_table[25],},
{item=item_table[26],},
{item=item_table[27],},
{item=item_table[28],},
{item=item_table[29],},
{item=item_table[13],},
{item=item_table[14],},
{item=item_table[15],},
{item=item_table[16],},
{item=item_table[30],},
{item=item_table[17],},
{seq=2,gift_name="装备伏龙枪",},
{is_rare=1,},
{item=item_table[1],},
{item=item_table[2],},
{item=item_table[31],},
{item=item_table[32],},
{item=item_table[33],},
{item=item_table[34],},
{item=item_table[35],},
{item=item_table[36],},
{item=item_table[37],},
{item=item_table[38],},
{item=item_table[13],},
{item=item_table[14],},
{item=item_table[15],},
{item=item_table[16],},
{item=item_table[30],},
{item=item_table[17],},
{item=item_table[18],},
{item=item_table[39],},
{item=item_table[40],},
{item=item_table[41],},
{item=item_table[42],},
{item=item_table[43],},
{item=item_table[44],},
{seq=4,gift_name="魂环伏龙枪",},
{item=item_table[1],},
{seq=4,gift_name="魂环伏龙枪",},
{item=item_table[45],},
{item=item_table[46],},
{item=item_table[47],},
{item=item_table[48],},
{item=item_table[49],},
{item=item_table[50],},
{item=item_table[51],},
{item=item_table[52],},
{item=item_table[53],},
{item=item_table[54],},
{item=item_table[13],},
{item=item_table[14],},
{item=item_table[15],},
{item=item_table[16],},
{item=item_table[30],},
{item=item_table[17],},
{item=item_table[18],}
},

reward_pool_meta_table_map={
[68]=44,	-- depth:1
[83]=68,	-- depth:2
[82]=83,	-- depth:3
[81]=82,	-- depth:4
[80]=81,	-- depth:5
[79]=80,	-- depth:6
[78]=79,	-- depth:7
[77]=78,	-- depth:8
[76]=77,	-- depth:9
[66]=42,	-- depth:1
[74]=76,	-- depth:10
[73]=74,	-- depth:11
[72]=73,	-- depth:12
[71]=72,	-- depth:13
[70]=71,	-- depth:14
[69]=70,	-- depth:15
[67]=69,	-- depth:16
[75]=67,	-- depth:17
[1]=66,	-- depth:2
[41]=59,	-- depth:1
[18]=83,	-- depth:3
[17]=18,	-- depth:4
[16]=17,	-- depth:5
[15]=16,	-- depth:6
[14]=15,	-- depth:7
[13]=14,	-- depth:8
[12]=13,	-- depth:9
[11]=12,	-- depth:10
[10]=11,	-- depth:11
[9]=10,	-- depth:12
[8]=9,	-- depth:13
[7]=8,	-- depth:14
[6]=7,	-- depth:15
[5]=6,	-- depth:16
[4]=5,	-- depth:17
[3]=4,	-- depth:18
[2]=3,	-- depth:19
[19]=2,	-- depth:20
[20]=19,	-- depth:21
[21]=66,	-- depth:2
[22]=41,	-- depth:2
[40]=22,	-- depth:3
[39]=40,	-- depth:4
[38]=39,	-- depth:5
[37]=38,	-- depth:6
[36]=37,	-- depth:7
[35]=36,	-- depth:8
[34]=35,	-- depth:9
[33]=34,	-- depth:10
[84]=75,	-- depth:18
[32]=33,	-- depth:11
[30]=32,	-- depth:12
[29]=30,	-- depth:13
[28]=29,	-- depth:14
[27]=28,	-- depth:15
[26]=27,	-- depth:16
[25]=26,	-- depth:17
[24]=25,	-- depth:18
[23]=24,	-- depth:19
[31]=23,	-- depth:20
[85]=84,	-- depth:19
},
rate_weight={
{}
},

rate_weight_meta_table_map={
},
rate_show={
{}
},

rate_show_meta_table_map={
},
lucky_grade_reward={
{},
{seq=1,need_lucky=2400,item=item_table[55],},
{seq=2,need_lucky=3600,item=item_table[56],},
{seq=3,need_lucky=6000,item=item_table[57],},
{seq=4,need_lucky=12000,show_item=91421,item=item_table[58],}
},

lucky_grade_reward_meta_table_map={
},
rmb_buy={
{},
{seq=1,rmb_seq=1,rmb_price=128,reward_item={[0]=item_table[59],[1]=item_table[60],[2]=item_table[21],[3]=item_table[61],[4]=item_table[62],[5]=item_table[63]},show_reward_item=item_table[60],},
{seq=2,rmb_seq=2,rmb_price=648,reward_item={[0]=item_table[64],[1]=item_table[65],[2]=item_table[66],[3]=item_table[67],[4]=item_table[68],[5]=item_table[69]},show_reward_item=item_table[65],},
{seq=3,rmb_seq=3,rmb_price=1000,reward_item={[0]=item_table[64],[1]=item_table[70],[2]=item_table[66],[3]=item_table[67],[4]=item_table[68],[5]=item_table[69]},show_reward_item=item_table[70],},
{seq=4,rmb_seq=4,rmb_price=2000,reward_item={[0]=item_table[71],[1]=item_table[72],[2]=item_table[19],[3]=item_table[73],[4]=item_table[74],[5]=item_table[75]},show_reward_item=item_table[72],},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=7,},
{grade=7,reward_item={[0]=item_table[76],[1]=item_table[60],[2]=item_table[21],[3]=item_table[61],[4]=item_table[62],[5]=item_table[63]},},
{grade=7,reward_item={[0]=item_table[59],[1]=item_table[65],[2]=item_table[66],[3]=item_table[67],[4]=item_table[68],[5]=item_table[69]},},
{grade=7,},
{grade=7,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,}
},

rmb_buy_meta_table_map={
[45]=5,	-- depth:1
[32]=2,	-- depth:1
[33]=3,	-- depth:1
[34]=4,	-- depth:1
[35]=45,	-- depth:2
[38]=33,	-- depth:2
[47]=32,	-- depth:2
[39]=34,	-- depth:2
[40]=35,	-- depth:3
[42]=47,	-- depth:3
[43]=38,	-- depth:3
[44]=39,	-- depth:3
[48]=43,	-- depth:4
[37]=42,	-- depth:4
[30]=40,	-- depth:4
[25]=30,	-- depth:5
[28]=48,	-- depth:5
[7]=2,	-- depth:1
[8]=3,	-- depth:1
[9]=44,	-- depth:4
[10]=25,	-- depth:6
[12]=7,	-- depth:2
[13]=8,	-- depth:2
[14]=9,	-- depth:5
[15]=10,	-- depth:7
[17]=12,	-- depth:3
[18]=13,	-- depth:3
[19]=14,	-- depth:6
[20]=15,	-- depth:8
[22]=37,	-- depth:5
[23]=28,	-- depth:6
[24]=19,	-- depth:7
[49]=24,	-- depth:8
[27]=22,	-- depth:6
[29]=49,	-- depth:9
[50]=20,	-- depth:9
},
open_act_day={
{},
{start_day=5,end_day=99999,}
},

open_act_day_meta_table_map={
},
lucky_double={

},

lucky_double_meta_table_map={
},
other_default_table={daily_reward_item={[0]=item_table[77]},daily_reward_lucky=0,chongzhi_add_lucky=0,chongzhi_add_lucky_max=30000,open_day_1_auto_open=0,is_show_lucky_double=0,},

open_day_default_table={start_day=1,end_day=999,grade=1,rate_count=999,},

grade_default_table={grade=1,seq=1,mode=1,times=1,need_cost=100,add_lucky=1,},

reward_pool_default_table={grade=1,seq=3,item=item_table[57],is_rare=0,gift_name="万魂伏龙枪",},

rate_weight_default_table={min_lucky=12000,max_lucky=12800000,weight=10000000,},

rate_show_default_table={grade=1,model_show_type=1,model_bundle_name="model/tianshen/10112_prefab",model_asset_name=10112,model_show_itemid=46400,show_icon=0,show_fight_icon=0,},

lucky_grade_reward_default_table={grade=1,seq=0,need_lucky=1200,add_lucky=0,show_item=91420,item=item_table[78],},

rmb_buy_default_table={grade=1,seq=0,rmb_type=162,rmb_seq=0,rmb_price=68,reward_item={[0]=item_table[79],[1]=item_table[80],[2]=item_table[21],[3]=item_table[81],[4]=item_table[82],[5]=item_table[83]},show_reward_item=item_table[79],},

open_act_day_default_table={start_day=1,end_day=1,},

lucky_double_default_table={}

}

