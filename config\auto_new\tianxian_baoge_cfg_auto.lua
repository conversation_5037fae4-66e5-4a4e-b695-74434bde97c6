-- T-天仙宝阁.xls
local item_table={
[1]={item_id=37977,num=1,is_bind=1},
[2]={item_id=38074,num=1,is_bind=1},
[3]={item_id=37689,num=1,is_bind=1},
[4]={item_id=37872,num=1,is_bind=1},
[5]={item_id=38177,num=1,is_bind=1},
[6]={item_id=38418,num=1,is_bind=1},
[7]={item_id=37290,num=1,is_bind=1},
[8]={item_id=37475,num=1,is_bind=1},
[9]={item_id=37658,num=1,is_bind=1},
[10]={item_id=37793,num=1,is_bind=1},
[11]={item_id=38154,num=1,is_bind=1},
[12]={item_id=38671,num=1,is_bind=1},
[13]={item_id=37055,num=1,is_bind=1},
[14]={item_id=37212,num=1,is_bind=1},
[15]={item_id=37419,num=1,is_bind=1},
[16]={item_id=37624,num=1,is_bind=1},
[17]={item_id=37724,num=1,is_bind=1},
[18]={item_id=38135,num=1,is_bind=1},
[19]={item_id=37066,num=1,is_bind=1},
[20]={item_id=37248,num=1,is_bind=1},
[21]={item_id=37467,num=1,is_bind=1},
[22]={item_id=37650,num=1,is_bind=1},
[23]={item_id=37753,num=1,is_bind=1},
[24]={item_id=38139,num=1,is_bind=1},
[25]={item_id=37065,num=1,is_bind=1},
[26]={item_id=37246,num=1,is_bind=1},
[27]={item_id=37524,num=1,is_bind=1},
[28]={item_id=37649,num=1,is_bind=1},
[29]={item_id=37751,num=1,is_bind=1},
[30]={item_id=38152,num=1,is_bind=1},
[31]={item_id=37097,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
convert={
{round=1,consume="27740,1000|27741,1000",},
{round=1,reward_item={[0]=item_table[1]},},
{round=1,reward_item={[0]=item_table[2]},},
{round=1,reward_item={[0]=item_table[3]},},
{round=1,reward_item={[0]=item_table[4]},},
{round=1,reward_item={[0]=item_table[5]},},
{consume="27740,800|27741,1000",reward_item={[0]=item_table[6]},},
{seq=1,reward_item={[0]=item_table[7]},},
{seq=2,consume="27741,600",reward_item={[0]=item_table[8]},},
{seq=3,reward_item={[0]=item_table[9]},},
{seq=4,consume="27741,300",reward_item={[0]=item_table[10]},},
{seq=5,reward_item={[0]=item_table[11]},},
{seq=6,consume="27741,900",reward_item={[0]=item_table[12]},},
{round=3,reward_item={[0]=item_table[13]},},
{round=3,reward_item={[0]=item_table[14]},},
{round=3,reward_item={[0]=item_table[15]},},
{round=3,reward_item={[0]=item_table[16]},},
{round=3,reward_item={[0]=item_table[17]},},
{round=3,reward_item={[0]=item_table[18]},},
{round=4,reward_item={[0]=item_table[19]},},
{seq=1,reward_item={[0]=item_table[20]},},
{round=4,reward_item={[0]=item_table[21]},},
{seq=3,reward_item={[0]=item_table[22]},},
{round=4,seq=4,consume="27741,500",reward_item={[0]=item_table[23]},},
{round=4,seq=5,consume="27740,200|27741,1000",reward_item={[0]=item_table[24]},},
{round=5,reward_item={[0]=item_table[25]},},
{seq=1,reward_item={[0]=item_table[26]},},
{round=5,seq=2,reward_item={[0]=item_table[27]},},
{round=5,seq=3,consume="27741,500",reward_item={[0]=item_table[28]},},
{round=5,reward_item={[0]=item_table[29]},},
{round=5,reward_item={[0]=item_table[30]},}
},

convert_meta_table_map={
[27]=28,	-- depth:1
[26]=1,	-- depth:1
[22]=28,	-- depth:1
[21]=22,	-- depth:2
[20]=1,	-- depth:1
[15]=27,	-- depth:2
[14]=1,	-- depth:1
[16]=28,	-- depth:1
[10]=11,	-- depth:1
[2]=27,	-- depth:2
[3]=28,	-- depth:1
[8]=9,	-- depth:1
[12]=13,	-- depth:1
[4]=29,	-- depth:1
[5]=24,	-- depth:1
[6]=25,	-- depth:1
[19]=25,	-- depth:1
[18]=24,	-- depth:1
[17]=29,	-- depth:1
[30]=24,	-- depth:1
[23]=24,	-- depth:1
[31]=25,	-- depth:1
},
interface={
{display_pos="-268|-450",rotation="0|-50|0",display_scale=0.5,},
{round=2,tab_name="惊涛骇浪",model_show_itemid="38418|37290|37475|37658|37793|38154",tianxian_task_bg="a2_zjm_txbg_task_2",tianxian_baoge_bg="a2_zjm_txbg_long_2",},
{round=3,tab_name="混沌天衍",model_show_itemid="37055|37212|37419|37624|37724|38135",display_scale=0.3,tianxian_task_bg="a2_zjm_txbg_task_3",tianxian_baoge_bg="a2_zjm_txbg_long_3",},
{round=4,tab_name="浩瀚星空",model_show_itemid="37066|37248|37467|37650|37753|38139",display_scale=0.3,tianxian_task_bg="a2_zjm_txbg_task_4",tianxian_baoge_bg="a2_zjm_txbg_long_4",},
{round=5,tab_name="无极天殇",model_show_itemid="37065|37246|37524|37649|37751|38152",display_pos="-268|-260",tianxian_task_bg="a2_zjm_txbg_task_5",tianxian_baoge_bg="a2_zjm_txbg_long_5",}
},

interface_meta_table_map={
},
activityjump={
{task_decs="前往如梦幻影可获得九天息壤或者本源仙晶",open_panel="WorldServer#worserv_everyday_recharge_boss",},
{task_decs="前往夺仙争霸可获得九天息壤",open_panel="national_war_map",activity_id=3103,},
{task_decs="前往仙盟争霸可获得九天息壤",open_panel="guild#guild_battle",activity_id=3076,},
{activity_id=3106,},
{task_decs="前往龙脉争霸可获得本源仙晶",open_panel="CrossLongMaiView",activity_id=3110,},
{task_decs="前往紫禁之巅可获得本源仙晶",open_panel="national_war#imperial_city_battle_info",},
{task_decs="前往冠绝征战可获得本源仙晶",open_panel="counrty_map_map_view#country_map_flag_grabbing_battlefield",activity_id=3112,},
{task_decs="前往锁妖宝塔可获得本源仙晶",open_panel="kuafuhonorhalls",activity_id=3073,},
{task_decs="前往逐鹿仙缘可获得本源仙晶",open_panel="kuafupvp",activity_id=3075,},
{task_decs="前往沧海夺锋可获得本源仙晶",open_panel="act_jjc#arena_kf1v1",activity_id=3074,},
{task_decs="前往诛仙战场可获得本源仙晶",open_panel="day_activity#dayact_zhuxie",activity_id=3102,},
{},
{round=2,},
{round=2,},
{round=2,},
{},
{round=2,},
{round=2,},
{round=2,},
{round=2,},
{round=2,},
{round=2,},
{round=2,},
{round=2,},
{round=3,},
{round=3,},
{round=3,},
{},
{round=3,},
{round=3,},
{round=3,},
{round=3,},
{round=3,},
{round=3,},
{round=3,},
{round=3,},
{round=4,},
{round=4,},
{round=4,},
{},
{round=4,},
{round=4,},
{round=4,},
{round=4,},
{round=4,},
{round=4,},
{round=4,},
{round=4,},
{round=5,},
{round=5,},
{round=5,},
{round=5,},
{round=5,},
{round=5,},
{round=5,},
{round=5,},
{round=5,},
{round=5,},
{round=5,},
{round=5,}
},

activityjump_meta_table_map={
[12]=4,	-- depth:1
[52]=12,	-- depth:2
[48]=52,	-- depth:3
[40]=48,	-- depth:4
[36]=40,	-- depth:5
[28]=36,	-- depth:6
[24]=28,	-- depth:7
[16]=24,	-- depth:8
[60]=16,	-- depth:9
[54]=6,	-- depth:1
[49]=1,	-- depth:1
[42]=54,	-- depth:2
[37]=49,	-- depth:2
[25]=37,	-- depth:3
[30]=42,	-- depth:3
[18]=30,	-- depth:4
[13]=25,	-- depth:4
[45]=9,	-- depth:1
[46]=10,	-- depth:1
[47]=11,	-- depth:1
[15]=3,	-- depth:1
[50]=2,	-- depth:1
[51]=15,	-- depth:2
[14]=50,	-- depth:2
[53]=5,	-- depth:1
[55]=7,	-- depth:1
[56]=8,	-- depth:1
[57]=45,	-- depth:2
[58]=46,	-- depth:2
[44]=56,	-- depth:2
[43]=55,	-- depth:2
[17]=53,	-- depth:2
[41]=17,	-- depth:3
[26]=14,	-- depth:3
[27]=51,	-- depth:3
[21]=57,	-- depth:3
[29]=41,	-- depth:4
[59]=47,	-- depth:2
[31]=43,	-- depth:3
[23]=59,	-- depth:3
[32]=44,	-- depth:3
[34]=58,	-- depth:3
[35]=23,	-- depth:4
[20]=32,	-- depth:4
[19]=31,	-- depth:4
[38]=26,	-- depth:4
[39]=27,	-- depth:4
[33]=21,	-- depth:4
[22]=34,	-- depth:4
},
heraid={
{heraid_pos="0|-250",heraid_rot="0|-40|0",heraid_scale=0.4,},
{round=2,model_heraid_itemid="37055|37212|37419|37624|37724|38135",heraid_scale=0.3,now_round_bg="a2_txbg_heraid_2",next_round_bg="a2_txbg_heraid_3",},
{round=3,model_heraid_itemid="37066|37248|37467|37650|37753|38139",now_round_bg="a2_txbg_heraid_3",next_round_bg="a2_txbg_heraid_4",},
{round=4,model_heraid_itemid="37065|37246|37524|37649|37751|38152",now_round_bg="a2_txbg_heraid_4",next_round_bg="a2_txbg_heraid_5",},
{round=5,model_heraid_itemid="37097|37977|37689|37872|38177",heraid_pos="0|-450",heraid_rot="0|-30|0",heraid_scale=0.5,now_round_bg="a2_txbg_heraid_5",next_round_bg="a2_txbg_heraid_1",}
},

heraid_meta_table_map={
},
other_default_table={role_level=300,open_server_day=5,continue_day=7,},

convert_default_table={round=2,seq=0,show=1,consume="27741,1000",reward_item={[0]=item_table[31]},limit=1,},

interface_default_table={round=1,show_index=1,tab_name="惊魂灵夜",model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid="37097|37977|37689|37872|38177",display_pos="-268|-184",rotation="0|-65|0",display_scale=0.35,prop_1=27740,prop_2=27741,tianxian_task_bg="a2_zjm_txbg_task_1",tianxian_baoge_bg="a2_zjm_txbg_long_1",},

activityjump_default_table={round=1,task_decs="前往势力护送可获得本源仙晶",open_panel="NewShipView",activity_id="",},

heraid_default_table={round=1,model_heraid_type=1,model_bundle_name="",model_asset_name="",model_heraid_itemid="38418|37290|37475|37658|37793|38154",heraid_pos="0|-270",heraid_rot="0|-65|0",heraid_scale=0.35,now_round_bg="a2_txbg_heraid_1",next_round_bg="a2_txbg_heraid_2",}

}

