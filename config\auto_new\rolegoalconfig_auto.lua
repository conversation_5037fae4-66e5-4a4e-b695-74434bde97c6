return {
	["reward_default_table"]={belong_chapter=1,
		reward_item={item_id=22000,num=1,is_bind=1,},chapter_name="第六章",},
	["reward"]={
		{belong_chapter=0,reward_item={item_id=22000,num=1,is_bind=1,},chapter_name="第一章",},
		{chapter_name="第二章",},
		{belong_chapter=2,reward_item={item_id=22000,num=1,is_bind=1,},chapter_name="第三章",},
		{belong_chapter=3,reward_item={item_id=22000,num=1,is_bind=1,},chapter_name="第四章",},
		{belong_chapter=4,reward_item={item_id=22000,num=1,is_bind=1,},chapter_name="第五章",},
		{belong_chapter=5,reward_item={item_id=22000,num=1,is_bind=1,},},
		{belong_chapter=6,reward_item={item_id=22000,num=1,is_bind=1,},chapter_name="第七章",},},
	["goal"]={
		[0]={belong_chapter=0,desc="身上装备强化等级达到{wordcolor;ffff00;36}",belong_section=0,param=36,role_goal_id=0,},
		[2]={belong_chapter=0,show_type=1,open_panel_name="fuben#fb_fangju",desc="通关防具副本第{wordcolor;ffff00;二}关",belong_section=2,goal_name="挑战防具副本",role_goal_id=2,},
		[4]={show_type=1,open_panel_name="fuben#fb_weapon",desc="武器副本第{wordcolor;ffff00;二}章获得{wordcolor;ffff00;20}颗星星评分",goal_name="挑战武器副本",param=20,role_goal_id=4,},
		[8]={belong_chapter=2,show_type=1,open_panel_name="fuben#fb_quality",desc="挑战品质副本第{wordcolor;ffff00;二}关第{wordcolor;ffff00;7}层",belong_section=2,goal_name="挑战品质副本",role_goal_id=8,},
		[16]={belong_chapter=5,show_type=1,open_panel_name="mount#mount_jinjie",desc="坐骑提升到{wordcolor;ffff00;4}阶",goal_name="坐骑进阶",param=5,role_goal_id=16,},
		[17]={belong_chapter=5,show_type=1,open_panel_name="zhanshendian",desc="通关镇魔塔第{wordcolor;ffff00;25}层",belong_section=2,goal_name="挑战镇魔塔",role_goal_id=17,},
		[9]={belong_chapter=3,show_type=1,open_panel_name="marry#marry_attr",desc="找到心仪对象{wordcolor;ffff00;结婚}",belong_section=0,goal_name="喜结良缘",role_goal_id=9,},
		[18]={belong_chapter=6,open_panel_name="peri#peri_atr",desc="拥有{wordcolor;ffff00;4}位仙女",belong_section=0,goal_name="左拥右抱",param=4,role_goal_id=18,},
		[5]={show_type=1,open_panel_name="zhanshendian",desc="通关镇魔塔第{wordcolor;ffff00;12}层",belong_section=2,goal_name="挑战镇魔塔",role_goal_id=5,},
		[10]={belong_chapter=3,open_panel_name="peri#peri_chanmian",desc="仙女缠绵至{wordcolor;ffff00;15}级",goal_name="仙女缠绵",param=15,role_goal_id=10,},
		[20]={belong_chapter=6,show_type=1,open_panel_name="fuben#fb_quality",desc="挑战品质副本第{wordcolor;ffff00;三}关第{wordcolor;ffff00;5}层",belong_section=2,goal_name="挑战品质副本",role_goal_id=20,},
		[11]={belong_chapter=3,desc="身上装备强化等级达到{wordcolor;ffff00;60}",belong_section=2,param=60,role_goal_id=11,},
		[3]={open_panel_name="wing#wing_jinghua",desc="羽翼进化到{wordcolor;ffff00;3}阶{wordcolor;ffff00;1}星",belong_section=0,goal_name="羽翼进化",param=21,role_goal_id=3,},
		[6]={belong_chapter=2,show_type=1,open_panel_name="mount#mount_jinjie",belong_section=0,goal_name="坐骑进阶",param=4,role_goal_id=6,},
		[12]={belong_chapter=4,open_panel_name="fairysword#fairysword_soul",desc="激活{wordcolor;ffff00;6}个剑魂技能",belong_section=0,goal_name="收集剑魂",param=6,role_goal_id=12,},
		[13]={belong_chapter=4,open_panel_name="wing#wing_jinghua",desc="羽翼进化到{wordcolor;ffff00;4}阶{wordcolor;ffff00;1}星",goal_name="羽翼进化",param=31,role_goal_id=13,},
		[7]={belong_chapter=2,open_panel_name="jingmai#meridian_gengu",desc="历史最高总根骨等级修炼至{wordcolor;ffff00;28}级",goal_name="修炼根骨",param=28,role_goal_id=7,},
		[14]={belong_chapter=4,open_panel_name="equipment#equipment_stone",desc="身上总宝石等级达到{wordcolor;ffff00;72}",belong_section=2,goal_name="提炼宝石",param=72,role_goal_id=14,},
		[19]={belong_chapter=6,open_panel_name="equipment#equipment_shen",desc="拥有{wordcolor;ffff00;4}件神装",goal_name="神兵利器",param=4,role_goal_id=19,},
		[15]={belong_chapter=5,desc="身上装备强化等级达到{wordcolor;ffff00;72}",belong_section=0,param=72,role_goal_id=15,},
		[1]={belong_chapter=0,show_type=1,open_panel_name="fuben#fb_exp",desc="经验副本第{wordcolor;ffff00;二}层{wordcolor;ffff00;地狱}难度获得{wordcolor;ffff00;3}星评分",goal_name="挑战经验副本",param=3,},},
	["goal_default_table"]={belong_chapter=1,desc="坐骑提升到{wordcolor;ffff00;3}阶",show_type=0,open_panel_name="equipment#equipment_strength",belong_section=1,goal_name="强化装备",param=1,role_goal_id=1,},
}
