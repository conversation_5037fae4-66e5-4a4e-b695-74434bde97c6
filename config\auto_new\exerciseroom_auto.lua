return {
	["exercise_room_cfg_default_table"]={consume_gold=0,boss_appear_s=120,high_level=109,low_level=70,exercise_type=1,appear_s=6,boss_kill_s=10,continue_interval_h=8,monster_kill_s=5,},
	["exercise_room_cfg"]={
		{boss_appear_s=300,high_level=89,},
		{consume_gold=50,boss_appear_s=150,high_level=89,exercise_type=2,appear_s=3,monster_kill_s=3,},
		{boss_appear_s=270,low_level=90,},
		{consume_gold=50,boss_appear_s=135,low_level=90,exercise_type=2,appear_s=3,monster_kill_s=3,},
		{boss_appear_s=240,high_level=200,low_level=110,},
		{consume_gold=50,high_level=200,low_level=110,exercise_type=2,appear_s=3,monster_kill_s=3,},},
	["reward_cfg_default_table"]={monster_type=1,low_level=70,high_level=109,reward_xiuwei=3,drop_id=0,},
	["reward_cfg"]={
		{high_level=89,},
		{drop_id=1683,high_level=89,reward_xiuwei=5,monster_type=2,},
		{low_level=90,},
		{low_level=90,drop_id=1683,reward_xiuwei=5,monster_type=2,},
		{low_level=110,high_level=200,},
		{low_level=110,drop_id=1683,high_level=200,reward_xiuwei=5,monster_type=2,},},
}
