-- Y-运营活动-限时累充.xls
local item_table={
[1]={item_id=26193,num=1,is_bind=1},
[2]={item_id=47463,num=1,is_bind=1},
[3]={item_id=26353,num=5,is_bind=1},
[4]={item_id=26358,num=2,is_bind=1},
[5]={item_id=44183,num=2,is_bind=1},
[6]={item_id=47456,num=1,is_bind=1},
[7]={item_id=26348,num=2,is_bind=1},
[8]={item_id=26359,num=3,is_bind=1},
[9]={item_id=44183,num=3,is_bind=1},
[10]={item_id=26194,num=1,is_bind=1},
[11]={item_id=47457,num=1,is_bind=1},
[12]={item_id=26351,num=2,is_bind=1},
[13]={item_id=26361,num=3,is_bind=1},
[14]={item_id=44184,num=3,is_bind=1},
[15]={item_id=47458,num=1,is_bind=1},
[16]={item_id=26354,num=2,is_bind=1},
[17]={item_id=26362,num=4,is_bind=1},
[18]={item_id=44185,num=4,is_bind=1},
[19]={item_id=46584,num=100,is_bind=1},
[20]={item_id=47459,num=1,is_bind=1},
[21]={item_id=26348,num=5,is_bind=1},
[22]={item_id=26357,num=1,is_bind=1},
[23]={item_id=44185,num=5,is_bind=1},
[24]={item_id=46584,num=200,is_bind=1},
[25]={item_id=47460,num=1,is_bind=1},
[26]={item_id=26351,num=5,is_bind=1},
[27]={item_id=26360,num=2,is_bind=1},
[28]={item_id=26459,num=1,is_bind=1},
[29]={item_id=46584,num=400,is_bind=1},
[30]={item_id=47461,num=1,is_bind=1},
[31]={item_id=26354,num=5,is_bind=1},
[32]={item_id=26363,num=3,is_bind=1},
[33]={item_id=26460,num=1,is_bind=1},
[34]={item_id=46584,num=500,is_bind=1},
[35]={item_id=47462,num=1,is_bind=1},
[36]={item_id=26569,num=1,is_bind=1},
[37]={item_id=26570,num=1,is_bind=1},
[38]={item_id=26463,num=1,is_bind=1},
[39]={item_id=26191,num=1,is_bind=1},
[40]={item_id=50076,num=1,is_bind=1},
[41]={item_id=26350,num=5,is_bind=1},
[42]={item_id=26356,num=2,is_bind=1},
[43]={item_id=44182,num=2,is_bind=1},
[44]={item_id=47471,num=1,is_bind=1},
[45]={item_id=47464,num=1,is_bind=1},
[46]={item_id=47465,num=1,is_bind=1},
[47]={item_id=47487,num=1,is_bind=1},
[48]={item_id=47480,num=1,is_bind=1},
[49]={item_id=47481,num=1,is_bind=1},
[50]={item_id=47482,num=1,is_bind=1},
[51]={item_id=47483,num=1,is_bind=1},
[52]={item_id=47484,num=1,is_bind=1},
[53]={item_id=47485,num=1,is_bind=1},
[54]={item_id=47486,num=1,is_bind=1},
[55]={item_id=47495,num=1,is_bind=1},
[56]={item_id=47488,num=1,is_bind=1},
[57]={item_id=47489,num=1,is_bind=1},
[58]={item_id=47490,num=1,is_bind=1},
[59]={item_id=46589,num=100,is_bind=1},
[60]={item_id=47491,num=1,is_bind=1},
[61]={item_id=46589,num=200,is_bind=1},
[62]={item_id=47492,num=1,is_bind=1},
[63]={item_id=46589,num=400,is_bind=1},
[64]={item_id=47493,num=1,is_bind=1},
[65]={item_id=46589,num=500,is_bind=1},
[66]={item_id=47494,num=1,is_bind=1},
[67]={item_id=47503,num=1,is_bind=1},
[68]={item_id=47496,num=1,is_bind=1},
[69]={item_id=47497,num=1,is_bind=1},
[70]={item_id=47498,num=1,is_bind=1},
[71]={item_id=46590,num=100,is_bind=1},
[72]={item_id=47499,num=1,is_bind=1},
[73]={item_id=46590,num=200,is_bind=1},
[74]={item_id=47500,num=1,is_bind=1},
[75]={item_id=46590,num=400,is_bind=1},
[76]={item_id=47501,num=1,is_bind=1},
[77]={item_id=46590,num=500,is_bind=1},
[78]={item_id=47502,num=1,is_bind=1},
[79]={item_id=47511,num=1,is_bind=1},
[80]={item_id=47504,num=1,is_bind=1},
[81]={item_id=47505,num=1,is_bind=1},
[82]={item_id=47506,num=1,is_bind=1},
[83]={item_id=47507,num=1,is_bind=1},
[84]={item_id=47508,num=1,is_bind=1},
[85]={item_id=47509,num=1,is_bind=1},
[86]={item_id=47510,num=1,is_bind=1},
[87]={item_id=47519,num=1,is_bind=1},
[88]={item_id=47512,num=1,is_bind=1},
[89]={item_id=47513,num=1,is_bind=1},
[90]={item_id=47514,num=1,is_bind=1},
[91]={item_id=47515,num=1,is_bind=1},
[92]={item_id=47516,num=1,is_bind=1},
[93]={item_id=47517,num=1,is_bind=1},
[94]={item_id=47518,num=1,is_bind=1},
[95]={item_id=47479,num=1,is_bind=1},
[96]={item_id=47472,num=1,is_bind=1},
[97]={item_id=47473,num=1,is_bind=1},
[98]={item_id=47474,num=1,is_bind=1},
[99]={item_id=47475,num=1,is_bind=1},
[100]={item_id=47476,num=1,is_bind=1},
[101]={item_id=47477,num=1,is_bind=1},
[102]={item_id=47478,num=1,is_bind=1},
[103]={item_id=47466,num=1,is_bind=1},
[104]={item_id=47467,num=1,is_bind=1},
[105]={item_id=47468,num=1,is_bind=1},
[106]={item_id=47469,num=1,is_bind=1},
[107]={item_id=47470,num=1,is_bind=1},
[108]={item_id=46048,num=1,is_bind=1},
[109]={item_id=39150,num=3,is_bind=1},
[110]={item_id=56319,num=1,is_bind=1},
[111]={item_id=44182,num=1,is_bind=1},
[112]={item_id=26473,num=1,is_bind=1},
[113]={item_id=39150,num=5,is_bind=1},
[114]={item_id=56319,num=2,is_bind=1},
[115]={item_id=39150,num=10,is_bind=1},
[116]={item_id=56320,num=2,is_bind=1},
[117]={item_id=47527,num=1,is_bind=1},
[118]={item_id=39151,num=2,is_bind=1},
[119]={item_id=56320,num=3,is_bind=1},
[120]={item_id=47520,num=1,is_bind=1},
[121]={item_id=39151,num=3,is_bind=1},
[122]={item_id=56321,num=3,is_bind=1},
[123]={item_id=47521,num=1,is_bind=1},
[124]={item_id=39151,num=4,is_bind=1},
[125]={item_id=56321,num=4,is_bind=1},
[126]={item_id=47522,num=1,is_bind=1},
[127]={item_id=39151,num=5,is_bind=1},
[128]={item_id=56322,num=1,is_bind=1},
[129]={item_id=47523,num=1,is_bind=1},
[130]={item_id=39152,num=1,is_bind=1},
[131]={item_id=47524,num=1,is_bind=1},
[132]={item_id=39152,num=2,is_bind=1},
[133]={item_id=56323,num=1,is_bind=1},
[134]={item_id=47525,num=1,is_bind=1},
[135]={item_id=39152,num=5,is_bind=1},
[136]={item_id=46584,num=1000,is_bind=1},
[137]={item_id=47526,num=1,is_bind=1},
[138]={item_id=39152,num=10,is_bind=1},
[139]={item_id=56324,num=2,is_bind=1},
[140]={item_id=26464,num=1,is_bind=1},
[141]={item_id=47540,num=1,is_bind=1},
[142]={item_id=47541,num=1,is_bind=1},
[143]={item_id=46590,num=1000,is_bind=1},
[144]={item_id=47542,num=1,is_bind=1},
[145]={item_id=47551,num=1,is_bind=1},
[146]={item_id=47544,num=1,is_bind=1},
[147]={item_id=47545,num=1,is_bind=1},
[148]={item_id=47546,num=1,is_bind=1},
[149]={item_id=47547,num=1,is_bind=1},
[150]={item_id=47548,num=1,is_bind=1},
[151]={item_id=47549,num=1,is_bind=1},
[152]={item_id=47550,num=1,is_bind=1},
[153]={item_id=47559,num=1,is_bind=1},
[154]={item_id=47552,num=1,is_bind=1},
[155]={item_id=47553,num=1,is_bind=1},
[156]={item_id=47554,num=1,is_bind=1},
[157]={item_id=47555,num=1,is_bind=1},
[158]={item_id=47556,num=1,is_bind=1},
[159]={item_id=47557,num=1,is_bind=1},
[160]={item_id=46589,num=1000,is_bind=1},
[161]={item_id=47558,num=1,is_bind=1},
[162]={item_id=47567,num=1,is_bind=1},
[163]={item_id=47560,num=1,is_bind=1},
[164]={item_id=47561,num=1,is_bind=1},
[165]={item_id=47562,num=1,is_bind=1},
[166]={item_id=47563,num=1,is_bind=1},
[167]={item_id=47564,num=1,is_bind=1},
[168]={item_id=47565,num=1,is_bind=1},
[169]={item_id=47566,num=1,is_bind=1},
[170]={item_id=47575,num=1,is_bind=1},
[171]={item_id=47568,num=1,is_bind=1},
[172]={item_id=47569,num=1,is_bind=1},
[173]={item_id=47570,num=1,is_bind=1},
[174]={item_id=47571,num=1,is_bind=1},
[175]={item_id=47572,num=1,is_bind=1},
[176]={item_id=47573,num=1,is_bind=1},
[177]={item_id=47574,num=1,is_bind=1},
[178]={item_id=47583,num=1,is_bind=1},
[179]={item_id=47576,num=1,is_bind=1},
[180]={item_id=47577,num=1,is_bind=1},
[181]={item_id=47578,num=1,is_bind=1},
[182]={item_id=47579,num=1,is_bind=1},
[183]={item_id=47580,num=1,is_bind=1},
[184]={item_id=47581,num=1,is_bind=1},
[185]={item_id=47582,num=1,is_bind=1},
[186]={item_id=47535,num=1,is_bind=1},
[187]={item_id=47528,num=1,is_bind=1},
[188]={item_id=47529,num=1,is_bind=1},
[189]={item_id=47530,num=1,is_bind=1},
[190]={item_id=47531,num=1,is_bind=1},
[191]={item_id=47532,num=1,is_bind=1},
[192]={item_id=47533,num=1,is_bind=1},
[193]={item_id=47534,num=1,is_bind=1},
[194]={item_id=47543,num=1,is_bind=1},
[195]={item_id=47536,num=1,is_bind=1},
[196]={item_id=47537,num=1,is_bind=1},
[197]={item_id=47538,num=1,is_bind=1},
[198]={item_id=47539,num=1,is_bind=1},
[199]={item_id=38004,num=1,is_bind=1},
[200]={item_id=46584,num=1,is_bind=1},
[201]={item_id=38105,num=1,is_bind=1},
[202]={item_id=38005,num=1,is_bind=1},
[203]={item_id=38106,num=1,is_bind=1},
[204]={item_id=38006,num=1,is_bind=1},
[205]={item_id=38107,num=1,is_bind=1},
[206]={item_id=38011,num=1,is_bind=1},
[207]={item_id=38110,num=1,is_bind=1},
[208]={item_id=38007,num=1,is_bind=1},
[209]={item_id=37614,num=1,is_bind=1},
[210]={item_id=46385,num=1,is_bind=1},
[211]={item_id=46392,num=1,is_bind=1},
[212]={item_id=37616,num=1,is_bind=1},
[213]={item_id=37618,num=1,is_bind=1},
[214]={item_id=37620,num=1,is_bind=1},
[215]={item_id=37619,num=1,is_bind=1},
[216]={item_id=37617,num=1,is_bind=1},
[217]={item_id=37629,num=1,is_bind=1},
[218]={item_id=37631,num=1,is_bind=1},
[219]={item_id=37632,num=1,is_bind=1},
[220]={item_id=37634,num=1,is_bind=1},
[221]={item_id=26347,num=5,is_bind=1},
[222]={item_id=26355,num=2,is_bind=1},
[223]={item_id=38121,num=1,is_bind=1},
}

return {
config_param={
{grade=0,interface=0,},
{start_server_day=18,end_server_day=24,grade=1,interface=1,},
{start_server_day=25,end_server_day=31,},
{start_server_day=32,end_server_day=38,},
{start_server_day=39,end_server_day=45,},
{start_server_day=46,end_server_day=52,},
{start_server_day=53,end_server_day=59,grade=6,interface=6,},
{start_server_day=60,end_server_day=66,},
{start_server_day=67,end_server_day=73,},
{start_server_day=74,end_server_day=80,},
{start_server_day=81,end_server_day=87,},
{start_server_day=88,end_server_day=94,},
{start_server_day=95,end_server_day=101,},
{start_server_day=102,end_server_day=108,},
{start_server_day=109,end_server_day=115,},
{start_server_day=116,end_server_day=122,},
{start_server_day=123,end_server_day=129,},
{start_server_day=130,end_server_day=136,},
{start_server_day=137,end_server_day=143,grade=2,interface=2,},
{start_server_day=144,end_server_day=150,},
{start_server_day=151,end_server_day=157,},
{start_server_day=158,end_server_day=164,},
{start_server_day=165,end_server_day=171,},
{start_server_day=172,end_server_day=178,},
{start_server_day=179,end_server_day=185,},
{start_server_day=186,end_server_day=192,},
{start_server_day=193,end_server_day=199,grade=9,interface=9,},
{start_server_day=200,end_server_day=206,grade=10,interface=10,},
{start_server_day=207,end_server_day=213,grade=11,interface=11,},
{start_server_day=214,end_server_day=220,grade=12,interface=12,},
{start_server_day=221,end_server_day=227,},
{start_server_day=228,end_server_day=234,grade=13,interface=13,},
{start_server_day=235,end_server_day=241,grade=14,interface=14,},
{start_server_day=242,end_server_day=248,grade=3,interface=3,},
{start_server_day=249,end_server_day=255,grade=4,interface=4,},
{start_server_day=256,end_server_day=262,grade=5,interface=5,},
{start_server_day=263,end_server_day=269,},
{start_server_day=270,end_server_day=276,grade=7,interface=7,},
{start_server_day=277,end_server_day=283,grade=8,interface=8,},
{start_server_day=284,end_server_day=290,},
{start_server_day=291,end_server_day=9999,},
{week_index=5,grade=41,interface=41,},
{week_index=5,grade=42,interface=42,},
{week_index=5,grade=43,interface=43,},
{start_server_day=32,end_server_day=38,},
{start_server_day=39,end_server_day=45,},
{start_server_day=46,end_server_day=52,},
{start_server_day=53,end_server_day=59,},
{start_server_day=60,end_server_day=66,},
{start_server_day=67,end_server_day=73,},
{start_server_day=74,end_server_day=80,},
{start_server_day=81,end_server_day=87,},
{start_server_day=88,end_server_day=94,},
{start_server_day=95,end_server_day=101,},
{start_server_day=102,end_server_day=108,},
{start_server_day=109,end_server_day=115,},
{start_server_day=116,end_server_day=122,},
{start_server_day=123,end_server_day=129,},
{start_server_day=130,end_server_day=136,},
{start_server_day=137,end_server_day=143,},
{start_server_day=144,end_server_day=150,},
{start_server_day=151,end_server_day=157,},
{start_server_day=158,end_server_day=164,},
{week_index=5,grade=46,interface=46,},
{week_index=5,grade=47,interface=47,},
{week_index=5,grade=48,interface=48,},
{week_index=5,grade=49,interface=49,},
{week_index=5,grade=50,interface=50,},
{week_index=5,grade=51,interface=51,},
{week_index=5,grade=52,interface=52,},
{week_index=5,grade=53,interface=53,},
{week_index=5,grade=56,interface=56,},
{week_index=5,grade=54,interface=54,},
{week_index=5,grade=55,interface=55,},
{week_index=5,grade=44,interface=44,},
{week_index=5,grade=45,interface=45,},
{start_server_day=256,end_server_day=262,},
{start_server_day=263,end_server_day=269,},
{start_server_day=270,end_server_day=276,},
{start_server_day=277,end_server_day=283,},
{start_server_day=284,end_server_day=290,},
{start_server_day=291,end_server_day=9999,}
},

config_param_meta_table_map={
[37]=7,	-- depth:1
[40]=27,	-- depth:1
[6]=36,	-- depth:1
[26]=39,	-- depth:1
[25]=38,	-- depth:1
[23]=36,	-- depth:1
[8]=38,	-- depth:1
[24]=7,	-- depth:1
[9]=39,	-- depth:1
[10]=27,	-- depth:1
[5]=35,	-- depth:1
[13]=29,	-- depth:1
[14]=30,	-- depth:1
[15]=32,	-- depth:1
[12]=28,	-- depth:1
[16]=33,	-- depth:1
[4]=34,	-- depth:1
[18]=2,	-- depth:1
[20]=34,	-- depth:1
[3]=19,	-- depth:1
[22]=35,	-- depth:1
[70]=29,	-- depth:1
[65]=24,	-- depth:2
[66]=25,	-- depth:2
[67]=26,	-- depth:2
[68]=27,	-- depth:1
[69]=28,	-- depth:1
[64]=23,	-- depth:2
[71]=30,	-- depth:1
[79]=66,	-- depth:3
[73]=32,	-- depth:1
[74]=33,	-- depth:1
[75]=34,	-- depth:1
[76]=35,	-- depth:1
[77]=64,	-- depth:3
[78]=65,	-- depth:3
[80]=67,	-- depth:3
[63]=76,	-- depth:2
[72]=31,	-- depth:1
[62]=72,	-- depth:2
[44]=3,	-- depth:2
[60]=44,	-- depth:3
[81]=68,	-- depth:2
[43]=2,	-- depth:1
[45]=75,	-- depth:2
[46]=76,	-- depth:2
[47]=64,	-- depth:3
[48]=65,	-- depth:3
[49]=66,	-- depth:3
[50]=67,	-- depth:3
[51]=68,	-- depth:2
[52]=72,	-- depth:2
[53]=69,	-- depth:2
[54]=70,	-- depth:2
[55]=71,	-- depth:2
[56]=73,	-- depth:2
[57]=74,	-- depth:2
[58]=72,	-- depth:2
[59]=43,	-- depth:2
[61]=75,	-- depth:2
[82]=72,	-- depth:2
},
reward={
{grade=0,},
{grade=0,},
{grade=0,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{grade=0,reward_item={[0]=item_table[1],[1]=item_table[6],[2]=item_table[7],[3]=item_table[8],[4]=item_table[9]},},
{grade=0,reward_item={[0]=item_table[10],[1]=item_table[11],[2]=item_table[12],[3]=item_table[13],[4]=item_table[14]},},
{grade=0,reward_item={[0]=item_table[10],[1]=item_table[15],[2]=item_table[16],[3]=item_table[17],[4]=item_table[18]},},
{grade=0,reward_item={[0]=item_table[19],[1]=item_table[20],[2]=item_table[21],[3]=item_table[22],[4]=item_table[23]},},
{grade=0,reward_item={[0]=item_table[24],[1]=item_table[25],[2]=item_table[26],[3]=item_table[27],[4]=item_table[28]},},
{grade=0,reward_item={[0]=item_table[29],[1]=item_table[30],[2]=item_table[31],[3]=item_table[32],[4]=item_table[33]},},
{grade=0,reward_item={[0]=item_table[34],[1]=item_table[35],[2]=item_table[36],[3]=item_table[37],[4]=item_table[38]},},
{grade=1,},
{grade=1,reward_item={[0]=item_table[39],[1]=item_table[40],[2]=item_table[41],[3]=item_table[42],[4]=item_table[43]},},
{grade=1,reward_item={[0]=item_table[1],[1]=item_table[44],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{grade=1,reward_item={[0]=item_table[1],[1]=item_table[45],[2]=item_table[7],[3]=item_table[8],[4]=item_table[9]},},
{grade=1,reward_item={[0]=item_table[10],[1]=item_table[46],[2]=item_table[12],[3]=item_table[13],[4]=item_table[14]},},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,reward_item={[0]=item_table[1],[1]=item_table[47],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{grade=3,reward_item={[0]=item_table[1],[1]=item_table[48],[2]=item_table[7],[3]=item_table[8],[4]=item_table[9]},},
{grade=3,reward_item={[0]=item_table[10],[1]=item_table[49],[2]=item_table[12],[3]=item_table[13],[4]=item_table[14]},},
{grade=3,reward_item={[0]=item_table[10],[1]=item_table[50],[2]=item_table[16],[3]=item_table[17],[4]=item_table[18]},},
{grade=3,reward_item={[0]=item_table[19],[1]=item_table[51],[2]=item_table[21],[3]=item_table[22],[4]=item_table[23]},},
{grade=3,reward_item={[0]=item_table[24],[1]=item_table[52],[2]=item_table[26],[3]=item_table[27],[4]=item_table[28]},},
{grade=3,reward_item={[0]=item_table[29],[1]=item_table[53],[2]=item_table[31],[3]=item_table[32],[4]=item_table[33]},},
{grade=3,reward_item={[0]=item_table[34],[1]=item_table[54],[2]=item_table[36],[3]=item_table[37],[4]=item_table[38]},},
{grade=4,},
{grade=4,},
{grade=4,reward_item={[0]=item_table[1],[1]=item_table[55],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{grade=4,reward_item={[0]=item_table[1],[1]=item_table[56],[2]=item_table[7],[3]=item_table[8],[4]=item_table[9]},},
{grade=4,reward_item={[0]=item_table[10],[1]=item_table[57],[2]=item_table[12],[3]=item_table[13],[4]=item_table[14]},},
{grade=4,reward_item={[0]=item_table[10],[1]=item_table[58],[2]=item_table[16],[3]=item_table[17],[4]=item_table[18]},},
{grade=4,reward_item={[0]=item_table[59],[1]=item_table[60],[2]=item_table[21],[3]=item_table[22],[4]=item_table[23]},},
{grade=4,reward_item={[0]=item_table[61],[1]=item_table[62],[2]=item_table[26],[3]=item_table[27],[4]=item_table[28]},},
{grade=4,reward_item={[0]=item_table[63],[1]=item_table[64],[2]=item_table[31],[3]=item_table[32],[4]=item_table[33]},},
{grade=4,reward_item={[0]=item_table[65],[1]=item_table[66],[2]=item_table[36],[3]=item_table[37],[4]=item_table[38]},},
{grade=5,},
{grade=5,},
{grade=5,reward_item={[0]=item_table[1],[1]=item_table[67],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{grade=5,reward_item={[0]=item_table[1],[1]=item_table[68],[2]=item_table[7],[3]=item_table[8],[4]=item_table[9]},},
{grade=5,reward_item={[0]=item_table[10],[1]=item_table[69],[2]=item_table[12],[3]=item_table[13],[4]=item_table[14]},},
{grade=5,reward_item={[0]=item_table[10],[1]=item_table[70],[2]=item_table[16],[3]=item_table[17],[4]=item_table[18]},},
{grade=5,reward_item={[0]=item_table[71],[1]=item_table[72],[2]=item_table[21],[3]=item_table[22],[4]=item_table[23]},},
{grade=5,reward_item={[0]=item_table[73],[1]=item_table[74],[2]=item_table[26],[3]=item_table[27],[4]=item_table[28]},},
{grade=5,reward_item={[0]=item_table[75],[1]=item_table[76],[2]=item_table[31],[3]=item_table[32],[4]=item_table[33]},},
{grade=5,reward_item={[0]=item_table[77],[1]=item_table[78],[2]=item_table[36],[3]=item_table[37],[4]=item_table[38]},},
{grade=6,},
{grade=6,},
{grade=6,reward_item={[0]=item_table[1],[1]=item_table[79],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{grade=6,reward_item={[0]=item_table[1],[1]=item_table[80],[2]=item_table[7],[3]=item_table[8],[4]=item_table[9]},},
{grade=6,reward_item={[0]=item_table[10],[1]=item_table[81],[2]=item_table[12],[3]=item_table[13],[4]=item_table[14]},},
{grade=6,reward_item={[0]=item_table[10],[1]=item_table[82],[2]=item_table[16],[3]=item_table[17],[4]=item_table[18]},},
{grade=6,reward_item={[0]=item_table[19],[1]=item_table[83],[2]=item_table[21],[3]=item_table[22],[4]=item_table[23]},},
{grade=6,reward_item={[0]=item_table[24],[1]=item_table[84],[2]=item_table[26],[3]=item_table[27],[4]=item_table[28]},},
{grade=6,reward_item={[0]=item_table[29],[1]=item_table[85],[2]=item_table[31],[3]=item_table[32],[4]=item_table[33]},},
{grade=6,reward_item={[0]=item_table[34],[1]=item_table[86],[2]=item_table[36],[3]=item_table[37],[4]=item_table[38]},},
{grade=7,},
{grade=7,},
{grade=7,reward_item={[0]=item_table[1],[1]=item_table[87],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{grade=7,reward_item={[0]=item_table[1],[1]=item_table[88],[2]=item_table[7],[3]=item_table[8],[4]=item_table[9]},},
{grade=7,reward_item={[0]=item_table[10],[1]=item_table[89],[2]=item_table[12],[3]=item_table[13],[4]=item_table[14]},},
{grade=7,reward_item={[0]=item_table[10],[1]=item_table[90],[2]=item_table[16],[3]=item_table[17],[4]=item_table[18]},},
{grade=7,reward_item={[0]=item_table[59],[1]=item_table[91],[2]=item_table[21],[3]=item_table[22],[4]=item_table[23]},},
{grade=7,reward_item={[0]=item_table[61],[1]=item_table[92],[2]=item_table[26],[3]=item_table[27],[4]=item_table[28]},},
{grade=7,reward_item={[0]=item_table[63],[1]=item_table[93],[2]=item_table[31],[3]=item_table[32],[4]=item_table[33]},},
{grade=7,reward_item={[0]=item_table[65],[1]=item_table[94],[2]=item_table[36],[3]=item_table[37],[4]=item_table[38]},},
{grade=8,},
{grade=8,},
{grade=8,reward_item={[0]=item_table[1],[1]=item_table[95],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{grade=8,reward_item={[0]=item_table[1],[1]=item_table[96],[2]=item_table[7],[3]=item_table[8],[4]=item_table[9]},},
{grade=8,reward_item={[0]=item_table[10],[1]=item_table[97],[2]=item_table[12],[3]=item_table[13],[4]=item_table[14]},},
{grade=8,reward_item={[0]=item_table[10],[1]=item_table[98],[2]=item_table[16],[3]=item_table[17],[4]=item_table[18]},},
{grade=8,ID=7,stage_value=100000,reward_item={[0]=item_table[71],[1]=item_table[99],[2]=item_table[21],[3]=item_table[22],[4]=item_table[23]},},
{grade=8,reward_item={[0]=item_table[73],[1]=item_table[100],[2]=item_table[26],[3]=item_table[27],[4]=item_table[28]},},
{grade=8,reward_item={[0]=item_table[75],[1]=item_table[101],[2]=item_table[31],[3]=item_table[32],[4]=item_table[33]},},
{grade=8,reward_item={[0]=item_table[77],[1]=item_table[102],[2]=item_table[36],[3]=item_table[37],[4]=item_table[38]},},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,reward_item={[0]=item_table[10],[1]=item_table[103],[2]=item_table[16],[3]=item_table[17],[4]=item_table[18]},},
{grade=10,reward_item={[0]=item_table[59],[1]=item_table[104],[2]=item_table[21],[3]=item_table[22],[4]=item_table[23]},},
{grade=10,reward_item={[0]=item_table[61],[1]=item_table[105],[2]=item_table[26],[3]=item_table[27],[4]=item_table[28]},},
{grade=10,reward_item={[0]=item_table[63],[1]=item_table[106],[2]=item_table[31],[3]=item_table[32],[4]=item_table[33]},},
{grade=10,reward_item={[0]=item_table[65],[1]=item_table[107],[2]=item_table[36],[3]=item_table[37],[4]=item_table[38]},},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=14,},
{grade=14,},
{grade=14,},
{grade=14,},
{grade=14,},
{grade=14,},
{grade=14,},
{grade=14,},
{grade=14,},
{grade=14,},
{grade=15,},
{grade=15,},
{grade=15,},
{grade=15,},
{grade=15,},
{grade=15,},
{grade=15,},
{grade=15,},
{grade=15,},
{grade=15,},
{reward_item={[0]=item_table[39],[1]=item_table[108],[2]=item_table[109],[3]=item_table[110],[4]=item_table[111]},},
{ID=2,stage_value=1960,reward_item={[0]=item_table[39],[1]=item_table[112],[2]=item_table[113],[3]=item_table[114],[4]=item_table[43]},},
{ID=3,stage_value=3960,reward_item={[0]=item_table[1],[1]=item_table[40],[2]=item_table[115],[3]=item_table[116],[4]=item_table[5]},},
{ID=4,stage_value=6560,reward_item={[0]=item_table[1],[1]=item_table[117],[2]=item_table[118],[3]=item_table[119],[4]=item_table[9]},},
{ID=5,stage_value=12960,reward_item={[0]=item_table[10],[1]=item_table[120],[2]=item_table[121],[3]=item_table[122],[4]=item_table[14]},},
{ID=6,stage_value=20000,reward_item={[0]=item_table[10],[1]=item_table[123],[2]=item_table[124],[3]=item_table[125],[4]=item_table[18]},},
{ID=7,stage_value=60000,reward_item={[0]=item_table[19],[1]=item_table[126],[2]=item_table[127],[3]=item_table[128],[4]=item_table[23]},},
{ID=8,stage_value=120000,reward_item={[0]=item_table[24],[1]=item_table[129],[2]=item_table[130],[3]=item_table[128],[4]=item_table[28]},},
{ID=9,stage_value=200000,reward_item={[0]=item_table[29],[1]=item_table[131],[2]=item_table[132],[3]=item_table[133],[4]=item_table[33]},},
{ID=10,stage_value=300000,reward_item={[0]=item_table[34],[1]=item_table[134],[2]=item_table[135],[3]=item_table[133],[4]=item_table[38]},},
{ID=11,stage_value=600000,reward_item={[0]=item_table[136],[1]=item_table[137],[2]=item_table[138],[3]=item_table[139],[4]=item_table[140]},},
{grade=42,},
{grade=42,},
{grade=42,},
{grade=42,},
{grade=42,},
{grade=42,},
{grade=42,},
{grade=42,},
{grade=42,},
{grade=42,},
{grade=42,},
{grade=43,},
{grade=43,},
{grade=43,},
{grade=43,},
{grade=43,},
{grade=43,},
{grade=43,},
{grade=43,},
{grade=43,reward_item={[0]=item_table[75],[1]=item_table[141],[2]=item_table[132],[3]=item_table[133],[4]=item_table[33]},},
{grade=43,reward_item={[0]=item_table[77],[1]=item_table[142],[2]=item_table[135],[3]=item_table[133],[4]=item_table[38]},},
{grade=43,reward_item={[0]=item_table[143],[1]=item_table[144],[2]=item_table[138],[3]=item_table[139],[4]=item_table[140]},},
{grade=44,},
{grade=44,},
{grade=44,},
{grade=44,reward_item={[0]=item_table[1],[1]=item_table[145],[2]=item_table[118],[3]=item_table[119],[4]=item_table[9]},},
{grade=44,reward_item={[0]=item_table[10],[1]=item_table[146],[2]=item_table[121],[3]=item_table[122],[4]=item_table[14]},},
{grade=44,reward_item={[0]=item_table[10],[1]=item_table[147],[2]=item_table[124],[3]=item_table[125],[4]=item_table[18]},},
{grade=44,reward_item={[0]=item_table[19],[1]=item_table[148],[2]=item_table[127],[3]=item_table[128],[4]=item_table[23]},},
{grade=44,reward_item={[0]=item_table[24],[1]=item_table[149],[2]=item_table[130],[3]=item_table[128],[4]=item_table[28]},},
{grade=44,reward_item={[0]=item_table[29],[1]=item_table[150],[2]=item_table[132],[3]=item_table[133],[4]=item_table[33]},},
{grade=44,reward_item={[0]=item_table[34],[1]=item_table[151],[2]=item_table[135],[3]=item_table[133],[4]=item_table[38]},},
{grade=44,reward_item={[0]=item_table[136],[1]=item_table[152],[2]=item_table[138],[3]=item_table[139],[4]=item_table[140]},},
{grade=45,},
{grade=45,},
{grade=45,},
{grade=45,reward_item={[0]=item_table[1],[1]=item_table[153],[2]=item_table[118],[3]=item_table[119],[4]=item_table[9]},},
{grade=45,reward_item={[0]=item_table[10],[1]=item_table[154],[2]=item_table[121],[3]=item_table[122],[4]=item_table[14]},},
{grade=45,reward_item={[0]=item_table[10],[1]=item_table[155],[2]=item_table[124],[3]=item_table[125],[4]=item_table[18]},},
{grade=45,reward_item={[0]=item_table[59],[1]=item_table[156],[2]=item_table[127],[3]=item_table[128],[4]=item_table[23]},},
{grade=45,reward_item={[0]=item_table[61],[1]=item_table[157],[2]=item_table[130],[3]=item_table[128],[4]=item_table[28]},},
{grade=45,reward_item={[0]=item_table[63],[1]=item_table[158],[2]=item_table[132],[3]=item_table[133],[4]=item_table[33]},},
{grade=45,reward_item={[0]=item_table[65],[1]=item_table[159],[2]=item_table[135],[3]=item_table[133],[4]=item_table[38]},},
{grade=45,reward_item={[0]=item_table[160],[1]=item_table[161],[2]=item_table[138],[3]=item_table[139],[4]=item_table[140]},},
{grade=46,},
{grade=46,},
{grade=46,},
{grade=46,},
{grade=46,},
{grade=46,},
{grade=46,reward_item={[0]=item_table[71],[1]=item_table[126],[2]=item_table[127],[3]=item_table[128],[4]=item_table[23]},},
{grade=46,reward_item={[0]=item_table[73],[1]=item_table[129],[2]=item_table[130],[3]=item_table[128],[4]=item_table[28]},},
{grade=46,reward_item={[0]=item_table[75],[1]=item_table[131],[2]=item_table[132],[3]=item_table[133],[4]=item_table[33]},},
{grade=46,reward_item={[0]=item_table[77],[1]=item_table[134],[2]=item_table[135],[3]=item_table[133],[4]=item_table[38]},},
{grade=46,reward_item={[0]=item_table[143],[1]=item_table[137],[2]=item_table[138],[3]=item_table[139],[4]=item_table[140]},},
{grade=47,},
{grade=47,},
{grade=47,},
{grade=47,reward_item={[0]=item_table[1],[1]=item_table[162],[2]=item_table[118],[3]=item_table[119],[4]=item_table[9]},},
{grade=47,reward_item={[0]=item_table[10],[1]=item_table[163],[2]=item_table[121],[3]=item_table[122],[4]=item_table[14]},},
{grade=47,reward_item={[0]=item_table[10],[1]=item_table[164],[2]=item_table[124],[3]=item_table[125],[4]=item_table[18]},},
{grade=47,reward_item={[0]=item_table[19],[1]=item_table[165],[2]=item_table[127],[3]=item_table[128],[4]=item_table[23]},},
{grade=47,reward_item={[0]=item_table[24],[1]=item_table[166],[2]=item_table[130],[3]=item_table[128],[4]=item_table[28]},},
{grade=47,reward_item={[0]=item_table[29],[1]=item_table[167],[2]=item_table[132],[3]=item_table[133],[4]=item_table[33]},},
{grade=47,reward_item={[0]=item_table[34],[1]=item_table[168],[2]=item_table[135],[3]=item_table[133],[4]=item_table[38]},},
{grade=47,reward_item={[0]=item_table[136],[1]=item_table[169],[2]=item_table[138],[3]=item_table[139],[4]=item_table[140]},},
{grade=48,},
{grade=48,},
{grade=48,},
{grade=48,reward_item={[0]=item_table[1],[1]=item_table[170],[2]=item_table[118],[3]=item_table[119],[4]=item_table[9]},},
{grade=48,reward_item={[0]=item_table[10],[1]=item_table[171],[2]=item_table[121],[3]=item_table[122],[4]=item_table[14]},},
{grade=48,reward_item={[0]=item_table[10],[1]=item_table[172],[2]=item_table[124],[3]=item_table[125],[4]=item_table[18]},},
{grade=48,reward_item={[0]=item_table[59],[1]=item_table[173],[2]=item_table[127],[3]=item_table[128],[4]=item_table[23]},},
{grade=48,reward_item={[0]=item_table[61],[1]=item_table[174],[2]=item_table[130],[3]=item_table[128],[4]=item_table[28]},},
{grade=48,reward_item={[0]=item_table[63],[1]=item_table[175],[2]=item_table[132],[3]=item_table[133],[4]=item_table[33]},},
{grade=48,reward_item={[0]=item_table[65],[1]=item_table[176],[2]=item_table[135],[3]=item_table[133],[4]=item_table[38]},},
{grade=48,reward_item={[0]=item_table[160],[1]=item_table[177],[2]=item_table[138],[3]=item_table[139],[4]=item_table[140]},},
{grade=49,},
{grade=49,},
{grade=49,},
{grade=49,reward_item={[0]=item_table[1],[1]=item_table[178],[2]=item_table[118],[3]=item_table[119],[4]=item_table[9]},},
{grade=49,reward_item={[0]=item_table[10],[1]=item_table[179],[2]=item_table[121],[3]=item_table[122],[4]=item_table[14]},},
{grade=49,reward_item={[0]=item_table[10],[1]=item_table[180],[2]=item_table[124],[3]=item_table[125],[4]=item_table[18]},},
{grade=49,reward_item={[0]=item_table[71],[1]=item_table[181],[2]=item_table[127],[3]=item_table[128],[4]=item_table[23]},},
{grade=49,reward_item={[0]=item_table[73],[1]=item_table[182],[2]=item_table[130],[3]=item_table[128],[4]=item_table[28]},},
{grade=49,reward_item={[0]=item_table[75],[1]=item_table[183],[2]=item_table[132],[3]=item_table[133],[4]=item_table[33]},},
{grade=49,reward_item={[0]=item_table[77],[1]=item_table[184],[2]=item_table[135],[3]=item_table[133],[4]=item_table[38]},},
{grade=49,reward_item={[0]=item_table[143],[1]=item_table[185],[2]=item_table[138],[3]=item_table[139],[4]=item_table[140]},},
{grade=50,},
{grade=50,},
{grade=50,},
{grade=50,},
{grade=50,},
{grade=50,},
{grade=50,},
{grade=50,},
{grade=50,},
{grade=50,},
{grade=50,},
{grade=51,},
{grade=51,},
{grade=51,},
{grade=51,reward_item={[0]=item_table[1],[1]=item_table[186],[2]=item_table[118],[3]=item_table[119],[4]=item_table[9]},},
{grade=51,reward_item={[0]=item_table[10],[1]=item_table[187],[2]=item_table[121],[3]=item_table[122],[4]=item_table[14]},},
{grade=51,reward_item={[0]=item_table[10],[1]=item_table[188],[2]=item_table[124],[3]=item_table[125],[4]=item_table[18]},},
{grade=51,reward_item={[0]=item_table[59],[1]=item_table[189],[2]=item_table[127],[3]=item_table[128],[4]=item_table[23]},},
{grade=51,reward_item={[0]=item_table[61],[1]=item_table[190],[2]=item_table[130],[3]=item_table[128],[4]=item_table[28]},},
{grade=51,reward_item={[0]=item_table[63],[1]=item_table[191],[2]=item_table[132],[3]=item_table[133],[4]=item_table[33]},},
{grade=51,reward_item={[0]=item_table[65],[1]=item_table[192],[2]=item_table[135],[3]=item_table[133],[4]=item_table[38]},},
{grade=51,reward_item={[0]=item_table[160],[1]=item_table[193],[2]=item_table[138],[3]=item_table[139],[4]=item_table[140]},},
{grade=52,},
{grade=52,},
{grade=52,},
{grade=52,reward_item={[0]=item_table[1],[1]=item_table[194],[2]=item_table[118],[3]=item_table[119],[4]=item_table[9]},},
{grade=52,reward_item={[0]=item_table[10],[1]=item_table[195],[2]=item_table[121],[3]=item_table[122],[4]=item_table[14]},},
{grade=52,reward_item={[0]=item_table[10],[1]=item_table[196],[2]=item_table[124],[3]=item_table[125],[4]=item_table[18]},},
{grade=52,reward_item={[0]=item_table[71],[1]=item_table[197],[2]=item_table[127],[3]=item_table[128],[4]=item_table[23]},},
{grade=52,reward_item={[0]=item_table[73],[1]=item_table[198],[2]=item_table[130],[3]=item_table[128],[4]=item_table[28]},},
{grade=52,},
{grade=52,},
{grade=52,},
{grade=53,},
{grade=53,},
{grade=53,},
{grade=53,},
{grade=53,},
{grade=53,},
{grade=53,},
{grade=53,},
{grade=53,},
{grade=53,},
{grade=53,},
{grade=54,},
{grade=54,},
{grade=54,},
{grade=54,},
{grade=54,},
{grade=54,},
{grade=54,},
{grade=54,},
{grade=54,},
{grade=54,},
{grade=54,},
{grade=55,},
{grade=55,},
{grade=55,},
{grade=55,},
{grade=55,},
{grade=55,},
{grade=55,},
{grade=55,},
{grade=55,},
{grade=55,},
{grade=55,},
{grade=56,},
{grade=56,},
{grade=56,},
{grade=56,},
{grade=56,},
{grade=56,},
{grade=56,},
{grade=56,},
{grade=56,},
{grade=56,},
{grade=56,}
},

reward_meta_table_map={
[326]=161,	-- depth:1
[315]=326,	-- depth:2
[304]=315,	-- depth:3
[293]=304,	-- depth:4
[282]=293,	-- depth:5
[271]=282,	-- depth:6
[260]=271,	-- depth:7
[249]=260,	-- depth:8
[238]=249,	-- depth:9
[227]=238,	-- depth:10
[216]=227,	-- depth:11
[205]=216,	-- depth:12
[172]=205,	-- depth:13
[194]=172,	-- depth:14
[183]=194,	-- depth:15
[43]=163,	-- depth:1
[242]=165,	-- depth:1
[241]=164,	-- depth:1
[240]=163,	-- depth:1
[239]=162,	-- depth:1
[10]=170,	-- depth:1
[237]=171,	-- depth:1
[243]=166,	-- depth:1
[236]=170,	-- depth:1
[234]=168,	-- depth:1
[233]=167,	-- depth:1
[232]=166,	-- depth:1
[231]=165,	-- depth:1
[230]=164,	-- depth:1
[229]=240,	-- depth:2
[235]=169,	-- depth:1
[228]=239,	-- depth:2
[244]=167,	-- depth:1
[246]=169,	-- depth:1
[8]=168,	-- depth:1
[259]=171,	-- depth:1
[258]=170,	-- depth:1
[257]=169,	-- depth:1
[256]=168,	-- depth:1
[255]=167,	-- depth:1
[245]=168,	-- depth:1
[254]=166,	-- depth:1
[252]=164,	-- depth:1
[251]=229,	-- depth:3
[250]=228,	-- depth:3
[9]=169,	-- depth:1
[248]=171,	-- depth:1
[247]=170,	-- depth:1
[253]=165,	-- depth:1
[12]=162,	-- depth:1
[226]=171,	-- depth:1
[225]=170,	-- depth:1
[14]=164,	-- depth:1
[204]=171,	-- depth:1
[203]=170,	-- depth:1
[202]=169,	-- depth:1
[201]=168,	-- depth:1
[200]=167,	-- depth:1
[206]=250,	-- depth:4
[199]=166,	-- depth:1
[197]=164,	-- depth:1
[196]=251,	-- depth:4
[195]=206,	-- depth:5
[15]=165,	-- depth:1
[193]=171,	-- depth:1
[192]=170,	-- depth:1
[198]=165,	-- depth:1
[207]=196,	-- depth:5
[208]=164,	-- depth:1
[209]=165,	-- depth:1
[224]=169,	-- depth:1
[223]=168,	-- depth:1
[222]=167,	-- depth:1
[221]=166,	-- depth:1
[220]=165,	-- depth:1
[219]=164,	-- depth:1
[218]=207,	-- depth:6
[217]=195,	-- depth:6
[13]=163,	-- depth:1
[215]=171,	-- depth:1
[214]=170,	-- depth:1
[213]=169,	-- depth:1
[212]=168,	-- depth:1
[211]=167,	-- depth:1
[210]=166,	-- depth:1
[261]=217,	-- depth:7
[262]=218,	-- depth:7
[263]=219,	-- depth:2
[264]=220,	-- depth:2
[3]=163,	-- depth:1
[314]=215,	-- depth:2
[313]=214,	-- depth:2
[312]=213,	-- depth:2
[311]=212,	-- depth:2
[310]=211,	-- depth:2
[316]=261,	-- depth:8
[309]=210,	-- depth:2
[307]=208,	-- depth:2
[306]=262,	-- depth:8
[305]=316,	-- depth:9
[4]=164,	-- depth:1
[303]=204,	-- depth:2
[302]=203,	-- depth:2
[308]=209,	-- depth:2
[317]=306,	-- depth:9
[318]=263,	-- depth:3
[319]=264,	-- depth:3
[334]=235,	-- depth:2
[333]=234,	-- depth:2
[332]=233,	-- depth:2
[331]=232,	-- depth:2
[330]=231,	-- depth:2
[329]=230,	-- depth:2
[328]=317,	-- depth:10
[327]=305,	-- depth:10
[2]=12,	-- depth:2
[325]=226,	-- depth:2
[324]=225,	-- depth:2
[323]=224,	-- depth:2
[322]=223,	-- depth:2
[321]=222,	-- depth:2
[320]=221,	-- depth:2
[301]=202,	-- depth:2
[191]=169,	-- depth:1
[300]=201,	-- depth:2
[298]=199,	-- depth:2
[278]=168,	-- depth:1
[277]=167,	-- depth:1
[276]=166,	-- depth:1
[275]=165,	-- depth:1
[274]=164,	-- depth:1
[273]=328,	-- depth:11
[279]=169,	-- depth:1
[272]=327,	-- depth:11
[270]=171,	-- depth:1
[269]=170,	-- depth:1
[268]=169,	-- depth:1
[267]=168,	-- depth:1
[266]=167,	-- depth:1
[265]=320,	-- depth:3
[7]=167,	-- depth:1
[280]=170,	-- depth:1
[281]=171,	-- depth:1
[6]=166,	-- depth:1
[297]=198,	-- depth:2
[296]=197,	-- depth:2
[295]=273,	-- depth:12
[294]=272,	-- depth:12
[5]=165,	-- depth:1
[292]=193,	-- depth:2
[291]=192,	-- depth:2
[290]=191,	-- depth:2
[289]=168,	-- depth:1
[288]=167,	-- depth:1
[287]=166,	-- depth:1
[286]=165,	-- depth:1
[285]=164,	-- depth:1
[284]=295,	-- depth:13
[283]=294,	-- depth:13
[299]=200,	-- depth:2
[190]=289,	-- depth:2
[189]=288,	-- depth:2
[188]=287,	-- depth:2
[94]=4,	-- depth:2
[93]=3,	-- depth:2
[92]=2,	-- depth:3
[36]=166,	-- depth:1
[90]=170,	-- depth:1
[89]=169,	-- depth:1
[95]=5,	-- depth:2
[88]=168,	-- depth:1
[86]=166,	-- depth:1
[85]=165,	-- depth:1
[84]=164,	-- depth:1
[83]=163,	-- depth:1
[82]=92,	-- depth:4
[37]=167,	-- depth:1
[96]=6,	-- depth:2
[97]=7,	-- depth:2
[98]=8,	-- depth:2
[113]=83,	-- depth:2
[112]=82,	-- depth:5
[34]=164,	-- depth:1
[110]=170,	-- depth:1
[109]=169,	-- depth:1
[108]=168,	-- depth:1
[107]=167,	-- depth:1
[106]=166,	-- depth:1
[105]=15,	-- depth:2
[104]=14,	-- depth:2
[103]=13,	-- depth:2
[102]=112,	-- depth:6
[35]=165,	-- depth:1
[100]=10,	-- depth:2
[99]=9,	-- depth:2
[80]=170,	-- depth:1
[114]=84,	-- depth:2
[79]=169,	-- depth:1
[77]=167,	-- depth:1
[57]=87,	-- depth:1
[56]=166,	-- depth:1
[55]=165,	-- depth:1
[54]=164,	-- depth:1
[53]=163,	-- depth:1
[52]=102,	-- depth:7
[58]=168,	-- depth:1
[40]=170,	-- depth:1
[49]=169,	-- depth:1
[48]=168,	-- depth:1
[47]=167,	-- depth:1
[46]=166,	-- depth:1
[45]=165,	-- depth:1
[44]=164,	-- depth:1
[50]=170,	-- depth:1
[59]=169,	-- depth:1
[60]=170,	-- depth:1
[39]=169,	-- depth:1
[76]=166,	-- depth:1
[75]=165,	-- depth:1
[74]=164,	-- depth:1
[73]=163,	-- depth:1
[72]=52,	-- depth:8
[38]=168,	-- depth:1
[70]=170,	-- depth:1
[69]=169,	-- depth:1
[68]=168,	-- depth:1
[67]=167,	-- depth:1
[66]=166,	-- depth:1
[65]=165,	-- depth:1
[64]=164,	-- depth:1
[63]=163,	-- depth:1
[62]=72,	-- depth:9
[78]=168,	-- depth:1
[42]=62,	-- depth:10
[115]=85,	-- depth:2
[117]=87,	-- depth:1
[335]=236,	-- depth:2
[22]=42,	-- depth:11
[23]=113,	-- depth:3
[24]=114,	-- depth:3
[25]=115,	-- depth:3
[26]=86,	-- depth:2
[20]=110,	-- depth:2
[27]=117,	-- depth:2
[160]=100,	-- depth:3
[159]=99,	-- depth:3
[158]=98,	-- depth:3
[157]=97,	-- depth:3
[156]=96,	-- depth:3
[155]=95,	-- depth:3
[28]=88,	-- depth:2
[19]=109,	-- depth:2
[18]=108,	-- depth:2
[17]=107,	-- depth:2
[187]=286,	-- depth:2
[186]=285,	-- depth:2
[185]=284,	-- depth:14
[184]=283,	-- depth:14
[16]=106,	-- depth:2
[182]=281,	-- depth:2
[181]=280,	-- depth:2
[180]=279,	-- depth:2
[179]=278,	-- depth:2
[178]=277,	-- depth:2
[177]=276,	-- depth:2
[176]=275,	-- depth:2
[175]=274,	-- depth:2
[174]=185,	-- depth:15
[173]=184,	-- depth:15
[154]=94,	-- depth:3
[116]=26,	-- depth:3
[153]=93,	-- depth:3
[29]=89,	-- depth:2
[32]=22,	-- depth:12
[130]=160,	-- depth:4
[129]=159,	-- depth:4
[128]=158,	-- depth:4
[127]=157,	-- depth:4
[126]=156,	-- depth:4
[132]=32,	-- depth:13
[125]=155,	-- depth:4
[123]=153,	-- depth:4
[122]=132,	-- depth:14
[33]=163,	-- depth:1
[120]=90,	-- depth:2
[119]=29,	-- depth:3
[118]=28,	-- depth:3
[124]=154,	-- depth:4
[133]=103,	-- depth:3
[134]=104,	-- depth:3
[135]=105,	-- depth:3
[150]=120,	-- depth:3
[149]=119,	-- depth:4
[148]=118,	-- depth:4
[147]=27,	-- depth:3
[146]=116,	-- depth:4
[145]=25,	-- depth:4
[144]=24,	-- depth:4
[143]=23,	-- depth:4
[142]=122,	-- depth:15
[30]=150,	-- depth:4
[140]=20,	-- depth:3
[139]=19,	-- depth:3
[138]=18,	-- depth:3
[137]=17,	-- depth:3
[136]=16,	-- depth:3
[152]=142,	-- depth:16
[336]=237,	-- depth:2
},
interface={
{reward_item={[0]=item_table[39],[1]=item_table[199],[2]=item_table[200],[3]=item_table[200]},jindu_limit="720|12960|120000|500000",},
{interface=1,reward_item={[0]=item_table[39],[1]=item_table[201],[2]=item_table[200],[3]=item_table[200]},},
{interface=2,reward_item={[0]=item_table[39],[1]=item_table[202],[2]=item_table[200],[3]=item_table[200]},},
{interface=3,reward_item={[0]=item_table[39],[1]=item_table[203],[2]=item_table[200],[3]=item_table[200]},},
{interface=4,reward_item={[0]=item_table[39],[1]=item_table[204],[2]=item_table[200],[3]=item_table[200]},},
{interface=5,reward_item={[0]=item_table[39],[1]=item_table[205],[2]=item_table[200],[3]=item_table[200]},},
{interface=6,reward_item={[0]=item_table[39],[1]=item_table[206],[2]=item_table[200],[3]=item_table[200]},},
{interface=7,reward_item={[0]=item_table[39],[1]=item_table[207],[2]=item_table[200],[3]=item_table[200]},},
{interface=8,reward_item={[0]=item_table[39],[1]=item_table[208],[2]=item_table[200],[3]=item_table[200]},},
{interface=9,},
{interface=10,},
{interface=11,},
{interface=12,},
{interface=13,},
{interface=14,},
{interface=15,pic_1="total_dizuo_1",pic_10="total_reward_bg2",jindu="cslc_jindu_2",list_bg="cslc_04_1",},
{interface=41,reward_item={[0]=item_table[39],[1]=item_table[209],[2]=item_table[210],[3]=item_table[211]},},
{interface=42,reward_item={[0]=item_table[39],[1]=item_table[212],[2]=item_table[200],[3]=item_table[200]},},
{interface=43,reward_item={[0]=item_table[39],[1]=item_table[213],[2]=item_table[210],[3]=item_table[211]},},
{interface=44,reward_item={[0]=item_table[39],[1]=item_table[214],[2]=item_table[200],[3]=item_table[200]},},
{interface=45,reward_item={[0]=item_table[39],[1]=item_table[215],[2]=item_table[210],[3]=item_table[211]},},
{interface=46,reward_item={[0]=item_table[39],[1]=item_table[216],[2]=item_table[200],[3]=item_table[200]},},
{interface=47,reward_item={[0]=item_table[39],[1]=item_table[217],[2]=item_table[210],[3]=item_table[211]},},
{interface=48,reward_item={[0]=item_table[39],[1]=item_table[218],[2]=item_table[200],[3]=item_table[200]},},
{interface=49,reward_item={[0]=item_table[39],[1]=item_table[219],[2]=item_table[200],[3]=item_table[200]},},
{interface=50,reward_item={[0]=item_table[39],[1]=item_table[220],[2]=item_table[200],[3]=item_table[200]},},
{interface=51,},
{interface=52,},
{interface=53,reward_item={[0]=item_table[39],[1]=item_table[220],[2]=item_table[200],[3]=item_table[200]},},
{interface=54,},
{interface=55,},
{interface=56,}
},

interface_meta_table_map={
[27]=29,	-- depth:1
[31]=27,	-- depth:2
[12]=16,	-- depth:1
[10]=12,	-- depth:2
[14]=10,	-- depth:3
[18]=16,	-- depth:1
[20]=16,	-- depth:1
[22]=16,	-- depth:1
[8]=16,	-- depth:1
[24]=16,	-- depth:1
[6]=16,	-- depth:1
[26]=16,	-- depth:1
[4]=16,	-- depth:1
[28]=26,	-- depth:2
[2]=16,	-- depth:1
[30]=28,	-- depth:3
[32]=30,	-- depth:4
},
model={
{},
{id=7,model_show_itemid=38103,},
{id=10,model_show_itemid=37712,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=14,},
{grade=14,},
{grade=14,},
{grade=15,},
{grade=15,},
{grade=15,},
{grade=41,},
{grade=41,},
{grade=41,},
{grade=42,},
{grade=42,},
{grade=42,},
{grade=43,},
{grade=43,},
{grade=43,},
{grade=44,},
{grade=44,},
{grade=44,},
{grade=45,},
{grade=45,},
{grade=45,},
{grade=46,},
{grade=46,},
{grade=46,},
{grade=47,},
{grade=47,},
{grade=47,},
{grade=48,},
{grade=48,},
{grade=48,},
{grade=49,},
{grade=49,},
{grade=49,},
{grade=50,},
{grade=50,},
{grade=50,},
{grade=51,},
{grade=51,},
{grade=51,},
{grade=52,},
{grade=52,},
{grade=52,},
{grade=53,},
{grade=53,},
{grade=53,},
{grade=54,},
{grade=54,},
{grade=54,},
{grade=55,},
{grade=55,},
{grade=55,},
{grade=56,},
{grade=56,},
{grade=56,}
},

model_meta_table_map={
[63]=3,	-- depth:1
[68]=2,	-- depth:1
[93]=63,	-- depth:2
[65]=68,	-- depth:2
[66]=93,	-- depth:3
[92]=65,	-- depth:3
[60]=66,	-- depth:4
[62]=92,	-- depth:4
[84]=60,	-- depth:5
[90]=84,	-- depth:6
[83]=62,	-- depth:5
[71]=83,	-- depth:6
[72]=90,	-- depth:7
[89]=71,	-- depth:7
[74]=89,	-- depth:8
[75]=72,	-- depth:8
[87]=75,	-- depth:9
[77]=74,	-- depth:9
[59]=77,	-- depth:10
[86]=59,	-- depth:11
[80]=86,	-- depth:12
[81]=87,	-- depth:10
[69]=81,	-- depth:11
[78]=69,	-- depth:12
[48]=78,	-- depth:13
[56]=80,	-- depth:13
[26]=56,	-- depth:14
[24]=48,	-- depth:14
[23]=26,	-- depth:15
[21]=24,	-- depth:15
[20]=23,	-- depth:16
[18]=21,	-- depth:16
[17]=20,	-- depth:17
[15]=18,	-- depth:17
[14]=17,	-- depth:18
[12]=15,	-- depth:18
[11]=14,	-- depth:19
[9]=12,	-- depth:19
[8]=11,	-- depth:20
[6]=9,	-- depth:20
[5]=8,	-- depth:21
[27]=6,	-- depth:21
[57]=27,	-- depth:22
[29]=5,	-- depth:22
[32]=29,	-- depth:23
[54]=57,	-- depth:23
[53]=32,	-- depth:24
[51]=54,	-- depth:24
[50]=53,	-- depth:25
[95]=50,	-- depth:26
[47]=95,	-- depth:27
[45]=51,	-- depth:25
[44]=47,	-- depth:28
[42]=45,	-- depth:26
[41]=44,	-- depth:29
[39]=42,	-- depth:27
[38]=41,	-- depth:30
[36]=39,	-- depth:28
[35]=38,	-- depth:31
[33]=36,	-- depth:29
[30]=33,	-- depth:30
[96]=30,	-- depth:31
},
config_param_default_table={start_server_day=10,end_server_day=17,week_index=3,grade=15,interface=15,open_level=100,},

reward_default_table={grade=41,ID=1,stage_value=720,reward_item={[0]=item_table[39],[1]=item_table[108],[2]=item_table[221],[3]=item_table[222],[4]=item_table[111]},special_frame=0,special_content=0,},

interface_default_table={interface=0,effect_1="",pic_1="total_dizuo1",pic_2="total_kongbai",pic_3="total_left_wenzidi",pic_4="total_wenan",pic_5="total_xuanchuandi1",pic_6="total_fanlidi",pic_7="total_biaoqian",pic_8="total_jinshukuang",pic_9="total_reward_bg",pic_10="total_reward_bg1",rule_1="累充越多，奖励越丰厚！",rule_2="1.活动期间<color=#95d12b>充值到指定额度</color>可免费领取豪华大礼，时不我待，不容错过！\n2.活动结束后未及时领取的奖励通过<color=#95d12b>邮件</color>发放",model_name="珍稀形象",render_type=1,render_int_param1=0,render_string_param1="",render_string_param2="",mw_scale=0.8,mw_position="270|-200|0",reward_position="-138|120#-48|185#42|185#132|120",reward_item={[0]=item_table[39],[1]=item_table[223],[2]=item_table[200],[3]=item_table[200]},jindu_limit="720|12960|120000|400000",jindu="cslc_jindu_1",list_bg="cslc_04",},

model_default_table={grade=0,id=5,model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid=37233,model_pos="-220|0|0",model_rot="0|0|0",model_scale=1,}

}

