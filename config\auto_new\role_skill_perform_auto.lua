-- J-角色技能展示.xls

return {
full_show={
[736]={skill_id=736,},
[737]={skill_id=737,},
[738]={skill_id=738,},
[2101]={skill_id=2101,},
[2102]={skill_id=2102,},
[2103]={skill_id=2103,},
[727]={skill_id=727,},
[728]={skill_id=728,},
[729]={skill_id=729,},
[2121]={skill_id=2121,},
[2122]={skill_id=2122,},
[2123]={skill_id=2123,},
[739]={skill_id=739,},
[740]={skill_id=740,},
[741]={skill_id=741,},
[2131]={skill_id=2131,},
[2132]={skill_id=2132,},
[2133]={skill_id=2133,},
[703]={skill_id=703,},
[704]={skill_id=704,},
[705]={skill_id=705,},
[2011]={skill_id=2011,},
[2012]={skill_id=2012,},
[2013]={skill_id=2013,},
[718]={skill_id=718,},
[719]={skill_id=719,},
[720]={skill_id=720,},
[2051]={skill_id=2051,},
[2052]={skill_id=2052,},
[2053]={skill_id=2053,},
[742]={skill_id=742,},
[743]={skill_id=743,},
[744]={skill_id=744,},
[2141]={skill_id=2141,},
[2142]={skill_id=2142,},
[2143]={skill_id=2143,camera_field=36,},
[712]={skill_id=712,},
[713]={skill_id=713,},
[714]={skill_id=714,},
[2043]={skill_id=2043,},
[2042]={skill_id=2042,},
[2041]={skill_id=2041,},
[700]={skill_id=700,},
[701]={skill_id=701,},
[702]={skill_id=702,camera_field=24,},
[2001]={skill_id=2001,},
[2002]={skill_id=2002,},
[2003]={skill_id=2003,},
[715]={skill_id=715,},
[716]={skill_id=716,},
[717]={skill_id=717,},
[2061]={skill_id=2061,},
[2062]={skill_id=2062,},
[2063]={skill_id=2063,},
[709]={skill_id=709,},
[710]={skill_id=710,},
[711]={skill_id=711,},
[2031]={skill_id=2031,},
[2032]={skill_id=2032,},
[2033]={skill_id=2033,},
[721]={skill_id=721,camera_field=28,},
[722]={skill_id=722,},
[723]={skill_id=723,},
[2072]={skill_id=2072,camera_field=26,},
[2071]={skill_id=2071,},
[2073]={skill_id=2073,},
[733]={skill_id=733,},
[734]={skill_id=734,},
[735]={skill_id=735,},
[2151]={skill_id=2151,},
[2152]={skill_id=2152,},
[2153]={skill_id=2153,},
[724]={skill_id=724,},
[725]={skill_id=725,},
[726]={skill_id=726,},
[2091]={skill_id=2091,},
[2092]={skill_id=2092,},
[2093]={skill_id=2093,enemy_pos_x=-15,},
[730]={skill_id=730,},
[731]={skill_id=731,},
[732]={skill_id=732,},
[2111]={skill_id=2111,},
[2112]={skill_id=2112,},
[2113]={skill_id=2113,},
[706]={skill_id=706,},
[707]={skill_id=707,},
[708]={skill_id=708,camera_field=30,},
[2021]={skill_id=2021,},
[2022]={skill_id=2022,camera_field=45,},
[2023]={skill_id=2023,},
[745]={skill_id=745,},
[746]={skill_id=746,},
[747]={skill_id=747,},
[2161]={skill_id=2161,},
[2162]={skill_id=2162,},
[2163]={skill_id=2163,},
[6101]={skill_id=6101,},
[6201]={skill_id=6201,},
[6202]={skill_id=6202,},
[6203]={skill_id=6203,},
[6204]={skill_id=6204,camera_field=34,},
[4501]={skill_id=4501,},
[4502]={skill_id=4502,},
[4503]={skill_id=4503,camera_field=32,},
[4504]={skill_id=4504,},
[4505]={skill_id=4505,},
[4506]={skill_id=4506,},
[7100]={skill_id=7100,enemy_pos_x=-12,},
[7101]={skill_id=7101,},
[7102]={skill_id=7102,},
[7103]={skill_id=7103,},
[7104]={skill_id=7104,},
[6102]={skill_id=6102,},
[6205]={skill_id=6205,},
[6206]={skill_id=6206,},
[6207]={skill_id=6207,},
[6208]={skill_id=6208,},
[6103]={skill_id=6103,},
[6209]={skill_id=6209,},
[6210]={skill_id=6210,},
[6211]={skill_id=6211,},
[6212]={skill_id=6212,},
[6104]={skill_id=6104,},
[6213]={skill_id=6213,},
[6214]={skill_id=6214,},
[6215]={skill_id=6215,},
[6216]={skill_id=6216,},
[6105]={skill_id=6105,},
[6217]={skill_id=6217,},
[6218]={skill_id=6218,},
[6219]={skill_id=6219,},
[6220]={skill_id=6220,},
[7309]={skill_id=7309,},
[7310]={skill_id=7310,},
[7311]={skill_id=7311,},
[7312]={skill_id=7312,},
[7313]={skill_id=7313,},
[7314]={skill_id=7314,},
[7356]={skill_id=7356,},
[7357]={skill_id=7357,enemy_pos_x=-12,},
[7358]={skill_id=7358,},
[7359]={skill_id=7359,},
[7360]={skill_id=7360,},
[7361]={skill_id=7361,},
[7318]={skill_id=7318,},
[7319]={skill_id=7319,},
[7320]={skill_id=7320,camera_field=35,enemy_pos_x=-15,},
[7321]={skill_id=7321,camera_field=48,enemy_pos_x=-18,},
[7322]={skill_id=7322,},
[7323]={skill_id=7323,},
[7362]={skill_id=7362,},
[7363]={skill_id=7363,},
[7364]={skill_id=7364,},
[7365]={skill_id=7365,},
[7366]={skill_id=7366,},
[7367]={skill_id=7367,}
},

full_show_meta_table_map={
[4502]=4503,	-- depth:1
[4501]=4502,	-- depth:2
[6203]=6204,	-- depth:1
[6201]=6203,	-- depth:2
[6101]=6201,	-- depth:3
[2023]=2022,	-- depth:1
[4504]=4501,	-- depth:3
[2021]=2023,	-- depth:2
[707]=708,	-- depth:1
[706]=707,	-- depth:2
[6202]=6101,	-- depth:4
[4505]=4504,	-- depth:4
[6103]=6202,	-- depth:5
[6102]=6103,	-- depth:6
[6219]=6102,	-- depth:7
[6218]=6219,	-- depth:8
[6217]=6218,	-- depth:9
[6105]=6217,	-- depth:10
[6216]=6105,	-- depth:11
[6215]=6216,	-- depth:12
[6214]=6215,	-- depth:13
[6213]=6214,	-- depth:14
[4506]=4505,	-- depth:5
[6104]=6213,	-- depth:15
[6211]=6104,	-- depth:16
[6210]=6211,	-- depth:17
[6209]=6210,	-- depth:18
[2113]=2021,	-- depth:3
[6208]=6209,	-- depth:19
[6207]=6208,	-- depth:20
[6206]=6207,	-- depth:21
[6205]=6206,	-- depth:22
[6212]=6205,	-- depth:23
[2112]=2113,	-- depth:4
[730]=706,	-- depth:3
[732]=730,	-- depth:4
[2103]=2112,	-- depth:5
[2063]=732,	-- depth:5
[2062]=2063,	-- depth:6
[2111]=2103,	-- depth:6
[2061]=2062,	-- depth:7
[722]=721,	-- depth:1
[723]=722,	-- depth:2
[2071]=2072,	-- depth:1
[2073]=2061,	-- depth:8
[6220]=6212,	-- depth:24
[2151]=2073,	-- depth:9
[2102]=2111,	-- depth:7
[2152]=2151,	-- depth:10
[2121]=2102,	-- depth:8
[2003]=2152,	-- depth:11
[2002]=2003,	-- depth:12
[2122]=2121,	-- depth:9
[2123]=2122,	-- depth:10
[2041]=2071,	-- depth:2
[2042]=2002,	-- depth:13
[2043]=2042,	-- depth:14
[714]=2041,	-- depth:3
[2142]=2043,	-- depth:15
[731]=2142,	-- depth:16
[2153]=731,	-- depth:17
[2101]=2123,	-- depth:11
[7357]=708,	-- depth:1
[7312]=7357,	-- depth:2
[7322]=7321,	-- depth:1
[7323]=7322,	-- depth:2
[7362]=7320,	-- depth:1
[7363]=7362,	-- depth:2
[7319]=7363,	-- depth:3
[7365]=7323,	-- depth:3
[7318]=7319,	-- depth:4
[7360]=7312,	-- depth:3
[7359]=7360,	-- depth:4
[7358]=7359,	-- depth:5
[7364]=7318,	-- depth:5
[7356]=7358,	-- depth:6
[7314]=7356,	-- depth:7
[7313]=7314,	-- depth:8
[7361]=7313,	-- depth:9
[7311]=7361,	-- depth:10
[2093]=7321,	-- depth:1
[7309]=7311,	-- depth:11
[2032]=2093,	-- depth:2
[2033]=2032,	-- depth:3
[724]=2033,	-- depth:4
[725]=724,	-- depth:5
[726]=725,	-- depth:6
[2091]=726,	-- depth:7
[2092]=2091,	-- depth:8
[7366]=7365,	-- depth:4
[745]=2092,	-- depth:9
[746]=745,	-- depth:10
[747]=746,	-- depth:11
[2161]=747,	-- depth:12
[2162]=2161,	-- depth:13
[2163]=2162,	-- depth:14
[7100]=4503,	-- depth:1
[7101]=7100,	-- depth:2
[7102]=7101,	-- depth:3
[7103]=7102,	-- depth:4
[7104]=7103,	-- depth:5
[7310]=7309,	-- depth:12
[7367]=7366,	-- depth:5
},
boss_mabi={
{tab_index_list="1,2",},
{index=2,title="BOSS麻痹",desc="<color=#99ffbb>效果：</color>\n获得仙修之力后，在攻击魔罗时，会持续使用天雷之力，有几率使其进入<color=#fabf79>麻痹</color>状态，麻痹时将无法攻击，可有效减少受到伤害，是越级挑战的必备属性。\n<color=#99ffbb>获取：</color>\n前往仙修提升",attr_desc="boss麻痹",attr_name="boss_palsy_per",bg=1,},
{index=3,title="BOSS压制",desc="<color=#99ffbb>效果：</color>\n玩家当前等级高于魔罗当前等级时，系统自动判断压制，获得超强免伤增伤！但是，等级不足时，将会被魔罗压制，难以使用正常实力挑战魔罗！\n<color=#99ffbb>获取：</color>\n提升等级",attr_desc="角色等级",attr_name="level",tab_index_list="4,5",bg=3,},
{index=4,title="BOSS秒杀",desc="<color=#99ffbb>效果：</color>\n攻击魔罗时，有几率引缘天雷，对魔罗造成天罚。天罚对魔罗会造成绝对压制，使魔罗<color=#fabf79>立刻死亡</color>。\n<color=#99ffbb>获取：</color>\n前往仙修提升",attr_desc="boss秒杀",attr_name="boss_seckill_per",bg=4,}
},

boss_mabi_meta_table_map={
},
boss_mabi_tab={
{},
{id=2,title="气衍仙法",open_view="EsotericaView",icon=2,},
{id=3,title="气衍万劫",open_view="XiuWeiView",icon=3,},
{id=4,title="日月修行",open_view="fubenpanel#fubenpanel_exp",icon=4,},
{id=5,title="悬赏任务",open_view="bizuo",icon=5,}
},

boss_mabi_tab_meta_table_map={
},
full_show_default_table={skill_id=736,camera_field=22,shower_pos_x=0,enemy_pos_x=-7,},

boss_mabi_default_table={index=1,title="BOSS破盾",desc="<color=#99ffbb>效果：</color>\n攻击魔罗时可削弱魔罗的硬直条，硬直条被清零时，魔罗将进入<color=#fabf79>破防崩溃</color>状态，防御降低50%！此时是对魔罗造成巨量伤害的绝好时机。\n<color=#99ffbb>获取：</color>\n变身神灵可以大幅提升BOSS破盾效率",attr_desc="",attr_name="",tab_index_list=3,bg=2,},

boss_mabi_tab_default_table={id=1,title="化身诸神",open_view="TianShenView#10#uip=1",icon=1,}

}

