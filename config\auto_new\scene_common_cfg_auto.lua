-- C-场景通用配置表.xls
local item_table={
[1]={item_id=26416,num=1,is_bind=1},
[2]={item_id=44099,num=1,is_bind=1},
}

return {
scene_cfg_limit={
[105]={},
[149]={}
},

scene_cfg_limit_meta_table_map={
},
fb_combine={
{},
{type=1,},
{type=2,level_limit=1,},
{type=3,},
{type=4,},
{type=5,pre_show_level=9999,},
{type=6,},
{type=7,},
{type=8,},
{type=9,}
},

fb_combine_meta_table_map={
[5]=3,	-- depth:1
},
force_born={

},

force_born_meta_table_map={
},
boss_rebirth={
{}
},

boss_rebirth_meta_table_map={
},
boss_tired={
{},
{dec_tired_type=5,consume_item=23681,scene_type=121,desc="使用后须弥神域次数+1",},
{dec_tired_type=1,consume_item=22615,dec_value_limit=5,scene_type=75,desc="使用后伏魔战场次数+1",}
},

boss_tired_meta_table_map={
},
boss_invoke={
{}
},

boss_invoke_meta_table_map={
},
change_aoi={

},

change_aoi_meta_table_map={
},
fb_sweep={
{},
{type=51,},
{type=52,level_limit=20,pre_show_level=20,},
{type=32,level_limit=1,pre_show_level=1,}
},

fb_sweep_meta_table_map={
},
hanging_range={
{},
{scene_type=75,},
{scene_type=77,},
{scene_type=126,},
{scene_type=3,},
{scene_type=149,}
},

hanging_range_meta_table_map={
},
boss_rewards={
{},
{index=2,},
{index=3,},
{index=4,}
},

boss_rewards_meta_table_map={
},
scene_cfg_limit_default_table={},

fb_combine_default_table={type=0,level_limit=220,combine_once_gold=0,item=item_table[1],pre_show_level=1,},

force_born_default_table={},

boss_rebirth_default_table={rebirth_item=46041,rebirth_all_item_1=46043,rebirth_item_2=46042,rebirth_range=15,show_scene_type="75|53|77|121",rebirth_item_3=50048,},

boss_tired_default_table={dec_tired_type=2,consume_item=36366,dec_value=1,dec_value_limit=-1,scene_type=53,desc="使用后谪仙之境次数+1",},

boss_invoke_default_table={boss_invoke_card_1=46039,boss_invoke_card_2=46040,boss_invoke_range=15,boss_invoke_count=6,boss_invoke_open_day=3,},

change_aoi_default_table={},

fb_sweep_default_table={type=33,level_limit=220,need_gold=0,item=item_table[1],pre_show_level=220,},

hanging_range_default_table={scene_type=53,aoi_range=20,run_range=25,},

boss_rewards_default_table={index=1,reward_list={[0]=item_table[2]},reward_count=10,}

}

