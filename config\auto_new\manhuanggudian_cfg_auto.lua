-- F-副本-荒古墓.xls
local item_table={
[1]={item_id=29476,num=13,is_bind=1},
[2]={item_id=29476,num=3,is_bind=1},
[3]={item_id=29476,num=14,is_bind=1},
[4]={item_id=29476,num=15,is_bind=1},
[5]={item_id=29478,num=1,is_bind=1},
[6]={item_id=29476,num=16,is_bind=1},
[7]={item_id=29478,num=2,is_bind=1},
[8]={item_id=29476,num=17,is_bind=1},
[9]={item_id=29478,num=3,is_bind=1},
[10]={item_id=29476,num=18,is_bind=1},
[11]={item_id=29478,num=4,is_bind=1},
[12]={item_id=29476,num=19,is_bind=1},
[13]={item_id=29478,num=5,is_bind=1},
[14]={item_id=29476,num=20,is_bind=1},
[15]={item_id=29478,num=6,is_bind=1},
[16]={item_id=29476,num=4,is_bind=1},
[17]={item_id=29476,num=21,is_bind=1},
[18]={item_id=29478,num=7,is_bind=1},
[19]={item_id=29476,num=22,is_bind=1},
[20]={item_id=29478,num=8,is_bind=1},
[21]={item_id=29476,num=23,is_bind=1},
[22]={item_id=29476,num=5,is_bind=1},
[23]={item_id=29476,num=24,is_bind=1},
[24]={item_id=29478,num=9,is_bind=1},
[25]={item_id=29476,num=6,is_bind=1},
[26]={item_id=29476,num=12,is_bind=1},
[27]={item_id=29478,num=11,is_bind=1},
[28]={item_id=29478,num=12,is_bind=1},
[29]={item_id=29476,num=34,is_bind=1},
[30]={item_id=29478,num=16,is_bind=1},
[31]={item_id=29476,num=25,is_bind=1},
[32]={item_id=29478,num=10,is_bind=1},
[33]={item_id=29476,num=27,is_bind=1},
[34]={item_id=29476,num=31,is_bind=1},
[35]={item_id=29478,num=18,is_bind=1},
[36]={item_id=29476,num=26,is_bind=1},
[37]={item_id=29476,num=36,is_bind=1},
[38]={item_id=29478,num=15,is_bind=1},
[39]={item_id=29476,num=40,is_bind=1},
[40]={item_id=29476,num=45,is_bind=1},
[41]={item_id=29476,num=49,is_bind=1},
[42]={item_id=29478,num=23,is_bind=1},
[43]={item_id=29476,num=48,is_bind=1},
[44]={item_id=29478,num=20,is_bind=1},
[45]={item_id=29477,num=7,is_bind=1},
[46]={item_id=29479,num=3,is_bind=1},
[47]={item_id=29478,num=25,is_bind=1},
[48]={item_id=29477,num=1,is_bind=1},
[49]={item_id=29477,num=6,is_bind=1},
[50]={item_id=29478,num=21,is_bind=1},
[51]={item_id=29477,num=2,is_bind=1},
[52]={item_id=29477,num=3,is_bind=1},
[53]={item_id=29477,num=8,is_bind=1},
[54]={item_id=29479,num=9,is_bind=1},
[55]={item_id=29476,num=56,is_bind=1},
[56]={item_id=29479,num=1,is_bind=1},
[57]={item_id=29477,num=4,is_bind=1},
[58]={item_id=29477,num=9,is_bind=1},
[59]={item_id=29476,num=42,is_bind=1},
[60]={item_id=29477,num=5,is_bind=1},
[61]={item_id=29477,num=11,is_bind=1},
[62]={item_id=29479,num=6,is_bind=1},
[63]={item_id=29476,num=51,is_bind=1},
[64]={item_id=29478,num=22,is_bind=1},
[65]={item_id=29477,num=12,is_bind=1},
[66]={item_id=29477,num=15,is_bind=1},
[67]={item_id=29479,num=11,is_bind=1},
[68]={item_id=29476,num=54,is_bind=1},
[69]={item_id=29478,num=24,is_bind=1},
[70]={item_id=29476,num=55,is_bind=1},
[71]={item_id=29478,num=26,is_bind=1},
[72]={item_id=29479,num=2,is_bind=1},
[73]={item_id=29476,num=28,is_bind=1},
[74]={item_id=29476,num=57,is_bind=1},
[75]={item_id=29477,num=21,is_bind=1},
[76]={item_id=29479,num=7,is_bind=1},
[77]={item_id=29476,num=65,is_bind=1},
[78]={item_id=29478,num=28,is_bind=1},
[79]={item_id=29476,num=29,is_bind=1},
[80]={item_id=29476,num=60,is_bind=1},
[81]={item_id=29476,num=61,is_bind=1},
[82]={item_id=29478,num=30,is_bind=1},
[83]={item_id=29477,num=16,is_bind=1},
[84]={item_id=29479,num=8,is_bind=1},
[85]={item_id=29476,num=63,is_bind=1},
[86]={item_id=29478,num=31,is_bind=1},
[87]={item_id=29476,num=30,is_bind=1},
[88]={item_id=29476,num=93,is_bind=1},
[89]={item_id=29478,num=39,is_bind=1},
[90]={item_id=29477,num=29,is_bind=1},
[91]={item_id=29479,num=12,is_bind=1},
[92]={item_id=29476,num=96,is_bind=1},
[93]={item_id=29478,num=43,is_bind=1},
[94]={item_id=29477,num=18,is_bind=1},
[95]={item_id=29476,num=78,is_bind=1},
[96]={item_id=29478,num=33,is_bind=1},
[97]={item_id=29477,num=23,is_bind=1},
[98]={item_id=29479,num=13,is_bind=1},
[99]={item_id=29476,num=82,is_bind=1},
[100]={item_id=29478,num=36,is_bind=1},
[101]={item_id=29476,num=66,is_bind=1},
[102]={item_id=29478,num=27,is_bind=1},
[103]={item_id=29477,num=20,is_bind=1},
[104]={item_id=29479,num=10,is_bind=1},
[105]={item_id=29476,num=67,is_bind=1},
[106]={item_id=29477,num=19,is_bind=1},
[107]={item_id=29476,num=69,is_bind=1},
[108]={item_id=29478,num=29,is_bind=1},
[109]={item_id=29478,num=13,is_bind=1},
[110]={item_id=29476,num=90,is_bind=1},
[111]={item_id=29477,num=26,is_bind=1},
[112]={item_id=29476,num=94,is_bind=1},
[113]={item_id=29478,num=40,is_bind=1},
[114]={item_id=29476,num=32,is_bind=1},
[115]={item_id=29477,num=24,is_bind=1},
[116]={item_id=29476,num=99,is_bind=1},
[117]={item_id=29478,num=42,is_bind=1},
[118]={item_id=29477,num=27,is_bind=1},
[119]={item_id=29479,num=14,is_bind=1},
[120]={item_id=29476,num=101,is_bind=1},
[121]={item_id=29478,num=44,is_bind=1},
[122]={item_id=29477,num=28,is_bind=1},
[123]={item_id=29476,num=106,is_bind=1},
[124]={item_id=29478,num=46,is_bind=1},
[125]={item_id=29476,num=83,is_bind=1},
[126]={item_id=29478,num=37,is_bind=1},
[127]={item_id=29476,num=75,is_bind=1},
[128]={item_id=29476,num=79,is_bind=1},
[129]={item_id=29477,num=22,is_bind=1},
[130]={item_id=29476,num=80,is_bind=1},
[131]={item_id=29478,num=38,is_bind=1},
[132]={item_id=29476,num=97,is_bind=1},
[133]={item_id=29478,num=41,is_bind=1},
[134]={item_id=29476,num=71,is_bind=1},
[135]={item_id=29478,num=35,is_bind=1},
[136]={item_id=29476,num=84,is_bind=1},
[137]={item_id=29476,num=88,is_bind=1},
[138]={item_id=29478,num=14,is_bind=1},
[139]={item_id=29477,num=1,is_bind=0},
[140]={item_id=29479,num=1,is_bind=0},
[141]={item_id=29476,num=1,is_bind=0},
[142]={item_id=29478,num=1,is_bind=0},
[143]={item_id=29476,num=33,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
wave={
{},
{wave=2,},
{wave=3,},
{wave=4,},
{wave=5,},
{wave=6,},
{wave=7,},
{wave=8,},
{wave=9,},
{wave=10,},
{wave=11,},
{wave=12,},
{wave=13,enter_pos_x=290,enter_pos_y=33,},
{wave=14,},
{wave=15,},
{wave=16,},
{wave=17,},
{wave=18,},
{wave=19,enter_pos_x=285,enter_pos_y=38,},
{wave=20,},
{wave=21,},
{wave=22,},
{wave=23,},
{wave=24,},
{wave=25,enter_pos_x=280,enter_pos_y=44,},
{wave=26,},
{wave=27,},
{wave=28,},
{wave=29,},
{wave=30,},
{wave=31,},
{wave=32,},
{wave=33,},
{wave=34,},
{wave=35,},
{wave=36,enter_pos_x=275,enter_pos_y=49,},
{wave=37,},
{wave=38,},
{wave=39,},
{wave=40,},
{wave=41,},
{wave=42,enter_pos_x=270,enter_pos_y=54,},
{wave=43,},
{wave=44,},
{wave=45,},
{wave=46,enter_pos_x=265,enter_pos_y=59,},
{wave=47,},
{wave=48,},
{wave=49,},
{wave=50,},
{wave=51,},
{wave=52,},
{wave=53,enter_pos_x=261,enter_pos_y=65,},
{wave=54,},
{wave=55,},
{wave=56,},
{wave=57,enter_pos_x=256,enter_pos_y=70,},
{wave=58,},
{wave=59,},
{wave=60,},
{wave=61,},
{wave=62,},
{wave=63,},
{wave=64,},
{wave=65,},
{wave=66,enter_pos_x=251,enter_pos_y=75,},
{wave=67,enter_pos_x=246,enter_pos_y=80,},
{wave=68,},
{wave=69,},
{wave=70,},
{wave=71,},
{wave=72,},
{wave=73,enter_pos_x=241,enter_pos_y=86,},
{wave=74,enter_pos_x=241,enter_pos_y=86,},
{wave=75,},
{wave=76,},
{wave=77,},
{wave=78,},
{wave=79,enter_pos_x=236,enter_pos_y=91,},
{wave=80,},
{wave=81,},
{wave=82,},
{wave=83,},
{wave=84,},
{wave=85,enter_pos_x=231,enter_pos_y=96,},
{wave=86,},
{wave=87,},
{wave=88,enter_pos_x=224,enter_pos_y=103,},
{wave=89,},
{wave=90,},
{wave=91,},
{wave=92,},
{wave=93,},
{wave=94,enter_pos_x=219,enter_pos_y=108,},
{wave=95,},
{wave=96,},
{wave=97,},
{wave=98,},
{wave=99,},
{wave=100,},
{wave=101,},
{wave=102,},
{wave=103,},
{wave=104,},
{wave=105,enter_pos_x=214,enter_pos_y=112,},
{wave=106,enter_pos_x=209,enter_pos_y=117,},
{wave=107,},
{wave=108,},
{wave=109,},
{wave=110,},
{wave=111,},
{wave=112,enter_pos_x=204,enter_pos_y=122,},
{wave=113,},
{wave=114,},
{wave=115,},
{wave=116,},
{wave=117,},
{wave=118,enter_pos_x=199,enter_pos_y=126,},
{wave=119,},
{wave=120,},
{wave=121,},
{wave=122,},
{wave=123,},
{wave=124,enter_pos_x=195,enter_pos_y=131,},
{wave=125,},
{wave=126,},
{wave=127,},
{wave=128,},
{wave=129,},
{wave=130,enter_pos_x=190,enter_pos_y=136,},
{wave=131,},
{wave=132,},
{wave=133,},
{wave=134,},
{wave=135,},
{wave=136,enter_pos_x=185,enter_pos_y=140,},
{wave=137,},
{wave=138,},
{wave=139,},
{wave=140,},
{wave=141,},
{wave=142,enter_pos_x=180,enter_pos_y=145,},
{wave=143,},
{wave=144,},
{wave=145,},
{wave=146,},
{wave=147,},
{wave=148,},
{wave=149,},
{wave=150,enter_pos_x=175,enter_pos_y=150,relive_pos_x=231,relive_pos_y=96,},
{wave=151,relive_pos_x=165,relive_pos_y=159,},
{wave=152,},
{wave=153,},
{wave=154,},
{wave=155,},
{wave=156,},
{wave=157,},
{wave=158,},
{wave=159,enter_pos_x=170,enter_pos_y=154,},
{wave=160,},
{wave=161,},
{wave=162,enter_pos_x=165,enter_pos_y=159,},
{wave=163,enter_pos_x=159,enter_pos_y=164,},
{wave=164,},
{wave=165,},
{wave=166,},
{wave=167,},
{wave=168,},
{wave=169,enter_pos_x=154,enter_pos_y=169,},
{wave=170,},
{wave=171,},
{wave=172,},
{wave=173,},
{wave=174,},
{wave=175,enter_pos_x=148,enter_pos_y=175,},
{wave=176,},
{wave=177,},
{wave=178,},
{wave=179,},
{wave=180,},
{wave=181,},
{wave=182,},
{wave=183,},
{wave=184,},
{wave=185,},
{wave=186,enter_pos_x=143,enter_pos_y=180,},
{wave=187,},
{wave=188,},
{wave=189,},
{wave=190,enter_pos_x=137,enter_pos_y=186,},
{wave=191,},
{wave=192,},
{wave=193,enter_pos_x=132,enter_pos_y=191,},
{wave=194,},
{wave=195,},
{wave=196,},
{wave=197,},
{wave=198,},
{wave=199,},
{wave=200,},
{wave=201,enter_pos_x=127,enter_pos_y=197,},
{wave=202,},
{wave=203,},
{wave=204,},
{wave=205,enter_pos_x=121,enter_pos_y=202,},
{wave=206,},
{wave=207,},
{wave=208,},
{wave=209,},
{wave=210,},
{wave=211,},
{wave=212,},
{wave=213,},
{wave=214,},
{wave=215,},
{wave=216,enter_pos_x=116,enter_pos_y=207,},
{wave=217,},
{wave=218,},
{wave=219,},
{wave=220,},
{wave=221,},
{wave=222,enter_pos_x=110,enter_pos_y=213,},
{wave=223,},
{wave=224,},
{wave=225,relive_pos_x=165,relive_pos_y=159,},
{wave=226,},
{wave=227,},
{wave=228,enter_pos_x=105,enter_pos_y=218,relive_pos_x=94,relive_pos_y=229,},
{wave=229,enter_pos_x=99,enter_pos_y=224,},
{wave=230,},
{wave=231,},
{wave=232,},
{wave=233,},
{wave=234,},
{wave=235,enter_pos_x=94,enter_pos_y=229,},
{wave=236,},
{wave=237,},
{wave=238,enter_pos_x=85,enter_pos_y=238,},
{wave=239,},
{wave=240,},
{wave=241,},
{wave=242,},
{wave=243,},
{wave=244,},
{wave=245,enter_pos_x=81,enter_pos_y=242,},
{wave=246,},
{wave=247,},
{wave=248,},
{wave=249,},
{wave=250,enter_pos_x=76,enter_pos_y=247,},
{wave=251,},
{wave=252,},
{wave=253,},
{wave=254,},
{wave=255,},
{wave=256,enter_pos_x=72,enter_pos_y=251,},
{wave=257,},
{wave=258,},
{wave=259,},
{wave=260,},
{wave=261,},
{wave=262,enter_pos_x=68,enter_pos_y=256,},
{wave=263,},
{wave=264,},
{wave=265,},
{wave=266,},
{wave=267,},
{wave=268,},
{wave=269,},
{wave=270,},
{wave=271,},
{wave=272,},
{wave=273,enter_pos_x=63,enter_pos_y=260,},
{wave=274,},
{wave=275,enter_pos_x=59,enter_pos_y=265,},
{wave=276,},
{wave=277,},
{wave=278,},
{wave=279,},
{wave=280,},
{wave=281,enter_pos_x=55,enter_pos_y=269,},
{wave=282,},
{wave=283,},
{wave=284,},
{wave=285,},
{wave=286,enter_pos_x=50,enter_pos_y=273,},
{wave=287,},
{wave=288,},
{wave=289,},
{wave=290,},
{wave=291,},
{wave=292,enter_pos_x=46,enter_pos_y=278,},
{wave=293,},
{wave=294,},
{wave=295,},
{wave=296,},
{wave=297,},
{wave=298,enter_pos_x=42,enter_pos_y=282,},
{wave=299,},
{wave=300,}
},

wave_meta_table_map={
[56]=57,	-- depth:1
[55]=56,	-- depth:2
[54]=53,	-- depth:1
[52]=54,	-- depth:2
[50]=52,	-- depth:3
[49]=50,	-- depth:4
[48]=46,	-- depth:1
[47]=48,	-- depth:2
[58]=55,	-- depth:3
[51]=49,	-- depth:5
[59]=58,	-- depth:4
[61]=66,	-- depth:1
[62]=61,	-- depth:2
[63]=62,	-- depth:3
[64]=63,	-- depth:4
[45]=47,	-- depth:3
[68]=67,	-- depth:1
[69]=68,	-- depth:2
[70]=69,	-- depth:3
[71]=70,	-- depth:4
[72]=71,	-- depth:5
[75]=73,	-- depth:1
[77]=75,	-- depth:2
[60]=59,	-- depth:5
[44]=45,	-- depth:4
[65]=64,	-- depth:5
[43]=44,	-- depth:5
[14]=13,	-- depth:1
[16]=14,	-- depth:2
[17]=16,	-- depth:3
[18]=17,	-- depth:4
[20]=19,	-- depth:1
[21]=20,	-- depth:2
[22]=21,	-- depth:3
[23]=22,	-- depth:4
[24]=23,	-- depth:5
[26]=25,	-- depth:1
[15]=18,	-- depth:5
[28]=26,	-- depth:2
[27]=28,	-- depth:3
[40]=42,	-- depth:1
[39]=40,	-- depth:2
[38]=39,	-- depth:3
[37]=38,	-- depth:4
[35]=36,	-- depth:1
[41]=37,	-- depth:5
[33]=35,	-- depth:2
[32]=33,	-- depth:3
[31]=32,	-- depth:4
[30]=27,	-- depth:4
[34]=31,	-- depth:5
[29]=30,	-- depth:5
[227]=228,	-- depth:1
[226]=227,	-- depth:2
[225]=228,	-- depth:1
[222]=225,	-- depth:2
[223]=225,	-- depth:2
[221]=222,	-- depth:3
[229]=228,	-- depth:1
[219]=221,	-- depth:4
[218]=219,	-- depth:5
[224]=223,	-- depth:3
[220]=218,	-- depth:6
[235]=228,	-- depth:1
[231]=229,	-- depth:2
[232]=231,	-- depth:3
[233]=232,	-- depth:4
[234]=233,	-- depth:5
[236]=235,	-- depth:2
[237]=236,	-- depth:3
[238]=228,	-- depth:1
[239]=238,	-- depth:2
[240]=239,	-- depth:3
[217]=220,	-- depth:7
[241]=240,	-- depth:4
[230]=234,	-- depth:6
[216]=225,	-- depth:2
[201]=225,	-- depth:2
[214]=216,	-- depth:3
[242]=241,	-- depth:5
[190]=225,	-- depth:2
[191]=190,	-- depth:3
[192]=191,	-- depth:4
[193]=225,	-- depth:2
[194]=193,	-- depth:3
[195]=194,	-- depth:4
[196]=195,	-- depth:5
[197]=196,	-- depth:6
[198]=197,	-- depth:7
[199]=201,	-- depth:3
[215]=214,	-- depth:4
[200]=199,	-- depth:4
[203]=200,	-- depth:5
[204]=203,	-- depth:6
[205]=225,	-- depth:2
[206]=205,	-- depth:3
[207]=206,	-- depth:4
[208]=207,	-- depth:5
[209]=208,	-- depth:6
[210]=209,	-- depth:7
[211]=215,	-- depth:5
[212]=211,	-- depth:6
[213]=212,	-- depth:7
[202]=204,	-- depth:7
[243]=242,	-- depth:6
[281]=228,	-- depth:1
[245]=228,	-- depth:1
[275]=228,	-- depth:1
[276]=275,	-- depth:2
[277]=276,	-- depth:3
[278]=277,	-- depth:4
[279]=278,	-- depth:5
[280]=281,	-- depth:2
[189]=192,	-- depth:5
[282]=280,	-- depth:3
[283]=282,	-- depth:4
[284]=283,	-- depth:5
[285]=284,	-- depth:6
[274]=279,	-- depth:6
[286]=228,	-- depth:1
[288]=286,	-- depth:2
[289]=288,	-- depth:3
[290]=289,	-- depth:4
[291]=290,	-- depth:5
[292]=228,	-- depth:1
[293]=292,	-- depth:2
[294]=293,	-- depth:3
[295]=294,	-- depth:4
[296]=295,	-- depth:5
[297]=296,	-- depth:6
[298]=228,	-- depth:1
[287]=291,	-- depth:6
[273]=228,	-- depth:1
[272]=273,	-- depth:2
[271]=272,	-- depth:3
[246]=245,	-- depth:2
[247]=246,	-- depth:3
[248]=247,	-- depth:4
[249]=248,	-- depth:5
[250]=228,	-- depth:1
[251]=250,	-- depth:2
[252]=251,	-- depth:3
[253]=252,	-- depth:4
[254]=253,	-- depth:5
[255]=254,	-- depth:6
[256]=228,	-- depth:1
[257]=256,	-- depth:2
[258]=257,	-- depth:3
[259]=258,	-- depth:4
[260]=259,	-- depth:5
[261]=260,	-- depth:6
[262]=228,	-- depth:1
[263]=262,	-- depth:2
[264]=263,	-- depth:3
[265]=264,	-- depth:4
[266]=265,	-- depth:5
[267]=266,	-- depth:6
[268]=271,	-- depth:4
[269]=268,	-- depth:5
[270]=269,	-- depth:6
[244]=249,	-- depth:6
[188]=189,	-- depth:6
[186]=225,	-- depth:2
[105]=150,	-- depth:1
[106]=150,	-- depth:1
[107]=106,	-- depth:2
[108]=107,	-- depth:3
[109]=108,	-- depth:4
[110]=109,	-- depth:5
[111]=110,	-- depth:6
[112]=150,	-- depth:1
[113]=112,	-- depth:2
[114]=113,	-- depth:3
[115]=114,	-- depth:4
[104]=105,	-- depth:2
[116]=115,	-- depth:5
[118]=150,	-- depth:1
[119]=118,	-- depth:2
[120]=119,	-- depth:3
[121]=120,	-- depth:4
[122]=121,	-- depth:5
[123]=122,	-- depth:6
[124]=150,	-- depth:1
[125]=124,	-- depth:2
[126]=125,	-- depth:3
[127]=126,	-- depth:4
[128]=127,	-- depth:5
[117]=116,	-- depth:6
[103]=104,	-- depth:3
[102]=103,	-- depth:4
[101]=102,	-- depth:5
[74]=150,	-- depth:1
[76]=74,	-- depth:2
[78]=76,	-- depth:3
[79]=150,	-- depth:1
[80]=79,	-- depth:2
[81]=80,	-- depth:3
[82]=81,	-- depth:4
[83]=82,	-- depth:5
[84]=83,	-- depth:6
[85]=150,	-- depth:1
[86]=85,	-- depth:2
[87]=86,	-- depth:3
[88]=150,	-- depth:1
[89]=88,	-- depth:2
[90]=89,	-- depth:3
[91]=90,	-- depth:4
[92]=91,	-- depth:5
[93]=92,	-- depth:6
[94]=150,	-- depth:1
[95]=94,	-- depth:2
[96]=95,	-- depth:3
[97]=96,	-- depth:4
[98]=97,	-- depth:5
[99]=98,	-- depth:6
[100]=101,	-- depth:6
[129]=128,	-- depth:6
[187]=188,	-- depth:7
[130]=150,	-- depth:1
[132]=130,	-- depth:2
[162]=225,	-- depth:2
[163]=225,	-- depth:2
[164]=163,	-- depth:3
[165]=164,	-- depth:4
[166]=165,	-- depth:5
[167]=166,	-- depth:6
[168]=167,	-- depth:7
[169]=225,	-- depth:2
[170]=169,	-- depth:3
[171]=170,	-- depth:4
[172]=171,	-- depth:5
[161]=162,	-- depth:3
[173]=172,	-- depth:6
[175]=225,	-- depth:2
[176]=175,	-- depth:3
[177]=176,	-- depth:4
[178]=177,	-- depth:5
[179]=178,	-- depth:6
[180]=179,	-- depth:7
[181]=186,	-- depth:3
[182]=181,	-- depth:4
[183]=182,	-- depth:5
[184]=183,	-- depth:6
[185]=184,	-- depth:7
[174]=173,	-- depth:7
[160]=161,	-- depth:4
[159]=225,	-- depth:2
[158]=159,	-- depth:3
[133]=132,	-- depth:3
[134]=133,	-- depth:4
[135]=134,	-- depth:5
[136]=150,	-- depth:1
[137]=136,	-- depth:2
[138]=137,	-- depth:3
[139]=138,	-- depth:4
[140]=139,	-- depth:5
[141]=140,	-- depth:6
[142]=150,	-- depth:1
[143]=142,	-- depth:2
[144]=143,	-- depth:3
[145]=144,	-- depth:4
[146]=145,	-- depth:5
[147]=146,	-- depth:6
[148]=150,	-- depth:1
[149]=148,	-- depth:2
[299]=298,	-- depth:2
[151]=150,	-- depth:1
[152]=151,	-- depth:2
[153]=152,	-- depth:3
[154]=158,	-- depth:4
[155]=154,	-- depth:5
[156]=155,	-- depth:6
[157]=156,	-- depth:7
[131]=135,	-- depth:6
[300]=299,	-- depth:3
},
monster={
{},
{wave=2,monster_id=21001,},
{wave=3,monster_id=21002,monster_num=1,},
{wave=4,monster_id=21003,},
{wave=5,monster_id=21004,},
{wave=6,monster_id=21005,},
{wave=7,monster_id=21006,pos_x=285,pos_y=38,},
{wave=8,monster_id=21007,},
{wave=9,monster_id=21008,monster_num=1,},
{wave=10,monster_id=21009,},
{wave=11,monster_id=21010,},
{wave=12,monster_id=21011,},
{wave=13,monster_id=21012,pos_x=280,pos_y=44,},
{wave=14,monster_id=21013,},
{wave=15,monster_id=21014,monster_num=1,},
{wave=16,monster_id=21015,},
{wave=17,monster_id=21016,},
{wave=18,monster_id=21017,},
{wave=19,monster_id=21018,pos_x=275,pos_y=49,},
{wave=20,monster_id=21019,},
{wave=21,monster_id=21020,monster_num=1,},
{wave=22,monster_id=21021,},
{wave=23,monster_id=21022,},
{wave=24,monster_id=21023,},
{wave=25,monster_id=21024,pos_x=270,pos_y=54,},
{wave=26,monster_id=21025,},
{wave=27,monster_id=21026,monster_num=1,},
{wave=28,monster_id=21027,},
{wave=29,monster_id=21028,},
{wave=30,monster_id=21029,},
{wave=31,monster_id=21030,},
{wave=32,monster_id=21031,},
{wave=33,monster_id=21032,monster_num=1,},
{wave=34,monster_id=21033,},
{wave=35,monster_id=21034,pos_x=265,pos_y=59,},
{wave=36,monster_id=21035,},
{wave=37,monster_id=21036,pos_x=261,pos_y=65,},
{wave=38,monster_id=21037,},
{wave=39,monster_id=21038,monster_num=1,},
{wave=40,monster_id=21039,},
{wave=41,monster_id=21040,},
{wave=42,monster_id=21041,},
{wave=43,monster_id=21042,pos_x=256,pos_y=70,},
{wave=44,monster_id=21043,},
{wave=45,monster_id=21044,monster_num=1,},
{wave=46,monster_id=21045,},
{wave=47,monster_id=21046,},
{wave=48,monster_id=21047,},
{wave=49,monster_id=21048,pos_x=251,pos_y=75,},
{wave=50,monster_id=21049,},
{wave=51,monster_id=21050,},
{wave=52,monster_id=21051,},
{wave=53,monster_id=21052,},
{wave=54,monster_id=21053,monster_num=1,},
{wave=55,monster_id=21054,pos_x=246,pos_y=80,},
{wave=56,monster_id=21055,},
{wave=57,monster_id=21056,},
{wave=58,monster_id=21057,},
{wave=59,monster_id=21058,},
{wave=60,monster_id=21059,monster_num=1,},
{wave=61,monster_id=21060,pos_x=241,pos_y=86,},
{wave=62,monster_id=21061,},
{wave=63,monster_id=21062,},
{wave=64,monster_id=21063,},
{wave=65,monster_id=21064,},
{wave=66,monster_id=21065,monster_num=1,},
{wave=67,monster_id=21066,},
{wave=68,monster_id=21067,pos_x=236,pos_y=91,},
{wave=69,monster_id=21068,monster_num=1,},
{wave=70,monster_id=21069,},
{wave=71,monster_id=21070,},
{wave=72,monster_id=21071,},
{wave=73,monster_id=21072,pos_x=231,pos_y=96,},
{wave=74,monster_id=21073,},
{wave=75,monster_id=21074,monster_num=1,},
{wave=76,monster_id=21075,pos_x=224,pos_y=103,},
{wave=77,monster_id=21076,},
{wave=78,monster_id=21077,monster_num=1,},
{wave=79,monster_id=21078,},
{wave=80,monster_id=21079,},
{wave=81,monster_id=21080,},
{wave=82,monster_id=21081,},
{wave=83,monster_id=21082,},
{wave=84,monster_id=21083,monster_num=1,},
{wave=85,monster_id=21084,pos_x=219,pos_y=108,},
{wave=86,monster_id=21085,},
{wave=87,monster_id=21086,},
{wave=88,monster_id=21087,pos_x=214,pos_y=112,},
{wave=89,monster_id=21088,},
{wave=90,monster_id=21089,monster_num=1,},
{wave=91,monster_id=21090,},
{wave=92,monster_id=21091,},
{wave=93,monster_id=21092,},
{wave=94,monster_id=21093,pos_x=209,pos_y=117,},
{wave=95,monster_id=21094,},
{wave=96,monster_id=21095,monster_num=1,},
{wave=97,monster_id=21096,},
{wave=98,monster_id=21097,},
{wave=99,monster_id=21098,},
{wave=100,monster_id=21099,pos_x=204,pos_y=122,},
{wave=101,monster_id=21100,},
{wave=102,monster_id=21101,monster_num=1,},
{wave=103,monster_id=21102,},
{wave=104,monster_id=21103,},
{wave=105,monster_id=21104,},
{wave=106,monster_id=21105,},
{wave=107,monster_id=21106,},
{wave=108,monster_id=21107,monster_num=1,},
{wave=109,monster_id=21108,},
{wave=110,monster_id=21109,pos_x=199,pos_y=126,},
{wave=111,monster_id=21110,},
{wave=112,monster_id=21111,},
{wave=113,monster_id=21112,},
{wave=114,monster_id=21113,},
{wave=115,monster_id=21114,pos_x=195,pos_y=131,},
{wave=116,monster_id=21115,},
{wave=117,monster_id=21116,monster_num=1,},
{wave=118,monster_id=21117,pos_x=190,pos_y=136,},
{wave=119,monster_id=21118,},
{wave=120,monster_id=21119,monster_num=1,},
{wave=121,monster_id=21120,},
{wave=122,monster_id=21121,},
{wave=123,monster_id=21122,},
{wave=124,monster_id=21123,pos_x=185,pos_y=140,},
{wave=125,monster_id=21124,},
{wave=126,monster_id=21125,monster_num=1,},
{wave=127,monster_id=21126,},
{wave=128,monster_id=21127,},
{wave=129,monster_id=21128,},
{wave=130,monster_id=21129,pos_x=180,pos_y=145,},
{wave=131,monster_id=21130,},
{wave=132,monster_id=21131,monster_num=1,},
{wave=133,monster_id=21132,},
{wave=134,monster_id=21133,},
{wave=135,monster_id=21134,},
{wave=136,monster_id=21135,},
{wave=137,monster_id=21136,},
{wave=138,monster_id=21137,monster_num=1,},
{wave=139,monster_id=21138,},
{wave=140,monster_id=21139,pos_x=175,pos_y=150,},
{wave=141,monster_id=21140,},
{wave=142,monster_id=21141,},
{wave=143,monster_id=21142,},
{wave=144,monster_id=21143,monster_num=1,},
{wave=145,monster_id=21144,pos_x=170,pos_y=154,},
{wave=146,monster_id=21145,},
{wave=147,monster_id=21146,},
{wave=148,monster_id=21147,},
{wave=149,monster_id=21148,pos_x=165,pos_y=159,},
{wave=150,monster_id=21149,monster_num=1,},
{wave=151,monster_id=21150,pos_x=159,pos_y=164,},
{wave=152,monster_id=21151,},
{wave=153,monster_id=21152,monster_num=1,},
{wave=154,monster_id=21153,},
{wave=155,monster_id=21154,},
{wave=156,monster_id=21155,},
{wave=157,monster_id=21156,pos_x=154,pos_y=169,},
{wave=158,monster_id=21157,},
{wave=159,monster_id=21158,monster_num=1,},
{wave=160,monster_id=21159,},
{wave=161,monster_id=21160,},
{wave=162,monster_id=21161,},
{wave=163,monster_id=21162,pos_x=148,pos_y=175,},
{wave=164,monster_id=21163,},
{wave=165,monster_id=21164,},
{wave=166,monster_id=21165,},
{wave=167,monster_id=21166,},
{wave=168,monster_id=21167,monster_num=1,},
{wave=169,monster_id=21168,},
{wave=170,monster_id=21169,pos_x=143,pos_y=180,},
{wave=171,monster_id=21170,},
{wave=172,monster_id=21171,},
{wave=173,monster_id=21172,},
{wave=174,monster_id=21173,monster_num=1,},
{wave=175,monster_id=21174,pos_x=137,pos_y=186,},
{wave=176,monster_id=21175,},
{wave=177,monster_id=21176,},
{wave=178,monster_id=21177,},
{wave=179,monster_id=21178,},
{wave=180,monster_id=21179,monster_num=1,},
{wave=181,monster_id=21180,pos_x=132,pos_y=191,},
{wave=182,monster_id=21181,},
{wave=183,monster_id=21182,monster_num=1,},
{wave=184,monster_id=21183,},
{wave=185,monster_id=21184,},
{wave=186,monster_id=21185,},
{wave=187,monster_id=21186,pos_x=127,pos_y=197,},
{wave=188,monster_id=21187,},
{wave=189,monster_id=21188,monster_num=1,},
{wave=190,monster_id=21189,},
{wave=191,monster_id=21190,},
{wave=192,monster_id=21191,},
{wave=193,monster_id=21192,},
{wave=194,monster_id=21193,},
{wave=195,monster_id=21194,monster_num=1,},
{wave=196,monster_id=21195,pos_x=121,pos_y=202,},
{wave=197,monster_id=21196,},
{wave=198,monster_id=21197,},
{wave=199,monster_id=21198,},
{wave=200,monster_id=21199,pos_x=116,pos_y=207,},
{wave=201,monster_id=21200,},
{wave=202,monster_id=21201,},
{wave=203,monster_id=21202,},
{wave=204,monster_id=21203,monster_num=1,},
{wave=205,monster_id=21204,pos_x=110,pos_y=213,},
{wave=206,monster_id=21205,},
{wave=207,monster_id=21206,monster_num=1,},
{wave=208,monster_id=21207,},
{wave=209,monster_id=21208,},
{wave=210,monster_id=21209,},
{wave=211,monster_id=21210,pos_x=105,pos_y=218,},
{wave=212,monster_id=21211,},
{wave=213,monster_id=21212,monster_num=1,},
{wave=214,monster_id=21213,},
{wave=215,monster_id=21214,},
{wave=216,monster_id=21215,},
{wave=217,monster_id=21216,pos_x=99,pos_y=224,},
{wave=218,monster_id=21217,},
{wave=219,monster_id=21218,},
{wave=220,monster_id=21219,},
{wave=221,monster_id=21220,},
{wave=222,monster_id=21221,monster_num=1,},
{wave=223,monster_id=21222,pos_x=94,pos_y=229,},
{wave=224,monster_id=21223,},
{wave=225,monster_id=21224,monster_num=1,},
{wave=226,monster_id=21225,},
{wave=227,monster_id=21226,pos_x=85,pos_y=238,},
{wave=228,monster_id=21227,monster_num=1,},
{wave=229,monster_id=21228,},
{wave=230,monster_id=21229,},
{wave=231,monster_id=21230,},
{wave=232,monster_id=21231,},
{wave=233,monster_id=21232,},
{wave=234,monster_id=21233,monster_num=1,},
{wave=235,monster_id=21234,pos_x=81,pos_y=242,},
{wave=236,monster_id=21235,},
{wave=237,monster_id=21236,},
{wave=238,monster_id=21237,},
{wave=239,monster_id=21238,},
{wave=240,monster_id=21239,monster_num=1,},
{wave=241,monster_id=21240,},
{wave=242,monster_id=21241,pos_x=76,pos_y=247,},
{wave=243,monster_id=21242,},
{wave=244,monster_id=21243,},
{wave=245,monster_id=21244,},
{wave=246,monster_id=21245,},
{wave=247,monster_id=21246,},
{wave=248,monster_id=21247,pos_x=72,pos_y=251,},
{wave=249,monster_id=21248,monster_num=1,},
{wave=250,monster_id=21249,},
{wave=251,monster_id=21250,},
{wave=252,monster_id=21251,monster_num=1,},
{wave=253,monster_id=21252,pos_x=68,pos_y=256,},
{wave=254,monster_id=21253,},
{wave=255,monster_id=21254,},
{wave=256,monster_id=21255,},
{wave=257,monster_id=21256,},
{wave=258,monster_id=21257,},
{wave=259,monster_id=21258,},
{wave=260,monster_id=21259,pos_x=63,pos_y=260,},
{wave=261,monster_id=21260,monster_num=1,},
{wave=262,monster_id=21261,},
{wave=263,monster_id=21262,pos_x=59,pos_y=265,},
{wave=264,monster_id=21263,},
{wave=265,monster_id=21264,},
{wave=266,monster_id=21265,},
{wave=267,monster_id=21266,monster_num=1,},
{wave=268,monster_id=21267,},
{wave=269,monster_id=21268,},
{wave=270,monster_id=21269,monster_num=1,},
{wave=271,monster_id=21270,pos_x=55,pos_y=269,},
{wave=272,monster_id=21271,},
{wave=273,monster_id=21272,},
{wave=274,monster_id=21273,},
{wave=275,monster_id=21274,},
{wave=276,monster_id=21275,},
{wave=277,monster_id=21276,pos_x=50,pos_y=273,},
{wave=278,monster_id=21277,},
{wave=279,monster_id=21278,monster_num=1,},
{wave=280,monster_id=21279,},
{wave=281,monster_id=21280,},
{wave=282,monster_id=21281,monster_num=1,},
{wave=283,monster_id=21282,},
{wave=284,monster_id=21283,pos_x=46,pos_y=278,},
{wave=285,monster_id=21284,},
{wave=286,monster_id=21285,},
{wave=287,monster_id=21286,pos_x=42,pos_y=282,},
{wave=288,monster_id=21287,monster_num=1,},
{wave=289,monster_id=21288,},
{wave=290,monster_id=21289,},
{wave=291,monster_id=21290,},
{wave=292,monster_id=21291,},
{wave=293,monster_id=21292,},
{wave=294,monster_id=21293,},
{wave=295,monster_id=21294,},
{wave=296,monster_id=21295,pos_x=37,pos_y=287,},
{wave=297,monster_id=21296,monster_num=1,},
{wave=298,monster_id=21297,},
{wave=299,monster_id=21298,pos_x=33,pos_y=291,},
{wave=300,monster_id=21299,monster_num=1,}
},

monster_meta_table_map={
[6]=3,	-- depth:1
[148]=149,	-- depth:1
[152]=151,	-- depth:1
[143]=145,	-- depth:1
[142]=145,	-- depth:1
[146]=145,	-- depth:1
[251]=253,	-- depth:1
[250]=253,	-- depth:1
[155]=151,	-- depth:1
[254]=253,	-- depth:1
[158]=157,	-- depth:1
[160]=157,	-- depth:1
[161]=157,	-- depth:1
[164]=163,	-- depth:1
[247]=248,	-- depth:1
[154]=151,	-- depth:1
[136]=140,	-- depth:1
[137]=140,	-- depth:1
[116]=115,	-- depth:1
[262]=263,	-- depth:1
[119]=118,	-- depth:1
[121]=118,	-- depth:1
[122]=118,	-- depth:1
[139]=140,	-- depth:1
[259]=260,	-- depth:1
[127]=124,	-- depth:1
[128]=124,	-- depth:1
[131]=130,	-- depth:1
[257]=260,	-- depth:1
[133]=130,	-- depth:1
[134]=130,	-- depth:1
[256]=260,	-- depth:1
[166]=163,	-- depth:1
[125]=124,	-- depth:1
[167]=163,	-- depth:1
[245]=248,	-- depth:1
[202]=200,	-- depth:1
[203]=200,	-- depth:1
[206]=205,	-- depth:1
[233]=235,	-- depth:1
[208]=205,	-- depth:1
[209]=205,	-- depth:1
[232]=235,	-- depth:1
[199]=200,	-- depth:1
[214]=211,	-- depth:1
[215]=211,	-- depth:1
[230]=227,	-- depth:1
[218]=217,	-- depth:1
[229]=227,	-- depth:1
[220]=217,	-- depth:1
[221]=217,	-- depth:1
[224]=223,	-- depth:1
[212]=211,	-- depth:1
[169]=170,	-- depth:1
[236]=235,	-- depth:1
[112]=115,	-- depth:1
[172]=170,	-- depth:1
[173]=170,	-- depth:1
[244]=248,	-- depth:1
[176]=175,	-- depth:1
[178]=175,	-- depth:1
[179]=175,	-- depth:1
[197]=196,	-- depth:1
[182]=181,	-- depth:1
[184]=181,	-- depth:1
[185]=181,	-- depth:1
[188]=187,	-- depth:1
[239]=242,	-- depth:1
[190]=187,	-- depth:1
[191]=187,	-- depth:1
[238]=242,	-- depth:1
[193]=196,	-- depth:1
[194]=196,	-- depth:1
[241]=242,	-- depth:1
[113]=115,	-- depth:1
[265]=263,	-- depth:1
[38]=37,	-- depth:1
[40]=37,	-- depth:1
[41]=37,	-- depth:1
[44]=43,	-- depth:1
[286]=287,	-- depth:1
[46]=43,	-- depth:1
[47]=43,	-- depth:1
[289]=287,	-- depth:1
[52]=49,	-- depth:1
[53]=49,	-- depth:1
[283]=284,	-- depth:1
[56]=55,	-- depth:1
[109]=110,	-- depth:1
[58]=55,	-- depth:1
[59]=55,	-- depth:1
[281]=284,	-- depth:1
[50]=49,	-- depth:1
[34]=35,	-- depth:1
[290]=287,	-- depth:1
[8]=7,	-- depth:1
[298]=299,	-- depth:1
[10]=7,	-- depth:1
[11]=7,	-- depth:1
[14]=13,	-- depth:1
[16]=13,	-- depth:1
[17]=13,	-- depth:1
[295]=296,	-- depth:1
[20]=19,	-- depth:1
[22]=19,	-- depth:1
[23]=19,	-- depth:1
[293]=296,	-- depth:1
[26]=25,	-- depth:1
[292]=296,	-- depth:1
[28]=25,	-- depth:1
[29]=25,	-- depth:1
[31]=35,	-- depth:1
[32]=35,	-- depth:1
[62]=61,	-- depth:1
[226]=227,	-- depth:1
[64]=61,	-- depth:1
[86]=85,	-- depth:1
[280]=284,	-- depth:1
[89]=88,	-- depth:1
[91]=88,	-- depth:1
[92]=88,	-- depth:1
[95]=94,	-- depth:1
[269]=271,	-- depth:1
[97]=94,	-- depth:1
[98]=94,	-- depth:1
[268]=271,	-- depth:1
[101]=100,	-- depth:1
[103]=100,	-- depth:1
[104]=100,	-- depth:1
[266]=263,	-- depth:1
[106]=110,	-- depth:1
[107]=110,	-- depth:1
[83]=85,	-- depth:1
[82]=85,	-- depth:1
[272]=271,	-- depth:1
[74]=73,	-- depth:1
[278]=277,	-- depth:1
[67]=68,	-- depth:1
[71]=68,	-- depth:1
[65]=61,	-- depth:1
[70]=68,	-- depth:1
[275]=277,	-- depth:1
[79]=76,	-- depth:1
[80]=76,	-- depth:1
[274]=277,	-- depth:1
[77]=76,	-- depth:1
[228]=227,	-- depth:1
[282]=284,	-- depth:1
[297]=296,	-- depth:1
[267]=263,	-- depth:1
[249]=248,	-- depth:1
[264]=267,	-- depth:2
[231]=228,	-- depth:2
[261]=260,	-- depth:1
[279]=277,	-- depth:1
[288]=287,	-- depth:1
[240]=242,	-- depth:1
[276]=279,	-- depth:2
[234]=235,	-- depth:1
[270]=271,	-- depth:1
[243]=240,	-- depth:2
[258]=261,	-- depth:2
[285]=282,	-- depth:2
[252]=253,	-- depth:1
[291]=288,	-- depth:2
[237]=234,	-- depth:2
[273]=270,	-- depth:2
[255]=252,	-- depth:2
[294]=297,	-- depth:2
[246]=249,	-- depth:2
[150]=149,	-- depth:1
[222]=217,	-- depth:1
[66]=61,	-- depth:1
[69]=68,	-- depth:1
[72]=69,	-- depth:2
[75]=73,	-- depth:1
[78]=76,	-- depth:1
[81]=78,	-- depth:2
[63]=66,	-- depth:2
[84]=85,	-- depth:1
[90]=88,	-- depth:1
[93]=90,	-- depth:2
[96]=94,	-- depth:1
[99]=96,	-- depth:2
[102]=100,	-- depth:1
[105]=102,	-- depth:2
[87]=84,	-- depth:2
[60]=55,	-- depth:1
[57]=60,	-- depth:2
[54]=49,	-- depth:1
[9]=7,	-- depth:1
[12]=9,	-- depth:2
[15]=13,	-- depth:1
[18]=15,	-- depth:2
[21]=19,	-- depth:1
[24]=21,	-- depth:2
[27]=25,	-- depth:1
[30]=27,	-- depth:2
[33]=35,	-- depth:1
[36]=33,	-- depth:2
[39]=37,	-- depth:1
[42]=39,	-- depth:2
[45]=43,	-- depth:1
[48]=45,	-- depth:2
[51]=54,	-- depth:2
[108]=110,	-- depth:1
[225]=223,	-- depth:1
[111]=108,	-- depth:2
[117]=115,	-- depth:1
[180]=175,	-- depth:1
[183]=181,	-- depth:1
[186]=183,	-- depth:2
[189]=187,	-- depth:1
[192]=189,	-- depth:2
[195]=196,	-- depth:1
[177]=180,	-- depth:2
[198]=195,	-- depth:2
[204]=200,	-- depth:1
[207]=205,	-- depth:1
[210]=207,	-- depth:2
[213]=211,	-- depth:1
[216]=213,	-- depth:2
[219]=222,	-- depth:2
[201]=204,	-- depth:2
[174]=170,	-- depth:1
[171]=174,	-- depth:2
[168]=163,	-- depth:1
[120]=118,	-- depth:1
[123]=120,	-- depth:2
[126]=124,	-- depth:1
[129]=126,	-- depth:2
[132]=130,	-- depth:1
[135]=132,	-- depth:2
[138]=140,	-- depth:1
[141]=138,	-- depth:2
[144]=145,	-- depth:1
[147]=144,	-- depth:2
[153]=151,	-- depth:1
[156]=153,	-- depth:2
[159]=157,	-- depth:1
[162]=159,	-- depth:2
[165]=168,	-- depth:2
[114]=117,	-- depth:2
[300]=299,	-- depth:1
},
reward={
{client_show_reward={[0]=item_table[1]},},
{wave=2,},
{wave=3,reward_item={[0]=item_table[2]},desc2="注意躲避BOSS技能",client_show_reward={[0]=item_table[3]},},
{wave=4,client_show_reward={[0]=item_table[3]},},
{wave=5,client_show_reward={[0]=item_table[4],[1]=item_table[5]},},
{wave=6,client_show_reward={[0]=item_table[4],[1]=item_table[5]},},
{wave=7,client_show_reward={[0]=item_table[6],[1]=item_table[5]},},
{wave=8,client_show_reward={[0]=item_table[6],[1]=item_table[7]},},
{wave=9,reward_item={[0]=item_table[2],[1]=item_table[5]},desc2="注意躲避BOSS技能",client_show_reward={[0]=item_table[8],[1]=item_table[7]},},
{wave=10,client_show_reward={[0]=item_table[8],[1]=item_table[9]},},
{wave=11,},
{wave=12,client_show_reward={[0]=item_table[10],[1]=item_table[9]},},
{wave=13,client_show_reward={[0]=item_table[10],[1]=item_table[11]},},
{wave=14,client_show_reward={[0]=item_table[12],[1]=item_table[11]},},
{wave=15,reward_item={[0]=item_table[2],[1]=item_table[7]},desc2="注意躲避BOSS技能",client_show_reward={[0]=item_table[12],[1]=item_table[13]},},
{wave=16,client_show_reward={[0]=item_table[14],[1]=item_table[13]},},
{wave=17,client_show_reward={[0]=item_table[14],[1]=item_table[15]},},
{wave=18,reward_item={[0]=item_table[16],[1]=item_table[5]},desc2="注意躲避BOSS技能",client_show_reward={[0]=item_table[17],[1]=item_table[15]},},
{wave=19,client_show_reward={[0]=item_table[17],[1]=item_table[15]},},
{wave=20,client_show_reward={[0]=item_table[17],[1]=item_table[18]},},
{wave=21,client_show_reward={[0]=item_table[19],[1]=item_table[18]},},
{wave=22,client_show_reward={[0]=item_table[19],[1]=item_table[20]},},
{wave=23,client_show_reward={[0]=item_table[21],[1]=item_table[20]},},
{wave=24,reward_item={[0]=item_table[22],[1]=item_table[11]},desc2="注意躲避BOSS技能",client_show_reward={[0]=item_table[21],[1]=item_table[20]},},
{wave=25,client_show_reward={[0]=item_table[23],[1]=item_table[24]},},
{wave=26,},
{wave=27,reward_item={[0]=item_table[25],[1]=item_table[5]},},
{wave=28,},
{wave=29,},
{wave=30,reward_item={[0]=item_table[26],[1]=item_table[9]},},
{wave=31,},
{wave=32,},
{wave=33,reward_item={[0]=item_table[26],[1]=item_table[13]},},
{wave=34,},
{wave=35,},
{wave=36,reward_item={[0]=item_table[6],[1]=item_table[24]},},
{wave=37,},
{wave=38,},
{wave=39,reward_item={[0]=item_table[4],[1]=item_table[15]},},
{wave=40,},
{wave=41,},
{wave=42,},
{wave=43,},
{wave=44,},
{wave=45,reward_item={[0]=item_table[17],[1]=item_table[27]},},
{wave=46,},
{wave=47,},
{wave=48,reward_item={[0]=item_table[10],[1]=item_table[15]},},
{wave=49,},
{wave=50,},
{wave=51,reward_item={[0]=item_table[14],[1]=item_table[20]},},
{wave=52,},
{wave=53,},
{wave=54,reward_item={[0]=item_table[23],[1]=item_table[28]},},
{wave=55,},
{wave=56,},
{wave=57,reward_item={[0]=item_table[23],[1]=item_table[24]},},
{wave=58,},
{wave=59,},
{wave=60,reward_item={[0]=item_table[19],[1]=item_table[24]},},
{wave=61,},
{wave=62,},
{wave=63,reward_item={[0]=item_table[29],[1]=item_table[30]},},
{wave=64,client_show_reward={[0]=item_table[31],[1]=item_table[32]},},
{wave=65,},
{wave=66,reward_item={[0]=item_table[33],[1]=item_table[24]},},
{wave=67,},
{wave=68,},
{wave=69,reward_item={[0]=item_table[31],[1]=item_table[24]},desc2="注意躲避BOSS技能",client_show_reward={[0]=item_table[31],[1]=item_table[32]},},
{wave=70,},
{wave=71,},
{wave=72,reward_item={[0]=item_table[34],[1]=item_table[35]},desc2="注意躲避BOSS技能",client_show_reward={[0]=item_table[36],[1]=item_table[32]},},
{wave=73,},
{wave=74,client_show_reward={[0]=item_table[36],[1]=item_table[32]},},
{wave=75,reward_item={[0]=item_table[37],[1]=item_table[38]},},
{wave=76,},
{wave=77,},
{wave=78,reward_item={[0]=item_table[37],[1]=item_table[38]},},
{wave=79,},
{wave=80,},
{wave=81,reward_item={[0]=item_table[39],[1]=item_table[35]},desc1="深入腹地",},
{wave=82,},
{wave=83,client_show_reward={[0]=item_table[36],[1]=item_table[32]},},
{wave=84,},
{wave=85,},
{wave=86,},
{wave=87,reward_item={[0]=item_table[40],[1]=item_table[35]},},
{wave=88,},
{wave=89,},
{wave=90,reward_item={[0]=item_table[41],[1]=item_table[42]},},
{wave=91,},
{wave=92,client_show_reward={[0]=item_table[33],[1]=item_table[27]},},
{wave=93,reward_item={[0]=item_table[43],[1]=item_table[35]},},
{wave=94,},
{wave=95,},
{wave=96,reward_item={[0]=item_table[43],[1]=item_table[44]},client_show_reward={[0]=item_table[33],[1]=item_table[27]},},
{wave=97,},
{wave=98,},
{wave=99,reward_item={[0]=item_table[45],[1]=item_table[46],[2]=item_table[41],[3]=item_table[47]},client_show_reward={[0]=item_table[48],[1]=item_table[33],[2]=item_table[27]},},
{wave=100,client_show_reward={[0]=item_table[48],[1]=item_table[33],[2]=item_table[27]},},
{wave=101,},
{wave=102,reward_item={[0]=item_table[49],[1]=item_table[43],[2]=item_table[50]},},
{wave=103,desc2="注意躲避BOSS技能",},
{wave=104,client_show_reward={[0]=item_table[51],[1]=item_table[33],[2]=item_table[27]},},
{wave=105,reward_item={[0]=item_table[49],[1]=item_table[43],[2]=item_table[50]},},
{wave=106,desc2="注意躲避BOSS技能",},
{wave=107,client_show_reward={[0]=item_table[52],[1]=item_table[33],[2]=item_table[27]},},
{wave=108,reward_item={[0]=item_table[53],[1]=item_table[54],[2]=item_table[55],[3]=item_table[42]},desc1="深入腹地",client_show_reward={[0]=item_table[52],[1]=item_table[56],[2]=item_table[33],[3]=item_table[27]},},
{wave=109,desc2="注意躲避BOSS技能",},
{wave=110,client_show_reward={[0]=item_table[57],[1]=item_table[56],[2]=item_table[33],[3]=item_table[27]},},
{wave=111,reward_item={[0]=item_table[58],[1]=item_table[46],[2]=item_table[59],[3]=item_table[35]},},
{wave=112,},
{wave=113,client_show_reward={[0]=item_table[60],[1]=item_table[56],[2]=item_table[33],[3]=item_table[27]},},
{wave=114,reward_item={[0]=item_table[58],[1]=item_table[46],[2]=item_table[59],[3]=item_table[35]},},
{wave=115,desc2="注意躲避BOSS技能",},
{wave=116,},
{wave=117,reward_item={[0]=item_table[61],[1]=item_table[62],[2]=item_table[63],[3]=item_table[64]},},
{wave=118,desc2="注意躲避BOSS技能",},
{wave=119,},
{wave=120,reward_item={[0]=item_table[65],[1]=item_table[46],[2]=item_table[63],[3]=item_table[50]},},
{wave=121,},
{wave=122,},
{wave=123,},
{wave=124,},
{wave=125,},
{wave=126,reward_item={[0]=item_table[66],[1]=item_table[67],[2]=item_table[68],[3]=item_table[69]},},
{wave=127,},
{wave=128,},
{wave=129,},
{wave=130,},
{wave=131,},
{wave=132,},
{wave=133,},
{wave=134,},
{wave=135,reward_item={[0]=item_table[66],[1]=item_table[67],[2]=item_table[70],[3]=item_table[71]},},
{wave=136,},
{wave=137,desc1="深入腹地",client_show_reward={[0]=item_table[49],[1]=item_table[72],[2]=item_table[73],[3]=item_table[27]},},
{wave=138,},
{wave=139,},
{wave=140,},
{wave=141,reward_item={[0]=item_table[65],[1]=item_table[62],[2]=item_table[74],[3]=item_table[69]},},
{wave=142,},
{wave=143,},
{wave=144,reward_item={[0]=item_table[75],[1]=item_table[76],[2]=item_table[77],[3]=item_table[78]},},
{wave=145,client_show_reward={[0]=item_table[49],[1]=item_table[72],[2]=item_table[79],[3]=item_table[27]},},
{wave=146,},
{wave=147,},
{wave=148,},
{wave=149,client_show_reward={[0]=item_table[49],[1]=item_table[72],[2]=item_table[79],[3]=item_table[27]},},
{wave=150,reward_item={[0]=item_table[66],[1]=item_table[62],[2]=item_table[80],[3]=item_table[69]},},
{wave=151,client_show_reward={[0]=item_table[49],[1]=item_table[72],[2]=item_table[79],[3]=item_table[27]},},
{wave=152,client_show_reward={[0]=item_table[49],[1]=item_table[72],[2]=item_table[79],[3]=item_table[27]},},
{wave=153,reward_item={[0]=item_table[66],[1]=item_table[76],[2]=item_table[81],[3]=item_table[82]},},
{wave=154,},
{wave=155,},
{wave=156,},
{wave=157,},
{wave=158,client_show_reward={[0]=item_table[49],[1]=item_table[72],[2]=item_table[79],[3]=item_table[28]},},
{wave=159,},
{wave=160,},
{wave=161,},
{wave=162,reward_item={[0]=item_table[83],[1]=item_table[84],[2]=item_table[85],[3]=item_table[86]},},
{wave=163,client_show_reward={[0]=item_table[49],[1]=item_table[72],[2]=item_table[79],[3]=item_table[28]},},
{wave=164,},
{wave=165,reward_item={[0]=item_table[66],[1]=item_table[62],[2]=item_table[80],[3]=item_table[69]},},
{wave=166,},
{wave=167,},
{wave=168,},
{wave=169,},
{wave=170,},
{wave=171,reward_item={[0]=item_table[83],[1]=item_table[84],[2]=item_table[77],[3]=item_table[86]},},
{wave=172,client_show_reward={[0]=item_table[49],[1]=item_table[72],[2]=item_table[87],[3]=item_table[28]},},
{wave=173,},
{wave=174,reward_item={[0]=item_table[75],[1]=item_table[54],[2]=item_table[88],[3]=item_table[89]},},
{wave=175,},
{wave=176,},
{wave=177,},
{wave=178,},
{wave=179,},
{wave=180,reward_item={[0]=item_table[90],[1]=item_table[91],[2]=item_table[92],[3]=item_table[93]},},
{wave=181,},
{wave=182,client_show_reward={[0]=item_table[49],[1]=item_table[72],[2]=item_table[87],[3]=item_table[28]},},
{wave=183,reward_item={[0]=item_table[94],[1]=item_table[62],[2]=item_table[95],[3]=item_table[96]},},
{wave=184,},
{wave=185,},
{wave=186,},
{wave=187,},
{wave=188,},
{wave=189,reward_item={[0]=item_table[97],[1]=item_table[98],[2]=item_table[99],[3]=item_table[100]},},
{wave=190,},
{wave=191,},
{wave=192,reward_item={[0]=item_table[66],[1]=item_table[62],[2]=item_table[101],[3]=item_table[102]},},
{wave=193,},
{wave=194,desc1="一夫当关",client_show_reward={[0]=item_table[45],[1]=item_table[72],[2]=item_table[34],[3]=item_table[28]},},
{wave=195,},
{wave=196,},
{wave=197,},
{wave=198,reward_item={[0]=item_table[103],[1]=item_table[104],[2]=item_table[105],[3]=item_table[86]},},
{wave=199,},
{wave=200,},
{wave=201,},
{wave=202,},
{wave=203,},
{wave=204,reward_item={[0]=item_table[66],[1]=item_table[62],[2]=item_table[85],[3]=item_table[102]},},
{wave=205,},
{wave=206,desc2="注意躲避BOSS技能",},
{wave=207,reward_item={[0]=item_table[106],[1]=item_table[84],[2]=item_table[107],[3]=item_table[108]},},
{wave=208,},
{wave=209,client_show_reward={[0]=item_table[45],[1]=item_table[72],[2]=item_table[34],[3]=item_table[109]},},
{wave=210,reward_item={[0]=item_table[75],[1]=item_table[54],[2]=item_table[110],[3]=item_table[89]},},
{wave=211,client_show_reward={[0]=item_table[45],[1]=item_table[72],[2]=item_table[34],[3]=item_table[109]},},
{wave=212,},
{wave=213,},
{wave=214,},
{wave=215,},
{wave=216,reward_item={[0]=item_table[111],[1]=item_table[91],[2]=item_table[112],[3]=item_table[113]},},
{wave=217,},
{wave=218,},
{wave=219,},
{wave=220,client_show_reward={[0]=item_table[45],[1]=item_table[72],[2]=item_table[114],[3]=item_table[109]},},
{wave=221,client_show_reward={[0]=item_table[45],[1]=item_table[72],[2]=item_table[114],[3]=item_table[109]},},
{wave=222,reward_item={[0]=item_table[115],[1]=item_table[54],[2]=item_table[116],[3]=item_table[117]},},
{wave=223,},
{wave=224,},
{wave=225,reward_item={[0]=item_table[118],[1]=item_table[119],[2]=item_table[120],[3]=item_table[121]},},
{wave=226,client_show_reward={[0]=item_table[45],[1]=item_table[72],[2]=item_table[114],[3]=item_table[109]},},
{wave=227,},
{wave=228,},
{wave=229,},
{wave=230,desc2="注意躲避BOSS技能",},
{wave=231,reward_item={[0]=item_table[115],[1]=item_table[54],[2]=item_table[116],[3]=item_table[117]},},
{wave=232,},
{wave=233,},
{wave=234,reward_item={[0]=item_table[122],[1]=item_table[119],[2]=item_table[123],[3]=item_table[124]},},
{wave=235,},
{wave=236,},
{wave=237,},
{wave=238,},
{wave=239,},
{wave=240,reward_item={[0]=item_table[94],[1]=item_table[62],[2]=item_table[95],[3]=item_table[96]},},
{wave=241,},
{wave=242,desc2="注意躲避BOSS技能",},
{wave=243,reward_item={[0]=item_table[115],[1]=item_table[119],[2]=item_table[125],[3]=item_table[126]},},
{wave=244,},
{wave=245,},
{wave=246,reward_item={[0]=item_table[94],[1]=item_table[62],[2]=item_table[127],[3]=item_table[82]},},
{wave=247,},
{wave=248,},
{wave=249,},
{wave=250,},
{wave=251,},
{wave=252,reward_item={[0]=item_table[75],[1]=item_table[91],[2]=item_table[95],[3]=item_table[126]},},
{wave=253,},
{wave=254,},
{wave=255,},
{wave=256,},
{wave=257,},
{wave=258,},
{wave=259,},
{wave=260,},
{wave=261,reward_item={[0]=item_table[75],[1]=item_table[98],[2]=item_table[128],[3]=item_table[89]},},
{wave=262,},
{wave=263,},
{wave=264,},
{wave=265,},
{wave=266,},
{wave=267,},
{wave=268,},
{wave=269,},
{wave=270,reward_item={[0]=item_table[129],[1]=item_table[98],[2]=item_table[130],[3]=item_table[131]},},
{wave=271,},
{wave=272,},
{wave=273,reward_item={[0]=item_table[75],[1]=item_table[54],[2]=item_table[110],[3]=item_table[89]},},
{wave=274,},
{wave=275,},
{wave=276,},
{wave=277,},
{wave=278,},
{wave=279,reward_item={[0]=item_table[118],[1]=item_table[67],[2]=item_table[132],[3]=item_table[133]},},
{wave=280,desc1="决战古殿",},
{wave=281,},
{wave=282,reward_item={[0]=item_table[66],[1]=item_table[62],[2]=item_table[107],[3]=item_table[102]},},
{wave=283,},
{wave=284,},
{wave=285,},
{wave=286,},
{wave=287,},
{wave=288,reward_item={[0]=item_table[129],[1]=item_table[67],[2]=item_table[134],[3]=item_table[135]},},
{wave=289,},
{wave=290,},
{wave=291,reward_item={[0]=item_table[75],[1]=item_table[54],[2]=item_table[136],[3]=item_table[100]},},
{wave=292,},
{wave=293,},
{wave=294,},
{wave=295,},
{wave=296,},
{wave=297,reward_item={[0]=item_table[129],[1]=item_table[54],[2]=item_table[137],[3]=item_table[131]},desc1="决战古殿",client_show_reward={[0]=item_table[45],[1]=item_table[72],[2]=item_table[29],[3]=item_table[109]},},
{wave=298,client_show_reward={[0]=item_table[45],[1]=item_table[72],[2]=item_table[29],[3]=item_table[138]},},
{wave=299,desc2="注意躲避BOSS技能",},
{wave=300,reward_item={[0]=item_table[94],[1]=item_table[62],[2]=item_table[127],[3]=item_table[96]},}
},

reward_meta_table_map={
[62]=64,	-- depth:1
[61]=62,	-- depth:2
[59]=61,	-- depth:3
[58]=59,	-- depth:4
[56]=58,	-- depth:5
[65]=56,	-- depth:6
[55]=65,	-- depth:7
[52]=55,	-- depth:8
[50]=52,	-- depth:9
[49]=50,	-- depth:10
[283]=280,	-- depth:1
[47]=49,	-- depth:11
[46]=47,	-- depth:12
[44]=46,	-- depth:13
[53]=44,	-- depth:14
[277]=283,	-- depth:2
[67]=53,	-- depth:15
[68]=67,	-- depth:16
[250]=277,	-- depth:3
[256]=250,	-- depth:4
[259]=256,	-- depth:5
[247]=259,	-- depth:6
[262]=247,	-- depth:7
[244]=262,	-- depth:8
[265]=244,	-- depth:9
[241]=265,	-- depth:10
[238]=241,	-- depth:11
[268]=238,	-- depth:12
[235]=268,	-- depth:13
[271]=235,	-- depth:14
[274]=271,	-- depth:15
[73]=74,	-- depth:1
[71]=68,	-- depth:17
[70]=71,	-- depth:18
[43]=70,	-- depth:19
[41]=43,	-- depth:20
[253]=274,	-- depth:16
[292]=253,	-- depth:17
[40]=41,	-- depth:21
[295]=292,	-- depth:18
[11]=10,	-- depth:1
[34]=40,	-- depth:22
[35]=34,	-- depth:23
[26]=25,	-- depth:1
[37]=35,	-- depth:24
[32]=37,	-- depth:25
[31]=32,	-- depth:26
[2]=1,	-- depth:1
[38]=31,	-- depth:27
[289]=295,	-- depth:19
[29]=38,	-- depth:28
[28]=29,	-- depth:29
[286]=289,	-- depth:20
[242]=280,	-- depth:1
[240]=280,	-- depth:1
[251]=242,	-- depth:2
[239]=251,	-- depth:3
[140]=137,	-- depth:1
[128]=140,	-- depth:2
[134]=128,	-- depth:3
[202]=194,	-- depth:1
[125]=134,	-- depth:4
[245]=239,	-- depth:4
[197]=202,	-- depth:2
[131]=125,	-- depth:5
[246]=280,	-- depth:1
[143]=131,	-- depth:6
[249]=246,	-- depth:2
[248]=245,	-- depth:5
[243]=280,	-- depth:1
[182]=194,	-- depth:1
[158]=194,	-- depth:1
[236]=248,	-- depth:6
[179]=182,	-- depth:2
[211]=194,	-- depth:1
[185]=179,	-- depth:3
[214]=211,	-- depth:2
[176]=185,	-- depth:4
[208]=214,	-- depth:3
[122]=143,	-- depth:7
[188]=176,	-- depth:5
[173]=188,	-- depth:6
[170]=158,	-- depth:2
[191]=197,	-- depth:3
[220]=194,	-- depth:1
[205]=191,	-- depth:4
[167]=170,	-- depth:3
[223]=220,	-- depth:2
[164]=167,	-- depth:4
[161]=164,	-- depth:5
[226]=280,	-- depth:1
[229]=226,	-- depth:2
[155]=161,	-- depth:6
[152]=194,	-- depth:1
[232]=229,	-- depth:3
[149]=137,	-- depth:1
[234]=280,	-- depth:1
[146]=149,	-- depth:2
[237]=240,	-- depth:2
[217]=223,	-- depth:3
[100]=137,	-- depth:1
[119]=122,	-- depth:8
[83]=137,	-- depth:1
[82]=83,	-- depth:2
[272]=236,	-- depth:7
[252]=280,	-- depth:1
[79]=82,	-- depth:3
[273]=280,	-- depth:1
[77]=79,	-- depth:4
[76]=77,	-- depth:5
[275]=272,	-- depth:8
[276]=273,	-- depth:2
[278]=275,	-- depth:9
[85]=76,	-- depth:6
[279]=280,	-- depth:1
[282]=280,	-- depth:1
[284]=278,	-- depth:10
[285]=282,	-- depth:2
[287]=284,	-- depth:11
[288]=280,	-- depth:1
[290]=287,	-- depth:12
[291]=280,	-- depth:1
[293]=290,	-- depth:13
[294]=291,	-- depth:2
[296]=293,	-- depth:14
[298]=280,	-- depth:1
[281]=296,	-- depth:15
[86]=85,	-- depth:7
[80]=86,	-- depth:8
[264]=249,	-- depth:3
[101]=100,	-- depth:2
[270]=280,	-- depth:1
[104]=137,	-- depth:1
[263]=281,	-- depth:16
[107]=137,	-- depth:1
[261]=280,	-- depth:1
[266]=263,	-- depth:17
[260]=266,	-- depth:18
[258]=264,	-- depth:4
[113]=137,	-- depth:1
[257]=260,	-- depth:19
[116]=113,	-- depth:2
[255]=258,	-- depth:5
[254]=257,	-- depth:20
[110]=137,	-- depth:1
[98]=101,	-- depth:3
[200]=205,	-- depth:5
[92]=137,	-- depth:1
[97]=92,	-- depth:2
[88]=80,	-- depth:9
[269]=254,	-- depth:21
[267]=255,	-- depth:6
[89]=88,	-- depth:10
[91]=97,	-- depth:3
[94]=91,	-- depth:4
[95]=94,	-- depth:5
[207]=211,	-- depth:2
[206]=194,	-- depth:1
[231]=226,	-- depth:2
[204]=194,	-- depth:1
[203]=206,	-- depth:2
[230]=226,	-- depth:2
[209]=206,	-- depth:2
[221]=206,	-- depth:2
[210]=211,	-- depth:2
[222]=220,	-- depth:2
[227]=230,	-- depth:3
[212]=209,	-- depth:3
[213]=210,	-- depth:3
[215]=212,	-- depth:4
[225]=220,	-- depth:2
[216]=220,	-- depth:2
[224]=221,	-- depth:3
[218]=224,	-- depth:4
[219]=222,	-- depth:3
[201]=204,	-- depth:2
[228]=231,	-- depth:3
[233]=227,	-- depth:4
[150]=149,	-- depth:2
[198]=194,	-- depth:1
[75]=72,	-- depth:1
[102]=104,	-- depth:2
[103]=104,	-- depth:2
[105]=107,	-- depth:2
[106]=107,	-- depth:2
[109]=110,	-- depth:2
[111]=110,	-- depth:2
[112]=109,	-- depth:3
[114]=113,	-- depth:2
[115]=113,	-- depth:2
[117]=137,	-- depth:1
[118]=137,	-- depth:1
[120]=137,	-- depth:1
[121]=118,	-- depth:2
[123]=120,	-- depth:2
[199]=203,	-- depth:3
[126]=137,	-- depth:1
[127]=121,	-- depth:3
[66]=69,	-- depth:1
[60]=69,	-- depth:1
[6]=3,	-- depth:1
[12]=3,	-- depth:1
[21]=3,	-- depth:1
[27]=69,	-- depth:1
[30]=69,	-- depth:1
[33]=69,	-- depth:1
[36]=69,	-- depth:1
[39]=69,	-- depth:1
[42]=39,	-- depth:2
[45]=69,	-- depth:1
[48]=69,	-- depth:1
[51]=69,	-- depth:1
[54]=69,	-- depth:1
[57]=69,	-- depth:1
[63]=69,	-- depth:1
[129]=123,	-- depth:3
[124]=127,	-- depth:4
[132]=129,	-- depth:4
[163]=206,	-- depth:2
[165]=158,	-- depth:2
[166]=163,	-- depth:3
[168]=165,	-- depth:3
[169]=166,	-- depth:4
[171]=182,	-- depth:2
[172]=206,	-- depth:2
[174]=182,	-- depth:2
[175]=172,	-- depth:3
[177]=174,	-- depth:3
[178]=175,	-- depth:4
[180]=182,	-- depth:2
[181]=178,	-- depth:5
[183]=182,	-- depth:2
[184]=181,	-- depth:6
[186]=183,	-- depth:3
[187]=184,	-- depth:7
[189]=194,	-- depth:1
[190]=199,	-- depth:4
[192]=194,	-- depth:1
[193]=190,	-- depth:5
[195]=192,	-- depth:2
[196]=193,	-- depth:6
[162]=158,	-- depth:2
[160]=169,	-- depth:5
[300]=298,	-- depth:2
[299]=298,	-- depth:2
[145]=118,	-- depth:2
[147]=150,	-- depth:3
[148]=145,	-- depth:3
[130]=124,	-- depth:5
[151]=206,	-- depth:2
[153]=158,	-- depth:2
[154]=160,	-- depth:6
[144]=149,	-- depth:2
[156]=168,	-- depth:4
[159]=156,	-- depth:5
[141]=137,	-- depth:1
[139]=130,	-- depth:6
[138]=141,	-- depth:2
[136]=139,	-- depth:7
[135]=137,	-- depth:1
[133]=136,	-- depth:8
[157]=154,	-- depth:7
[142]=133,	-- depth:9
[81]=72,	-- depth:1
[96]=81,	-- depth:2
[93]=96,	-- depth:3
[90]=96,	-- depth:3
[87]=81,	-- depth:2
[84]=87,	-- depth:3
[99]=81,	-- depth:2
[78]=81,	-- depth:2
},
buy_times={
{price=50,},
{buy_times=2,price=60,},
{buy_times=3,},
{buy_times=4,},
{buy_times=5,},
{buy_times=6,},
{buy_times=7,},
{buy_times=8,}
},

buy_times_meta_table_map={
},
other_default_table={open_level=999,everyday_times=2,buy_times_need_gold=50,help_times=5,help_honor=50,prepare_time_s=5,wave_time=30,scene_id=7600,pos_x=294,pos_y=30,show_reward_item={[0]=item_table[139],[1]=item_table[140],[2]=item_table[141],[3]=item_table[142]},},

wave_default_table={wave=1,enter_pos_x=294,enter_pos_y=30,relive_pos_x=294,relive_pos_y=30,},

monster_default_table={wave=1,monster_id=21000,pos_x=290,pos_y=33,range=8,monster_num=8,},

reward_default_table={wave=1,wave_exp=244,reward_item={},dropid_list="10001|10003|10018|10019|10020",desc1="突破包围",desc2="把小怪拉一起效率更高",client_show_reward={[0]=item_table[45],[1]=item_table[72],[2]=item_table[143],[3]=item_table[109]},},

buy_times_default_table={buy_times=1,price=70,}

}

