-- L-领主每日商店.xls
local item_table={
[1]={item_id=30447,num=1,is_bind=1},
[2]={item_id=26357,num=1,is_bind=1},
[3]={item_id=26375,num=1,is_bind=1},
[4]={item_id=26515,num=1,is_bind=1},
[5]={item_id=22011,num=1,is_bind=1},
[6]={item_id=22531,num=1,is_bind=1},
[7]={item_id=48077,num=1,is_bind=1},
[8]={item_id=22532,num=1,is_bind=1},
[9]={item_id=22010,num=1,is_bind=1},
[10]={item_id=26130,num=1,is_bind=1},
[11]={item_id=26347,num=1,is_bind=1},
[12]={item_id=48502,num=1,is_bind=1},
[13]={item_id=26129,num=1,is_bind=1},
[14]={item_id=48071,num=1,is_bind=1},
[15]={item_id=30448,num=1,is_bind=1},
[16]={item_id=26409,num=5,is_bind=1},
[17]={item_id=26214,num=1,is_bind=1},
[18]={item_id=22734,num=1,is_bind=1},
[19]={item_id=29615,num=2,is_bind=1},
[20]={item_id=26360,num=1,is_bind=1},
[21]={item_id=26363,num=1,is_bind=1},
[22]={item_id=26380,num=1,is_bind=1},
[23]={item_id=26369,num=1,is_bind=1},
[24]={item_id=26502,num=1,is_bind=1},
[25]={item_id=26500,num=1,is_bind=1},
[26]={item_id=28826,num=1,is_bind=1},
[27]={item_id=27910,num=1,is_bind=1},
[28]={item_id=44049,num=1,is_bind=1},
[29]={item_id=48144,num=1,is_bind=1},
[30]={item_id=48145,num=1,is_bind=1},
[31]={item_id=48146,num=1,is_bind=1},
[32]={item_id=48078,num=1,is_bind=1},
[33]={item_id=48079,num=1,is_bind=1},
[34]={item_id=48080,num=1,is_bind=1},
[35]={item_id=26501,num=1,is_bind=1},
[36]={item_id=26517,num=1,is_bind=1},
[37]={item_id=26516,num=1,is_bind=1},
[38]={item_id=44050,num=1,is_bind=1},
[39]={item_id=44051,num=1,is_bind=1},
[40]={item_id=44070,num=1,is_bind=1},
[41]={item_id=44071,num=1,is_bind=1},
[42]={item_id=48147,num=1,is_bind=1},
[43]={item_id=48148,num=1,is_bind=1},
[44]={item_id=48081,num=1,is_bind=1},
[45]={item_id=48082,num=1,is_bind=1},
[46]={item_id=26099,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
shop={
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,seq=3,is_fixed=1,},
{grade=1,seq=4,},
{grade=1,seq=5,reward_item={[0]=item_table[1]},},
{grade=1,seq=6,},
{grade=1,reward_item={[0]=item_table[2]},},
{grade=1,seq=8,},
{grade=1,seq=9,},
{grade=1,reward_item={[0]=item_table[3]},},
{grade=1,seq=11,},
{grade=1,seq=12,},
{grade=1,seq=13,},
{grade=1,reward_item={[0]=item_table[4]},},
{grade=1,seq=15,},
{grade=1,buy_limit=5,},
{grade=1,seq=17,consume_num=3,reward_item={[0]=item_table[5]},},
{grade=1,consume_num=2,reward_item={[0]=item_table[6]},},
{grade=1,consume_num=12,reward_item={[0]=item_table[7]},},
{grade=1,seq=20,},
{grade=1,seq=21,},
{grade=1,seq=22,},
{grade=1,},
{grade=1,seq=24,consume_num=10,reward_item={[0]=item_table[8]},},
{grade=1,seq=25,consume_num=1,reward_item={[0]=item_table[9]},},
{grade=1,reward_item={[0]=item_table[10]},},
{grade=1,seq=27,reward_item={[0]=item_table[11]},},
{seq=0,consume_num=10,is_fixed=1,},
{seq=1,consume_num=5,reward_item={[0]=item_table[12]},},
{consume_num=1,reward_item={[0]=item_table[13]},buy_limit=10,is_fixed=1,},
{consume_num=2,reward_item={[0]=item_table[14]},buy_limit=3,is_fixed=1,},
{seq=3,consume_num=5,reward_item={[0]=item_table[15]},buy_limit=10,},
{reward_item={[0]=item_table[16]},buy_limit=9,},
{seq=4,consume_num=1,reward_item={[0]=item_table[17]},buy_limit=3,},
{consume_num=5,reward_item={[0]=item_table[18]},},
{seq=5,consume_num=15,reward_item={[0]=item_table[19]},buy_limit=4,},
{seq=5,reward_item={[0]=item_table[2]},},
{seq=6,reward_item={[0]=item_table[20]},},
{seq=7,reward_item={[0]=item_table[21]},},
{seq=8,reward_item={[0]=item_table[3]},},
{seq=9,reward_item={[0]=item_table[22]},},
{seq=10,reward_item={[0]=item_table[23]},},
{seq=11,reward_item={[0]=item_table[10]},},
{seq=12,reward_item={[0]=item_table[24]},},
{seq=13,reward_item={[0]=item_table[4]},},
{seq=14,consume_num=1,reward_item={[0]=item_table[25]},buy_limit=5,},
{seq=15,consume_num=3,reward_item={[0]=item_table[26]},buy_limit=2,},
{seq=16,reward_item={[0]=item_table[27]},},
{seq=17,consume_num=10,reward_item={[0]=item_table[28]},},
{seq=18,consume_num=34,reward_item={[0]=item_table[29]},buy_limit=3,},
{seq=19,consume_num=45,reward_item={[0]=item_table[30]},buy_limit=3,},
{seq=20,consume_num=68,reward_item={[0]=item_table[31]},buy_limit=3,},
{seq=21,consume_num=15,reward_item={[0]=item_table[32]},buy_limit=3,},
{seq=22,consume_num=23,reward_item={[0]=item_table[33]},buy_limit=3,},
{seq=23,reward_item={[0]=item_table[34]},buy_limit=3,},
{seq=24,reward_item={[0]=item_table[35]},},
{seq=25,consume_num=9,reward_item={[0]=item_table[36]},},
{seq=26,consume_num=3,reward_item={[0]=item_table[37]},buy_limit=3,},
{seq=27,reward_item={[0]=item_table[38]},},
{seq=28,consume_num=3,reward_item={[0]=item_table[39]},},
{seq=29,reward_item={[0]=item_table[40]},},
{seq=30,consume_num=10,reward_item={[0]=item_table[41]},buy_limit=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,reward_item={[0]=item_table[2]},},
{grade=3,seq=10,},
{grade=3,seq=11,},
{grade=3,seq=12,},
{grade=3,seq=13,},
{grade=3,seq=14,},
{grade=3,},
{grade=3,seq=16,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,seq=20,},
{grade=3,seq=21,},
{grade=3,seq=22,},
{grade=3,consume_num=75,reward_item={[0]=item_table[42]},},
{seq=24,consume_num=83,reward_item={[0]=item_table[43]},},
{grade=3,seq=25,},
{grade=3,consume_num=28,reward_item={[0]=item_table[44]},},
{seq=27,consume_num=30,reward_item={[0]=item_table[45]},},
{grade=3,seq=28,},
{grade=3,seq=29,},
{grade=3,seq=30,},
{grade=3,seq=31,},
{grade=3,seq=32,},
{grade=3,seq=33,},
{grade=3,seq=34,},
{grade=4,},
{grade=4,seq=1,is_fixed=1,},
{grade=4,},
{grade=4,seq=3,},
{grade=4,reward_item={[0]=item_table[12]},},
{grade=4,seq=5,},
{grade=4,seq=6,},
{grade=4,seq=7,},
{grade=4,seq=8,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,seq=15,},
{grade=4,},
{grade=4,seq=17,},
{grade=4,seq=18,},
{grade=4,seq=19,},
{grade=4,},
{grade=4,},
{grade=4,seq=22,},
{grade=4,seq=23,},
{grade=4,seq=24,},
{grade=4,seq=25,},
{grade=4,seq=26,},
{grade=4,seq=27,},
{grade=4,seq=28,}
},

shop_meta_table_map={
[9]=39,	-- depth:1
[124]=60,	-- depth:1
[126]=62,	-- depth:1
[73]=42,	-- depth:1
[74]=39,	-- depth:1
[75]=40,	-- depth:1
[76]=41,	-- depth:1
[77]=42,	-- depth:1
[78]=43,	-- depth:1
[49]=50,	-- depth:1
[8]=40,	-- depth:1
[45]=58,	-- depth:1
[97]=62,	-- depth:1
[108]=73,	-- depth:2
[109]=74,	-- depth:2
[110]=75,	-- depth:2
[111]=76,	-- depth:2
[112]=77,	-- depth:2
[113]=78,	-- depth:2
[13]=43,	-- depth:1
[12]=42,	-- depth:1
[11]=43,	-- depth:1
[10]=40,	-- depth:1
[95]=60,	-- depth:1
[57]=59,	-- depth:1
[1]=29,	-- depth:1
[84]=49,	-- depth:2
[80]=45,	-- depth:2
[85]=50,	-- depth:1
[89]=56,	-- depth:1
[93]=58,	-- depth:1
[96]=61,	-- depth:1
[99]=1,	-- depth:2
[115]=80,	-- depth:3
[119]=84,	-- depth:3
[120]=85,	-- depth:2
[122]=58,	-- depth:1
[125]=61,	-- depth:1
[64]=99,	-- depth:3
[24]=122,	-- depth:2
[14]=45,	-- depth:2
[34]=33,	-- depth:1
[36]=35,	-- depth:1
[44]=59,	-- depth:1
[46]=47,	-- depth:1
[5]=34,	-- depth:2
[101]=31,	-- depth:1
[103]=36,	-- depth:2
[104]=34,	-- depth:2
[105]=35,	-- depth:1
[106]=36,	-- depth:2
[107]=37,	-- depth:1
[19]=51,	-- depth:1
[16]=47,	-- depth:1
[121]=57,	-- depth:2
[15]=47,	-- depth:1
[114]=44,	-- depth:2
[7]=37,	-- depth:1
[20]=52,	-- depth:1
[3]=101,	-- depth:2
[123]=59,	-- depth:1
[116]=15,	-- depth:2
[117]=47,	-- depth:1
[118]=48,	-- depth:1
[6]=33,	-- depth:1
[17]=49,	-- depth:2
[98]=63,	-- depth:1
[91]=89,	-- depth:2
[22]=57,	-- depth:2
[66]=3,	-- depth:3
[68]=103,	-- depth:3
[69]=104,	-- depth:3
[70]=105,	-- depth:2
[71]=106,	-- depth:3
[72]=107,	-- depth:2
[30]=32,	-- depth:1
[79]=114,	-- depth:3
[21]=54,	-- depth:1
[81]=116,	-- depth:3
[82]=117,	-- depth:2
[28]=48,	-- depth:1
[27]=59,	-- depth:1
[86]=53,	-- depth:1
[87]=56,	-- depth:1
[88]=87,	-- depth:2
[90]=59,	-- depth:1
[92]=57,	-- depth:2
[94]=59,	-- depth:1
[23]=59,	-- depth:1
[83]=118,	-- depth:2
[127]=63,	-- depth:1
[102]=32,	-- depth:1
[67]=102,	-- depth:2
[4]=35,	-- depth:1
[100]=33,	-- depth:1
[65]=100,	-- depth:2
[2]=65,	-- depth:3
},
config_param={
{},
{start_day=3,end_day=5,grade=2,},
{start_day=6,end_day=10,grade=3,},
{start_day=11,end_day=9999,grade=4,}
},

config_param_meta_table_map={
},
other_default_table={refresh_type=2,refresh_consume=200,show_num=9,exchange_item_id=31899,open_panel="boss#boss_vip",boss_shop_panel="FashionExchangeShopView#fashion_exchange_shop_high1",},

shop_default_table={grade=2,seq=2,consume_item_id=31899,consume_num=25,reward_item={[0]=item_table[46]},buy_limit=1,is_fixed=0,},

config_param_default_table={start_day=1,end_day=2,grade=1,}

}

