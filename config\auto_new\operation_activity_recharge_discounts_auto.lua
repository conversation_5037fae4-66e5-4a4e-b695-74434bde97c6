-- Y-运营活动-充值立减.xls
local item_table={
[1]={item_id=37717,num=1,is_bind=1},
[2]={item_id=26409,num=20,is_bind=1},
[3]={item_id=26367,num=3,is_bind=1},
[4]={item_id=26368,num=3,is_bind=1},
[5]={item_id=26369,num=1,is_bind=1},
[6]={item_id=26344,num=20,is_bind=1},
[7]={item_id=37540,num=1,is_bind=1},
[8]={item_id=26409,num=45,is_bind=1},
[9]={item_id=26367,num=4,is_bind=1},
[10]={item_id=26368,num=4,is_bind=1},
[11]={item_id=26369,num=2,is_bind=1},
[12]={item_id=26344,num=30,is_bind=1},
[13]={item_id=26367,num=5,is_bind=1},
[14]={item_id=26368,num=5,is_bind=1},
[15]={item_id=26345,num=5,is_bind=1},
[16]={item_id=37023,num=1,is_bind=1},
[17]={item_id=26409,num=90,is_bind=1},
[18]={item_id=26367,num=10,is_bind=1},
[19]={item_id=26368,num=10,is_bind=1},
[20]={item_id=26369,num=3,is_bind=1},
[21]={item_id=26345,num=10,is_bind=1},
[22]={item_id=37041,num=1,is_bind=1},
[23]={item_id=26367,num=20,is_bind=1},
[24]={item_id=26368,num=20,is_bind=1},
[25]={item_id=26369,num=4,is_bind=1},
[26]={item_id=26345,num=20,is_bind=1},
[27]={item_id=26569,num=1,is_bind=1},
[28]={item_id=26409,num=135,is_bind=1},
[29]={item_id=26367,num=30,is_bind=1},
[30]={item_id=26368,num=30,is_bind=1},
[31]={item_id=26369,num=5,is_bind=1},
[32]={item_id=26345,num=25,is_bind=1},
[33]={item_id=48443,num=1,is_bind=1},
[34]={item_id=26367,num=40,is_bind=1},
[35]={item_id=26368,num=40,is_bind=1},
[36]={item_id=26369,num=8,is_bind=1},
[37]={item_id=26345,num=30,is_bind=1},
[38]={item_id=37736,num=1,is_bind=1},
[39]={item_id=27947,num=5,is_bind=1},
[40]={item_id=26378,num=2,is_bind=1},
[41]={item_id=26379,num=2,is_bind=1},
[42]={item_id=26380,num=1,is_bind=1},
[43]={item_id=26376,num=10,is_bind=1},
[44]={item_id=26378,num=3,is_bind=1},
[45]={item_id=26379,num=3,is_bind=1},
[46]={item_id=26376,num=20,is_bind=1},
[47]={item_id=37541,num=1,is_bind=1},
[48]={item_id=27947,num=10,is_bind=1},
[49]={item_id=26378,num=4,is_bind=1},
[50]={item_id=26379,num=4,is_bind=1},
[51]={item_id=26380,num=2,is_bind=1},
[52]={item_id=26376,num=30,is_bind=1},
[53]={item_id=26378,num=5,is_bind=1},
[54]={item_id=26379,num=5,is_bind=1},
[55]={item_id=26377,num=5,is_bind=1},
[56]={item_id=38721,num=1,is_bind=1},
[57]={item_id=27947,num=15,is_bind=1},
[58]={item_id=26378,num=10,is_bind=1},
[59]={item_id=26379,num=10,is_bind=1},
[60]={item_id=26380,num=3,is_bind=1},
[61]={item_id=26377,num=10,is_bind=1},
[62]={item_id=38720,num=1,is_bind=1},
[63]={item_id=26378,num=20,is_bind=1},
[64]={item_id=26379,num=20,is_bind=1},
[65]={item_id=26380,num=4,is_bind=1},
[66]={item_id=26377,num=20,is_bind=1},
[67]={item_id=27947,num=20,is_bind=1},
[68]={item_id=26378,num=30,is_bind=1},
[69]={item_id=26379,num=30,is_bind=1},
[70]={item_id=26380,num=5,is_bind=1},
[71]={item_id=26377,num=25,is_bind=1},
[72]={item_id=38773,num=1,is_bind=1},
[73]={item_id=26378,num=40,is_bind=1},
[74]={item_id=26379,num=40,is_bind=1},
[75]={item_id=26380,num=8,is_bind=1},
[76]={item_id=26377,num=30,is_bind=1},
[77]={item_id=37710,num=1,is_bind=1},
[78]={item_id=47587,num=5,is_bind=1},
[79]={item_id=29852,num=2,is_bind=1},
[80]={item_id=29853,num=2,is_bind=1},
[81]={item_id=26361,num=1,is_bind=1},
[82]={item_id=26353,num=1,is_bind=1},
[83]={item_id=29852,num=4,is_bind=1},
[84]={item_id=29853,num=4,is_bind=1},
[85]={item_id=26353,num=3,is_bind=1},
[86]={item_id=37542,num=1,is_bind=1},
[87]={item_id=47587,num=10,is_bind=1},
[88]={item_id=29852,num=6,is_bind=1},
[89]={item_id=29853,num=6,is_bind=1},
[90]={item_id=26362,num=2,is_bind=1},
[91]={item_id=26353,num=8,is_bind=1},
[92]={item_id=29852,num=10,is_bind=1},
[93]={item_id=29853,num=10,is_bind=1},
[94]={item_id=26354,num=2,is_bind=1},
[95]={item_id=37207,num=1,is_bind=1},
[96]={item_id=47587,num=16,is_bind=1},
[97]={item_id=29852,num=20,is_bind=1},
[98]={item_id=29853,num=20,is_bind=1},
[99]={item_id=26363,num=3,is_bind=1},
[100]={item_id=26354,num=5,is_bind=1},
[101]={item_id=37208,num=1,is_bind=1},
[102]={item_id=29852,num=40,is_bind=1},
[103]={item_id=29853,num=40,is_bind=1},
[104]={item_id=26363,num=4,is_bind=1},
[105]={item_id=26354,num=10,is_bind=1},
[106]={item_id=47587,num=24,is_bind=1},
[107]={item_id=29852,num=60,is_bind=1},
[108]={item_id=29853,num=60,is_bind=1},
[109]={item_id=26363,num=5,is_bind=1},
[110]={item_id=26354,num=12,is_bind=1},
[111]={item_id=48442,num=1,is_bind=1},
[112]={item_id=29852,num=80,is_bind=1},
[113]={item_id=29853,num=80,is_bind=1},
[114]={item_id=26363,num=8,is_bind=1},
[115]={item_id=26354,num=15,is_bind=1},
[116]={item_id=37792,num=1,is_bind=1},
[117]={item_id=26411,num=20,is_bind=1},
[118]={item_id=26373,num=2,is_bind=1},
[119]={item_id=26374,num=2,is_bind=1},
[120]={item_id=26375,num=1,is_bind=1},
[121]={item_id=27909,num=1,is_bind=1},
[122]={item_id=26373,num=3,is_bind=1},
[123]={item_id=26374,num=3,is_bind=1},
[124]={item_id=27909,num=2,is_bind=1},
[125]={item_id=37539,num=1,is_bind=1},
[126]={item_id=26411,num=45,is_bind=1},
[127]={item_id=26373,num=4,is_bind=1},
[128]={item_id=26374,num=4,is_bind=1},
[129]={item_id=26375,num=2,is_bind=1},
[130]={item_id=27909,num=5,is_bind=1},
[131]={item_id=26373,num=5,is_bind=1},
[132]={item_id=26374,num=5,is_bind=1},
[133]={item_id=27910,num=3,is_bind=1},
[134]={item_id=37126,num=1,is_bind=1},
[135]={item_id=26411,num=90,is_bind=1},
[136]={item_id=26373,num=10,is_bind=1},
[137]={item_id=26374,num=10,is_bind=1},
[138]={item_id=26375,num=3,is_bind=1},
[139]={item_id=27910,num=7,is_bind=1},
[140]={item_id=37302,num=1,is_bind=1},
[141]={item_id=26373,num=20,is_bind=1},
[142]={item_id=26374,num=20,is_bind=1},
[143]={item_id=26375,num=4,is_bind=1},
[144]={item_id=27910,num=15,is_bind=1},
[145]={item_id=26411,num=135,is_bind=1},
[146]={item_id=26373,num=30,is_bind=1},
[147]={item_id=26374,num=30,is_bind=1},
[148]={item_id=26375,num=5,is_bind=1},
[149]={item_id=27910,num=18,is_bind=1},
[150]={item_id=26373,num=40,is_bind=1},
[151]={item_id=26374,num=40,is_bind=1},
[152]={item_id=26375,num=8,is_bind=1},
[153]={item_id=27910,num=22,is_bind=1},
[154]={item_id=37701,num=1,is_bind=1},
[155]={item_id=26198,num=5,is_bind=1},
[156]={item_id=26357,num=1,is_bind=1},
[157]={item_id=26356,num=3,is_bind=1},
[158]={item_id=26355,num=3,is_bind=1},
[159]={item_id=26347,num=5,is_bind=1},
[160]={item_id=26198,num=10,is_bind=1},
[161]={item_id=26357,num=2,is_bind=1},
[162]={item_id=26356,num=4,is_bind=1},
[163]={item_id=26355,num=4,is_bind=1},
[164]={item_id=26347,num=10,is_bind=1},
[165]={item_id=37601,num=1,is_bind=1},
[166]={item_id=26198,num=15,is_bind=1},
[167]={item_id=26356,num=5,is_bind=1},
[168]={item_id=26355,num=5,is_bind=1},
[169]={item_id=26348,num=5,is_bind=1},
[170]={item_id=26357,num=3,is_bind=1},
[171]={item_id=26356,num=10,is_bind=1},
[172]={item_id=26355,num=10,is_bind=1},
[173]={item_id=26348,num=10,is_bind=1},
[174]={item_id=26198,num=20,is_bind=1},
[175]={item_id=26357,num=4,is_bind=1},
[176]={item_id=26356,num=20,is_bind=1},
[177]={item_id=26355,num=20,is_bind=1},
[178]={item_id=26348,num=15,is_bind=1},
[179]={item_id=26357,num=5,is_bind=1},
[180]={item_id=26356,num=30,is_bind=1},
[181]={item_id=26355,num=30,is_bind=1},
[182]={item_id=26348,num=20,is_bind=1},
[183]={item_id=37226,num=1,is_bind=1},
[184]={item_id=26198,num=30,is_bind=1},
[185]={item_id=26357,num=8,is_bind=1},
[186]={item_id=26356,num=40,is_bind=1},
[187]={item_id=26355,num=40,is_bind=1},
[188]={item_id=26348,num=25,is_bind=1},
[189]={item_id=37020,num=1,is_bind=1},
[190]={item_id=26198,num=50,is_bind=1},
[191]={item_id=26357,num=10,is_bind=1},
[192]={item_id=26348,num=30,is_bind=1},
[193]={item_id=30473,num=1,is_bind=1},
[194]={item_id=26199,num=5,is_bind=1},
[195]={item_id=26360,num=1,is_bind=1},
[196]={item_id=26359,num=3,is_bind=1},
[197]={item_id=26358,num=3,is_bind=1},
[198]={item_id=26351,num=5,is_bind=1},
[199]={item_id=26199,num=10,is_bind=1},
[200]={item_id=26360,num=2,is_bind=1},
[201]={item_id=26359,num=4,is_bind=1},
[202]={item_id=26358,num=4,is_bind=1},
[203]={item_id=26351,num=10,is_bind=1},
[204]={item_id=30474,num=1,is_bind=1},
[205]={item_id=26199,num=15,is_bind=1},
[206]={item_id=26359,num=5,is_bind=1},
[207]={item_id=26358,num=5,is_bind=1},
[208]={item_id=26360,num=3,is_bind=1},
[209]={item_id=26359,num=10,is_bind=1},
[210]={item_id=26358,num=10,is_bind=1},
[211]={item_id=26199,num=20,is_bind=1},
[212]={item_id=26360,num=4,is_bind=1},
[213]={item_id=26359,num=20,is_bind=1},
[214]={item_id=26358,num=20,is_bind=1},
[215]={item_id=26351,num=15,is_bind=1},
[216]={item_id=26360,num=5,is_bind=1},
[217]={item_id=26359,num=30,is_bind=1},
[218]={item_id=26358,num=30,is_bind=1},
[219]={item_id=26351,num=20,is_bind=1},
[220]={item_id=26199,num=30,is_bind=1},
[221]={item_id=26360,num=8,is_bind=1},
[222]={item_id=26359,num=40,is_bind=1},
[223]={item_id=26358,num=40,is_bind=1},
[224]={item_id=26351,num=25,is_bind=1},
[225]={item_id=48444,num=1,is_bind=1},
[226]={item_id=26199,num=50,is_bind=1},
[227]={item_id=26360,num=10,is_bind=1},
[228]={item_id=26351,num=30,is_bind=1},
[229]={item_id=30523,num=1,is_bind=1},
[230]={item_id=28445,num=5,is_bind=1},
[231]={item_id=26372,num=5,is_bind=1},
[232]={item_id=28445,num=10,is_bind=1},
[233]={item_id=26372,num=10,is_bind=1},
[234]={item_id=30524,num=1,is_bind=1},
[235]={item_id=28445,num=15,is_bind=1},
[236]={item_id=28445,num=20,is_bind=1},
[237]={item_id=26372,num=15,is_bind=1},
[238]={item_id=26372,num=20,is_bind=1},
[239]={item_id=48120,num=1,is_bind=1},
[240]={item_id=28445,num=30,is_bind=1},
[241]={item_id=26372,num=25,is_bind=1},
[242]={item_id=28445,num=50,is_bind=1},
[243]={item_id=26375,num=10,is_bind=1},
[244]={item_id=26372,num=30,is_bind=1},
[245]={item_id=30491,num=1,is_bind=1},
[246]={item_id=26174,num=5,is_bind=1},
[247]={item_id=26363,num=1,is_bind=1},
[248]={item_id=26362,num=3,is_bind=1},
[249]={item_id=26361,num=3,is_bind=1},
[250]={item_id=26174,num=10,is_bind=1},
[251]={item_id=26363,num=2,is_bind=1},
[252]={item_id=26362,num=4,is_bind=1},
[253]={item_id=26361,num=4,is_bind=1},
[254]={item_id=30492,num=1,is_bind=1},
[255]={item_id=26174,num=15,is_bind=1},
[256]={item_id=26362,num=5,is_bind=1},
[257]={item_id=26361,num=5,is_bind=1},
[258]={item_id=26362,num=10,is_bind=1},
[259]={item_id=26361,num=10,is_bind=1},
[260]={item_id=26174,num=20,is_bind=1},
[261]={item_id=26362,num=20,is_bind=1},
[262]={item_id=26361,num=20,is_bind=1},
[263]={item_id=26362,num=30,is_bind=1},
[264]={item_id=26361,num=30,is_bind=1},
[265]={item_id=26354,num=20,is_bind=1},
[266]={item_id=48119,num=1,is_bind=1},
[267]={item_id=26174,num=30,is_bind=1},
[268]={item_id=26362,num=40,is_bind=1},
[269]={item_id=26361,num=40,is_bind=1},
[270]={item_id=26354,num=25,is_bind=1},
[271]={item_id=26174,num=50,is_bind=1},
[272]={item_id=26363,num=10,is_bind=1},
[273]={item_id=26354,num=30,is_bind=1},
[274]={item_id=26367,num=2,is_bind=1},
[275]={item_id=26368,num=2,is_bind=1},
[276]={item_id=26344,num=10,is_bind=1},
}

return {
open_day={
{},
{start_day=8,end_day=9999,grade=2,}
},

open_day_meta_table_map={
},
gift={
{need_gold=3280,base_gold=3280,max_random_gold=1640,},
{seq=1,min_random_gold=2592,item_list={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{seq=2,need_gold=10000,base_gold=10000,min_random_gold=4000,max_random_gold=5000,item_list={[0]=item_table[7],[1]=item_table[8],[2]=item_table[9],[3]=item_table[10],[4]=item_table[11],[5]=item_table[12]},},
{seq=3,need_gold=20000,base_gold=20000,min_random_gold=8000,max_random_gold=10000,item_list={[0]=item_table[7],[1]=item_table[8],[2]=item_table[13],[3]=item_table[14],[4]=item_table[11],[5]=item_table[15]},},
{seq=4,need_gold=50000,base_gold=50000,min_random_gold=20000,max_random_gold=25000,item_list={[0]=item_table[16],[1]=item_table[17],[2]=item_table[18],[3]=item_table[19],[4]=item_table[20],[5]=item_table[21]},},
{seq=5,need_gold=100000,base_gold=100000,min_random_gold=40000,max_random_gold=50000,item_list={[0]=item_table[22],[1]=item_table[17],[2]=item_table[23],[3]=item_table[24],[4]=item_table[25],[5]=item_table[26]},},
{seq=6,need_gold=150000,base_gold=150000,min_random_gold=60000,max_random_gold=75000,item_list={[0]=item_table[27],[1]=item_table[28],[2]=item_table[29],[3]=item_table[30],[4]=item_table[31],[5]=item_table[32]},},
{seq=7,need_gold=200000,base_gold=200000,min_random_gold=80000,max_random_gold=100000,item_list={[0]=item_table[33],[1]=item_table[28],[2]=item_table[34],[3]=item_table[35],[4]=item_table[36],[5]=item_table[37]},},
{activity_day=2,item_list={[0]=item_table[38],[1]=item_table[39],[2]=item_table[40],[3]=item_table[41],[4]=item_table[42],[5]=item_table[43]},},
{activity_day=2,item_list={[0]=item_table[38],[1]=item_table[39],[2]=item_table[44],[3]=item_table[45],[4]=item_table[42],[5]=item_table[46]},},
{activity_day=2,item_list={[0]=item_table[47],[1]=item_table[48],[2]=item_table[49],[3]=item_table[50],[4]=item_table[51],[5]=item_table[52]},},
{activity_day=2,item_list={[0]=item_table[47],[1]=item_table[48],[2]=item_table[53],[3]=item_table[54],[4]=item_table[51],[5]=item_table[55]},},
{activity_day=2,item_list={[0]=item_table[56],[1]=item_table[57],[2]=item_table[58],[3]=item_table[59],[4]=item_table[60],[5]=item_table[61]},},
{activity_day=2,item_list={[0]=item_table[62],[1]=item_table[57],[2]=item_table[63],[3]=item_table[64],[4]=item_table[65],[5]=item_table[66]},},
{activity_day=2,item_list={[0]=item_table[27],[1]=item_table[67],[2]=item_table[68],[3]=item_table[69],[4]=item_table[70],[5]=item_table[71]},},
{activity_day=2,item_list={[0]=item_table[72],[1]=item_table[67],[2]=item_table[73],[3]=item_table[74],[4]=item_table[75],[5]=item_table[76]},},
{activity_day=3,item_list={[0]=item_table[77],[1]=item_table[78],[2]=item_table[79],[3]=item_table[80],[4]=item_table[81],[5]=item_table[82]},},
{activity_day=3,item_list={[0]=item_table[77],[1]=item_table[78],[2]=item_table[83],[3]=item_table[84],[4]=item_table[81],[5]=item_table[85]},},
{activity_day=3,item_list={[0]=item_table[86],[1]=item_table[87],[2]=item_table[88],[3]=item_table[89],[4]=item_table[90],[5]=item_table[91]},},
{activity_day=3,item_list={[0]=item_table[86],[1]=item_table[87],[2]=item_table[92],[3]=item_table[93],[4]=item_table[90],[5]=item_table[94]},},
{activity_day=3,item_list={[0]=item_table[95],[1]=item_table[96],[2]=item_table[97],[3]=item_table[98],[4]=item_table[99],[5]=item_table[100]},},
{activity_day=3,item_list={[0]=item_table[101],[1]=item_table[96],[2]=item_table[102],[3]=item_table[103],[4]=item_table[104],[5]=item_table[105]},},
{activity_day=3,item_list={[0]=item_table[27],[1]=item_table[106],[2]=item_table[107],[3]=item_table[108],[4]=item_table[109],[5]=item_table[110]},},
{activity_day=3,item_list={[0]=item_table[111],[1]=item_table[106],[2]=item_table[112],[3]=item_table[113],[4]=item_table[114],[5]=item_table[115]},},
{activity_day=4,item_list={[0]=item_table[116],[1]=item_table[117],[2]=item_table[118],[3]=item_table[119],[4]=item_table[120],[5]=item_table[121]},},
{activity_day=4,item_list={[0]=item_table[77],[1]=item_table[117],[2]=item_table[122],[3]=item_table[123],[4]=item_table[120],[5]=item_table[124]},},
{activity_day=4,item_list={[0]=item_table[125],[1]=item_table[126],[2]=item_table[127],[3]=item_table[128],[4]=item_table[129],[5]=item_table[130]},},
{activity_day=4,item_list={[0]=item_table[86],[1]=item_table[126],[2]=item_table[131],[3]=item_table[132],[4]=item_table[129],[5]=item_table[133]},},
{activity_day=4,item_list={[0]=item_table[134],[1]=item_table[135],[2]=item_table[136],[3]=item_table[137],[4]=item_table[138],[5]=item_table[139]},},
{activity_day=4,item_list={[0]=item_table[140],[1]=item_table[135],[2]=item_table[141],[3]=item_table[142],[4]=item_table[143],[5]=item_table[144]},},
{activity_day=4,item_list={[0]=item_table[27],[1]=item_table[145],[2]=item_table[146],[3]=item_table[147],[4]=item_table[148],[5]=item_table[149]},},
{activity_day=4,item_list={[0]=item_table[111],[1]=item_table[145],[2]=item_table[150],[3]=item_table[151],[4]=item_table[152],[5]=item_table[153]},},
{grade=2,min_random_gold=2160,item_list={[0]=item_table[154],[1]=item_table[155],[2]=item_table[156],[3]=item_table[157],[4]=item_table[158],[5]=item_table[159]},},
{grade=2,seq=1,min_random_gold=3333,item_list={[0]=item_table[154],[1]=item_table[160],[2]=item_table[161],[3]=item_table[162],[4]=item_table[163],[5]=item_table[164]},},
{grade=2,seq=2,min_random_gold=6667,item_list={[0]=item_table[165],[1]=item_table[166],[2]=item_table[161],[3]=item_table[167],[4]=item_table[168],[5]=item_table[169]},},
{grade=2,seq=3,min_random_gold=16667,item_list={[0]=item_table[165],[1]=item_table[166],[2]=item_table[170],[3]=item_table[171],[4]=item_table[172],[5]=item_table[173]},},
{grade=2,seq=4,min_random_gold=33333,item_list={[0]=item_table[27],[1]=item_table[174],[2]=item_table[175],[3]=item_table[176],[4]=item_table[177],[5]=item_table[178]},},
{grade=2,seq=5,min_random_gold=66667,item_list={[0]=item_table[27],[1]=item_table[174],[2]=item_table[179],[3]=item_table[180],[4]=item_table[181],[5]=item_table[182]},},
{grade=2,seq=6,need_gold=300000,base_gold=300000,min_random_gold=100000,max_random_gold=150000,item_list={[0]=item_table[183],[1]=item_table[184],[2]=item_table[185],[3]=item_table[186],[4]=item_table[187],[5]=item_table[188]},},
{grade=2,seq=7,need_gold=500000,base_gold=500000,min_random_gold=166667,max_random_gold=250000,item_list={[0]=item_table[189],[1]=item_table[190],[2]=item_table[191],[3]=item_table[186],[4]=item_table[187],[5]=item_table[192]},},
{activity_day=2,item_list={[0]=item_table[193],[1]=item_table[194],[2]=item_table[195],[3]=item_table[196],[4]=item_table[197],[5]=item_table[198]},},
{activity_day=2,item_list={[0]=item_table[193],[1]=item_table[199],[2]=item_table[200],[3]=item_table[201],[4]=item_table[202],[5]=item_table[203]},},
{activity_day=2,item_list={[0]=item_table[204],[1]=item_table[205],[2]=item_table[200],[3]=item_table[206],[4]=item_table[207],[5]=item_table[198]},},
{activity_day=2,item_list={[0]=item_table[204],[1]=item_table[205],[2]=item_table[208],[3]=item_table[209],[4]=item_table[210],[5]=item_table[203]},},
{activity_day=2,item_list={[0]=item_table[27],[1]=item_table[211],[2]=item_table[212],[3]=item_table[213],[4]=item_table[214],[5]=item_table[215]},},
{activity_day=2,item_list={[0]=item_table[27],[1]=item_table[211],[2]=item_table[216],[3]=item_table[217],[4]=item_table[218],[5]=item_table[219]},},
{activity_day=2,item_list={[0]=item_table[33],[1]=item_table[220],[2]=item_table[221],[3]=item_table[222],[4]=item_table[223],[5]=item_table[224]},},
{activity_day=2,item_list={[0]=item_table[225],[1]=item_table[226],[2]=item_table[227],[3]=item_table[222],[4]=item_table[223],[5]=item_table[228]},},
{activity_day=3,item_list={[0]=item_table[229],[1]=item_table[230],[2]=item_table[120],[3]=item_table[123],[4]=item_table[122],[5]=item_table[231]},},
{activity_day=3,item_list={[0]=item_table[229],[1]=item_table[232],[2]=item_table[129],[3]=item_table[128],[4]=item_table[127],[5]=item_table[233]},},
{activity_day=3,item_list={[0]=item_table[234],[1]=item_table[235],[2]=item_table[129],[3]=item_table[132],[4]=item_table[131],[5]=item_table[231]},},
{activity_day=3,item_list={[0]=item_table[234],[1]=item_table[235],[2]=item_table[138],[3]=item_table[137],[4]=item_table[136],[5]=item_table[233]},},
{activity_day=3,item_list={[0]=item_table[27],[1]=item_table[236],[2]=item_table[143],[3]=item_table[142],[4]=item_table[141],[5]=item_table[237]},},
{activity_day=3,item_list={[0]=item_table[27],[1]=item_table[236],[2]=item_table[148],[3]=item_table[147],[4]=item_table[146],[5]=item_table[238]},},
{activity_day=3,item_list={[0]=item_table[239],[1]=item_table[240],[2]=item_table[152],[3]=item_table[151],[4]=item_table[150],[5]=item_table[241]},},
{activity_day=3,item_list={[0]=item_table[111],[1]=item_table[242],[2]=item_table[243],[3]=item_table[151],[4]=item_table[150],[5]=item_table[244]},},
{activity_day=4,item_list={[0]=item_table[245],[1]=item_table[246],[2]=item_table[247],[3]=item_table[248],[4]=item_table[249],[5]=item_table[100]},},
{activity_day=4,item_list={[0]=item_table[245],[1]=item_table[250],[2]=item_table[251],[3]=item_table[252],[4]=item_table[253],[5]=item_table[105]},},
{activity_day=4,item_list={[0]=item_table[254],[1]=item_table[255],[2]=item_table[251],[3]=item_table[256],[4]=item_table[257],[5]=item_table[100]},},
{activity_day=4,item_list={[0]=item_table[254],[1]=item_table[255],[2]=item_table[99],[3]=item_table[258],[4]=item_table[259],[5]=item_table[105]},},
{activity_day=4,item_list={[0]=item_table[27],[1]=item_table[260],[2]=item_table[104],[3]=item_table[261],[4]=item_table[262],[5]=item_table[115]},},
{activity_day=4,item_list={[0]=item_table[27],[1]=item_table[260],[2]=item_table[109],[3]=item_table[263],[4]=item_table[264],[5]=item_table[265]},},
{activity_day=4,item_list={[0]=item_table[266],[1]=item_table[267],[2]=item_table[114],[3]=item_table[268],[4]=item_table[269],[5]=item_table[270]},},
{activity_day=4,item_list={[0]=item_table[111],[1]=item_table[271],[2]=item_table[272],[3]=item_table[268],[4]=item_table[269],[5]=item_table[273]},}
},

gift_meta_table_map={
[18]=2,	-- depth:1
[26]=2,	-- depth:1
[41]=33,	-- depth:1
[49]=33,	-- depth:1
[57]=33,	-- depth:1
[10]=2,	-- depth:1
[25]=1,	-- depth:1
[17]=25,	-- depth:2
[9]=25,	-- depth:2
[31]=7,	-- depth:1
[34]=3,	-- depth:1
[36]=5,	-- depth:1
[37]=6,	-- depth:1
[38]=8,	-- depth:1
[30]=6,	-- depth:1
[35]=4,	-- depth:1
[29]=5,	-- depth:1
[32]=8,	-- depth:1
[27]=3,	-- depth:1
[12]=4,	-- depth:1
[28]=4,	-- depth:1
[11]=3,	-- depth:1
[14]=6,	-- depth:1
[15]=7,	-- depth:1
[16]=8,	-- depth:1
[13]=5,	-- depth:1
[20]=4,	-- depth:1
[21]=5,	-- depth:1
[22]=6,	-- depth:1
[23]=7,	-- depth:1
[24]=8,	-- depth:1
[19]=3,	-- depth:1
[54]=38,	-- depth:2
[61]=37,	-- depth:2
[60]=36,	-- depth:2
[59]=35,	-- depth:2
[58]=34,	-- depth:2
[62]=38,	-- depth:2
[56]=40,	-- depth:1
[55]=39,	-- depth:1
[53]=37,	-- depth:2
[47]=39,	-- depth:1
[51]=35,	-- depth:2
[50]=34,	-- depth:2
[48]=40,	-- depth:1
[46]=38,	-- depth:2
[45]=37,	-- depth:2
[44]=36,	-- depth:2
[43]=35,	-- depth:2
[42]=34,	-- depth:2
[63]=39,	-- depth:1
[52]=36,	-- depth:2
[64]=40,	-- depth:1
},
open_day_default_table={start_day=1,end_day=7,grade=1,},

gift_default_table={grade=1,activity_day=1,seq=0,need_gold=6480,base_gold=6480,min_random_gold=1312,max_random_gold=3240,item_list={[0]=item_table[1],[1]=item_table[2],[2]=item_table[274],[3]=item_table[275],[4]=item_table[5],[5]=item_table[276]},}

}

