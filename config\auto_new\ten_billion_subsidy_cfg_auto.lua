-- B-百亿补贴.xls
local item_table={
[1]={item_id=22099,num=1980,is_bind=1},
[2]={item_id=26193,num=1,is_bind=1},
[3]={item_id=59661,num=1,is_bind=1},
[4]={item_id=22099,num=1680,is_bind=1},
[5]={item_id=27855,num=1,is_bind=1},
[6]={item_id=27857,num=1,is_bind=1},
[7]={item_id=26357,num=1,is_bind=1},
[8]={item_id=26360,num=1,is_bind=1},
[9]={item_id=26369,num=1,is_bind=1},
[10]={item_id=26380,num=1,is_bind=1},
[11]={item_id=40215,num=1,is_bind=1},
[12]={item_id=26519,num=1,is_bind=1},
[13]={item_id=26505,num=1,is_bind=1},
[14]={item_id=26230,num=1,is_bind=1},
[15]={item_id=26231,num=1,is_bind=1},
[16]={item_id=30805,num=1,is_bind=1},
[17]={item_id=48071,num=1,is_bind=1},
[18]={item_id=48118,num=1,is_bind=1},
[19]={item_id=27854,num=1,is_bind=1},
[20]={item_id=27858,num=1,is_bind=1},
[21]={item_id=26186,num=1,is_bind=1},
[22]={item_id=27836,num=1,is_bind=1},
[23]={item_id=27837,num=1,is_bind=1},
[24]={item_id=26363,num=1,is_bind=1},
[25]={item_id=26520,num=1,is_bind=1},
[26]={item_id=26504,num=1,is_bind=1},
[27]={item_id=29759,num=1,is_bind=1},
[28]={item_id=29760,num=1,is_bind=1},
[29]={item_id=29761,num=1,is_bind=1},
[30]={item_id=29762,num=1,is_bind=1},
[31]={item_id=29763,num=1,is_bind=1},
[32]={item_id=29764,num=1,is_bind=1},
[33]={item_id=29765,num=1,is_bind=1},
[34]={item_id=29766,num=1,is_bind=1},
[35]={item_id=29716,num=1,is_bind=1},
[36]={item_id=29717,num=1,is_bind=1},
[37]={item_id=29718,num=1,is_bind=1},
[38]={item_id=29719,num=1,is_bind=1},
[39]={item_id=57811,num=1,is_bind=1},
[40]={item_id=29720,num=1,is_bind=1},
[41]={item_id=29721,num=1,is_bind=1},
[42]={item_id=29722,num=1,is_bind=1},
[43]={item_id=29723,num=1,is_bind=1},
[44]={item_id=57814,num=1,is_bind=1},
[45]={item_id=29754,num=1,is_bind=1},
[46]={item_id=29755,num=1,is_bind=1},
[47]={item_id=29756,num=1,is_bind=1},
[48]={item_id=29757,num=1,is_bind=1},
[49]={item_id=29758,num=1,is_bind=1},
[50]={item_id=57833,num=1,is_bind=1},
[51]={item_id=57834,num=1,is_bind=1},
[52]={item_id=57835,num=1,is_bind=1},
[53]={item_id=57802,num=1,is_bind=1},
[54]={item_id=57803,num=1,is_bind=1},
[55]={item_id=57804,num=1,is_bind=1},
[56]={item_id=57805,num=1,is_bind=1},
[57]={item_id=57808,num=1,is_bind=1},
[58]={item_id=57809,num=1,is_bind=1},
[59]={item_id=57810,num=1,is_bind=1},
[60]={item_id=57815,num=1,is_bind=1},
[61]={item_id=57816,num=1,is_bind=1},
[62]={item_id=57817,num=1,is_bind=1},
[63]={item_id=29693,num=1,is_bind=1},
[64]={item_id=29694,num=1,is_bind=1},
[65]={item_id=29695,num=1,is_bind=1},
[66]={item_id=29696,num=1,is_bind=1},
[67]={item_id=29816,num=1,is_bind=1},
[68]={item_id=29817,num=1,is_bind=1},
[69]={item_id=29818,num=1,is_bind=1},
[70]={item_id=29819,num=1,is_bind=1},
[71]={item_id=26545,num=1,is_bind=1},
[72]={item_id=26546,num=1,is_bind=1},
[73]={item_id=26548,num=1,is_bind=1},
[74]={item_id=26549,num=1,is_bind=1},
[75]={item_id=26550,num=1,is_bind=1},
[76]={item_id=26551,num=1,is_bind=1},
[77]={item_id=26553,num=1,is_bind=1},
[78]={item_id=26554,num=1,is_bind=1},
[79]={item_id=26555,num=1,is_bind=1},
[80]={item_id=26556,num=1,is_bind=1},
[81]={item_id=26127,num=1,is_bind=1},
[82]={item_id=30779,num=1,is_bind=1},
[83]={item_id=30780,num=1,is_bind=1},
[84]={item_id=26437,num=1,is_bind=1},
[85]={item_id=26438,num=1,is_bind=1},
[86]={item_id=26439,num=1,is_bind=1},
[87]={item_id=26440,num=1,is_bind=1},
[88]={item_id=26441,num=1,is_bind=1},
[89]={item_id=26442,num=1,is_bind=1},
[90]={item_id=26348,num=1,is_bind=1},
[91]={item_id=26351,num=1,is_bind=1},
[92]={item_id=26344,num=1,is_bind=1},
[93]={item_id=26345,num=1,is_bind=1},
[94]={item_id=26346,num=1,is_bind=1},
[95]={item_id=26347,num=1,is_bind=1},
[96]={item_id=26349,num=1,is_bind=1},
[97]={item_id=26350,num=1,is_bind=1},
[98]={item_id=26376,num=1,is_bind=1},
[99]={item_id=26377,num=1,is_bind=1},
[100]={item_id=26355,num=1,is_bind=1},
[101]={item_id=26356,num=1,is_bind=1},
[102]={item_id=26358,num=1,is_bind=1},
[103]={item_id=26359,num=1,is_bind=1},
[104]={item_id=26367,num=1,is_bind=1},
[105]={item_id=26368,num=1,is_bind=1},
[106]={item_id=26378,num=1,is_bind=1},
[107]={item_id=26379,num=1,is_bind=1},
[108]={item_id=30721,num=1,is_bind=0},
[109]={item_id=30442,num=10,is_bind=0},
[110]={item_id=30443,num=100,is_bind=0},
[111]={item_id=30779,num=2,is_bind=0},
[112]={item_id=22532,num=10,is_bind=0},
[113]={item_id=22531,num=20,is_bind=0},
[114]={item_id=40054,num=1,is_bind=0},
[115]={item_id=40054,num=2,is_bind=0},
[116]={item_id=26042,num=1,is_bind=0},
[117]={item_id=32292,num=1,is_bind=1},
[118]={item_id=37904,num=1,is_bind=1},
[119]={item_id=37804,num=1,is_bind=1},
[120]={item_id=26552,num=1,is_bind=1},
[121]={item_id=26191,num=1,is_bind=1},
[122]={item_id=26506,num=1,is_bind=1},
[123]={item_id=46382,num=1,is_bind=0},
[124]={item_id=46383,num=1,is_bind=0},
[125]={item_id=46384,num=1,is_bind=0},
[126]={item_id=18806,num=1,is_bind=1},
[127]={item_id=18807,num=1,is_bind=1},
[128]={item_id=18808,num=1,is_bind=1},
[129]={item_id=18809,num=1,is_bind=1},
[130]={item_id=18810,num=1,is_bind=1},
[131]={item_id=18811,num=1,is_bind=1},
[132]={item_id=18812,num=1,is_bind=1},
[133]={item_id=18813,num=1,is_bind=1},
[134]={item_id=18814,num=1,is_bind=1},
[135]={item_id=18815,num=1,is_bind=1},
[136]={item_id=18816,num=1,is_bind=1},
[137]={item_id=18817,num=1,is_bind=1},
[138]={item_id=18818,num=1,is_bind=1},
[139]={item_id=59661,num=1,is_bind=0},
[140]={item_id=22099,num=300,is_bind=1},
[141]={item_id=36418,num=300,is_bind=1},
[142]={item_id=40055,num=1,is_bind=1},
[143]={item_id=40054,num=1,is_bind=1},
[144]={item_id=57832,num=1,is_bind=1},
[145]={item_id=30808,num=1,is_bind=1},
[146]={item_id=37730,num=1,is_bind=0},
[147]={item_id=37035,num=1,is_bind=0},
[148]={item_id=37603,num=1,is_bind=0},
[149]={item_id=37237,num=1,is_bind=0},
[150]={item_id=38700,num=1,is_bind=0},
[151]={item_id=37238,num=1,is_bind=0},
[152]={item_id=46381,num=1,is_bind=0},
[153]={item_id=18805,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
grade={
{end_open_day=1,},
{start_open_day=2,end_open_day=2,grade=2,},
{start_open_day=3,end_open_day=3,grade=3,},
{start_open_day=4,end_open_day=4,grade=4,},
{start_open_day=5,end_open_day=5,grade=5,},
{start_open_day=6,end_open_day=6,grade=6,},
{start_open_day=7,end_open_day=7,grade=7,},
{start_open_day=8,grade=8,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=3,},
{type=3,},
{type=3,},
{type=3,},
{type=3,},
{type=3,},
{type=3,},
{type=3,},
{type=4,},
{type=4,},
{type=4,},
{type=4,},
{type=4,},
{type=4,},
{type=4,},
{type=4,},
{type=5,},
{type=6,},
{start_open_day=2,end_open_day=2,},
{start_open_day=3,end_open_day=3,},
{start_open_day=4,end_open_day=4,},
{start_open_day=5,end_open_day=5,},
{type=6,start_open_day=6,},
{type=7,},
{type=8,},
{type=9,}
},

grade_meta_table_map={
[9]=1,	-- depth:1
[34]=9,	-- depth:2
[17]=34,	-- depth:3
[25]=17,	-- depth:4
[38]=39,	-- depth:1
[37]=39,	-- depth:1
[36]=39,	-- depth:1
[35]=39,	-- depth:1
[32]=8,	-- depth:1
[24]=32,	-- depth:2
[16]=24,	-- depth:3
[22]=6,	-- depth:1
[10]=2,	-- depth:1
[11]=3,	-- depth:1
[12]=4,	-- depth:1
[13]=5,	-- depth:1
[14]=22,	-- depth:2
[15]=7,	-- depth:1
[18]=10,	-- depth:2
[30]=14,	-- depth:3
[29]=13,	-- depth:2
[28]=12,	-- depth:2
[27]=11,	-- depth:2
[26]=18,	-- depth:3
[19]=27,	-- depth:3
[20]=28,	-- depth:3
[23]=15,	-- depth:2
[31]=23,	-- depth:3
[21]=29,	-- depth:3
},
member_rmb_buy={
{member_level=2,},
{rmb_seq=1,price=198,reward={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},reward_show={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},},
{rmb_seq=2,cur_member_level=2,price=168,reward={[0]=item_table[4],[1]=item_table[2],[2]=item_table[3]},reward_show={[0]=item_table[4],[1]=item_table[2],[2]=item_table[3]},}
},

member_rmb_buy_meta_table_map={
},
member_level={
{pay_one_get_more_choose_item_num=0,pay_one_get_more_daily_free_refresh_times=0,},
{member_level=2,high_price_shop_discount=8,daily_discount_ticket_num=3,discount_ticket_pool=1,pay_one_get_more_daily_use_times=1,no_limit_discount_daily_use_limit=2,limit_discount_daily_use_limit=2,free_ticket_daily_use_limit=1,},
{member_level=3,high_price_shop_discount=7,open_quota_shop=1,discount_ticket_extend_times=2,daily_discount_ticket_num=4,discount_ticket_pool=2,pay_one_get_more_daily_use_times=2,no_limit_discount_daily_use_limit=3,limit_discount_daily_use_limit=3,free_ticket_daily_use_limit=2,}
},

member_level_meta_table_map={
},
member_benefits={
{benefits_desc="专属9折",is_show_arrow=0,required_level=1,},
{benefits_seq=2,required_level=1,},
{benefits_seq=3,benefits_desc="2张",is_show_arrow=0,required_level=1,},
{benefits_seq=4,benefits_name="立减券",benefits_icon="a3_bybt_hy_jy10",},
{benefits_seq=5,benefits_desc="日限用1张",is_show_arrow=0,required_level=1,},
{benefits_seq=6,benefits_desc="专属8折",required_level=2,},
{benefits_seq=7,benefits_desc="1次",is_show_arrow=0,required_level=2,},
{benefits_seq=8,benefits_desc="3张",required_level=2,},
{benefits_seq=9,required_level=2,},
{benefits_seq=10,benefits_name="付一得三",benefits_icon="a3_bybt_hy_jy3",required_level=2,},
{benefits_seq=11,benefits_desc="日限用2张",required_level=2,},
{benefits_seq=12,benefits_desc="日限用2张",required_level=2,},
{benefits_seq=13,benefits_desc="日限用1张",required_level=2,},
{benefits_seq=14,benefits_desc="专属7折",},
{benefits_seq=15,benefits_name="2元专场",benefits_icon="a3_bybt_hy_jy2",},
{benefits_seq=16,benefits_name="膨胀次数",benefits_desc="2次",benefits_icon="a3_bybt_hy_jy7",},
{benefits_seq=17,benefits_name="折扣券",benefits_desc="4张",benefits_icon="a3_bybt_hy_jy8",},
{benefits_seq=18,benefits_name="0元商店",benefits_icon="a3_bybt_hy_jy5",},
{benefits_seq=19,benefits_name="付一得三",benefits_desc="购买权+1",benefits_icon="a3_bybt_hy_jy3",},
{benefits_seq=20,benefits_name="立减券",benefits_desc="日限用3张",benefits_icon="a3_bybt_hy_jy10",},
{benefits_seq=21,benefits_name="满减券",benefits_desc="日限用3张",benefits_icon="a3_bybt_hy_jy9",},
{benefits_seq=22,benefits_name="免单券",benefits_desc="赠送1张",benefits_icon="a3_bybt_hy_jy1",}
},

member_benefits_meta_table_map={
[9]=18,	-- depth:1
[12]=21,	-- depth:1
[13]=22,	-- depth:1
[8]=17,	-- depth:1
[11]=20,	-- depth:1
[7]=16,	-- depth:1
[5]=21,	-- depth:1
[4]=5,	-- depth:2
[3]=17,	-- depth:1
[2]=7,	-- depth:2
},
daily_gift={
{}
},

daily_gift_meta_table_map={
},
ten_billion_subsidy_shop={
{grade=1,price=4,subsidy=26,old_price=30,},
{grade=1,rmb_seq=101,},
{grade=1,rmb_seq=102,},
{grade=1,rmb_seq=103,},
{grade=1,item_seq=4,rmb_seq=104,},
{grade=1,item_seq=5,rmb_seq=105,},
{grade=1,item_seq=6,rmb_seq=106,},
{grade=1,rmb_seq=107,reward={[0]=item_table[5]},},
{item_seq=8,rmb_seq=108,reward={[0]=item_table[6]},},
{grade=1,item_seq=9,rmb_seq=109,},
{grade=1,item_seq=10,rmb_seq=110,},
{item_seq=11,rmb_seq=111,reward={[0]=item_table[7]},},
{grade=1,rmb_seq=112,reward={[0]=item_table[8]},},
{item_seq=13,rmb_seq=113,reward={[0]=item_table[9]},},
{item_seq=14,rmb_seq=114,reward={[0]=item_table[10]},},
{item_seq=15,rmb_seq=115,reward={[0]=item_table[11]},},
{item_seq=16,rmb_seq=116,reward={[0]=item_table[12]},},
{grade=1,item_seq=17,rmb_seq=117,},
{grade=1,item_seq=18,rmb_seq=118,},
{item_seq=19,rmb_seq=119,reward={[0]=item_table[13]},},
{grade=2,rmb_seq=200,},
{grade=2,rmb_seq=201,},
{grade=2,rmb_seq=202,},
{grade=2,rmb_seq=203,},
{grade=2,rmb_seq=204,},
{grade=2,rmb_seq=205,},
{grade=2,rmb_seq=206,},
{grade=2,rmb_seq=207,},
{grade=2,rmb_seq=208,},
{grade=2,rmb_seq=209,},
{grade=2,rmb_seq=210,},
{grade=2,rmb_seq=211,},
{grade=2,rmb_seq=212,},
{grade=2,rmb_seq=213,},
{grade=2,rmb_seq=214,},
{grade=2,rmb_seq=215,},
{grade=2,rmb_seq=216,},
{grade=2,rmb_seq=217,},
{grade=2,item_seq=18,rmb_seq=218,},
{grade=2,item_seq=19,rmb_seq=219,},
{item_seq=20,rmb_seq=220,reward={[0]=item_table[13]},},
{grade=3,rmb_seq=300,},
{grade=3,rmb_seq=301,},
{grade=3,rmb_seq=302,},
{grade=3,rmb_seq=303,},
{grade=3,rmb_seq=304,},
{grade=3,rmb_seq=305,},
{grade=3,rmb_seq=306,},
{grade=3,rmb_seq=307,},
{grade=3,rmb_seq=308,},
{grade=3,rmb_seq=309,},
{grade=3,rmb_seq=310,},
{grade=3,rmb_seq=311,},
{grade=3,rmb_seq=312,},
{grade=3,rmb_seq=313,},
{grade=3,rmb_seq=314,},
{grade=3,rmb_seq=315,},
{grade=3,item_seq=16,rmb_seq=316,},
{grade=3,item_seq=17,rmb_seq=317,},
{grade=3,rmb_seq=318,},
{grade=3,rmb_seq=319,},
{grade=3,rmb_seq=320,},
{grade=4,rmb_seq=400,},
{grade=4,rmb_seq=401,},
{grade=4,rmb_seq=402,},
{grade=4,rmb_seq=403,},
{grade=4,rmb_seq=404,},
{grade=4,rmb_seq=405,},
{grade=4,rmb_seq=406,},
{grade=4,rmb_seq=407,},
{grade=4,rmb_seq=408,},
{grade=4,rmb_seq=409,},
{grade=4,rmb_seq=410,},
{grade=4,rmb_seq=411,},
{grade=4,rmb_seq=412,reward={[0]=item_table[7]},},
{grade=4,item_seq=13,rmb_seq=413,},
{grade=4,item_seq=14,rmb_seq=414,},
{grade=4,item_seq=15,rmb_seq=415,},
{grade=4,item_seq=16,rmb_seq=416,},
{grade=4,item_seq=17,rmb_seq=417,},
{grade=4,rmb_seq=418,},
{grade=4,rmb_seq=419,},
{grade=4,rmb_seq=420,},
{grade=4,rmb_seq=421,},
{grade=5,rmb_seq=500,},
{grade=5,rmb_seq=501,},
{grade=5,rmb_seq=502,},
{grade=5,rmb_seq=503,},
{grade=5,rmb_seq=504,},
{grade=5,rmb_seq=505,},
{grade=5,rmb_seq=506,},
{grade=5,rmb_seq=507,},
{grade=5,rmb_seq=508,},
{grade=5,rmb_seq=509,},
{grade=5,rmb_seq=510,},
{grade=5,rmb_seq=511,},
{grade=5,rmb_seq=512,},
{grade=5,rmb_seq=513,},
{grade=5,rmb_seq=514,},
{grade=5,rmb_seq=515,},
{grade=5,rmb_seq=516,},
{grade=5,rmb_seq=517,},
{grade=5,rmb_seq=518,},
{grade=5,item_seq=19,rmb_seq=519,},
{grade=5,rmb_seq=520,},
{grade=5,rmb_seq=521,reward={[0]=item_table[13]},},
{grade=6,rmb_seq=600,},
{grade=6,rmb_seq=601,},
{grade=6,rmb_seq=602,},
{grade=6,rmb_seq=603,},
{grade=6,rmb_seq=604,},
{grade=6,rmb_seq=605,},
{grade=6,rmb_seq=606,},
{grade=6,rmb_seq=607,},
{grade=6,rmb_seq=608,},
{grade=6,rmb_seq=609,},
{grade=6,rmb_seq=610,},
{grade=6,rmb_seq=611,},
{grade=6,rmb_seq=612,},
{grade=6,rmb_seq=613,},
{grade=6,rmb_seq=614,},
{grade=6,rmb_seq=615,},
{grade=6,rmb_seq=616,},
{grade=6,rmb_seq=617,},
{grade=6,item_seq=18,rmb_seq=618,},
{grade=6,rmb_seq=619,},
{grade=6,item_seq=20,rmb_seq=620,},
{grade=6,rmb_seq=621,},
{grade=7,rmb_seq=700,},
{grade=7,rmb_seq=701,},
{grade=7,rmb_seq=702,},
{grade=7,rmb_seq=703,},
{grade=7,rmb_seq=704,},
{grade=7,rmb_seq=705,},
{grade=7,rmb_seq=706,},
{grade=7,rmb_seq=707,},
{grade=7,rmb_seq=708,},
{grade=7,rmb_seq=709,},
{grade=7,rmb_seq=710,},
{grade=7,rmb_seq=711,},
{grade=7,rmb_seq=712,},
{grade=7,rmb_seq=713,},
{grade=7,rmb_seq=714,},
{grade=7,rmb_seq=715,},
{grade=7,rmb_seq=716,},
{grade=7,rmb_seq=717,},
{grade=7,rmb_seq=718,},
{grade=7,rmb_seq=719,},
{grade=7,rmb_seq=720,},
{grade=7,rmb_seq=721,},
{rmb_seq=800,price=4,subsidy=26,old_price=30,},
{item_seq=1,rmb_seq=801,price=9,reward={[0]=item_table[2]},subsidy=51,old_price=60,},
{item_seq=2,rmb_seq=802,reward={[0]=item_table[14]},},
{item_seq=3,rmb_seq=803,price=20,reward={[0]=item_table[15]},subsidy=80,old_price=100,},
{item_seq=4,rmb_seq=804,reward={[0]=item_table[16]},buy_limit=20,subsidy=65,old_price=70,},
{item_seq=5,rmb_seq=805,price=3,reward={[0]=item_table[17]},buy_limit=2,subsidy=17,old_price=20,},
{item_seq=6,rmb_seq=806,price=7,reward={[0]=item_table[18]},buy_limit=2,subsidy=43,},
{item_seq=7,rmb_seq=807,reward={[0]=item_table[19]},subsidy=95,old_price=100,},
{item_seq=8,rmb_seq=808,reward={[0]=item_table[5]},},
{item_seq=9,rmb_seq=809,reward={[0]=item_table[6]},},
{item_seq=10,rmb_seq=810,reward={[0]=item_table[20]},},
{item_seq=11,rmb_seq=811,price=3,reward={[0]=item_table[21]},subsidy=147,old_price=150,},
{item_seq=12,rmb_seq=812,price=1,reward={[0]=item_table[22]},buy_limit=50,},
{item_seq=13,rmb_seq=813,reward={[0]=item_table[23]},},
{item_seq=14,rmb_seq=814,reward={[0]=item_table[7]},},
{item_seq=15,rmb_seq=815,reward={[0]=item_table[8]},},
{item_seq=16,rmb_seq=816,reward={[0]=item_table[9]},},
{item_seq=17,rmb_seq=817,reward={[0]=item_table[10]},},
{item_seq=18,rmb_seq=818,reward={[0]=item_table[24]},},
{item_seq=19,rmb_seq=819,reward={[0]=item_table[11]},},
{item_seq=20,rmb_seq=820,reward={[0]=item_table[12]},},
{item_seq=21,rmb_seq=821,price=10,reward={[0]=item_table[25]},buy_limit=5,subsidy=470,old_price=480,},
{item_seq=22,rmb_seq=822,reward={[0]=item_table[26]},buy_limit=5,subsidy=155,old_price=160,},
{item_seq=23,rmb_seq=823,reward={[0]=item_table[13]},}
},

ten_billion_subsidy_shop_meta_table_map={
[129]=151,	-- depth:1
[42]=151,	-- depth:1
[159]=158,	-- depth:1
[160]=158,	-- depth:1
[161]=158,	-- depth:1
[21]=151,	-- depth:1
[164]=163,	-- depth:1
[85]=151,	-- depth:1
[166]=163,	-- depth:1
[165]=163,	-- depth:1
[168]=163,	-- depth:1
[167]=163,	-- depth:1
[107]=151,	-- depth:1
[170]=163,	-- depth:1
[63]=151,	-- depth:1
[169]=163,	-- depth:1
[139]=161,	-- depth:2
[80]=170,	-- depth:2
[79]=169,	-- depth:2
[78]=168,	-- depth:2
[77]=167,	-- depth:2
[73]=161,	-- depth:2
[138]=160,	-- depth:2
[76]=166,	-- depth:2
[137]=159,	-- depth:2
[136]=158,	-- depth:1
[70]=158,	-- depth:1
[71]=159,	-- depth:2
[75]=163,	-- depth:1
[72]=160,	-- depth:2
[124]=80,	-- depth:3
[93]=159,	-- depth:2
[123]=79,	-- depth:3
[114]=158,	-- depth:1
[115]=159,	-- depth:2
[116]=160,	-- depth:2
[117]=161,	-- depth:2
[102]=80,	-- depth:3
[101]=79,	-- depth:3
[100]=78,	-- depth:3
[99]=77,	-- depth:3
[98]=76,	-- depth:3
[119]=75,	-- depth:2
[95]=161,	-- depth:2
[94]=160,	-- depth:2
[141]=75,	-- depth:2
[92]=158,	-- depth:1
[120]=76,	-- depth:3
[121]=77,	-- depth:3
[122]=78,	-- depth:3
[97]=75,	-- depth:2
[142]=76,	-- depth:3
[55]=76,	-- depth:3
[57]=78,	-- depth:3
[13]=163,	-- depth:1
[14]=13,	-- depth:2
[58]=170,	-- depth:2
[37]=58,	-- depth:3
[36]=78,	-- depth:3
[153]=154,	-- depth:1
[35]=77,	-- depth:3
[33]=75,	-- depth:2
[15]=13,	-- depth:2
[31]=161,	-- depth:2
[30]=160,	-- depth:2
[29]=159,	-- depth:2
[28]=158,	-- depth:1
[34]=76,	-- depth:3
[10]=161,	-- depth:2
[56]=77,	-- depth:3
[171]=173,	-- depth:1
[54]=75,	-- depth:2
[143]=77,	-- depth:3
[52]=161,	-- depth:2
[51]=160,	-- depth:2
[12]=13,	-- depth:2
[7]=158,	-- depth:1
[9]=7,	-- depth:2
[50]=159,	-- depth:2
[49]=158,	-- depth:1
[144]=78,	-- depth:3
[145]=79,	-- depth:3
[146]=80,	-- depth:3
[8]=158,	-- depth:1
[16]=13,	-- depth:2
[118]=162,	-- depth:1
[125]=171,	-- depth:2
[127]=173,	-- depth:1
[130]=152,	-- depth:1
[149]=127,	-- depth:2
[147]=125,	-- depth:3
[131]=153,	-- depth:2
[132]=154,	-- depth:1
[133]=155,	-- depth:1
[135]=157,	-- depth:1
[113]=157,	-- depth:1
[140]=162,	-- depth:1
[111]=155,	-- depth:1
[87]=153,	-- depth:2
[53]=162,	-- depth:1
[59]=171,	-- depth:2
[110]=154,	-- depth:1
[48]=157,	-- depth:1
[46]=155,	-- depth:1
[45]=154,	-- depth:1
[44]=153,	-- depth:2
[43]=152,	-- depth:1
[40]=173,	-- depth:1
[38]=59,	-- depth:3
[32]=162,	-- depth:1
[61]=40,	-- depth:2
[27]=157,	-- depth:1
[24]=154,	-- depth:1
[23]=153,	-- depth:2
[22]=152,	-- depth:1
[19]=173,	-- depth:1
[17]=19,	-- depth:2
[11]=162,	-- depth:1
[6]=157,	-- depth:1
[4]=154,	-- depth:1
[3]=153,	-- depth:2
[2]=152,	-- depth:1
[25]=155,	-- depth:1
[64]=152,	-- depth:1
[174]=172,	-- depth:1
[66]=154,	-- depth:1
[86]=152,	-- depth:1
[65]=153,	-- depth:2
[91]=157,	-- depth:1
[83]=127,	-- depth:2
[96]=162,	-- depth:1
[103]=125,	-- depth:3
[89]=155,	-- depth:1
[81]=125,	-- depth:3
[88]=154,	-- depth:1
[105]=127,	-- depth:2
[69]=157,	-- depth:1
[108]=152,	-- depth:1
[67]=155,	-- depth:1
[109]=153,	-- depth:2
[74]=162,	-- depth:1
[18]=172,	-- depth:1
[5]=156,	-- depth:1
[20]=18,	-- depth:2
[106]=172,	-- depth:1
[90]=156,	-- depth:1
[104]=172,	-- depth:1
[84]=106,	-- depth:2
[126]=104,	-- depth:2
[39]=172,	-- depth:1
[41]=39,	-- depth:2
[128]=106,	-- depth:2
[150]=106,	-- depth:2
[82]=104,	-- depth:2
[148]=104,	-- depth:2
[47]=156,	-- depth:1
[68]=156,	-- depth:1
[134]=156,	-- depth:1
[60]=39,	-- depth:2
[62]=41,	-- depth:3
[26]=156,	-- depth:1
[112]=156,	-- depth:1
},
time_limit_shop={
{grade=1,reward={[0]=item_table[27]},buy_limit=1,},
{rmb_seq=101,grade=1,reward={[0]=item_table[28]},},
{rmb_seq=102,grade=1,reward={[0]=item_table[29]},},
{rmb_seq=103,grade=1,reward={[0]=item_table[30]},},
{rmb_seq=104,grade=1,},
{rmb_seq=105,grade=1,},
{rmb_seq=106,grade=1,},
{rmb_seq=107,grade=1,},
{rmb_seq=200,grade=2,reward={[0]=item_table[31]},buy_limit=1,},
{rmb_seq=201,item_seq=1,reward={[0]=item_table[32]},},
{rmb_seq=202,item_seq=2,reward={[0]=item_table[33]},},
{rmb_seq=203,item_seq=3,reward={[0]=item_table[34]},},
{rmb_seq=204,grade=2,},
{rmb_seq=205,grade=2,},
{rmb_seq=206,grade=2,},
{rmb_seq=207,grade=2,},
{rmb_seq=300,grade=3,reward={[0]=item_table[35]},buy_limit=1,},
{rmb_seq=301,grade=3,reward={[0]=item_table[36]},},
{rmb_seq=302,grade=3,reward={[0]=item_table[37]},},
{rmb_seq=303,grade=3,reward={[0]=item_table[38]},},
{rmb_seq=304,grade=3,item_seq=4,},
{rmb_seq=305,grade=3,item_seq=5,},
{rmb_seq=306,grade=3,item_seq=6,},
{rmb_seq=307,grade=3,reward={[0]=item_table[39]},},
{rmb_seq=400,grade=4,reward={[0]=item_table[40]},buy_limit=1,},
{rmb_seq=401,item_seq=1,reward={[0]=item_table[41]},},
{rmb_seq=402,item_seq=2,reward={[0]=item_table[42]},},
{rmb_seq=403,item_seq=3,reward={[0]=item_table[43]},},
{rmb_seq=404,grade=4,reward={[0]=item_table[44]},},
{rmb_seq=405,grade=4,item_seq=5,},
{rmb_seq=406,grade=4,item_seq=6,},
{rmb_seq=407,grade=4,item_seq=7,},
{rmb_seq=500,reward={[0]=item_table[45]},buy_limit=1,},
{rmb_seq=501,item_seq=1,reward={[0]=item_table[46]},buy_limit=1,},
{rmb_seq=502,item_seq=2,reward={[0]=item_table[47]},buy_limit=1,},
{rmb_seq=503,item_seq=3,reward={[0]=item_table[48]},buy_limit=1,},
{rmb_seq=504,item_seq=4,reward={[0]=item_table[49]},buy_limit=1,},
{rmb_seq=505,item_seq=5,price=6,limit_time_discount_price=2,subsidy=33,old_price=35,},
{rmb_seq=506,item_seq=6,price=9,reward={[0]=item_table[50]},limit_time_discount_price=5,subsidy=65,old_price=70,},
{rmb_seq=507,item_seq=7,reward={[0]=item_table[51]},},
{rmb_seq=508,item_seq=8,reward={[0]=item_table[52]},},
{rmb_seq=509,item_seq=9,reward={[0]=item_table[53]},},
{rmb_seq=510,item_seq=10,reward={[0]=item_table[54]},},
{rmb_seq=511,item_seq=11,reward={[0]=item_table[55]},},
{rmb_seq=512,item_seq=12,reward={[0]=item_table[56]},},
{rmb_seq=513,item_seq=13,reward={[0]=item_table[57]},},
{rmb_seq=514,item_seq=14,reward={[0]=item_table[58]},},
{rmb_seq=515,item_seq=15,reward={[0]=item_table[59]},buy_limit=5,subsidy=90,old_price=100,},
{rmb_seq=516,item_seq=16,reward={[0]=item_table[39]},},
{rmb_seq=517,item_seq=17,reward={[0]=item_table[44]},},
{rmb_seq=518,item_seq=18,reward={[0]=item_table[60]},},
{rmb_seq=519,item_seq=19,reward={[0]=item_table[61]},},
{rmb_seq=520,item_seq=20,price=24,reward={[0]=item_table[62]},buy_limit=3,limit_time_discount_price=20,subsidy=180,old_price=200,},
{rmb_seq=600,grade=6,reward={[0]=item_table[63]},buy_limit=1,},
{rmb_seq=601,item_seq=1,reward={[0]=item_table[64]},},
{rmb_seq=602,item_seq=2,reward={[0]=item_table[65]},},
{rmb_seq=603,item_seq=3,reward={[0]=item_table[66]},},
{rmb_seq=604,grade=6,},
{rmb_seq=605,grade=6,},
{rmb_seq=606,grade=6,},
{rmb_seq=607,grade=6,},
{rmb_seq=608,grade=6,},
{rmb_seq=609,grade=6,},
{rmb_seq=610,grade=6,},
{rmb_seq=611,grade=6,item_seq=11,},
{rmb_seq=612,grade=6,},
{rmb_seq=613,grade=6,},
{rmb_seq=614,grade=6,item_seq=14,},
{rmb_seq=615,grade=6,},
{rmb_seq=616,grade=6,},
{rmb_seq=617,grade=6,},
{rmb_seq=618,grade=6,item_seq=18,},
{rmb_seq=619,grade=6,item_seq=19,},
{rmb_seq=700,grade=7,reward={[0]=item_table[67]},buy_limit=1,},
{rmb_seq=701,item_seq=1,reward={[0]=item_table[68]},},
{rmb_seq=702,item_seq=2,reward={[0]=item_table[69]},},
{rmb_seq=703,item_seq=3,reward={[0]=item_table[70]},},
{rmb_seq=704,grade=7,item_seq=4,},
{rmb_seq=705,grade=7,item_seq=5,},
{rmb_seq=706,grade=7,item_seq=6,},
{rmb_seq=707,grade=7,item_seq=7,},
{rmb_seq=708,grade=7,item_seq=8,},
{rmb_seq=709,item_seq=9,reward={[0]=item_table[54]},},
{rmb_seq=710,grade=7,reward={[0]=item_table[55]},},
{rmb_seq=711,grade=7,},
{rmb_seq=712,grade=7,reward={[0]=item_table[57]},},
{rmb_seq=713,item_seq=13,reward={[0]=item_table[58]},},
{rmb_seq=714,grade=7,},
{rmb_seq=715,grade=7,item_seq=15,},
{rmb_seq=716,grade=7,item_seq=16,},
{rmb_seq=717,item_seq=17,reward={[0]=item_table[60]},},
{rmb_seq=718,grade=7,},
{rmb_seq=719,grade=7,},
{rmb_seq=800,grade=8,price=6,limit_time_discount_price=2,subsidy=33,old_price=35,},
{rmb_seq=801,grade=8,item_seq=1,},
{rmb_seq=802,item_seq=2,reward={[0]=item_table[51]},},
{rmb_seq=803,grade=8,item_seq=3,},
{rmb_seq=804,item_seq=4,reward={[0]=item_table[53]},},
{rmb_seq=805,grade=8,reward={[0]=item_table[54]},},
{rmb_seq=806,item_seq=6,reward={[0]=item_table[55]},},
{rmb_seq=807,grade=8,item_seq=7,},
{rmb_seq=808,item_seq=8,reward={[0]=item_table[57]},},
{rmb_seq=809,item_seq=9,reward={[0]=item_table[58]},},
{rmb_seq=810,grade=8,item_seq=10,},
{rmb_seq=811,grade=8,item_seq=11,},
{rmb_seq=812,item_seq=12,reward={[0]=item_table[44]},},
{rmb_seq=813,grade=8,item_seq=13,},
{rmb_seq=814,grade=8,reward={[0]=item_table[61]},},
{rmb_seq=815,grade=8,item_seq=15,}
},

time_limit_shop_meta_table_map={
[56]=54,	-- depth:1
[26]=25,	-- depth:1
[57]=54,	-- depth:1
[75]=74,	-- depth:1
[76]=74,	-- depth:1
[28]=25,	-- depth:1
[27]=25,	-- depth:1
[77]=74,	-- depth:1
[55]=54,	-- depth:1
[12]=9,	-- depth:1
[20]=36,	-- depth:1
[19]=35,	-- depth:1
[18]=34,	-- depth:1
[10]=9,	-- depth:1
[11]=9,	-- depth:1
[2]=34,	-- depth:1
[3]=35,	-- depth:1
[4]=36,	-- depth:1
[44]=48,	-- depth:1
[40]=48,	-- depth:1
[52]=48,	-- depth:1
[104]=48,	-- depth:1
[78]=94,	-- depth:1
[80]=40,	-- depth:2
[100]=104,	-- depth:2
[72]=52,	-- depth:2
[84]=104,	-- depth:2
[68]=48,	-- depth:1
[88]=68,	-- depth:2
[64]=84,	-- depth:3
[92]=72,	-- depth:3
[60]=80,	-- depth:3
[7]=80,	-- depth:3
[108]=68,	-- depth:2
[5]=78,	-- depth:2
[58]=78,	-- depth:2
[96]=104,	-- depth:2
[50]=38,	-- depth:1
[15]=100,	-- depth:3
[42]=38,	-- depth:1
[43]=39,	-- depth:1
[51]=39,	-- depth:1
[31]=52,	-- depth:2
[46]=38,	-- depth:1
[47]=39,	-- depth:1
[23]=48,	-- depth:1
[98]=94,	-- depth:1
[30]=51,	-- depth:2
[22]=47,	-- depth:2
[79]=39,	-- depth:1
[29]=98,	-- depth:2
[82]=98,	-- depth:2
[83]=79,	-- depth:2
[106]=94,	-- depth:1
[91]=79,	-- depth:2
[86]=106,	-- depth:2
[87]=79,	-- depth:2
[107]=51,	-- depth:2
[90]=106,	-- depth:2
[99]=79,	-- depth:2
[13]=98,	-- depth:2
[14]=99,	-- depth:3
[49]=53,	-- depth:1
[6]=79,	-- depth:2
[95]=39,	-- depth:1
[59]=79,	-- depth:2
[45]=53,	-- depth:1
[103]=95,	-- depth:2
[62]=82,	-- depth:3
[66]=86,	-- depth:3
[67]=87,	-- depth:3
[41]=53,	-- depth:1
[102]=94,	-- depth:1
[70]=90,	-- depth:3
[71]=91,	-- depth:3
[63]=83,	-- depth:3
[21]=102,	-- depth:2
[105]=49,	-- depth:2
[97]=41,	-- depth:2
[101]=45,	-- depth:2
[65]=45,	-- depth:2
[89]=49,	-- depth:2
[85]=65,	-- depth:3
[81]=41,	-- depth:2
[73]=53,	-- depth:1
[69]=89,	-- depth:3
[61]=81,	-- depth:3
[8]=81,	-- depth:3
[32]=53,	-- depth:1
[16]=101,	-- depth:3
[24]=32,	-- depth:2
[93]=73,	-- depth:2
[109]=53,	-- depth:1
},
pay_one_get_more_shop={
{price=4,},
{item_seq=1,rmb_seq=101,price=10,reward={[0]=item_table[2]},},
{item_seq=2,rmb_seq=102,reward={[0]=item_table[14]},},
{item_seq=3,rmb_seq=103,reward={[0]=item_table[15]},},
{item_seq=4,rmb_seq=104,price=4,reward={[0]=item_table[71]},},
{item_seq=5,rmb_seq=105,price=4,reward={[0]=item_table[72]},},
{item_seq=6,rmb_seq=106,price=10,reward={[0]=item_table[73]},},
{item_seq=7,rmb_seq=107,price=10,reward={[0]=item_table[74]},},
{item_seq=8,rmb_seq=108,price=10,reward={[0]=item_table[75]},},
{item_seq=9,rmb_seq=109,price=10,reward={[0]=item_table[76]},},
{item_seq=10,rmb_seq=110,reward={[0]=item_table[77]},},
{item_seq=11,rmb_seq=111,reward={[0]=item_table[78]},},
{item_seq=12,rmb_seq=112,reward={[0]=item_table[79]},},
{item_seq=13,rmb_seq=113,reward={[0]=item_table[80]},},
{grade=2,rmb_seq=200,},
{grade=2,rmb_seq=201,},
{grade=2,rmb_seq=202,},
{grade=2,rmb_seq=203,},
{grade=2,rmb_seq=204,},
{grade=2,rmb_seq=205,},
{grade=2,rmb_seq=206,},
{grade=2,rmb_seq=207,},
{grade=2,rmb_seq=208,},
{grade=2,rmb_seq=209,},
{grade=2,rmb_seq=210,},
{grade=2,rmb_seq=211,},
{grade=2,rmb_seq=212,},
{grade=2,rmb_seq=213,},
{grade=3,rmb_seq=300,},
{grade=3,rmb_seq=301,},
{grade=3,rmb_seq=302,},
{grade=3,rmb_seq=303,},
{grade=3,rmb_seq=304,},
{grade=3,rmb_seq=305,},
{grade=3,rmb_seq=306,},
{grade=3,rmb_seq=307,},
{grade=3,rmb_seq=308,},
{grade=3,rmb_seq=309,},
{grade=3,rmb_seq=310,},
{grade=3,rmb_seq=311,},
{grade=3,rmb_seq=312,},
{grade=3,rmb_seq=313,},
{grade=4,rmb_seq=400,},
{grade=4,rmb_seq=401,},
{grade=4,rmb_seq=402,},
{grade=4,rmb_seq=403,},
{grade=4,rmb_seq=404,},
{grade=4,rmb_seq=405,},
{grade=4,rmb_seq=406,},
{grade=4,rmb_seq=407,},
{grade=4,rmb_seq=408,},
{grade=4,rmb_seq=409,},
{grade=4,rmb_seq=410,},
{grade=4,rmb_seq=411,},
{grade=4,rmb_seq=412,},
{grade=4,rmb_seq=413,},
{grade=5,rmb_seq=500,},
{grade=5,rmb_seq=501,},
{grade=5,rmb_seq=502,},
{grade=5,rmb_seq=503,},
{grade=5,rmb_seq=504,},
{grade=5,rmb_seq=505,},
{grade=5,rmb_seq=506,},
{grade=5,rmb_seq=507,},
{grade=5,rmb_seq=508,},
{grade=5,rmb_seq=509,},
{grade=5,rmb_seq=510,},
{grade=5,rmb_seq=511,},
{grade=5,rmb_seq=512,},
{grade=5,rmb_seq=513,},
{grade=6,rmb_seq=600,},
{grade=6,rmb_seq=601,},
{grade=6,rmb_seq=602,},
{grade=6,rmb_seq=603,},
{grade=6,rmb_seq=604,},
{grade=6,rmb_seq=605,},
{grade=6,rmb_seq=606,},
{grade=6,rmb_seq=607,},
{grade=6,rmb_seq=608,},
{grade=6,rmb_seq=609,},
{grade=6,rmb_seq=610,},
{grade=6,rmb_seq=611,},
{grade=6,rmb_seq=612,},
{grade=6,rmb_seq=613,},
{grade=7,rmb_seq=700,},
{grade=7,rmb_seq=701,},
{grade=7,rmb_seq=702,},
{grade=7,rmb_seq=703,},
{grade=7,rmb_seq=704,},
{grade=7,rmb_seq=705,},
{grade=7,rmb_seq=706,},
{grade=7,rmb_seq=707,},
{grade=7,rmb_seq=708,},
{grade=7,rmb_seq=709,},
{grade=7,rmb_seq=710,},
{grade=7,rmb_seq=711,},
{grade=7,rmb_seq=712,},
{grade=7,rmb_seq=713,},
{grade=8,rmb_seq=800,},
{grade=8,rmb_seq=801,},
{grade=8,rmb_seq=802,},
{grade=8,rmb_seq=803,},
{grade=8,rmb_seq=804,},
{grade=8,rmb_seq=805,},
{grade=8,rmb_seq=806,},
{grade=8,rmb_seq=807,},
{grade=8,rmb_seq=808,},
{grade=8,rmb_seq=809,},
{grade=8,rmb_seq=810,},
{grade=8,rmb_seq=811,},
{grade=8,rmb_seq=812,},
{grade=8,rmb_seq=813,}
},

pay_one_get_more_shop_meta_table_map={
[57]=1,	-- depth:1
[29]=57,	-- depth:2
[71]=57,	-- depth:2
[85]=57,	-- depth:2
[15]=57,	-- depth:2
[43]=57,	-- depth:2
[99]=57,	-- depth:2
[46]=4,	-- depth:1
[53]=11,	-- depth:1
[54]=12,	-- depth:1
[55]=13,	-- depth:1
[111]=13,	-- depth:1
[110]=12,	-- depth:1
[59]=3,	-- depth:1
[60]=4,	-- depth:1
[67]=11,	-- depth:1
[68]=12,	-- depth:1
[69]=13,	-- depth:1
[70]=14,	-- depth:1
[98]=14,	-- depth:1
[73]=3,	-- depth:1
[74]=4,	-- depth:1
[81]=11,	-- depth:1
[82]=12,	-- depth:1
[83]=13,	-- depth:1
[45]=3,	-- depth:1
[109]=11,	-- depth:1
[87]=3,	-- depth:1
[88]=4,	-- depth:1
[95]=11,	-- depth:1
[96]=12,	-- depth:1
[102]=4,	-- depth:1
[101]=3,	-- depth:1
[97]=13,	-- depth:1
[84]=14,	-- depth:1
[56]=14,	-- depth:1
[112]=14,	-- depth:1
[42]=14,	-- depth:1
[18]=4,	-- depth:1
[25]=11,	-- depth:1
[26]=12,	-- depth:1
[27]=13,	-- depth:1
[28]=14,	-- depth:1
[31]=3,	-- depth:1
[32]=4,	-- depth:1
[17]=3,	-- depth:1
[39]=11,	-- depth:1
[41]=13,	-- depth:1
[40]=12,	-- depth:1
[100]=2,	-- depth:1
[20]=6,	-- depth:1
[86]=2,	-- depth:1
[19]=5,	-- depth:1
[89]=5,	-- depth:1
[90]=6,	-- depth:1
[108]=10,	-- depth:1
[107]=9,	-- depth:1
[106]=8,	-- depth:1
[105]=7,	-- depth:1
[92]=8,	-- depth:1
[93]=9,	-- depth:1
[94]=10,	-- depth:1
[21]=7,	-- depth:1
[16]=2,	-- depth:1
[104]=6,	-- depth:1
[103]=5,	-- depth:1
[91]=7,	-- depth:1
[22]=8,	-- depth:1
[78]=8,	-- depth:1
[24]=10,	-- depth:1
[47]=5,	-- depth:1
[48]=6,	-- depth:1
[49]=7,	-- depth:1
[50]=8,	-- depth:1
[51]=9,	-- depth:1
[52]=10,	-- depth:1
[38]=10,	-- depth:1
[37]=9,	-- depth:1
[36]=8,	-- depth:1
[35]=7,	-- depth:1
[58]=2,	-- depth:1
[34]=6,	-- depth:1
[23]=9,	-- depth:1
[33]=5,	-- depth:1
[62]=6,	-- depth:1
[63]=7,	-- depth:1
[64]=8,	-- depth:1
[65]=9,	-- depth:1
[66]=10,	-- depth:1
[30]=2,	-- depth:1
[72]=2,	-- depth:1
[75]=5,	-- depth:1
[76]=6,	-- depth:1
[77]=7,	-- depth:1
[79]=9,	-- depth:1
[80]=10,	-- depth:1
[61]=5,	-- depth:1
[44]=2,	-- depth:1
},
quota_shop={
{show_price=30,},
{item_seq=1,rmb_seq=101,show_price=60,reward={[0]=item_table[2]},},
{item_seq=2,rmb_seq=102,show_price=70,reward={[0]=item_table[16]},},
{item_seq=3,rmb_seq=103,show_price=20,reward={[0]=item_table[17]},buy_limit=2,},
{item_seq=4,rmb_seq=104,reward={[0]=item_table[19]},},
{item_seq=5,rmb_seq=105,reward={[0]=item_table[5]},},
{item_seq=6,rmb_seq=106,reward={[0]=item_table[6]},},
{item_seq=7,rmb_seq=107,reward={[0]=item_table[20]},},
{item_seq=8,rmb_seq=108,show_price=150,reward={[0]=item_table[21]},},
{item_seq=9,rmb_seq=109,show_price=160,reward={[0]=item_table[12]},},
{item_seq=10,rmb_seq=110,show_price=160,reward={[0]=item_table[26]},},
{item_seq=11,rmb_seq=111,reward={[0]=item_table[81]},buy_limit=10,},
{grade=2,rmb_seq=200,},
{grade=2,rmb_seq=201,},
{grade=2,rmb_seq=202,},
{grade=2,rmb_seq=203,},
{grade=2,rmb_seq=204,},
{grade=2,rmb_seq=205,},
{grade=2,rmb_seq=206,},
{grade=2,rmb_seq=207,},
{grade=2,rmb_seq=208,},
{grade=2,rmb_seq=209,},
{grade=2,rmb_seq=210,},
{grade=2,rmb_seq=211,},
{grade=3,rmb_seq=300,},
{grade=3,rmb_seq=301,},
{grade=3,rmb_seq=302,},
{grade=3,rmb_seq=303,},
{grade=3,rmb_seq=304,},
{grade=3,rmb_seq=305,},
{grade=3,rmb_seq=306,},
{grade=3,rmb_seq=307,},
{grade=3,rmb_seq=308,},
{grade=3,rmb_seq=309,},
{grade=3,rmb_seq=310,},
{grade=3,rmb_seq=311,},
{grade=4,rmb_seq=400,},
{grade=4,rmb_seq=401,},
{grade=4,rmb_seq=402,},
{grade=4,rmb_seq=403,},
{grade=4,rmb_seq=404,},
{grade=4,rmb_seq=405,},
{grade=4,rmb_seq=406,},
{grade=4,rmb_seq=407,},
{grade=4,rmb_seq=408,},
{grade=4,rmb_seq=409,},
{grade=4,rmb_seq=410,},
{grade=4,rmb_seq=411,},
{grade=5,rmb_seq=500,},
{grade=5,rmb_seq=501,},
{grade=5,rmb_seq=502,},
{grade=5,rmb_seq=503,},
{grade=5,rmb_seq=504,},
{grade=5,rmb_seq=505,},
{grade=5,rmb_seq=506,},
{grade=5,rmb_seq=507,},
{grade=5,rmb_seq=508,},
{grade=5,rmb_seq=509,},
{grade=5,rmb_seq=510,},
{grade=5,rmb_seq=511,},
{grade=6,rmb_seq=600,},
{grade=6,rmb_seq=601,},
{grade=6,rmb_seq=602,},
{grade=6,rmb_seq=603,},
{grade=6,rmb_seq=604,},
{grade=6,rmb_seq=605,},
{grade=6,rmb_seq=606,},
{grade=6,rmb_seq=607,},
{grade=6,rmb_seq=608,},
{grade=6,rmb_seq=609,},
{grade=6,rmb_seq=610,},
{grade=6,rmb_seq=611,},
{grade=7,rmb_seq=700,},
{grade=7,rmb_seq=701,},
{grade=7,rmb_seq=702,},
{grade=7,rmb_seq=703,},
{grade=7,rmb_seq=704,},
{grade=7,rmb_seq=705,},
{grade=7,rmb_seq=706,},
{grade=7,rmb_seq=707,},
{grade=7,rmb_seq=708,},
{grade=7,rmb_seq=709,},
{grade=7,rmb_seq=710,},
{grade=7,rmb_seq=711,},
{grade=8,rmb_seq=800,},
{grade=8,rmb_seq=801,},
{grade=8,rmb_seq=802,},
{grade=8,rmb_seq=803,},
{grade=8,rmb_seq=804,},
{grade=8,rmb_seq=805,},
{grade=8,rmb_seq=806,},
{grade=8,rmb_seq=807,},
{grade=8,rmb_seq=808,},
{grade=8,rmb_seq=809,},
{grade=8,rmb_seq=810,},
{grade=8,rmb_seq=811,}
},

quota_shop_meta_table_map={
[37]=1,	-- depth:1
[85]=37,	-- depth:2
[13]=37,	-- depth:2
[73]=37,	-- depth:2
[49]=37,	-- depth:2
[61]=37,	-- depth:2
[25]=37,	-- depth:2
[68]=8,	-- depth:1
[77]=5,	-- depth:1
[32]=8,	-- depth:1
[31]=7,	-- depth:1
[41]=5,	-- depth:1
[67]=7,	-- depth:1
[43]=7,	-- depth:1
[44]=8,	-- depth:1
[30]=6,	-- depth:1
[66]=6,	-- depth:1
[65]=5,	-- depth:1
[53]=5,	-- depth:1
[54]=6,	-- depth:1
[55]=7,	-- depth:1
[42]=6,	-- depth:1
[56]=8,	-- depth:1
[29]=5,	-- depth:1
[79]=7,	-- depth:1
[90]=6,	-- depth:1
[89]=5,	-- depth:1
[78]=6,	-- depth:1
[17]=5,	-- depth:1
[18]=6,	-- depth:1
[19]=7,	-- depth:1
[91]=7,	-- depth:1
[20]=8,	-- depth:1
[92]=8,	-- depth:1
[80]=8,	-- depth:1
[60]=12,	-- depth:1
[59]=11,	-- depth:1
[94]=10,	-- depth:1
[86]=2,	-- depth:1
[70]=10,	-- depth:1
[71]=11,	-- depth:1
[72]=12,	-- depth:1
[84]=12,	-- depth:1
[74]=2,	-- depth:1
[83]=11,	-- depth:1
[82]=10,	-- depth:1
[62]=2,	-- depth:1
[58]=10,	-- depth:1
[48]=12,	-- depth:1
[96]=12,	-- depth:1
[23]=11,	-- depth:1
[22]=10,	-- depth:1
[34]=10,	-- depth:1
[35]=11,	-- depth:1
[36]=12,	-- depth:1
[38]=2,	-- depth:1
[26]=2,	-- depth:1
[14]=2,	-- depth:1
[24]=12,	-- depth:1
[46]=10,	-- depth:1
[47]=11,	-- depth:1
[95]=11,	-- depth:1
[50]=2,	-- depth:1
[3]=12,	-- depth:1
[9]=4,	-- depth:1
[15]=3,	-- depth:2
[93]=9,	-- depth:2
[87]=3,	-- depth:2
[16]=4,	-- depth:1
[21]=9,	-- depth:2
[88]=4,	-- depth:1
[33]=9,	-- depth:2
[27]=3,	-- depth:2
[28]=4,	-- depth:1
[57]=9,	-- depth:2
[76]=4,	-- depth:1
[75]=3,	-- depth:2
[39]=3,	-- depth:2
[69]=9,	-- depth:2
[40]=4,	-- depth:1
[45]=9,	-- depth:2
[64]=4,	-- depth:1
[63]=3,	-- depth:2
[51]=3,	-- depth:2
[81]=9,	-- depth:2
[52]=4,	-- depth:1
},
gold_shop={
{price=1600,subsidy=400,},
{item_seq=1,price=3000,reward={[0]=item_table[82]},subsidy=12000,},
{item_seq=2,price=4500,reward={[0]=item_table[83]},subsidy=18000,},
{item_seq=3,price=2400,reward={[0]=item_table[81]},subsidy=597,},
{item_seq=4,price=750,reward={[0]=item_table[11]},subsidy=750,},
{item_seq=5,reward={[0]=item_table[7]},},
{item_seq=6,reward={[0]=item_table[8]},},
{item_seq=7,reward={[0]=item_table[9]},},
{item_seq=8,reward={[0]=item_table[10]},},
{item_seq=9,reward={[0]=item_table[84]},},
{item_seq=10,reward={[0]=item_table[85]},},
{item_seq=11,price=168,reward={[0]=item_table[86]},subsidy=72,},
{item_seq=12,reward={[0]=item_table[87]},},
{item_seq=13,reward={[0]=item_table[88]},},
{item_seq=14,reward={[0]=item_table[89]},},
{item_seq=15,price=630,reward={[0]=item_table[90]},subsidy=270,},
{item_seq=16,reward={[0]=item_table[91]},},
{item_seq=17,reward={[0]=item_table[92]},},
{item_seq=18,price=315,reward={[0]=item_table[93]},subsidy=135,},
{item_seq=19,reward={[0]=item_table[94]},},
{item_seq=20,reward={[0]=item_table[95]},},
{item_seq=21,price=21,reward={[0]=item_table[96]},buy_limit=10,subsidy=9,},
{item_seq=22,reward={[0]=item_table[97]},buy_limit=5,},
{item_seq=23,reward={[0]=item_table[98]},},
{item_seq=24,reward={[0]=item_table[99]},},
{item_seq=25,reward={[0]=item_table[100]},},
{item_seq=26,reward={[0]=item_table[101]},},
{item_seq=27,reward={[0]=item_table[102]},},
{item_seq=28,reward={[0]=item_table[103]},},
{item_seq=29,reward={[0]=item_table[104]},},
{item_seq=30,reward={[0]=item_table[105]},buy_limit=2,},
{item_seq=31,reward={[0]=item_table[106]},},
{item_seq=32,reward={[0]=item_table[107]},}
},

gold_shop_meta_table_map={
[30]=31,	-- depth:1
[29]=31,	-- depth:1
[28]=31,	-- depth:1
[27]=31,	-- depth:1
[26]=31,	-- depth:1
[21]=23,	-- depth:1
[32]=31,	-- depth:1
[33]=31,	-- depth:1
[13]=12,	-- depth:1
[14]=12,	-- depth:1
[15]=12,	-- depth:1
[10]=12,	-- depth:1
[9]=5,	-- depth:1
[11]=12,	-- depth:1
[8]=5,	-- depth:1
[6]=5,	-- depth:1
[7]=5,	-- depth:1
[24]=22,	-- depth:1
[20]=22,	-- depth:1
[19]=23,	-- depth:1
[18]=22,	-- depth:1
[16]=31,	-- depth:1
[25]=19,	-- depth:2
[17]=16,	-- depth:2
},
high_price_shop={
{price=3888,reward={[0]=item_table[108],[1]=item_table[109],[2]=item_table[110],[3]=item_table[111],[4]=item_table[112],[5]=item_table[113]},member_price=888,helper_num_reward_1=item_table[114],helper_num_reward_2=item_table[114],helper_num_reward_3=item_table[115],show_item_id=30721,position="0|0|0",scale=0.8,},
{member_level=2,},
{member_price=98,member_level=3,},
{item_seq=1,rmb_seq=101,},
{item_seq=1,rmb_seq=101,},
{member_price=98,member_level=3,},
{item_seq=2,rmb_seq=102,},
{item_seq=2,rmb_seq=102,},
{member_price=98,member_level=3,},
{item_seq=3,rmb_seq=103,},
{item_seq=3,rmb_seq=103,},
{member_price=98,member_level=3,},
{grade=2,item_seq=1,rmb_seq=201,reward={[0]=item_table[116]},member_price=168,tab_desc="大甩卖",desc="超多实惠",show_item_id=37712,position="0|-0.5|0",rotation="0|0|0",scale=0.8,},
{grade=2,item_seq=1,rmb_seq=201,reward={[0]=item_table[116]},member_level=2,tab_desc="大甩卖",desc="超多实惠",show_item_id=37712,position="0|-0.5|0",rotation="0|0|0",scale=0.8,},
{member_price=98,member_level=3,},
{grade=2,rmb_seq=200,member_price=168,tab_name="豪华专享",tab_desc="大甩卖",desc="超多实惠",},
{grade=2,rmb_seq=200,member_level=2,tab_name="豪华专享",tab_desc="大甩卖",desc="超多实惠",},
{member_price=98,member_level=3,},
{grade=2,item_seq=2,rmb_seq=203,price=3280,member_price=1188,tab_name="臻品直购",tab_desc="大甩卖",desc="超多实惠",},
{member_level=2,},
{member_price=648,member_level=3,}
},

high_price_shop_meta_table_map={
[11]=2,	-- depth:1
[5]=11,	-- depth:2
[8]=11,	-- depth:2
[6]=5,	-- depth:3
[9]=8,	-- depth:3
[12]=11,	-- depth:2
[18]=16,	-- depth:1
[20]=19,	-- depth:1
[21]=19,	-- depth:1
[10]=1,	-- depth:1
[7]=10,	-- depth:2
[4]=10,	-- depth:2
[15]=13,	-- depth:1
},
high_price_reduce={
{},
{seq=1,buy_times=3,reward={[0]=item_table[117]},reduce_value=20,},
{seq=2,buy_times=5,reward={[0]=item_table[73]},reduce_value=30,},
{seq=3,buy_times=10,reward={[0]=item_table[83]},reduce_value=50,}
},

high_price_reduce_meta_table_map={
},
participate_shop={
{price=1000,tab_name="火神三太子",},
{item_seq=1,price=1000,reward={[0]=item_table[117]},tab_name="随心染",},
{item_seq=2,reward={[0]=item_table[73]},},
{item_seq=3,price=4500,reward={[0]=item_table[83]},tab_name="6星幻兽升星卡",},
{item_seq=4,price=5832,reward={[0]=item_table[13]},tab_name="6级攻击玉魄",},
{item_seq=5,start_time=39600,end_time=43200,reward={[0]=item_table[74]},},
{item_seq=6,price=5000,reward={[0]=item_table[118]},tab_name="绝版相框",},
{item_seq=7,price=5832,reward={[0]=item_table[25]},tab_name="6级生命玉魄",},
{item_seq=8,start_time=43200,end_time=46800,reward={[0]=item_table[75]},},
{item_seq=9,start_time=57600,end_time=61200,},
{item_seq=10,price=1988,reward={[0]=item_table[13]},tab_name="6级攻击玉魄",},
{item_seq=11,start_time=57600,end_time=61200,reward={[0]=item_table[76]},},
{item_seq=12,price=5000,reward={[0]=item_table[119]},tab_name="绝版气泡",},
{item_seq=13,price=1988,reward={[0]=item_table[25]},tab_name="6级生命玉魄",},
{item_seq=14,start_time=61200,end_time=64800,reward={[0]=item_table[120]},},
{item_seq=15,start_time=72000,end_time=75600,price=2000,},
{item_seq=16,price=5888,reward={[0]=item_table[13]},tab_name="6级攻击玉魄",},
{item_seq=17,start_time=72000,end_time=75600,reward={[0]=item_table[73]},},
{item_seq=18,price=5000,reward={[0]=item_table[121]},tab_name="绝世·奇缘石",},
{item_seq=19,price=5888,reward={[0]=item_table[25]},tab_name="6级生命玉魄",},
{item_seq=20,start_time=75600,end_time=79200,reward={[0]=item_table[76]},},
{item_seq=21,price=1000,reward={[0]=item_table[17]},tab_name="高级合鸣石自选包",},
{item_seq=22,price=10000,reward={[0]=item_table[122]},tab_name="7级攻击玉魄",},
{item_seq=23,start_time=79200,end_time=82800,reward={[0]=item_table[120]},}
},

participate_shop_meta_table_map={
[16]=1,	-- depth:1
[10]=1,	-- depth:1
[23]=24,	-- depth:1
[13]=15,	-- depth:1
[14]=15,	-- depth:1
[5]=6,	-- depth:1
[7]=9,	-- depth:1
[17]=18,	-- depth:1
[4]=6,	-- depth:1
[19]=21,	-- depth:1
[20]=21,	-- depth:1
[22]=24,	-- depth:1
[11]=12,	-- depth:1
[8]=9,	-- depth:1
},
try_shop={
{},
{item_seq=1,reward={[0]=item_table[123]},},
{item_seq=2,reward={[0]=item_table[124]},},
{item_seq=3,reward={[0]=item_table[125]},}
},

try_shop_meta_table_map={
},
discount_ticket={
{ticket_pool_seq=0,quota_limit=10,},
{ticket_pool_seq=0,ticket_seq=2,},
{ticket_pool_seq=0,ticket_seq=3,},
{ticket_pool_seq=0,ticket_seq=4,},
{ticket_pool_seq=0,ticket_seq=5,},
{ticket_pool_seq=1,ticket_seq=6,},
{ticket_pool_seq=1,ticket_seq=7,},
{ticket_pool_seq=1,ticket_seq=8,},
{ticket_pool_seq=1,ticket_seq=9,},
{ticket_pool_seq=1,ticket_seq=10,},
{ticket_pool_seq=1,ticket_seq=11,},
{ticket_pool_seq=1,ticket_seq=12,},
{ticket_seq=13,reduce_quota=3,},
{ticket_seq=14,quota_limit=10,},
{ticket_seq=15,quota_limit=20,reduce_quota=3,},
{ticket_seq=16,quota_limit=30,reduce_quota=4,},
{ticket_seq=17,quota_limit=50,reduce_quota=6,},
{ticket_seq=18,quota_limit=80,reduce_quota=10,},
{ticket_seq=19,quota_limit=100,reduce_quota=12,},
{ticket_seq=20,quota_limit=150,reduce_quota=18,},
{ticket_seq=21,quota_limit=200,reduce_quota=24,},
{ticket_seq=22,quota_limit=300,reduce_quota=36,},
{ticket_seq=23,ticket_weight=50,},
{ticket_seq=24,reduce_quota=4,},
{ticket_seq=25,reduce_quota=5,}
},

discount_ticket_meta_table_map={
[24]=23,	-- depth:1
[25]=23,	-- depth:1
[6]=14,	-- depth:1
[5]=23,	-- depth:1
[12]=23,	-- depth:1
[11]=19,	-- depth:1
[10]=18,	-- depth:1
[9]=17,	-- depth:1
[8]=16,	-- depth:1
[7]=15,	-- depth:1
[4]=17,	-- depth:1
[3]=16,	-- depth:1
[2]=15,	-- depth:1
[13]=12,	-- depth:2
},
oneyuan_welfare={
{},
{grade=2,rmb_seq=2,model_name="幻兽UR自选",model_show_itemid=30551,display_pos="0|0.5|0",display_rotation="0|180|0",}
},

oneyuan_welfare_meta_table_map={
},
oneyuan_welfare_reward={
{},
{day=2,reward_item={[0]=item_table[126]},},
{day=3,reward_item={[0]=item_table[127]},},
{day=4,reward_item={[0]=item_table[128]},},
{day=5,reward_item={[0]=item_table[129]},},
{day=6,reward_item={[0]=item_table[130]},},
{day=7,reward_item={[0]=item_table[131]},},
{grade=2,reward_item={[0]=item_table[132]},},
{day=2,reward_item={[0]=item_table[133]},},
{day=3,reward_item={[0]=item_table[134]},},
{day=4,reward_item={[0]=item_table[135]},},
{day=5,reward_item={[0]=item_table[136]},},
{day=6,reward_item={[0]=item_table[137]},},
{day=7,reward_item={[0]=item_table[138]},}
},

oneyuan_welfare_reward_meta_table_map={
[9]=8,	-- depth:1
[10]=8,	-- depth:1
[11]=8,	-- depth:1
[12]=8,	-- depth:1
[13]=8,	-- depth:1
[14]=8,	-- depth:1
},
other_default_table={cumulate_order_num=1,cumulate_order_num_rward={[0]=item_table[139]},first_pay_single_min_quota=5,return_reward_price=30,first_pay_single_reward={[0]=item_table[139]},pay_one_get_more_refresh_shop_num=8,free_ticket_item_id=59661,free_ticket_quota=100,guide_ticket_seq=24,free_ticket_click_item_id=91621,full_discount_ticket_click_item_id=91620,},

grade_default_table={type=1,start_open_day=1,end_open_day=999,grade=1,},

member_rmb_buy_default_table={rmb_seq=0,member_level=3,cur_member_level=1,rmb_type=216,price=30,reward={[0]=item_table[140],[1]=item_table[121],[2]=item_table[141]},reward_show={[0]=item_table[140],[1]=item_table[121],[2]=item_table[141]},},

member_level_default_table={member_level=1,high_price_shop_discount=9,return_times=0,return_need_quota=0,return_quota_reward={},return_reward_price=0,open_quota_shop=0,daily_try_ticket_reward=0,daily_use_try_ticket_limit=0,discount_ticket_extend_times=1,daily_discount_ticket_num=2,discount_ticket_pool=0,pay_one_get_more_choose_item_num=3,pay_one_get_more_daily_use_times=0,pay_one_get_more_daily_free_refresh_times=3,pay_one_get_more_refresh_times_cost_gold=50,no_limit_discount_daily_use_limit=1,limit_discount_daily_use_limit=1,free_ticket_daily_use_limit=0,can_fetch_cumulate_order_num_reward=0,},

member_benefits_default_table={benefits_seq=1,benefits_name="大额直购",benefits_desc="专属商店",benefits_icon="a3_bybt_hy_jy4",is_show_arrow=1,required_level=3,},

daily_gift_default_table={grade=1,item_seq=0,rmb_seq=0,rmb_type=222,price=30,reward={[0]=item_table[140],[1]=item_table[3],[2]=item_table[142],[3]=item_table[143]},buy_limit=1,subsidy=100,need_member_level=3,},

ten_billion_subsidy_shop_default_table={grade=8,item_seq=0,rmb_seq=100,rmb_type=217,price=5,reward={[0]=item_table[121]},buy_limit=1,free_ticket_num=1,subsidy=49,old_price=50,discount=0,limit_time_discount_price=0,limit_time_discount_duration=0,},

time_limit_shop_default_table={rmb_seq=100,grade=5,item_seq=0,rmb_type=218,price=14,reward={[0]=item_table[144]},buy_limit=10,free_ticket_num=1,limit_time_discount_price=10,limit_time_discount_duration=7200,subsidy=89,old_price=99,discount=1,},

pay_one_get_more_shop_default_table={grade=1,item_seq=0,rmb_seq=100,rmb_type=219,price=30,reward={[0]=item_table[121]},buy_limit=1,discount=1,},

quota_shop_default_table={grade=1,item_seq=0,rmb_seq=100,rmb_type=220,price=2,show_price=100,discount=1,reward={[0]=item_table[121]},buy_limit=1,free_ticket_num=1,},

gold_shop_default_table={grade=1,item_seq=0,price=126,reward={[0]=item_table[145]},buy_limit=1,subsidy=54,free_member_level=2,},

high_price_shop_default_table={grade=1,item_seq=0,rmb_seq=100,rmb_type=221,price=328,reward={[0]=item_table[146],[1]=item_table[147],[2]=item_table[148],[3]=item_table[149],[4]=item_table[150],[5]=item_table[151]},buy_limit=1,free_ticket_num=0,member_price=128,member_level=1,help_return_gold=10,helper_num_1=1,helper_num_reward_1=item_table[116],helper_num_2=2,helper_num_reward_2=item_table[116],helper_num_3=3,helper_num_reward_3=item_table[116],tab_name="超值特惠",tab_desc="特价",desc="特价",show_item_id="37730|37035|37603|37237|37409|38102|37506|38700",position="0|-0.9|0",rotation="0|-9|0",scale=0.5,},

high_price_reduce_default_table={seq=0,buy_times=1,reward={[0]=item_table[16]},reduce_value=10,},

participate_shop_default_table={grade=1,item_seq=0,pre_item_seq=-1,start_time=36000,end_time=39600,price=4800,current_type=1,reward={[0]=item_table[16]},max_participate_num=5,fetch_reward_participate_num_limit=3,tab_name="极品秘籍",},

try_shop_default_table={grade=1,item_seq=0,reward={[0]=item_table[152]},cost=1,fake_price=3000,},

discount_ticket_default_table={ticket_pool_seq=2,ticket_seq=1,ticket_weight=100,quota_limit=0,reduce_quota=2,},

oneyuan_welfare_default_table={grade=1,rmb_type=237,rmb_seq=1,price=1,model_show_type=1,model_bundle_name="",model_asset_name="",model_name="火神·三太子",model_show_itemid=30643,display_pos="0|0|0",display_scale=1.1,display_rotation="0|200|0",title="a3_bybt_yyfl_title",},

oneyuan_welfare_reward_default_table={grade=1,day=1,reward_item={[0]=item_table[153]},}

}

