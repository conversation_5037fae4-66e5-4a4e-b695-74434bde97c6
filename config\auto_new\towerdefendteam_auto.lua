return {
	["other"]={
		{free_join_times=2,first_wave_s=10,team_life_tower_monster_id=1,auto_item_count=5,auto_item_id=22000,auto_fb_empty_grid=2,life_tower_monster_id=1,team_birth_pos_y=67,team_enter_sceneid=7004,team_birth_pos_x=104,},},
	["pass_level_config_default_table"]={},
	["wave_list"]={
		{monster_id_1=10101,wave=0,monster_id_0=10101,level=0,wave_level=32,monster_id_2=10101,},
		{monster_id_1=10102,monster_id_0=10102,level=0,wave_level=32,monster_id_2=10102,},
		{monster_id_1=10201,wave=2,monster_id_0=10201,level=0,wave_level=32,monster_id_2=10201,},
		{monster_id_1=10202,wave=3,monster_id_0=10202,level=0,wave_level=32,monster_id_2=10202,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=10203,wave=4,monster_count_0=1,monster_id_0=10203,level=0,wave_level=32,monster_id_2=10203,},
		{monster_id_1=10204,wave=5,monster_id_0=10204,level=0,wave_level=32,monster_id_2=10204,},
		{monster_id_1=10205,wave=6,monster_id_0=10205,level=0,wave_level=32,monster_id_2=10205,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=10206,wave=7,monster_count_0=2,monster_id_0=10206,level=0,wave_level=32,monster_id_2=10206,},
		{monster_id_1=10207,wave=8,monster_id_0=10207,level=0,wave_level=32,monster_id_2=10207,},
		{monster_count_1=1,monster_count_2=0,next_wave_s=99999,monster_id_1=10208,wave=9,monster_count_0=0,level=0,wave_level=32,},
		{monster_id_1=10111,wave=0,monster_id_0=10111,wave_level=40,monster_id_2=10111,},
		{monster_id_1=10301,monster_id_0=10301,wave_level=40,monster_id_2=10301,},
		{monster_id_1=10302,wave=2,monster_id_0=10302,wave_level=40,monster_id_2=10302,},
		{monster_id_1=10303,wave=3,monster_id_0=10303,wave_level=40,monster_id_2=10303,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=10304,wave=4,monster_count_0=1,monster_id_0=10304,wave_level=40,monster_id_2=10304,},
		{monster_id_1=10305,wave=5,monster_id_0=10305,wave_level=40,monster_id_2=10305,},
		{monster_id_1=10401,wave=6,monster_id_0=10401,wave_level=40,monster_id_2=10401,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=10402,wave=7,monster_count_0=2,monster_id_0=10402,wave_level=40,monster_id_2=10402,},
		{monster_id_1=10403,wave=8,monster_id_0=10403,wave_level=40,monster_id_2=10403,},
		{monster_count_1=1,monster_count_2=0,next_wave_s=99999,monster_id_1=10404,wave=9,monster_count_0=0,wave_level=40,},
		{monster_id_1=10405,wave=0,monster_id_0=10405,level=2,monster_id_2=10405,},
		{monster_id_1=10406,monster_id_0=10406,level=2,monster_id_2=10406,},
		{monster_id_1=10500,wave=2,monster_id_0=10500,level=2,monster_id_2=10500,},
		{monster_id_1=10501,wave=3,monster_id_0=10501,level=2,monster_id_2=10501,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=10502,wave=4,monster_count_0=1,monster_id_0=10502,level=2,monster_id_2=10502,},
		{monster_id_1=10503,wave=5,monster_id_0=10503,level=2,monster_id_2=10503,},
		{monster_id_1=10600,wave=6,monster_id_0=10600,level=2,monster_id_2=10600,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=10601,wave=7,monster_count_0=2,monster_id_0=10601,level=2,monster_id_2=10601,},
		{monster_id_1=10602,wave=8,monster_id_0=10602,level=2,monster_id_2=10602,},
		{monster_count_1=1,monster_count_2=0,next_wave_s=99999,monster_id_1=10603,wave=9,monster_count_0=0,level=2,},
		{monster_id_1=10604,wave=0,monster_id_0=10604,level=3,wave_level=60,monster_id_2=10604,},
		{monster_id_1=10700,monster_id_0=10700,level=3,wave_level=60,monster_id_2=10700,},
		{monster_id_1=10701,wave=2,monster_id_0=10701,level=3,wave_level=60,monster_id_2=10701,},
		{monster_id_1=10702,wave=3,monster_id_0=10702,level=3,wave_level=60,monster_id_2=10702,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=10703,wave=4,monster_count_0=1,monster_id_0=10703,level=3,wave_level=60,monster_id_2=10703,},
		{monster_id_1=10704,wave=5,monster_id_0=10704,level=3,wave_level=60,monster_id_2=10704,},
		{monster_id_1=10705,wave=6,monster_id_0=10705,level=3,wave_level=60,monster_id_2=10705,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=10706,wave=7,monster_count_0=2,monster_id_0=10706,level=3,wave_level=60,monster_id_2=10706,},
		{monster_id_1=10707,wave=8,monster_id_0=10707,level=3,wave_level=60,monster_id_2=10707,},
		{monster_count_1=1,monster_count_2=0,next_wave_s=99999,monster_id_1=10800,wave=9,monster_count_0=0,level=3,wave_level=60,},
		{monster_id_1=10801,wave=0,monster_id_0=10801,level=4,wave_level=70,monster_id_2=10801,},
		{monster_id_1=10802,monster_id_0=10802,level=4,wave_level=70,monster_id_2=10802,},
		{monster_id_1=10803,wave=2,monster_id_0=10803,level=4,wave_level=70,monster_id_2=10803,},
		{monster_id_1=10804,wave=3,monster_id_0=10804,level=4,wave_level=70,monster_id_2=10804,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=10805,wave=4,monster_count_0=1,monster_id_0=10805,level=4,wave_level=70,monster_id_2=10805,},
		{monster_id_1=10806,wave=5,monster_id_0=10806,level=4,wave_level=70,monster_id_2=10806,},
		{monster_id_1=10807,wave=6,monster_id_0=10807,level=4,wave_level=70,monster_id_2=10807,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=10900,wave=7,monster_count_0=2,monster_id_0=10900,level=4,wave_level=70,monster_id_2=10900,},
		{monster_id_1=10901,wave=8,monster_id_0=10901,level=4,wave_level=70,monster_id_2=10901,},
		{monster_count_1=1,monster_count_2=0,next_wave_s=99999,monster_id_1=10902,wave=9,monster_count_0=0,level=4,wave_level=70,},
		{monster_id_1=10903,wave=0,monster_id_0=10903,level=5,wave_level=80,monster_id_2=10903,},
		{monster_id_1=10904,monster_id_0=10904,level=5,wave_level=80,monster_id_2=10904,},
		{monster_id_1=10905,wave=2,monster_id_0=10905,level=5,wave_level=80,monster_id_2=10905,},
		{monster_id_1=10906,wave=3,monster_id_0=10906,level=5,wave_level=80,monster_id_2=10906,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=10907,wave=4,monster_count_0=1,monster_id_0=10907,level=5,wave_level=80,monster_id_2=10907,},
		{monster_id_1=11000,wave=5,monster_id_0=11000,level=5,wave_level=80,monster_id_2=11000,},
		{monster_id_1=11001,wave=6,monster_id_0=11001,level=5,wave_level=80,monster_id_2=11001,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=11002,wave=7,monster_count_0=2,monster_id_0=11002,level=5,wave_level=80,monster_id_2=11002,},
		{monster_id_1=11003,wave=8,monster_id_0=11003,level=5,wave_level=80,monster_id_2=11003,},
		{monster_count_1=1,monster_count_2=0,next_wave_s=99999,monster_id_1=11004,wave=9,monster_count_0=0,level=5,wave_level=80,},
		{monster_id_1=11005,wave=0,monster_id_0=11005,level=6,wave_level=90,monster_id_2=11005,},
		{monster_id_1=11006,monster_id_0=11006,level=6,wave_level=90,monster_id_2=11006,},
		{monster_id_1=11007,wave=2,monster_id_0=11007,level=6,wave_level=90,monster_id_2=11007,},
		{monster_id_1=11008,wave=3,monster_id_0=11008,level=6,wave_level=90,monster_id_2=11008,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=11009,wave=4,monster_count_0=1,monster_id_0=11009,level=6,wave_level=90,monster_id_2=11009,},
		{monster_id_1=11100,wave=5,monster_id_0=11100,level=6,wave_level=90,monster_id_2=11100,},
		{monster_id_1=11101,wave=6,monster_id_0=11101,level=6,wave_level=90,monster_id_2=11101,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=11102,wave=7,monster_count_0=2,monster_id_0=11102,level=6,wave_level=90,monster_id_2=11102,},
		{wave=8,monster_id_0=11103,level=6,wave_level=90,monster_id_2=11103,},
		{monster_count_1=1,monster_count_2=0,next_wave_s=99999,monster_id_1=11104,wave=9,monster_count_0=0,level=6,wave_level=90,},
		{monster_id_1=11105,wave=0,monster_id_0=11105,level=7,wave_level=95,monster_id_2=11105,},
		{monster_id_1=11106,monster_id_0=11106,level=7,wave_level=95,monster_id_2=11106,},
		{monster_id_1=11107,wave=2,monster_id_0=11107,level=7,wave_level=95,monster_id_2=11107,},
		{monster_id_1=11108,wave=3,monster_id_0=11108,level=7,wave_level=95,monster_id_2=11108,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=11109,wave=4,monster_count_0=1,monster_id_0=11109,level=7,wave_level=95,monster_id_2=11109,},
		{monster_id_1=10,wave=5,monster_id_0=10,level=7,wave_level=95,monster_id_2=10,},
		{monster_id_1=11,wave=6,monster_id_0=11,level=7,wave_level=95,monster_id_2=11,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=12,wave=7,monster_count_0=2,monster_id_0=12,level=7,wave_level=95,monster_id_2=12,},
		{monster_id_1=13,wave=8,monster_id_0=13,level=7,wave_level=95,monster_id_2=13,},
		{monster_count_1=1,monster_count_2=0,next_wave_s=99999,monster_id_1=14,wave=9,monster_count_0=0,level=7,wave_level=95,},},
	["skill_cfg"]={
		{param_c=1,attr_type=0,cd_s=3,skill_index=0,distance=16,param_a=100,perform_type=0,hurt_percent=0,param_b=5,},
		{param_c=1,attr_type=0,cd_s=3,distance=16,perform_type=0,hurt_percent=0,param_b=1,},
		{cd_s=1,skill_id=4,skill_index=2,distance=1,param_a=100,perform_type=0,},
		{cd_s=5,skill_id=5,skill_index=3,},
		{skill_id=6,skill_index=4,param_a=400,},
		{param_c=5,cd_s=15,skill_id=7,skill_index=5,param_a=120,},
		{attr_type=2,cd_s=1,skill_index=6,distance=1,param_a=200,perform_type=0,param_b=200,},
		{param_c=2,attr_type=2,cd_s=2,skill_id=2,skill_index=7,distance=18,param_a=0,perform_type=2,hurt_percent=50,},
		{param_c=5,attr_type=2,skill_id=3,skill_index=8,param_a=10,},
		{attr_type=2,cd_s=180,skill_id=1,skill_index=9,distance=1,param_a=5,perform_type=2,},
		{attr_type=3,cd_s=15,skill_id=8,skill_index=10,},
		{param_c=200,attr_type=3,skill_id=9,skill_index=11,distance=30,param_a=0,perform_type=2,},
		{param_c=10,attr_type=3,cd_s=180,skill_id=10,skill_index=12,distance=30,param_a=100,perform_type=2,},
		{attr_type=3,cd_s=15,skill_id=11,skill_index=13,param_a=5,},},
	["path_list"]={
		{pos_x_1=35,pos_y_2=45,pos_x_2=39,path_idx=0,path_type=0,},
		{pos_x_1=83,pos_y_2=46,pos_x_2=59,path_type=0,},
		{pos_x_1=91,pos_y_2=32,pos_y_1=26,pos_x_2=61,path_idx=2,path_type=0,},
		{pos_x_3=111,pos_y_2=79,pos_y_1=88,path_idx=0,pos_x_4=111,pos_y_3=66,pos_y_4=66,},
		{pos_x_3=111,pos_x_1=47,pos_y_1=40,pos_x_4=111,pos_y_3=66,pos_y_4=66,},
		{pos_x_3=111,pos_x_1=157,pos_y_2=42,pos_y_1=23,pos_x_2=143,path_idx=2,pos_x_4=111,pos_y_3=66,pos_y_4=66,},},
	["skill_cfg_default_table"]={param_c=0,attr_type=1,distance=14,cd_s=10,skill_id=0,skill_index=1,fix_hurt=0,param_a=300,perform_type=1,hurt_percent=100,param_b=0,energy=50,cd_factor=0,},
	["path_list_default_table"]={pos_x_3=36,pos_x_1=58,pos_y_2=50,pos_y_1=67,pos_x_2=73,path_idx=1,pos_x_4=34,pos_y_3=28,path_type=1,pos_y_4=25,},
	["buy_cost"]={
		{},
		{buy_times=2,},
		{buy_times=3,},
		{buy_times=4,},
		{buy_times=5,},},
	["wave_list_default_table"]={monster_count_1=4,monster_count_2=3,next_wave_s=30,monster_id_1=11103,wave=1,monster_count_0=3,monster_id_0=0,level=1,wave_level=50,monster_id_2=0,},
	["level_scene_cfg_default_table"]={birth_pos_y=31,scene_id=3001,fb_name="诸神黄昏",birth_pos_x=41,level=1,need_level=50,show_reward=22000,},
	["buy_cost_default_table"]={buy_times=1,gold_cost=50,},
	["skill_list_default_table"]={attr_type=1,tipType="",skill_index=1,releaseEffectPath="",readEffectPath="",icon=1913,hurt_percent=0,lauchType=5,hitType=0,is_extra=0,duration="",distance=16,cd_s=20,titleRes="",skillType=3,skill_name="风咒",fix_hurt=0,hitEffectPath="",desc="治疗技能，治疗血量随攻击力和技能等级变化<br/><font color='#1eff01'>升级技能可提升技能治疗量</font>",targetType=0,processEffectPath="",cd_factor=0,},
	["skill_list"]={
		{cd_s=2,icon=1901,skill_name="火神之力",lauchType=0,distance=10,desc="朱雀被动技能，提高自身攻击力<br/><font color='#1eff01'>升级技能可以提高加成百分比</font>",skillType=5,},
		{cd_s=1,skill_index=2,icon=1902,skill_name="烈炎术",lauchType=1,desc="朱雀单体技能，对目标造成大量伤害<br/><font color='#1eff01'>升级技能可以提高伤害</font>",skillType=1,hurt_percent=120,hitEffectPath=10500,hitType=1,processEffectPath=10500,},
		{cd_s=6,skill_index=3,releaseEffectPath=10501,icon=1903,skill_name="天火术",lauchType=2,desc="朱雀群体攻击，对多个目标造成伤害<br/><font color='#1eff01'>升级技能可以提高伤害</font>",hurt_percent=50,hitEffectPath=10501,hitType=2,},
		{cd_s=15,skill_index=4,icon=1904,skill_name="炽热红莲",lauchType=2,desc="群体攻击，在地面释放一个固定伤害的持续阵法 <font color='#1eff01'>升级技能可以提高伤害</font>",hitType=2,},
		{attr_type=2,cd_s=2,skill_index=5,icon=1905,skill_name="水神之盾",lauchType=0,distance=10,desc="被动技能，提高自身防御力和仇恨<br/><font color='#1eff01'>升级技能可提升技能增加防御和仇恨</font>",skillType=5,},
		{attr_type=2,cd_s=2,skill_index=6,icon=1906,skill_name="水神之血",lauchType=0,distance=10,desc="被动技能，提高自身血量上限<br/><font color='#1eff01'>升级技能可提升技能增加血量</font>",skillType=5,},
		{attr_type=2,skill_index=7,releaseEffectPath=10502,icon=1907,skill_name="冰封咆哮",desc="群体攻击，对自身周围目标造成伤害并嘲讽，并且减少受到伤害<font color='#1eff01'>升级技能可提升伤害减免</font>",hurt_percent=50,hitType=2,},
		{attr_type=2,cd_s=1,skill_index=8,releaseEffectPath=10503,icon=1908,skill_name="寒冰杀",desc="群体攻击，对多个目标造成伤害并降低其攻<br/>击力  <font color='#1eff01'>升级技能可降低敌方更多攻击力</font>",hurt_percent=50,hitEffectPath=10503,hitType=2,},
		{attr_type=3,cd_s=1,skill_index=9,icon=1909,lauchType=1,desc="单体攻击，附加固定伤害<br/><font color='#1eff01'>升级技能可以提高伤害</font>",skillType=1,hurt_percent=100,hitEffectPath=10504,hitType=1,processEffectPath=10504,},
		{attr_type=3,cd_s=15,skill_index=10,icon=1910,skill_name="风神治疗",targetType=1,skillType=4,hitEffectPath=10505,},
		{attr_type=3,skill_index=11,icon=1911,skill_name="风神祝福",targetType=1,desc="辅助技能，提高目标的攻防值<br/><font color='#1eff01'>升级技能可提升技能攻防效果</font>",skillType=4,hitEffectPath=10506,},
		{attr_type=3,cd_s=30,skill_index=12,releaseEffectPath=10507,icon=1912,skill_name="风之禁锢",lauchType=2,desc="群体攻击，对多个目标造成伤害并定身5秒<br/><font color='#1eff01'>升级技能可降低技能冷却</font>",hurt_percent=50,hitEffectPath=10507,hitType=2,cd_factor=1,},
		{cd_s=60,skill_index=13,releaseEffectPath=10501,is_extra=1,skill_name="雷霆之怒",lauchType=2,desc="群体攻击，对多个目标造成大量伤害<br/><font color='#1eff01'>删除后下次拾取技能不会获得该技能</font>",hurt_percent=300,fix_hurt=2000,hitEffectPath=10501,hitType=2,},
		{skill_index=14,releaseEffectPath=10501,is_extra=1,icon=1914,skill_name="燃烧",lauchType=2,desc="群体攻击，对多个目标造成伤害并附带燃烧效果<br/><font color='#1eff01'>删除后下次拾取技能不会获得该技能</font>",hurt_percent=40,hitEffectPath=10501,hitType=2,},
		{attr_type=2,cd_s=180,skill_index=15,is_extra=1,icon=1915,skill_name="神佑复生",lauchType=0,distance=10,desc="被动技能，死亡后立即复活，血量剩余50%<br/>触发冷却180秒<font color='#1eff01'>删除后下次拾取技能不会获得该技能</font>",skillType=5,},
		{attr_type=2,cd_s=60,skill_index=16,is_extra=1,icon=1916,skill_name="绝对防御",targetType=1,distance=10,desc="辅助技能，使用后自身获得5秒绝对防御<br/><font color='#1eff01'>删除后下次拾取技能不会获得该技能</font>",skillType=4,hitEffectPath=10503,},
		{attr_type=3,skill_index=17,is_extra=1,icon=1917,skill_name="回春术",targetType=1,desc="辅助技能，给队友附加持续治疗状态<br/><font color='#1eff01'>删除后下次拾取技能不会获得该技能</font>",skillType=4,hitEffectPath=10505,},
		{attr_type=3,cd_s=40,skill_index=18,releaseEffectPath=10507,is_extra=1,icon=1918,skill_name="群体诅咒",lauchType=2,desc="群体攻击，使多个目标降低10%攻防持续10秒<br/><font color='#1eff01'>删除后下次拾取技能不会获得该技能</font>",hurt_percent=50,hitEffectPath=10507,hitType=2,},
		{attr_type=0,cd_s=60,skill_index=19,is_extra=1,icon=1919,skill_name="雷霆震慑",lauchType=1,desc="单体攻击，使目标眩晕5秒<br/><font color='#1eff01'>删除后下次拾取技能不会获得该技能</font>",skillType=2,hurt_percent=100,hitEffectPath=10504,hitType=1,processEffectPath=10504,},
		{attr_type=0,cd_s=30,skill_index=20,is_extra=1,icon=1920,skill_name="熔岩护盾",targetType=1,desc="辅助技能，给队友或自己附加伤害反弹状态<br/><font color='#1eff01'>删除后下次拾取技能不会获得该技能</font>",skillType=4,hitEffectPath=10505,},},
	["pass_level_config"]={
		{},
		{},
		{},
		{},
		{},},
	["team_wave_list"]={
		{monster_id_1=10101,wave=0,monster_id_0=10101,wave_level=32,monster_id_2=10101,},
		{monster_id_1=10102,monster_id_0=10102,wave_level=32,monster_id_2=10102,},
		{monster_id_1=10201,wave=2,monster_id_0=10201,wave_level=32,monster_id_2=10201,},
		{monster_id_1=10202,wave=3,monster_id_0=10202,wave_level=32,monster_id_2=10202,},
		{monster_id_1=10203,wave=4,monster_id_0=10203,wave_level=32,monster_id_2=10203,},
		{monster_id_1=10204,wave=5,monster_id_0=10204,wave_level=32,monster_id_2=10204,},
		{monster_id_1=10205,wave=6,monster_id_0=10205,wave_level=32,monster_id_2=10205,},
		{monster_id_1=10206,wave=7,monster_id_0=10206,wave_level=32,monster_id_2=10206,},
		{monster_id_1=10207,wave=8,monster_id_0=10207,wave_level=32,monster_id_2=10207,},
		{monster_count_2=0,monster_id_1=10208,wave=9,monster_count_0=0,wave_level=32,},
		{monster_id_1=10111,wave=10,monster_id_0=10111,monster_id_2=10111,},
		{monster_id_1=10301,wave=11,monster_id_0=10301,monster_id_2=10301,},
		{monster_id_1=10302,wave=12,monster_id_0=10302,monster_id_2=10302,},
		{monster_id_1=10303,wave=13,monster_id_0=10303,monster_id_2=10303,},
		{monster_id_1=10304,wave=14,monster_id_0=10304,monster_id_2=10304,},
		{monster_id_1=10305,wave=15,monster_id_0=10305,monster_id_2=10305,},
		{monster_id_1=10401,wave=16,monster_id_0=10401,monster_id_2=10401,},
		{monster_id_1=10402,wave=17,monster_id_0=10402,monster_id_2=10402,},
		{monster_id_1=10403,wave=18,monster_id_0=10403,monster_id_2=10403,},
		{monster_count_2=0,monster_id_1=10404,wave=19,monster_count_0=0,},
		{monster_id_1=10405,wave=20,monster_id_0=10405,monster_id_2=10405,},
		{monster_id_1=10406,wave=21,monster_id_0=10406,monster_id_2=10406,},
		{monster_id_1=10500,wave=22,monster_id_0=10500,monster_id_2=10500,},
		{monster_id_1=10501,wave=23,monster_id_0=10501,monster_id_2=10501,},
		{monster_id_1=10502,wave=24,monster_id_0=10502,monster_id_2=10502,},
		{monster_id_1=10503,wave=25,monster_id_0=10503,monster_id_2=10503,},
		{monster_id_1=10600,wave=26,monster_id_0=10600,monster_id_2=10600,},
		{monster_id_1=10601,wave=27,monster_id_0=10601,monster_id_2=10601,},
		{monster_id_1=10602,wave=28,monster_id_0=10602,monster_id_2=10602,},
		{monster_count_2=0,monster_id_1=10603,wave=29,monster_count_0=0,},
		{monster_id_1=10604,wave=30,monster_id_0=10604,monster_id_2=10604,},
		{monster_id_1=10700,wave=31,monster_id_0=10700,monster_id_2=10700,},
		{monster_id_1=10701,wave=32,monster_id_0=10701,monster_id_2=10701,},
		{monster_id_1=10702,wave=33,monster_id_0=10702,monster_id_2=10702,},
		{monster_id_1=10703,wave=34,monster_id_0=10703,monster_id_2=10703,},
		{monster_id_1=10704,wave=35,monster_id_0=10704,monster_id_2=10704,},
		{monster_id_1=10705,wave=36,monster_id_0=10705,monster_id_2=10705,},
		{monster_id_1=10706,wave=37,monster_id_0=10706,monster_id_2=10706,},
		{monster_id_1=10707,wave=38,monster_id_0=10707,monster_id_2=10707,},
		{monster_count_2=0,monster_id_1=10800,wave=39,monster_count_0=0,},
		{monster_id_1=10801,wave=40,monster_id_0=10801,monster_id_2=10801,},
		{monster_id_1=10802,wave=41,monster_id_0=10802,monster_id_2=10802,},
		{monster_id_1=10803,wave=42,monster_id_0=10803,monster_id_2=10803,},
		{monster_id_1=10804,wave=43,monster_id_0=10804,monster_id_2=10804,},
		{monster_id_1=10805,wave=44,monster_id_0=10805,monster_id_2=10805,},
		{monster_id_1=10806,wave=45,monster_id_0=10806,monster_id_2=10806,},
		{monster_id_1=10807,wave=46,monster_id_0=10807,monster_id_2=10807,},
		{monster_id_1=10900,wave=47,monster_id_0=10900,monster_id_2=10900,},
		{monster_id_1=10901,wave=48,monster_id_0=10901,monster_id_2=10901,},
		{monster_count_2=0,monster_id_1=10902,wave=49,monster_count_0=0,},
		{monster_id_1=10903,wave=50,monster_id_0=10903,monster_id_2=10903,},
		{monster_id_1=10904,wave=51,monster_id_0=10904,monster_id_2=10904,},
		{monster_id_1=10905,wave=52,monster_id_0=10905,monster_id_2=10905,},
		{monster_id_1=10906,wave=53,monster_id_0=10906,monster_id_2=10906,},
		{monster_id_1=10907,wave=54,monster_id_0=10907,monster_id_2=10907,},
		{monster_id_1=11000,wave=55,monster_id_0=11000,monster_id_2=11000,},
		{monster_id_1=11001,wave=56,monster_id_0=11001,monster_id_2=11001,},
		{monster_id_1=11002,wave=57,monster_id_0=11002,monster_id_2=11002,},
		{monster_id_1=11003,wave=58,monster_id_0=11003,monster_id_2=11003,},
		{monster_count_2=0,monster_id_1=11004,wave=59,monster_count_0=0,},
		{monster_id_1=11005,wave=60,monster_id_0=11005,monster_id_2=11005,},
		{monster_id_1=11006,wave=61,monster_id_0=11006,monster_id_2=11006,},
		{monster_id_1=11007,wave=62,monster_id_0=11007,monster_id_2=11007,},
		{monster_id_1=11008,wave=63,monster_id_0=11008,monster_id_2=11008,},
		{monster_id_1=11009,wave=64,monster_id_0=11009,monster_id_2=11009,},
		{monster_id_1=11100,wave=65,monster_id_0=11100,monster_id_2=11100,},
		{monster_id_1=11101,wave=66,monster_id_0=11101,monster_id_2=11101,},
		{monster_id_1=11102,wave=67,monster_id_0=11102,monster_id_2=11102,},
		{wave=68,monster_id_0=11103,monster_id_2=11103,},
		{monster_count_2=0,monster_id_1=11104,wave=69,monster_count_0=0,},},
	["level_scene_cfg"]={
		{fb_name="神魔秘境",level=0,need_level=32,},
		{scene_id=3002,fb_name="神魔之井",need_level=40,},
		{scene_id=3003,fb_name="妖兽巢穴",level=2,},
		{scene_id=3004,fb_name="兽潮来袭",level=3,need_level=60,},
		{scene_id=3005,fb_name="魔神入侵",level=4,need_level=70,},
		{scene_id=3006,fb_name="末日灾变",level=5,need_level=80,},
		{scene_id=3007,fb_name="仙域守护",level=6,need_level=90,},
		{scene_id=3008,level=7,need_level=95,},},
	["team_wave_list_default_table"]={monster_count_1=2,monster_count_2=2,next_wave_s=60,desc="",monster_id_1=11103,wave=1,monster_count_0=2,monster_id_0=0,wave_level="",monster_id_2=0,kill_score=2,},
}
