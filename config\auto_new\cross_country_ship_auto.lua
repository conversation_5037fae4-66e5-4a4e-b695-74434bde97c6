-- K-跨服国家运镖.xls
local item_table={
[1]={item_id=50019,num=5,is_bind=0},
[2]={item_id=50011,num=80,is_bind=0},
[3]={item_id=44182,num=7,is_bind=0},
[4]={item_id=44183,num=5,is_bind=0},
[5]={item_id=48301,num=7,is_bind=0},
[6]={item_id=48302,num=5,is_bind=0},
[7]={item_id=50019,num=4,is_bind=0},
[8]={item_id=50011,num=60,is_bind=0},
[9]={item_id=44182,num=6,is_bind=0},
[10]={item_id=44183,num=4,is_bind=0},
[11]={item_id=48301,num=6,is_bind=0},
[12]={item_id=48302,num=4,is_bind=0},
[13]={item_id=50019,num=3,is_bind=0},
[14]={item_id=50011,num=40,is_bind=0},
[15]={item_id=44182,num=5,is_bind=0},
[16]={item_id=48301,num=5,is_bind=0},
[17]={item_id=50019,num=2,is_bind=0},
[18]={item_id=50011,num=30,is_bind=0},
[19]={item_id=44182,num=4,is_bind=0},
[20]={item_id=44183,num=3,is_bind=0},
[21]={item_id=48301,num=4,is_bind=0},
[22]={item_id=48302,num=3,is_bind=0},
[23]={item_id=50011,num=20,is_bind=0},
[24]={item_id=44182,num=3,is_bind=0},
[25]={item_id=44183,num=2,is_bind=0},
[26]={item_id=48301,num=3,is_bind=0},
[27]={item_id=48302,num=2,is_bind=0},
[28]={item_id=50011,num=56,is_bind=0},
[29]={item_id=50011,num=42,is_bind=0},
[30]={item_id=50011,num=28,is_bind=0},
[31]={item_id=50011,num=21,is_bind=0},
[32]={item_id=50011,num=48,is_bind=0},
[33]={item_id=50011,num=36,is_bind=0},
[34]={item_id=50011,num=24,is_bind=0},
[35]={item_id=50011,num=18,is_bind=0},
[36]={item_id=44182,num=2,is_bind=0},
[37]={item_id=48301,num=2,is_bind=0},
[38]={item_id=50019,num=1,is_bind=0},
[39]={item_id=50011,num=12,is_bind=0},
[40]={item_id=44183,num=1,is_bind=0},
[41]={item_id=48302,num=1,is_bind=0},
[42]={item_id=50011,num=27,is_bind=0},
[43]={item_id=50011,num=9,is_bind=0},
[44]={item_id=44182,num=1,is_bind=0},
[45]={item_id=48301,num=1,is_bind=0},
[46]={item_id=36420,num=180,is_bind=0},
[47]={item_id=26602,num=6,is_bind=0},
[48]={item_id=50011,num=2,is_bind=0},
[49]={item_id=27741,num=11,is_bind=0},
[50]={item_id=36420,num=240,is_bind=0},
[51]={item_id=26602,num=8,is_bind=0},
[52]={item_id=50011,num=3,is_bind=0},
[53]={item_id=27741,num=14,is_bind=0},
[54]={item_id=36420,num=300,is_bind=0},
[55]={item_id=26602,num=10,is_bind=0},
[56]={item_id=50011,num=4,is_bind=0},
[57]={item_id=27741,num=17,is_bind=0},
[58]={item_id=50011,num=25,is_bind=0},
[59]={item_id=39142,num=900,is_bind=0},
[60]={item_id=39136,num=1300,is_bind=0},
[61]={item_id=39142,num=800,is_bind=0},
[62]={item_id=39136,num=1200,is_bind=0},
[63]={item_id=39142,num=700,is_bind=0},
[64]={item_id=39136,num=1000,is_bind=0},
[65]={item_id=50011,num=16,is_bind=0},
[66]={item_id=39136,num=900,is_bind=0},
[67]={item_id=50011,num=14,is_bind=0},
[68]={item_id=39142,num=600,is_bind=0},
[69]={item_id=39136,num=800,is_bind=0},
[70]={item_id=39142,num=500,is_bind=0},
[71]={item_id=39136,num=700,is_bind=0},
[72]={item_id=50011,num=10,is_bind=0},
[73]={item_id=39136,num=500,is_bind=0},
[74]={item_id=26601,num=6,is_bind=0},
[75]={item_id=29476,num=4,is_bind=0},
[76]={item_id=26415,num=10,is_bind=0},
[77]={item_id=26601,num=8,is_bind=0},
[78]={item_id=29476,num=6,is_bind=0},
[79]={item_id=26415,num=12,is_bind=0},
[80]={item_id=26601,num=10,is_bind=0},
[81]={item_id=50011,num=5,is_bind=0},
[82]={item_id=29476,num=8,is_bind=0},
[83]={item_id=26415,num=14,is_bind=0},
[84]={item_id=26601,num=1,is_bind=0},
[85]={item_id=39142,num=1,is_bind=0},
[86]={item_id=39136,num=1,is_bind=0},
[87]={item_id=50011,num=1,is_bind=0},
[88]={item_id=36420,num=1,is_bind=0},
[89]={item_id=26415,num=1,is_bind=0},
[90]={item_id=36416,num=1,is_bind=0},
[91]={item_id=36420,num=150,is_bind=0},
[92]={item_id=26602,num=4,is_bind=0},
[93]={item_id=27741,num=8,is_bind=0},
[94]={item_id=50011,num=35,is_bind=0},
[95]={item_id=39142,num=1000,is_bind=0},
[96]={item_id=39136,num=1500,is_bind=0},
[97]={item_id=26601,num=4,is_bind=0},
[98]={item_id=29476,num=2,is_bind=0},
[99]={item_id=26415,num=8,is_bind=0},
}

return {
other={
{}
},

other_meta_table_map={
},
ship={
{},
{seq=1,ship_id=55013,},
{seq=2,ship_id=55011,},
{seq=3,ship_id=55012,}
},

ship_meta_table_map={
},
flag={
{},
{seq=1,gather_id=2014,refresh_pos="233,174",}
},

flag_meta_table_map={
},
gather_buff={

},

gather_buff_meta_table_map={
},
dart_score_rank_reward={
{min_hp_scale=5000,max_hp_scale=10000,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{min_hp_scale=5000,max_hp_scale=10000,reward_item={[0]=item_table[7],[1]=item_table[8],[2]=item_table[9],[3]=item_table[10],[4]=item_table[11],[5]=item_table[12]},},
{min_hp_scale=5000,max_hp_scale=10000,reward_item={[0]=item_table[13],[1]=item_table[14],[2]=item_table[15],[3]=item_table[10],[4]=item_table[16],[5]=item_table[12]},},
{min_hp_scale=5000,max_hp_scale=10000,reward_item={[0]=item_table[17],[1]=item_table[18],[2]=item_table[19],[3]=item_table[20],[4]=item_table[21],[5]=item_table[22]},},
{min_hp_scale=5000,max_hp_scale=10000,reward_item={[0]=item_table[17],[1]=item_table[23],[2]=item_table[24],[3]=item_table[25],[4]=item_table[26],[5]=item_table[27]},},
{max_hp_scale=4999,reward_item={[0]=item_table[7],[1]=item_table[28],[2]=item_table[15],[3]=item_table[10],[4]=item_table[16],[5]=item_table[12]},},
{min_rank=2,max_rank=3,max_hp_scale=4999,reward_item={[0]=item_table[13],[1]=item_table[29],[2]=item_table[19],[3]=item_table[20],[4]=item_table[21],[5]=item_table[22]},},
{min_rank=4,max_rank=5,max_hp_scale=4999,reward_item={[0]=item_table[17],[1]=item_table[30],[2]=item_table[19],[3]=item_table[20],[4]=item_table[21],[5]=item_table[22]},},
{min_rank=6,max_rank=7,max_hp_scale=4999,reward_item={[0]=item_table[17],[1]=item_table[31],[2]=item_table[24],[3]=item_table[25],[4]=item_table[26],[5]=item_table[27]},},
{min_rank=8,max_rank=10,max_hp_scale=4999,},
{min_progress=50,reward_item={[0]=item_table[7],[1]=item_table[32],[2]=item_table[19],[3]=item_table[20],[4]=item_table[21],[5]=item_table[22]},},
{min_rank=2,max_rank=3,min_progress=50,reward_item={[0]=item_table[13],[1]=item_table[33],[2]=item_table[19],[3]=item_table[25],[4]=item_table[21],[5]=item_table[27]},},
{min_rank=4,max_rank=5,min_progress=50,reward_item={[0]=item_table[17],[1]=item_table[34],[2]=item_table[24],[3]=item_table[25],[4]=item_table[26],[5]=item_table[27]},},
{min_rank=6,max_rank=7,min_progress=50,reward_item={[0]=item_table[17],[1]=item_table[35],[2]=item_table[36],[3]=item_table[25],[4]=item_table[37],[5]=item_table[27]},},
{min_rank=8,max_rank=10,min_progress=50,reward_item={[0]=item_table[38],[1]=item_table[39],[2]=item_table[36],[3]=item_table[40],[4]=item_table[37],[5]=item_table[41]},},
{min_progress=0,max_progress=50,reward_item={[0]=item_table[13],[1]=item_table[33],[2]=item_table[24],[3]=item_table[25],[4]=item_table[26],[5]=item_table[27]},},
{min_progress=0,max_progress=50,reward_item={[0]=item_table[17],[1]=item_table[42],[2]=item_table[24],[3]=item_table[25],[4]=item_table[26],[5]=item_table[27]},},
{min_progress=0,max_progress=50,reward_item={[0]=item_table[17],[1]=item_table[35],[2]=item_table[36],[3]=item_table[25],[4]=item_table[37],[5]=item_table[27]},},
{min_rank=6,max_rank=7,min_progress=0,max_progress=50,},
{min_progress=0,max_progress=50,reward_item={[0]=item_table[38],[1]=item_table[43],[2]=item_table[44],[3]=item_table[40],[4]=item_table[45],[5]=item_table[41]},}
},

dart_score_rank_reward_meta_table_map={
[4]=9,	-- depth:1
[3]=8,	-- depth:1
[2]=7,	-- depth:1
[17]=12,	-- depth:1
[18]=13,	-- depth:1
[5]=10,	-- depth:1
[20]=15,	-- depth:1
},
dart_score_reward={
{},
{min_score=500,max_score=799,reward_item={[0]=item_table[46],[1]=item_table[47],[2]=item_table[48],[3]=item_table[27],[4]=item_table[25],[5]=item_table[49]},},
{min_score=800,max_score=999,reward_item={[0]=item_table[50],[1]=item_table[51],[2]=item_table[52],[3]=item_table[22],[4]=item_table[20],[5]=item_table[53]},},
{min_score=1000,max_score=9999,reward_item={[0]=item_table[54],[1]=item_table[55],[2]=item_table[56],[3]=item_table[12],[4]=item_table[10],[5]=item_table[57]},}
},

dart_score_reward_meta_table_map={
},
rob_dart_score_rank_reward={
{},
{max_hp_scale=4999,reward_item={[0]=item_table[17],[1]=item_table[58],[2]=item_table[59],[3]=item_table[60]},},
{min_hp_scale=5000,max_hp_scale=10000,reward_item={[0]=item_table[38],[1]=item_table[23],[2]=item_table[61],[3]=item_table[62]},},
{min_rank=2,max_rank=3,reward_item={[0]=item_table[17],[1]=item_table[18],[2]=item_table[59],[3]=item_table[60]},},
{max_hp_scale=4999,reward_item={[0]=item_table[17],[1]=item_table[31],[2]=item_table[61],[3]=item_table[62]},},
{min_hp_scale=5000,max_hp_scale=10000,reward_item={[0]=item_table[38],[1]=item_table[35],[2]=item_table[63],[3]=item_table[64]},},
{min_rank=4,max_rank=5,reward_item={[0]=item_table[17],[1]=item_table[23],[2]=item_table[61],[3]=item_table[64]},},
{max_hp_scale=4999,reward_item={[0]=item_table[17],[1]=item_table[65],[2]=item_table[63],[3]=item_table[66]},},
{min_hp_scale=5000,max_hp_scale=10000,reward_item={[0]=item_table[38],[1]=item_table[67],[2]=item_table[68],[3]=item_table[69]},},
{min_rank=6,max_rank=7,reward_item={[0]=item_table[17],[1]=item_table[35],[2]=item_table[63],[3]=item_table[66]},},
{max_hp_scale=4999,reward_item={[0]=item_table[17],[1]=item_table[67],[2]=item_table[68],[3]=item_table[69]},},
{min_hp_scale=5000,max_hp_scale=10000,reward_item={[0]=item_table[38],[1]=item_table[39],[2]=item_table[70],[3]=item_table[71]},},
{min_rank=8,max_rank=10,reward_item={[0]=item_table[17],[1]=item_table[65],[2]=item_table[68],[3]=item_table[69]},},
{max_hp_scale=4999,reward_item={[0]=item_table[17],[1]=item_table[39],[2]=item_table[70],[3]=item_table[71]},},
{min_hp_scale=5000,max_hp_scale=10000,reward_item={[0]=item_table[38],[1]=item_table[72],[2]=item_table[70],[3]=item_table[73]},}
},

rob_dart_score_rank_reward_meta_table_map={
[5]=4,	-- depth:1
[8]=7,	-- depth:1
[11]=10,	-- depth:1
[14]=13,	-- depth:1
[6]=4,	-- depth:1
[9]=7,	-- depth:1
[12]=10,	-- depth:1
[15]=13,	-- depth:1
},
rob_dart_score_reward={
{},
{min_score=500,max_score=799,reward_item={[0]=item_table[74],[1]=item_table[48],[2]=item_table[75],[3]=item_table[76],[4]=item_table[49]},},
{min_score=800,max_score=999,reward_item={[0]=item_table[77],[1]=item_table[56],[2]=item_table[78],[3]=item_table[79],[4]=item_table[53]},},
{min_score=1000,max_score=9999,reward_item={[0]=item_table[80],[1]=item_table[81],[2]=item_table[82],[3]=item_table[83],[4]=item_table[57]},}
},

rob_dart_score_reward_meta_table_map={
},
ship_rule={
{},
{rule_index=2,child_title="保护",content="使本仙境剩余血量尽可能高的到达终点，有助于神灵羽化登仙。",},
{rule_index=3,child_title="攻击",content="尽可能对其他仙境神灵造成更多伤害，获取更多攻击积分。",},
{rule_index=4,child_title="NPC",content="仙友进入场景之后，可与NPC对话开启护送。只有本服战力前5的仙友才有资格召唤神灵。",},
{rule_index=5,child_title="系统发车",content="活动开启15分钟之后，会由系统选择神灵发出，并给相应仙境在线仙友发出提示。",},
{rule_perent_index=2,title="奖励规则",child_title="护送排名奖励",content="护送神灵，会获得护送积分，本仙境神灵达到终点之后，根据仙境神灵剩余血量，积分前10名的仙友会根据排名获得护送排名奖励。",},
{rule_index=2,child_title="攻击排名奖励",content="攻击其他仙境神灵，会获得攻击积分，其他仙境神灵到达终点之后，根据其他仙境神灵损失血量，对其他仙境攻击积分前10名的仙友会根据排名获得攻击排名奖励。",},
{rule_index=3,child_title="护送积分奖励",content="仙友护送神灵，会获得保护积分，保护积分达到标准的仙友可根据自身积分获得相应档位的保护积分奖励。",},
{rule_index=4,child_title="攻击积分奖励",content="攻击其他仙境神灵，会获得攻击积分，攻击积分到达标准的仙友可根据自身积分获得相应档位的表表积分奖励。",}
},

ship_rule_meta_table_map={
[7]=6,	-- depth:1
[8]=6,	-- depth:1
[9]=6,	-- depth:1
},
continue_kill={
{},
{kill_count=3,kill_desc="%s已一骑当千连续击败%s人！",},
{kill_count=4,kill_desc="%s正在无双杀戮连续击败%s人！",},
{kill_count=5,kill_desc="%s风华绝代连续击败%s人！",}
},

continue_kill_meta_table_map={
},
follow_offset={
[0]={index=0,offset_y=8,},
[1]={index=1,offset_y=8,},
[2]={index=2,offset_y=6,},
[3]={index=3,offset_x=-5,},
[4]={index=4,offset_y=4,},
[5]={index=5,offset_x=-5,},
[6]={index=6,offset_y=2,},
[7]={index=7,offset_x=-5,},
[8]={index=8,offset_y=0,},
[9]={index=9,offset_x=-5,},
[10]={index=10,offset_y=-2,},
[11]={index=11,offset_x=-5,},
[12]={index=12,offset_y=-4,},
[13]={index=13,offset_x=-5,},
[14]={index=14,offset_y=-6,},
[15]={index=15,offset_x=-5,},
[16]={index=16,},
[17]={index=17,offset_x=-5,},
[18]={index=18,offset_x=2,},
[19]={index=19,offset_x=-2,},
[20]={index=20,offset_x=4,},
[21]={index=21,offset_y=-10,},
[22]={index=22,offset_y=-10,},
[23]={index=23,offset_y=-10,},
[24]={index=24,offset_x=4,},
[25]={index=25,offset_x=-4,offset_y=-12,},
[26]={index=26,offset_x=2,},
[27]={index=27,offset_x=-2,},
[28]={index=28,offset_y=-14,},
[29]={index=29,offset_y=-14,},
[30]={index=30,offset_y=-14,},
[31]={index=31,offset_y=-14,}
},

follow_offset_meta_table_map={
[24]=25,	-- depth:1
[27]=25,	-- depth:1
[23]=19,	-- depth:1
[22]=18,	-- depth:1
[28]=24,	-- depth:2
[29]=25,	-- depth:1
[26]=25,	-- depth:1
[21]=25,	-- depth:1
[15]=14,	-- depth:1
[30]=18,	-- depth:1
[13]=12,	-- depth:1
[11]=10,	-- depth:1
[9]=8,	-- depth:1
[7]=6,	-- depth:1
[5]=4,	-- depth:1
[3]=2,	-- depth:1
[1]=17,	-- depth:1
[20]=21,	-- depth:2
[31]=19,	-- depth:1
},
other_default_table={open_world_level=0,scene_id=1500,bus_interval=180,occupy_flag_add_score=100,occupy_flag_add_rob_score=0,kill_role_rob_score_ratio=1000,kill_role_rob_need_score=1000,rob_role_dart_score_need_score=1000,rob_role_rob_dart_score_need_score=1000,bus_time="900|990|1080|1170",timed_add_dart_score_range=30,timed_add_dart_score_time=10,timed_add_dart_score=50,gather_refresh_interval=60,gather_refresh_num=3,gather_refresh_pos="76,29|133,36|213,35|236,97|172,99|231,172|166,169|85,167|76,100|142,176|95,120|71,21|139,31|217,28|243,103",preliminaries_rule="每个仙境战力<color=#6FBB6F>前5</color>的玩家可召唤诸神。活动开启<color=#6FBB6F>15</color>分钟后，未召唤诸神的仙境将由系统自动召唤。",konckout_match_rule="仙境护送玩法分为<color=#6FBB6F>护送</color>和<color=#6FBB6F>攻击</color>两部分。玩家在活动中，不仅要保证本仙境神灵以<color=#6FBB6F>尽可能高</color>的血量<color=#6FBB6F>到达终点</color>完成飞升，还要对<color=#6FBB6F>其他仙境的神灵</color>造成<color=#6FBB6F>足够高</color>的伤害，以获取<color=#6FBB6F>攻击奖励</color>。在活动中，需要各仙境玩家团结协作，<color=#6FBB6F>活动细节</color>介绍可阅读<color=#6FBB6F>规则</color>了解。",display_reward={[0]=item_table[38],[1]=item_table[84],[2]=item_table[85],[3]=item_table[86],[4]=item_table[45],[5]=item_table[41],[6]=item_table[44],[7]=item_table[40],[8]=item_table[87],[9]=item_table[88],[10]=item_table[87],[11]=item_table[89],[12]=item_table[90]},auto_start_tips="<color=#66c7ff>我方仙境发起了</color><color=#99ffbb>试炼挑战</color><color=#66c7ff>，请各位仙友前往护送。</color>",bus_npc_id=1068,cap_rank_count=10,guard_range=30,},

ship_default_table={seq=0,ship_id=55010,ship_pos="172,99",ship_way="159,63|136,41|175,43|215,36|231,64|235,98|255,141|231,174|164,169|83,166|80,107|78,61|78,31",},

flag_default_table={seq=0,gather_id=2012,gather_time=10,refresh_pos="171,97",range=0,},

gather_buff_default_table={},

dart_score_rank_reward_default_table={min_rank=1,max_rank=1,min_hp_scale=0,max_hp_scale=0,min_progress=100,max_progress=100,reward_item={[0]=item_table[38],[1]=item_table[67],[2]=item_table[36],[3]=item_table[40],[4]=item_table[37],[5]=item_table[41]},},

dart_score_reward_default_table={min_score=300,max_score=499,min_hp_scale=0,max_hp_scale=10000,reward_item={[0]=item_table[91],[1]=item_table[92],[2]=item_table[87],[3]=item_table[41],[4]=item_table[40],[5]=item_table[93]},},

rob_dart_score_rank_reward_default_table={min_rank=1,max_rank=1,min_hp_scale=0,max_hp_scale=0,reward_item={[0]=item_table[17],[1]=item_table[94],[2]=item_table[95],[3]=item_table[96]},},

rob_dart_score_reward_default_table={min_score=300,max_score=499,min_hp_scale=0,max_hp_scale=10000,reward_item={[0]=item_table[97],[1]=item_table[87],[2]=item_table[98],[3]=item_table[99],[4]=item_table[93]},},

ship_rule_default_table={rule_perent_index=1,rule_index=1,title="功能玩法",child_title="玩法概述",content="各位仙友可在活动中保护仙境神灵，也可在活动中攻击其他仙境的神灵。保护与攻击可获得不同奖励。",img_id=0,},

continue_kill_default_table={type=1,kill_count=2,kill_desc="%s已无人能挡连续击败%s人！",},

follow_offset_default_table={index=0,offset_x=5,offset_y=-8,}

}

