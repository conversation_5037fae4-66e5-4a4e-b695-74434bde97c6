-- Y-运营活动-幸运转盘.xls
local item_table={
[1]={item_id=37446,num=1,is_bind=1},
[2]={item_id=37243,num=1,is_bind=1},
[3]={item_id=37244,num=1,is_bind=1},
[4]={item_id=39152,num=1,is_bind=1},
[5]={item_id=50301,num=1,is_bind=1},
[6]={item_id=48088,num=1,is_bind=1},
[7]={item_id=56319,num=1,is_bind=1},
[8]={item_id=26193,num=1,is_bind=1},
[9]={item_id=26191,num=1,is_bind=1},
[10]={item_id=27909,num=1,is_bind=1},
[11]={item_id=56401,num=1,is_bind=1},
[12]={item_id=26501,num=1,is_bind=1},
[13]={item_id=26516,num=1,is_bind=1},
[14]={item_id=50300,num=1,is_bind=1},
[15]={item_id=26354,num=1,is_bind=1},
[16]={item_id=26351,num=1,is_bind=1},
[17]={item_id=39150,num=1,is_bind=1},
[18]={item_id=39151,num=1,is_bind=1},
[19]={item_id=50011,num=3,is_bind=1},
[20]={item_id=56321,num=3,is_bind=1},
[21]={item_id=26371,num=1,is_bind=1},
[22]={item_id=56402,num=1,is_bind=1},
[23]={item_id=26518,num=1,is_bind=1},
[24]={item_id=26502,num=1,is_bind=1},
[25]={item_id=26415,num=4,is_bind=1},
[26]={item_id=37253,num=1,is_bind=1},
[27]={item_id=37467,num=1,is_bind=1},
[28]={item_id=37248,num=1,is_bind=1},
[29]={item_id=27908,num=1,is_bind=1},
[30]={item_id=37127,num=1,is_bind=1},
[31]={item_id=26517,num=1,is_bind=1},
[32]={item_id=26519,num=1,is_bind=1},
[33]={item_id=26503,num=1,is_bind=1},
[34]={item_id=26067,num=1,is_bind=1},
[35]={item_id=26070,num=1,is_bind=1},
[36]={item_id=26369,num=1,is_bind=1},
[37]={item_id=26355,num=1,is_bind=1},
[38]={item_id=26356,num=1,is_bind=1},
[39]={item_id=26079,num=1,is_bind=1},
[40]={item_id=26080,num=1,is_bind=1},
[41]={item_id=56322,num=1,is_bind=1},
[42]={item_id=56320,num=1,is_bind=1},
[43]={item_id=26073,num=1,is_bind=1},
[44]={item_id=26076,num=1,is_bind=1},
[45]={item_id=26068,num=1,is_bind=1},
[46]={item_id=26069,num=1,is_bind=1},
[47]={item_id=26074,num=1,is_bind=1},
[48]={item_id=26075,num=1,is_bind=1},
[49]={item_id=26176,num=5,is_bind=1},
[50]={item_id=44184,num=1,is_bind=1},
[51]={item_id=26504,num=1,is_bind=1},
[52]={item_id=39105,num=20,is_bind=1},
[53]={item_id=39105,num=30,is_bind=1},
[54]={item_id=26520,num=1,is_bind=1},
[55]={item_id=26505,num=1,is_bind=1},
[56]={item_id=39105,num=50,is_bind=1},
[57]={item_id=48441,num=1,is_bind=1},
[58]={item_id=26521,num=1,is_bind=1},
[59]={item_id=45017,num=1,is_bind=1},
[60]={item_id=26506,num=1,is_bind=1},
[61]={item_id=44185,num=1,is_bind=1},
[62]={item_id=26176,num=10,is_bind=1},
}

return {
type={
{},
{activity_type=2305,seq=1,}
},

type_meta_table_map={
},
open_day={
{},
{start_day=18,end_day=9999,grade=2,},
{activity_type=2305,},
{activity_type=2305,}
},

open_day_meta_table_map={
[4]=2,	-- depth:1
},
mode={
{},
{mode=2,times=10,cost_item_num=10,},
{mode=3,times=50,cost_item_num=50,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,}
},

mode_meta_table_map={
[5]=2,	-- depth:1
[6]=3,	-- depth:1
},
reward_pool={
{item=item_table[1],is_rare=1,},
{seq=1,item=item_table[2],},
{seq=2,item=item_table[3],},
{seq=3,is_rare=1,},
{seq=4,item=item_table[4],},
{seq=5,item=item_table[5],},
{seq=6,item=item_table[6],},
{seq=7,item=item_table[7],},
{seq=8,item=item_table[8],},
{seq=9,item=item_table[9],},
{seq=10,item=item_table[10],},
{seq=11,item=item_table[11],},
{seq=12,item=item_table[12],},
{seq=13,item=item_table[13],},
{seq=14,item=item_table[14],},
{seq=15,item=item_table[15],},
{seq=16,item=item_table[16],},
{seq=17,item=item_table[17],},
{seq=18,item=item_table[18],},
{seq=19,item=item_table[19],},
{seq=20,item=item_table[20],},
{seq=21,item=item_table[21],},
{seq=22,item=item_table[22],},
{seq=23,item=item_table[23],},
{seq=24,item=item_table[24],},
{seq=25,item=item_table[25],},
{activity_day=2,item=item_table[26],},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=3,item=item_table[27],},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=4,item=item_table[28],},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{activity_day=2,},
{grade=2,},
{activity_day=3,},
{activity_day=3,},
{grade=2,},
{activity_day=3,},
{grade=2,},
{grade=2,},
{activity_day=3,},
{activity_day=3,},
{grade=2,},
{activity_day=3,},
{activity_day=3,item=item_table[29],},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{grade=2,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_type=2305,item=item_table[30],},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,item=item_table[24],},
{activity_type=2305,item=item_table[31],},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,item=item_table[32],},
{activity_type=2305,item=item_table[33],},
{activity_type=2305,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,item=item_table[29],},
{activity_day=2,},
{activity_day=2,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_day=2,},
{activity_day=2,},
{activity_type=2305,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_type=2305,},
{activity_day=3,},
{activity_type=2305,},
{activity_type=2305,},
{activity_day=3,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_type=2305,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{grade=2,},
{grade=2,item=item_table[34],},
{grade=2,item=item_table[35],},
{activity_type=2305,},
{activity_type=2305,item=item_table[36],},
{grade=2,},
{activity_type=2305,item=item_table[37],},
{grade=2,item=item_table[38],},
{activity_type=2305,},
{grade=2,},
{grade=2,item=item_table[39],},
{grade=2,item=item_table[40],},
{grade=2,item=item_table[41],},
{grade=2,item=item_table[42],},
{grade=2,},
{grade=2,},
{grade=2,},
{activity_type=2305,},
{grade=2,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{grade=2,},
{grade=2,},
{activity_type=2305,},
{activity_day=2,},
{activity_day=2,item=item_table[43],},
{activity_day=2,item=item_table[44],},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_day=2,},
{activity_day=2,},
{activity_type=2305,},
{activity_day=3,},
{activity_day=3,item=item_table[45],},
{activity_day=3,item=item_table[46],},
{activity_type=2305,},
{activity_day=3,},
{activity_type=2305,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_type=2305,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_type=2305,},
{activity_type=2305,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=4,},
{activity_day=4,item=item_table[47],},
{activity_day=4,item=item_table[48],},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_type=2305,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,}
},

reward_pool_meta_table_map={
[104]=26,	-- depth:1
[105]=1,	-- depth:1
[215]=7,	-- depth:1
[110]=6,	-- depth:1
[111]=7,	-- depth:1
[116]=12,	-- depth:1
[115]=11,	-- depth:1
[103]=25,	-- depth:1
[117]=13,	-- depth:1
[118]=14,	-- depth:1
[119]=15,	-- depth:1
[114]=10,	-- depth:1
[102]=24,	-- depth:1
[98]=20,	-- depth:1
[100]=22,	-- depth:1
[99]=21,	-- depth:1
[97]=19,	-- depth:1
[96]=18,	-- depth:1
[95]=17,	-- depth:1
[94]=16,	-- depth:1
[93]=15,	-- depth:1
[88]=10,	-- depth:1
[120]=16,	-- depth:1
[89]=11,	-- depth:1
[90]=12,	-- depth:1
[91]=13,	-- depth:1
[92]=14,	-- depth:1
[101]=23,	-- depth:1
[121]=17,	-- depth:1
[234]=26,	-- depth:1
[123]=19,	-- depth:1
[214]=6,	-- depth:1
[218]=10,	-- depth:1
[212]=4,	-- depth:1
[219]=11,	-- depth:1
[220]=12,	-- depth:1
[209]=1,	-- depth:1
[221]=13,	-- depth:1
[222]=14,	-- depth:1
[223]=15,	-- depth:1
[224]=16,	-- depth:1
[225]=17,	-- depth:1
[226]=18,	-- depth:1
[227]=19,	-- depth:1
[228]=20,	-- depth:1
[229]=21,	-- depth:1
[230]=22,	-- depth:1
[231]=23,	-- depth:1
[232]=24,	-- depth:1
[233]=25,	-- depth:1
[85]=7,	-- depth:1
[130]=26,	-- depth:1
[129]=25,	-- depth:1
[128]=24,	-- depth:1
[127]=23,	-- depth:1
[126]=22,	-- depth:1
[125]=21,	-- depth:1
[124]=20,	-- depth:1
[122]=18,	-- depth:1
[84]=6,	-- depth:1
[108]=4,	-- depth:1
[48]=100,	-- depth:2
[32]=84,	-- depth:2
[82]=4,	-- depth:1
[33]=85,	-- depth:2
[56]=82,	-- depth:2
[53]=1,	-- depth:1
[52]=104,	-- depth:2
[51]=103,	-- depth:2
[50]=102,	-- depth:2
[49]=101,	-- depth:2
[47]=99,	-- depth:2
[46]=98,	-- depth:2
[45]=97,	-- depth:2
[44]=96,	-- depth:2
[43]=95,	-- depth:2
[42]=94,	-- depth:2
[36]=88,	-- depth:2
[37]=89,	-- depth:2
[38]=90,	-- depth:2
[39]=91,	-- depth:2
[40]=92,	-- depth:2
[41]=93,	-- depth:2
[59]=33,	-- depth:3
[30]=56,	-- depth:3
[58]=32,	-- depth:3
[74]=48,	-- depth:3
[3]=4,	-- depth:1
[5]=4,	-- depth:1
[8]=4,	-- depth:1
[79]=1,	-- depth:1
[78]=52,	-- depth:3
[77]=51,	-- depth:3
[76]=50,	-- depth:3
[75]=49,	-- depth:3
[27]=1,	-- depth:1
[73]=47,	-- depth:3
[72]=46,	-- depth:3
[71]=45,	-- depth:3
[2]=4,	-- depth:1
[70]=44,	-- depth:3
[68]=42,	-- depth:3
[67]=41,	-- depth:3
[66]=40,	-- depth:3
[65]=39,	-- depth:3
[64]=38,	-- depth:3
[63]=37,	-- depth:3
[62]=36,	-- depth:3
[9]=4,	-- depth:1
[69]=43,	-- depth:3
[256]=48,	-- depth:3
[255]=47,	-- depth:3
[254]=46,	-- depth:3
[253]=45,	-- depth:3
[252]=44,	-- depth:3
[257]=49,	-- depth:3
[270]=62,	-- depth:4
[259]=233,	-- depth:2
[260]=52,	-- depth:3
[261]=209,	-- depth:2
[264]=56,	-- depth:3
[266]=58,	-- depth:4
[267]=59,	-- depth:4
[258]=232,	-- depth:2
[251]=43,	-- depth:3
[326]=222,	-- depth:2
[249]=41,	-- depth:3
[338]=130,	-- depth:2
[337]=233,	-- depth:2
[336]=232,	-- depth:2
[335]=127,	-- depth:2
[334]=126,	-- depth:2
[271]=63,	-- depth:4
[235]=261,	-- depth:3
[238]=264,	-- depth:4
[240]=266,	-- depth:5
[241]=267,	-- depth:5
[244]=270,	-- depth:5
[245]=271,	-- depth:5
[246]=220,	-- depth:2
[247]=221,	-- depth:2
[248]=222,	-- depth:2
[250]=42,	-- depth:3
[272]=64,	-- depth:4
[293]=241,	-- depth:6
[274]=248,	-- depth:3
[307]=255,	-- depth:4
[308]=256,	-- depth:4
[309]=257,	-- depth:4
[310]=258,	-- depth:3
[311]=259,	-- depth:3
[312]=260,	-- depth:4
[313]=209,	-- depth:2
[333]=125,	-- depth:2
[332]=124,	-- depth:2
[331]=227,	-- depth:2
[316]=108,	-- depth:2
[318]=214,	-- depth:2
[330]=122,	-- depth:2
[319]=111,	-- depth:2
[329]=225,	-- depth:2
[322]=218,	-- depth:2
[323]=219,	-- depth:2
[324]=220,	-- depth:2
[325]=221,	-- depth:2
[328]=224,	-- depth:2
[327]=223,	-- depth:2
[306]=254,	-- depth:4
[305]=253,	-- depth:4
[304]=252,	-- depth:4
[303]=251,	-- depth:4
[275]=249,	-- depth:4
[276]=250,	-- depth:4
[277]=303,	-- depth:5
[278]=304,	-- depth:5
[279]=305,	-- depth:5
[280]=306,	-- depth:5
[281]=307,	-- depth:5
[282]=308,	-- depth:5
[283]=309,	-- depth:5
[284]=310,	-- depth:4
[273]=247,	-- depth:3
[285]=311,	-- depth:4
[287]=235,	-- depth:4
[290]=238,	-- depth:5
[292]=240,	-- depth:6
[296]=244,	-- depth:6
[297]=245,	-- depth:6
[298]=272,	-- depth:5
[299]=273,	-- depth:4
[300]=274,	-- depth:4
[301]=275,	-- depth:5
[302]=276,	-- depth:5
[286]=312,	-- depth:5
[217]=9,	-- depth:2
[208]=130,	-- depth:2
[213]=5,	-- depth:2
[141]=37,	-- depth:3
[142]=38,	-- depth:3
[143]=39,	-- depth:3
[144]=40,	-- depth:3
[145]=41,	-- depth:3
[146]=42,	-- depth:3
[147]=43,	-- depth:3
[148]=44,	-- depth:3
[149]=45,	-- depth:3
[150]=46,	-- depth:3
[151]=47,	-- depth:3
[152]=48,	-- depth:3
[153]=49,	-- depth:3
[155]=51,	-- depth:3
[156]=208,	-- depth:3
[157]=53,	-- depth:2
[160]=56,	-- depth:3
[162]=58,	-- depth:4
[163]=59,	-- depth:4
[166]=62,	-- depth:4
[167]=141,	-- depth:4
[140]=166,	-- depth:5
[137]=163,	-- depth:5
[136]=162,	-- depth:5
[134]=160,	-- depth:4
[28]=2,	-- depth:2
[29]=3,	-- depth:2
[31]=5,	-- depth:2
[34]=8,	-- depth:2
[35]=9,	-- depth:2
[54]=28,	-- depth:3
[55]=29,	-- depth:3
[57]=31,	-- depth:3
[60]=34,	-- depth:3
[61]=35,	-- depth:3
[168]=116,	-- depth:2
[80]=54,	-- depth:4
[83]=57,	-- depth:4
[86]=60,	-- depth:4
[87]=61,	-- depth:4
[106]=2,	-- depth:2
[107]=3,	-- depth:2
[109]=5,	-- depth:2
[112]=8,	-- depth:2
[216]=8,	-- depth:2
[113]=9,	-- depth:2
[131]=27,	-- depth:2
[81]=55,	-- depth:4
[169]=143,	-- depth:4
[154]=50,	-- depth:3
[177]=151,	-- depth:4
[203]=177,	-- depth:5
[179]=153,	-- depth:4
[204]=152,	-- depth:4
[180]=154,	-- depth:4
[181]=155,	-- depth:4
[194]=142,	-- depth:4
[202]=150,	-- depth:4
[211]=3,	-- depth:2
[183]=79,	-- depth:2
[210]=2,	-- depth:2
[205]=179,	-- depth:5
[186]=134,	-- depth:5
[207]=181,	-- depth:5
[188]=136,	-- depth:6
[182]=156,	-- depth:4
[178]=204,	-- depth:5
[206]=180,	-- depth:5
[201]=149,	-- depth:4
[170]=144,	-- depth:4
[171]=145,	-- depth:4
[193]=167,	-- depth:5
[172]=146,	-- depth:4
[195]=169,	-- depth:5
[196]=170,	-- depth:5
[197]=171,	-- depth:5
[192]=140,	-- depth:6
[198]=172,	-- depth:5
[173]=147,	-- depth:4
[199]=173,	-- depth:5
[200]=148,	-- depth:4
[174]=200,	-- depth:5
[175]=201,	-- depth:5
[176]=202,	-- depth:5
[189]=137,	-- depth:6
[397]=319,	-- depth:3
[374]=166,	-- depth:5
[371]=397,	-- depth:4
[370]=162,	-- depth:5
[368]=160,	-- depth:4
[365]=313,	-- depth:3
[364]=156,	-- depth:4
[363]=337,	-- depth:3
[362]=336,	-- depth:3
[361]=153,	-- depth:4
[360]=152,	-- depth:4
[359]=151,	-- depth:4
[358]=150,	-- depth:4
[357]=149,	-- depth:4
[414]=362,	-- depth:4
[375]=323,	-- depth:3
[380]=172,	-- depth:5
[413]=361,	-- depth:5
[401]=375,	-- depth:4
[396]=370,	-- depth:6
[394]=368,	-- depth:5
[402]=324,	-- depth:3
[403]=325,	-- depth:3
[391]=365,	-- depth:4
[390]=364,	-- depth:5
[389]=363,	-- depth:4
[356]=148,	-- depth:4
[388]=414,	-- depth:5
[387]=413,	-- depth:6
[386]=360,	-- depth:5
[385]=359,	-- depth:5
[384]=358,	-- depth:5
[376]=402,	-- depth:4
[404]=326,	-- depth:3
[406]=380,	-- depth:6
[407]=199,	-- depth:6
[383]=357,	-- depth:5
[382]=356,	-- depth:5
[408]=382,	-- depth:6
[409]=383,	-- depth:6
[381]=407,	-- depth:7
[410]=384,	-- depth:6
[400]=374,	-- depth:6
[379]=171,	-- depth:5
[378]=404,	-- depth:4
[377]=403,	-- depth:4
[411]=385,	-- depth:6
[412]=386,	-- depth:6
[405]=379,	-- depth:6
[355]=381,	-- depth:8
[317]=109,	-- depth:3
[353]=405,	-- depth:7
[354]=406,	-- depth:7
[291]=83,	-- depth:5
[289]=211,	-- depth:3
[288]=210,	-- depth:3
[158]=106,	-- depth:3
[159]=107,	-- depth:3
[161]=109,	-- depth:3
[164]=112,	-- depth:3
[165]=113,	-- depth:3
[269]=61,	-- depth:4
[268]=216,	-- depth:3
[265]=291,	-- depth:6
[263]=289,	-- depth:4
[262]=288,	-- depth:4
[184]=158,	-- depth:4
[185]=159,	-- depth:4
[187]=161,	-- depth:4
[190]=164,	-- depth:4
[243]=269,	-- depth:5
[242]=268,	-- depth:4
[191]=165,	-- depth:4
[239]=265,	-- depth:7
[237]=263,	-- depth:5
[236]=262,	-- depth:5
[415]=389,	-- depth:5
[295]=243,	-- depth:6
[139]=191,	-- depth:5
[294]=242,	-- depth:5
[135]=187,	-- depth:5
[352]=378,	-- depth:5
[351]=377,	-- depth:5
[350]=376,	-- depth:5
[349]=401,	-- depth:5
[348]=400,	-- depth:7
[345]=371,	-- depth:5
[344]=396,	-- depth:7
[342]=394,	-- depth:6
[138]=190,	-- depth:5
[416]=390,	-- depth:6
[133]=185,	-- depth:5
[132]=184,	-- depth:5
[314]=210,	-- depth:3
[315]=211,	-- depth:3
[320]=216,	-- depth:3
[321]=113,	-- depth:3
[339]=391,	-- depth:5
[395]=317,	-- depth:4
[347]=321,	-- depth:4
[346]=320,	-- depth:4
[343]=395,	-- depth:5
[366]=314,	-- depth:4
[367]=315,	-- depth:4
[392]=314,	-- depth:4
[369]=343,	-- depth:6
[393]=315,	-- depth:4
[399]=347,	-- depth:5
[398]=346,	-- depth:5
[340]=314,	-- depth:4
[372]=398,	-- depth:6
[373]=399,	-- depth:6
[341]=315,	-- depth:4
},
item_random_desc={
{item_name="幽梦神鱼",item_id=37034,random_count=0.03,is_rare=1,},
{number=1,item_name="引梦魔鼎",item_id=37425,random_count=0.02,},
{number=2,item_name="灵梦仙翼",item_id=37229,random_count=0.01,},
{number=3,random_count=0.53,},
{number=4,item_name="史诗合鸣石",item_id=45017,random_count=0.53,},
{number=5,item_name="紫4星幽冥鬼蜮自选",item_id=28826,random_count=0.53,},
{number=6,item_name="圣品神纹自选包",item_id=48088,random_count=0.5,},
{number=7,item_name="完美合鸣石自选包",item_id=48071,random_count=1.06,},
{number=8,item_name="仙缘石·金",item_id=26193,random_count=0.21,},
{number=9,item_name="仙缘石·粉",item_id=26191,random_count=0.53,},
{number=10,item_name="极品聚灵瓶",item_id=27909,},
{number=11,item_name="超品聚灵瓶",item_id=27908,},
{number=12,item_name="3级攻击玉魄",item_id=26502,random_count=10.61,},
{number=13,item_name="3级生命玉魄",item_id=26517,random_count=10.61,},
{number=14,item_name="橙色图鉴经验",item_id=28807,random_count=2.12,},
{number=15,item_name="高级陨之灵",item_id=26354,},
{number=16,item_name="高级星之灵",item_id=26351,},
{number=17,item_name="高级灵剑丹",item_id=26377,},
{number=18,item_name="高级灵骑丹",item_id=26345,},
{number=19,item_name="强化水晶*5",item_id=26200,},
{number=20,item_name="强化水晶·饰品*5",item_id=26203,},
{number=21,item_name="中级魂之灵",item_id=26371,},
{number=22,item_name="战纹精华",item_id=30422,random_count=9.02,},
{number=23,item_name="5级生命玉魄",item_id=26519,random_count=1.06,},
{number=24,item_name="4级攻击玉魄",item_id=26503,random_count=4.24,},
{number=25,item_name="洗炼石*4",item_id=26415,random_count=10.61,},
{activity_day=2,item_name="圣诞花环",item_id=37426,},
{activity_day=2,item_name="圣诞赠礼",item_id=37227,},
{activity_day=2,item_name="柠檬甜",item_id=38656,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=3,item_name="朱雀焰",item_id=37422,},
{activity_day=3,item_name="花仙使者",item_id=38739,},
{activity_day=3,item_name="小福星",item_id=38667,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=4,item_name="神赐天翼",item_id=37218,},
{activity_day=4,item_name="火灵使者",item_id=38734,},
{activity_day=4,item_name="裁判所",item_id=38668,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{grade=2,item_name="精灵火羽",item_id=37052,},
{grade=2,item_name="元宵花灯",item_id=38743,},
{grade=2,item_name="幻界神鱼",item_id=37219,},
{grade=2,},
{grade=2,item_name="天星珠",item_id=26369,},
{grade=2,item_name="属性丹随机礼包",item_id=44180,},
{grade=2,item_name="幻星珠",item_id=26355,},
{grade=2,item_name="地星珠",item_id=26356,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,item_name="强化水晶",},
{grade=2,item_name="强化水晶·饰品",},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,item_name="洗炼石",},
{grade=2,item_name="八尺明镜",item_id=37236,},
{grade=2,item_name="小月儿",item_id=37427,},
{grade=2,item_name="幻梦蝶翼",item_id=38127,},
{activity_day=2,},
{activity_day=2,item_name="破陨珠",item_id=26360,},
{activity_day=2,},
{activity_day=2,item_name="玄陨珠",item_id=26358,},
{activity_day=2,item_name="灵陨珠",item_id=26359,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{grade=2,item_name="忠贞爱情",item_id=38126,},
{grade=2,item_name="莺莺燕羽",item_id=38654,},
{grade=2,item_name="寒梅雪琼",item_id=37220,},
{activity_day=3,},
{activity_day=3,item_name="摄魂珠",item_id=26363,},
{activity_day=3,},
{activity_day=3,item_name="离魂珠",item_id=26361,},
{activity_day=3,item_name="追魂珠",item_id=26362,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{grade=2,item_id=37051,},
{grade=2,item_id=37428,},
{grade=2,item_id=37515,},
{grade=2,},
{grade=2,item_id=26375,},
{activity_day=4,item_name="紫4星幽冥鬼蜮自选",},
{grade=2,item_id=26373,},
{grade=2,item_id=26374,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{grade=2,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{grade=2,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_type=2305,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_day=4,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_day=4,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{grade=2,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{grade=2,},
{grade=2,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{activity_type=2305,},
{activity_type=2305,},
{grade=2,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_day=2,},
{activity_day=2,},
{activity_type=2305,},
{activity_type=2305,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_day=3,},
{activity_type=2305,},
{activity_day=3,},
{activity_type=2305,},
{activity_type=2305,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{grade=2,},
{activity_type=2305,},
{grade=2,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_day=4,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_type=2305,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{grade=2,},
{grade=2,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_type=2305,}
},

item_random_desc_meta_table_map={
[108]=4,	-- depth:1
[30]=4,	-- depth:1
[82]=30,	-- depth:2
[56]=82,	-- depth:3
[212]=4,	-- depth:1
[121]=17,	-- depth:1
[74]=22,	-- depth:1
[73]=21,	-- depth:1
[72]=20,	-- depth:1
[71]=19,	-- depth:1
[70]=18,	-- depth:1
[69]=17,	-- depth:1
[68]=16,	-- depth:1
[122]=18,	-- depth:1
[123]=19,	-- depth:1
[124]=20,	-- depth:1
[64]=12,	-- depth:1
[63]=11,	-- depth:1
[125]=21,	-- depth:1
[126]=22,	-- depth:1
[238]=212,	-- depth:2
[120]=16,	-- depth:1
[115]=11,	-- depth:1
[229]=21,	-- depth:1
[228]=20,	-- depth:1
[116]=12,	-- depth:1
[219]=11,	-- depth:1
[94]=68,	-- depth:2
[95]=69,	-- depth:2
[96]=70,	-- depth:2
[97]=71,	-- depth:2
[98]=72,	-- depth:2
[230]=22,	-- depth:1
[99]=73,	-- depth:2
[89]=63,	-- depth:2
[220]=12,	-- depth:1
[224]=16,	-- depth:1
[186]=82,	-- depth:3
[100]=74,	-- depth:2
[226]=18,	-- depth:1
[227]=19,	-- depth:1
[90]=64,	-- depth:2
[225]=17,	-- depth:1
[316]=212,	-- depth:2
[264]=238,	-- depth:3
[290]=264,	-- depth:4
[37]=89,	-- depth:3
[38]=90,	-- depth:3
[42]=94,	-- depth:3
[43]=95,	-- depth:3
[44]=96,	-- depth:3
[45]=97,	-- depth:3
[46]=98,	-- depth:3
[47]=99,	-- depth:3
[48]=100,	-- depth:3
[134]=186,	-- depth:4
[160]=134,	-- depth:5
[342]=134,	-- depth:5
[203]=99,	-- depth:3
[199]=95,	-- depth:3
[201]=97,	-- depth:3
[200]=96,	-- depth:3
[202]=98,	-- depth:3
[328]=224,	-- depth:2
[334]=230,	-- depth:2
[194]=90,	-- depth:3
[330]=226,	-- depth:2
[331]=227,	-- depth:2
[324]=220,	-- depth:2
[323]=219,	-- depth:2
[332]=124,	-- depth:2
[178]=126,	-- depth:2
[177]=125,	-- depth:2
[198]=94,	-- depth:3
[176]=124,	-- depth:2
[174]=200,	-- depth:4
[173]=199,	-- depth:4
[172]=198,	-- depth:4
[333]=125,	-- depth:2
[168]=194,	-- depth:4
[167]=115,	-- depth:2
[193]=167,	-- depth:3
[175]=201,	-- depth:4
[329]=225,	-- depth:2
[232]=24,	-- depth:1
[209]=1,	-- depth:1
[246]=38,	-- depth:4
[298]=246,	-- depth:5
[297]=89,	-- depth:3
[250]=42,	-- depth:4
[251]=43,	-- depth:4
[252]=44,	-- depth:4
[253]=45,	-- depth:4
[254]=46,	-- depth:4
[255]=47,	-- depth:4
[256]=48,	-- depth:4
[282]=256,	-- depth:5
[271]=297,	-- depth:4
[272]=298,	-- depth:6
[281]=255,	-- depth:5
[280]=254,	-- depth:5
[279]=253,	-- depth:5
[276]=250,	-- depth:5
[245]=271,	-- depth:5
[204]=178,	-- depth:3
[302]=276,	-- depth:6
[304]=252,	-- depth:5
[213]=5,	-- depth:1
[214]=6,	-- depth:1
[215]=7,	-- depth:1
[216]=8,	-- depth:1
[217]=9,	-- depth:1
[218]=10,	-- depth:1
[221]=13,	-- depth:1
[222]=14,	-- depth:1
[223]=15,	-- depth:1
[231]=23,	-- depth:1
[152]=204,	-- depth:4
[233]=25,	-- depth:1
[234]=26,	-- depth:1
[308]=282,	-- depth:6
[307]=281,	-- depth:6
[306]=280,	-- depth:6
[305]=279,	-- depth:6
[303]=251,	-- depth:5
[151]=177,	-- depth:3
[278]=304,	-- depth:6
[149]=175,	-- depth:5
[67]=15,	-- depth:1
[75]=23,	-- depth:1
[76]=24,	-- depth:1
[77]=25,	-- depth:1
[78]=26,	-- depth:1
[79]=1,	-- depth:1
[83]=5,	-- depth:1
[84]=6,	-- depth:1
[85]=7,	-- depth:1
[86]=8,	-- depth:1
[87]=9,	-- depth:1
[88]=10,	-- depth:1
[91]=13,	-- depth:1
[92]=14,	-- depth:1
[93]=67,	-- depth:2
[101]=75,	-- depth:2
[150]=176,	-- depth:3
[103]=77,	-- depth:2
[104]=78,	-- depth:2
[105]=1,	-- depth:1
[109]=5,	-- depth:1
[66]=92,	-- depth:2
[110]=6,	-- depth:1
[65]=91,	-- depth:2
[61]=87,	-- depth:2
[2]=1,	-- depth:1
[3]=1,	-- depth:1
[27]=1,	-- depth:1
[31]=83,	-- depth:2
[32]=84,	-- depth:2
[33]=85,	-- depth:2
[34]=86,	-- depth:2
[35]=61,	-- depth:3
[36]=88,	-- depth:2
[39]=65,	-- depth:3
[40]=66,	-- depth:3
[41]=93,	-- depth:3
[49]=101,	-- depth:3
[50]=76,	-- depth:2
[51]=103,	-- depth:3
[52]=104,	-- depth:3
[53]=1,	-- depth:1
[57]=31,	-- depth:3
[58]=32,	-- depth:3
[59]=33,	-- depth:3
[60]=34,	-- depth:3
[62]=36,	-- depth:3
[111]=7,	-- depth:1
[102]=50,	-- depth:3
[113]=9,	-- depth:1
[146]=172,	-- depth:5
[112]=8,	-- depth:1
[142]=168,	-- depth:5
[141]=193,	-- depth:4
[277]=303,	-- depth:6
[147]=173,	-- depth:5
[148]=174,	-- depth:5
[394]=342,	-- depth:6
[130]=26,	-- depth:1
[368]=394,	-- depth:7
[128]=24,	-- depth:1
[127]=23,	-- depth:1
[119]=15,	-- depth:1
[129]=25,	-- depth:1
[114]=10,	-- depth:1
[117]=13,	-- depth:1
[118]=14,	-- depth:1
[286]=78,	-- depth:2
[336]=128,	-- depth:2
[337]=129,	-- depth:2
[360]=152,	-- depth:5
[359]=151,	-- depth:4
[338]=130,	-- depth:2
[358]=150,	-- depth:4
[412]=360,	-- depth:6
[349]=141,	-- depth:5
[350]=142,	-- depth:6
[411]=307,	-- depth:7
[296]=88,	-- depth:2
[295]=87,	-- depth:2
[356]=148,	-- depth:6
[294]=86,	-- depth:2
[293]=85,	-- depth:2
[292]=84,	-- depth:2
[291]=83,	-- depth:2
[283]=75,	-- depth:2
[354]=146,	-- depth:6
[284]=76,	-- depth:2
[285]=77,	-- depth:2
[355]=147,	-- depth:6
[287]=79,	-- depth:2
[357]=149,	-- depth:6
[299]=91,	-- depth:2
[376]=350,	-- depth:7
[410]=306,	-- depth:7
[402]=376,	-- depth:8
[313]=105,	-- depth:2
[312]=286,	-- depth:3
[311]=285,	-- depth:3
[406]=354,	-- depth:7
[310]=284,	-- depth:3
[309]=283,	-- depth:3
[317]=109,	-- depth:2
[318]=110,	-- depth:2
[319]=111,	-- depth:2
[320]=112,	-- depth:2
[321]=113,	-- depth:2
[322]=114,	-- depth:2
[325]=117,	-- depth:2
[326]=118,	-- depth:2
[327]=119,	-- depth:2
[407]=355,	-- depth:7
[408]=356,	-- depth:7
[401]=349,	-- depth:6
[386]=412,	-- depth:7
[385]=359,	-- depth:5
[384]=358,	-- depth:5
[383]=357,	-- depth:7
[382]=408,	-- depth:8
[380]=406,	-- depth:8
[375]=401,	-- depth:7
[409]=383,	-- depth:8
[301]=93,	-- depth:3
[300]=92,	-- depth:2
[335]=127,	-- depth:2
[381]=407,	-- depth:8
[208]=104,	-- depth:3
[274]=300,	-- depth:3
[166]=114,	-- depth:2
[169]=117,	-- depth:2
[170]=118,	-- depth:2
[171]=119,	-- depth:2
[179]=127,	-- depth:2
[180]=128,	-- depth:2
[181]=129,	-- depth:2
[182]=130,	-- depth:2
[183]=79,	-- depth:2
[143]=169,	-- depth:3
[187]=83,	-- depth:2
[188]=110,	-- depth:2
[189]=85,	-- depth:2
[190]=86,	-- depth:2
[191]=113,	-- depth:2
[165]=191,	-- depth:3
[164]=112,	-- depth:2
[107]=3,	-- depth:2
[131]=27,	-- depth:2
[140]=166,	-- depth:3
[139]=165,	-- depth:4
[145]=171,	-- depth:3
[138]=112,	-- depth:2
[137]=111,	-- depth:2
[136]=110,	-- depth:2
[135]=109,	-- depth:2
[192]=140,	-- depth:4
[153]=179,	-- depth:3
[155]=181,	-- depth:3
[156]=182,	-- depth:3
[157]=53,	-- depth:2
[161]=109,	-- depth:2
[162]=136,	-- depth:3
[275]=301,	-- depth:4
[163]=111,	-- depth:2
[154]=180,	-- depth:3
[195]=143,	-- depth:4
[144]=170,	-- depth:3
[197]=145,	-- depth:4
[29]=3,	-- depth:2
[248]=274,	-- depth:4
[249]=275,	-- depth:5
[257]=309,	-- depth:4
[261]=53,	-- depth:2
[273]=299,	-- depth:3
[247]=273,	-- depth:4
[270]=296,	-- depth:3
[196]=144,	-- depth:4
[258]=310,	-- depth:4
[259]=311,	-- depth:4
[260]=312,	-- depth:4
[268]=294,	-- depth:3
[267]=293,	-- depth:3
[269]=295,	-- depth:3
[244]=270,	-- depth:4
[243]=269,	-- depth:4
[242]=268,	-- depth:4
[205]=153,	-- depth:4
[106]=2,	-- depth:2
[81]=3,	-- depth:2
[206]=154,	-- depth:4
[207]=155,	-- depth:4
[80]=2,	-- depth:2
[28]=2,	-- depth:2
[210]=2,	-- depth:2
[211]=3,	-- depth:2
[235]=27,	-- depth:2
[55]=3,	-- depth:2
[54]=2,	-- depth:2
[239]=291,	-- depth:3
[240]=292,	-- depth:3
[241]=267,	-- depth:4
[266]=240,	-- depth:4
[265]=239,	-- depth:4
[378]=170,	-- depth:3
[379]=275,	-- depth:5
[387]=179,	-- depth:3
[377]=273,	-- depth:4
[133]=29,	-- depth:3
[388]=180,	-- depth:3
[396]=188,	-- depth:3
[413]=387,	-- depth:4
[389]=181,	-- depth:3
[390]=182,	-- depth:3
[391]=183,	-- depth:3
[132]=28,	-- depth:3
[395]=187,	-- depth:3
[397]=189,	-- depth:3
[398]=190,	-- depth:3
[399]=191,	-- depth:3
[400]=192,	-- depth:5
[403]=377,	-- depth:5
[405]=379,	-- depth:6
[414]=388,	-- depth:4
[404]=378,	-- depth:4
[348]=400,	-- depth:6
[373]=399,	-- depth:4
[345]=137,	-- depth:3
[344]=136,	-- depth:3
[343]=135,	-- depth:3
[339]=131,	-- depth:3
[184]=80,	-- depth:3
[185]=81,	-- depth:3
[315]=107,	-- depth:3
[314]=106,	-- depth:3
[415]=389,	-- depth:4
[236]=28,	-- depth:3
[237]=29,	-- depth:3
[262]=54,	-- depth:3
[263]=55,	-- depth:3
[289]=81,	-- depth:3
[288]=80,	-- depth:3
[346]=138,	-- depth:3
[374]=348,	-- depth:7
[347]=373,	-- depth:5
[352]=404,	-- depth:5
[372]=164,	-- depth:3
[371]=163,	-- depth:3
[370]=344,	-- depth:4
[369]=161,	-- depth:3
[365]=157,	-- depth:3
[364]=390,	-- depth:4
[351]=403,	-- depth:6
[362]=414,	-- depth:5
[363]=415,	-- depth:5
[158]=54,	-- depth:3
[159]=55,	-- depth:3
[353]=405,	-- depth:7
[361]=413,	-- depth:5
[416]=208,	-- depth:4
[340]=132,	-- depth:4
[341]=133,	-- depth:4
[366]=158,	-- depth:4
[367]=159,	-- depth:4
[392]=184,	-- depth:4
[393]=185,	-- depth:4
},
baodi={
{},
{times=2800,itemlist={[0]=item_table[3]},},
{times=5000,itemlist={[0]=item_table[1]},},
{times=7500,itemlist={[0]=item_table[2],[1]=item_table[3],[2]=item_table[1]},},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,itemlist={[0]=item_table[26]},},
{activity_day=2,itemlist={[0]=item_table[2],[1]=item_table[3],[2]=item_table[26]},},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,itemlist={[0]=item_table[27]},},
{activity_day=3,itemlist={[0]=item_table[2],[1]=item_table[3],[2]=item_table[27]},},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,itemlist={[0]=item_table[28]},},
{activity_day=4,itemlist={[0]=item_table[2],[1]=item_table[3],[2]=item_table[28]},},
{grade=2,},
{grade=2,times=4200,},
{grade=2,},
{grade=2,},
{activity_day=2,},
{activity_day=2,},
{grade=2,},
{grade=2,},
{activity_day=3,},
{activity_day=3,},
{grade=2,},
{grade=2,},
{activity_day=4,},
{activity_day=4,},
{grade=2,},
{grade=2,}
},

baodi_meta_table_map={
[29]=17,	-- depth:1
[25]=29,	-- depth:2
[21]=25,	-- depth:3
[20]=4,	-- depth:1
[19]=3,	-- depth:1
[18]=2,	-- depth:1
[16]=4,	-- depth:1
[14]=2,	-- depth:1
[12]=4,	-- depth:1
[11]=3,	-- depth:1
[10]=14,	-- depth:2
[8]=4,	-- depth:1
[6]=10,	-- depth:3
[15]=3,	-- depth:1
[7]=3,	-- depth:1
[31]=15,	-- depth:2
[30]=18,	-- depth:2
[28]=12,	-- depth:2
[24]=8,	-- depth:2
[26]=30,	-- depth:3
[23]=7,	-- depth:2
[22]=26,	-- depth:4
[27]=11,	-- depth:2
[32]=16,	-- depth:2
},
times_reward={
{itemlist={[0]=item_table[49]},},
{seq=2,need_times=30,},
{seq=3,need_times=50,itemlist={[0]=item_table[50]},},
{seq=4,need_times=100,},
{seq=5,need_times=150,},
{seq=6,need_times=200,},
{seq=7,need_times=300,itemlist={[0]=item_table[51]},},
{seq=8,need_times=400,},
{seq=9,need_times=500,itemlist={[0]=item_table[32]},},
{seq=10,need_times=600,},
{seq=11,need_times=700,itemlist={[0]=item_table[6]},},
{seq=12,need_times=800,itemlist={[0]=item_table[52]},},
{seq=13,need_times=900,},
{seq=14,need_times=1000,itemlist={[0]=item_table[53]},},
{seq=15,need_times=1200,itemlist={[0]=item_table[54]},},
{seq=16,need_times=1400,},
{seq=17,need_times=1600,itemlist={[0]=item_table[55]},},
{seq=18,need_times=1800,itemlist={[0]=item_table[56]},},
{seq=19,need_times=2000,},
{seq=20,need_times=2400,},
{seq=21,need_times=2800,itemlist={[0]=item_table[57]},},
{seq=22,need_times=3200,},
{seq=23,need_times=3600,itemlist={[0]=item_table[8]},},
{seq=24,need_times=4000,itemlist={[0]=item_table[58]},},
{seq=25,need_times=4400,itemlist={[0]=item_table[59]},},
{seq=26,need_times=4800,itemlist={[0]=item_table[60]},},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

times_reward_meta_table_map={
[27]=1,	-- depth:1
[36]=10,	-- depth:1
[42]=16,	-- depth:1
[34]=8,	-- depth:1
[32]=6,	-- depth:1
[30]=4,	-- depth:1
[46]=20,	-- depth:1
[48]=22,	-- depth:1
[19]=23,	-- depth:1
[5]=3,	-- depth:1
[13]=25,	-- depth:1
[2]=1,	-- depth:1
[49]=23,	-- depth:1
[43]=17,	-- depth:1
[50]=24,	-- depth:1
[45]=19,	-- depth:2
[44]=18,	-- depth:1
[47]=21,	-- depth:1
[37]=11,	-- depth:1
[40]=14,	-- depth:1
[39]=13,	-- depth:2
[38]=12,	-- depth:1
[35]=9,	-- depth:1
[33]=7,	-- depth:1
[31]=5,	-- depth:2
[29]=3,	-- depth:1
[28]=2,	-- depth:2
[51]=25,	-- depth:1
[41]=15,	-- depth:1
[52]=26,	-- depth:1
},
type_default_table={activity_type=2301,seq=0,cost_item_id=26176,cost_gold=40,},

open_day_default_table={activity_type=2301,start_day=1,end_day=17,grade=1,},

mode_default_table={activity_type=2301,mode=1,times=1,cost_item_num=1,},

reward_pool_default_table={activity_type=2301,grade=1,activity_day=1,seq=0,item=item_table[61],is_rare=0,},

item_random_desc_default_table={activity_type=2301,grade=1,activity_day=1,number=0,item_name="天命自选礼包",item_id=48187,random_count=5.31,is_rare=0,},

baodi_default_table={activity_type=2301,grade=1,activity_day=1,times=1400,itemlist={[0]=item_table[2]},},

times_reward_default_table={activity_type=2301,grade=1,seq=1,need_times=10,itemlist={[0]=item_table[62]},}

}

