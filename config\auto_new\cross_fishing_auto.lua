-- D-钓鱼.xls
local item_table={
[1]={item_id=22000,num=1,is_bind=1},
}
return {
other={
{}},

activity_open_time={
{},
{},
{}},

fish={
{least_count_1=15,least_count_2=18,least_count_3=12,},
{type=2,name="海虾",score=50,least_count_1=12,least_count_3=14,},
{type=3,name="大黄鱼",score=80,least_count_1=8,least_count_3=10,},
{type=4,name="金枪鱼",score=120,least_count_2=7,},
{type=5,name="毛蟹",score=200,least_count_2=7,},
{type=6,name="海龟",score=500,least_count_1=4,least_count_2=3,least_count_3=2,},
{type=7,name="海马",score=800,least_count_1=0,least_count_2=0,least_count_3=1,}},

fish_bait={
{},
{type=1,name="特级鱼饵",},
{type=2,name="黄金鱼饵",}},

event={
{},
{type=1,name="破旧宝箱",},
{type=2,name="垂钓的渔翁",},
{type=3,name="遇到盗贼",},
{type=4,name="传说中的大鱼",}},

treasure={
{},
{seq=1,weight=50,},
{seq=2,weight=50,},
{seq=3,weight=50,},
{seq=4,weight=50,},
{seq=5,weight=50,},
{seq=6,weight=100,},
{seq=7,weight=30,},
{seq=8,},
{seq=9,},
{seq=10,weight=10,},
{seq=11,weight=10,},
{seq=12,},
{seq=13,weight=2,},
{seq=14,},
{seq=15,weight=1,}},

steal_count_buy={
{},
{buy_count=1,need_gold=60,},
{buy_count=2,need_gold=70,},
{buy_count=3,need_gold=80,},
{buy_count=4,need_gold=90,}},

other_default_table={is_open=1,open_level=60,sceneid=5701,enter_pos_x=104,enter_pos_y=37,pull_count_down_s=6,give_bait_count=100,oil_special_status_duration=5,auto_fishing_need_gold=50,steal_count=5,be_stealed_count=5,steal_succ_rate=50,steal_succ_rate=50,steal_buy_count=5,},

activity_open_time_default_table={},

fish_default_table={type=1,name="秋刀鱼",score=30,least_count_1=6,least_count_2=8,least_count_3=4,be_stealed_rate=15,},

fish_bait_default_table={type=0,name="普通鱼饵",item_id=22000,},

event_default_table={type=0,name="鱼类上钩",},

treasure_default_table={seq=0,reward_item=item_table[1],weight=200,},

steal_count_buy_default_table={buy_count=0,need_gold=50,}

}

