-- k-跨服仙玉转盘.xls

return {
other={
{}
},

other_meta_table_map={
},
draw={
{weight=30000,gold_limit_max=5000,},
{reward_id=14,},
{reward_id=15,weight=574138,},
{reward_id=16,weight=50000,gold_limit_min=6000,gold_limit_max=10000,},
{reward_id=17,weight=1810426,},
{reward_id=18,weight=581184,},
{reward_id=19,},
{reward_id=20,weight=3413020,},
{reward_id=21,weight=50433,},
{reward_id=22,weight=110000,gold_limit_min=3333,},
{reward_id=23,weight=1505170,},
{reward_id=24,weight=55203,},
{jiangchi_index=1,weight=20000,},
{jiangchi_index=1,weight=1814870,},
{jiangchi_index=1,weight=578583,},
{jiangchi_index=1,weight=30000,},
{reward_id=17,},
{jiangchi_index=1,weight=590073,},
{jiangchi_index=1,},
{jiangchi_index=1,weight=3417465,},
{jiangchi_index=1,weight=54878,},
{jiangchi_index=1,weight=100000,},
{jiangchi_index=1,weight=1509614,},
{jiangchi_index=1,weight=59647,},
{jiangchi_index=2,weight=0,gold_limit_max=9999999,},
{reward_id=14,},
{jiangchi_index=2,weight=588027,},
{jiangchi_index=2,reward_id=16,gold_limit_min=8000,gold_limit_max=10000,},
{jiangchi_index=2,weight=1824315,},
{jiangchi_index=2,weight=608962,},
{jiangchi_index=2,},
{jiangchi_index=2,weight=3426909,},
{jiangchi_index=2,weight=64322,},
{jiangchi_index=2,reward_id=22,weight=55000,gold_limit_min=4444,},
{jiangchi_index=2,weight=1519058,},
{jiangchi_index=2,weight=69092,},
{open_day=9999,reward_id=1,},
{reward_id=2,},
{open_day=9999,reward_id=3,},
{open_day=9999,reward_id=4,},
{open_day=9999,reward_id=5,},
{open_day=9999,reward_id=6,},
{open_day=9999,reward_id=7,},
{open_day=9999,reward_id=8,},
{open_day=9999,reward_id=9,},
{open_day=9999,reward_id=10,},
{open_day=9999,reward_id=11,},
{open_day=9999,reward_id=12,},
{jiangchi_index=1,weight=20000,},
{reward_id=2,},
{open_day=9999,reward_id=3,},
{open_day=9999,reward_id=4,},
{reward_id=5,weight=1814870,},
{open_day=9999,reward_id=6,},
{jiangchi_index=1,},
{open_day=9999,reward_id=8,},
{open_day=9999,reward_id=9,},
{jiangchi_index=1,weight=100000,},
{open_day=9999,reward_id=11,},
{open_day=9999,reward_id=12,},
{open_day=9999,reward_id=1,},
{reward_id=2,},
{open_day=9999,reward_id=3,},
{open_day=9999,reward_id=4,},
{open_day=9999,reward_id=5,},
{jiangchi_index=2,weight=608962,},
{jiangchi_index=2,},
{jiangchi_index=2,weight=3426909,},
{jiangchi_index=2,weight=64322,},
{open_day=9999,reward_id=10,},
{jiangchi_index=2,weight=1519058,},
{open_day=9999,reward_id=12,}
},

draw_meta_table_map={
[19]=7,	-- depth:1
[31]=19,	-- depth:2
[2]=5,	-- depth:1
[41]=5,	-- depth:1
[33]=9,	-- depth:1
[35]=11,	-- depth:1
[38]=41,	-- depth:2
[39]=3,	-- depth:1
[32]=8,	-- depth:1
[47]=11,	-- depth:1
[44]=8,	-- depth:1
[45]=9,	-- depth:1
[30]=6,	-- depth:1
[48]=12,	-- depth:1
[55]=43,	-- depth:1
[67]=55,	-- depth:2
[42]=6,	-- depth:1
[29]=5,	-- depth:1
[36]=12,	-- depth:1
[27]=3,	-- depth:1
[13]=1,	-- depth:1
[14]=2,	-- depth:2
[15]=3,	-- depth:1
[17]=14,	-- depth:3
[18]=6,	-- depth:1
[21]=9,	-- depth:1
[20]=8,	-- depth:1
[23]=11,	-- depth:1
[24]=12,	-- depth:1
[26]=29,	-- depth:2
[66]=42,	-- depth:2
[68]=44,	-- depth:2
[65]=29,	-- depth:2
[56]=20,	-- depth:2
[69]=45,	-- depth:2
[63]=27,	-- depth:2
[62]=65,	-- depth:3
[60]=24,	-- depth:2
[59]=23,	-- depth:2
[57]=21,	-- depth:2
[51]=15,	-- depth:2
[53]=55,	-- depth:2
[71]=47,	-- depth:2
[37]=1,	-- depth:1
[22]=10,	-- depth:1
[54]=18,	-- depth:2
[46]=10,	-- depth:1
[72]=36,	-- depth:2
[50]=53,	-- depth:3
[49]=37,	-- depth:2
[64]=28,	-- depth:1
[40]=4,	-- depth:1
[58]=46,	-- depth:2
[70]=34,	-- depth:1
[61]=25,	-- depth:1
[16]=4,	-- depth:1
[52]=16,	-- depth:2
},
reward={
{ret_gold=100,show=1,show_name="幸运100%奖池",},
{reward_id=2,item_id=36978,},
{reward_id=3,item_id=29853,},
{reward_id=4,ret_gold=50,item_id=91220,show=2,show_name="幸运50%奖池",},
{reward_id=5,item_id=36979,},
{reward_id=6,item_id=29852,},
{reward_id=7,},
{reward_id=8,},
{reward_id=9,},
{reward_id=10,ret_gold=10,item_id=91216,show=3,show_name="幸运10%奖池",},
{reward_id=11,item_id=44180,},
{reward_id=12,item_id=26191,},
{reward_id=13,},
{reward_id=14,item_id=26347,},
{reward_id=15,item_id=50089,},
{reward_id=16,},
{reward_id=17,},
{reward_id=18,item_id=50085,},
{reward_id=19,item_id=48449,show_name="三等奖",},
{reward_id=20,item_id=26415,},
{reward_id=21,item_id=50093,},
{reward_id=22,},
{reward_id=23,item_id=48071,},
{reward_id=24,}
},

reward_meta_table_map={
[8]=20,	-- depth:1
[9]=21,	-- depth:1
[17]=11,	-- depth:1
[12]=19,	-- depth:1
[7]=19,	-- depth:1
[24]=12,	-- depth:2
[13]=1,	-- depth:1
[22]=10,	-- depth:1
[16]=4,	-- depth:1
},
draw_mode={
{},
{draw_type=1,count=10,cost_gold=300,}
},

draw_mode_meta_table_map={
},
other_default_table={init_gold=3000,cost_put_per=20,cost_gold=30,add_glod_desc="每抽1次，奖池增加5灵玉",},

draw_default_table={jiangchi_index=0,open_day=21,reward_id=13,weight=10000,gold_limit_min=1,gold_limit_max=99999999,},

reward_default_table={reward_id=1,ret_gold=0,item_id=91225,show=0,show_name="幸运奖",},

draw_mode_default_table={draw_type=0,count=1,cost_gold=30,}

}

